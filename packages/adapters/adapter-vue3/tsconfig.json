{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "declaration": true, "declarationMap": true, "outDir": "dist", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@micro-core/shared/*": ["../../shared/*/src"], "@micro-core/types": ["../../shared/types/src"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}