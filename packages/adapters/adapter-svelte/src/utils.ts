/**
 * Svelte 适配器工具函数
 * 使用 shared 包的通用工具函数实现
 */

import {
    cleanupContainer,
    createAdapterErrorInfo,
    createEnhancedContainer,
    formatAdapterError,
    isFunction,
    isObject,
    isString,
    mergeConfigs
} from '@micro-core/shared/utils';
import type {
    SvelteAppInstance,
    SvelteComponentInstance,
    SvelteConfig
} from './types';

/**
 * 创建 Svelte 适配器
 */
export function createSvelteAdapter(config: SvelteConfig): SvelteAppInstance {
    const finalConfig = createDefaultSvelteConfig(config);

    return {
        name: finalConfig.name,
        config: finalConfig,
        mount: async (element: HTMLElement) => {
            try {
                const container = createSvelteContainer(finalConfig.name, element);
                const instance = await mountSvelteComponent(finalConfig, container);
                return { container, instance };
            } catch (error) {
                throw formatSvelteError(error, 'mount', finalConfig.name);
            }
        },
        unmount: async (instance: any) => {
            try {
                if (instance?.instance) {
                    instance.instance.$destroy();
                }
                if (instance?.container) {
                    cleanupContainer(instance.container);
                }
            } catch (error) {
                throw formatSvelteError(error, 'unmount', finalConfig.name);
            }
        }
    };
}

/**
 * 检查是否为 Svelte 应用
 */
export function isSvelteApp(app: any): boolean {
    if (!isObject(app)) return false;

    // 检查 Svelte 组件特征
    if (app.$$ || app.$$set || app.$destroy) return true;

    // 检查 Svelte 构造函数特征
    if (isFunction(app) && app.prototype && app.prototype.$destroy) return true;

    // 检查 Svelte 配置特征
    if (app.svelteConfig) return true;

    return false;
}

/**
 * 检查是否为 Svelte 入口
 */
export function isSvelteEntry(entry: any): boolean {
    if (!isObject(entry)) return false;

    // 检查是否有 Svelte 组件导出
    if (entry.App || entry.Component) return true;

    // 检查是否有 Svelte 配置导出
    if (entry.svelteConfig) return true;

    // 检查默认导出是否为 Svelte 组件
    if (entry.default && isSvelteComponent(entry.default)) return true;

    return false;
}

/**
 * 获取 Svelte 版本
 */
export function getSvelteVersion(): string | null {
    try {
        // 尝试从全局对象获取
        if (typeof window !== 'undefined' && (window as any).svelte) {
            return (window as any).svelte.VERSION || null;
        }

        // 尝试从 Svelte 包获取
        const svelte = require('svelte/package.json');
        return svelte.version || null;
    } catch {
        return null;
    }
}

/**
 * 检查 Svelte 版本兼容性
 */
export function isSvelteVersionCompatible(version: string, minVersion = '3.0.0'): boolean {
    if (!version) return false;

    const parseVersion = (v: string) => {
        const parts = v.split('.').map(Number);
        return parts[0] * 10000 + (parts[1] || 0) * 100 + (parts[2] || 0);
    };

    return parseVersion(version) >= parseVersion(minVersion);
}

/**
 * 验证 Svelte 配置
 */
export function validateSvelteConfig(config: Partial<SvelteConfig>): string[] {
    const errors: string[] = [];

    if (!config.name || !isString(config.name)) {
        errors.push('配置中缺少有效的应用名称');
    }

    if (!config.component) {
        errors.push('配置中缺少 Svelte 组件');
    }

    if (config.svelte?.dev !== undefined && typeof config.svelte.dev !== 'boolean') {
        errors.push('svelte.dev 必须是布尔值');
    }

    if (config.svelte?.hydratable !== undefined && typeof config.svelte.hydratable !== 'boolean') {
        errors.push('svelte.hydratable 必须是布尔值');
    }

    return errors;
}

/**
 * 创建默认 Svelte 配置
 */
export function createDefaultSvelteConfig(config: Partial<SvelteConfig>): SvelteConfig {
    const defaultConfig: SvelteConfig = {
        name: '',
        component: null,
        props: {},
        svelte: {
            dev: process.env.NODE_ENV === 'development',
            hydratable: false,
            immutable: false,
            accessors: false
        },
        container: {
            className: 'svelte-app-container',
            style: {}
        },
        lifecycle: {}
    };

    return mergeConfigs(defaultConfig, config);
}

/**
 * 提取 Svelte 组件
 * 重构为多个简单函数以提高可测试性
 */
export function extractSvelteComponent(moduleExports: any, preferredName?: string): any {
    if (!isObject(moduleExports)) {
        throw new Error('模块导出必须是对象');
    }

    // 1. 检查首选组件
    const preferredComponent = checkPreferredSvelteComponent(moduleExports, preferredName);
    if (preferredComponent) return preferredComponent;

    // 2. 检查默认导出
    const defaultComponent = checkDefaultSvelteComponent(moduleExports);
    if (defaultComponent) return defaultComponent;

    // 3. 获取命名导出
    const namedComponents = getNamedSvelteComponents(moduleExports);
    if (namedComponents.length > 0) {
        return selectBestSvelteComponent(namedComponents);
    }

    throw new Error('未找到有效的 Svelte 组件');
}

/**
 * 检查首选 Svelte 组件
 */
function checkPreferredSvelteComponent(moduleExports: any, preferredName?: string): any {
    if (!preferredName) return null;

    const component = moduleExports[preferredName];
    if (component && isSvelteComponent(component)) {
        return component;
    }

    return null;
}

/**
 * 检查默认 Svelte 组件
 */
function checkDefaultSvelteComponent(moduleExports: any): any {
    const defaultExport = moduleExports.default;
    if (defaultExport && isSvelteComponent(defaultExport)) {
        return defaultExport;
    }

    return null;
}

/**
 * 获取命名 Svelte 组件
 */
function getNamedSvelteComponents(moduleExports: any): any[] {
    const components: any[] = [];

    for (const [key, value] of Object.entries(moduleExports)) {
        if (key !== 'default' && isSvelteComponent(value)) {
            components.push(value);
        }
    }

    return components;
}

/**
 * 选择最佳 Svelte 组件
 */
function selectBestSvelteComponent(components: any[]): any {
    // 优先选择有名称的组件
    const namedComponents = components.filter(comp => comp.name);
    if (namedComponents.length > 0) return namedComponents[0];

    // 返回第一个组件
    return components[0];
}

/**
 * 检查是否为 Svelte 组件
 */
export function isSvelteComponent(component: any): boolean {
    if (!isFunction(component)) return false;

    // 检查 Svelte 组件构造函数特征
    if (component.prototype && component.prototype.$destroy) return true;

    // 检查 Svelte 组件静态属性
    if (component.$$render || component.render) return true;

    return false;
}

/**
 * 检查是否为增强的 Svelte 组件
 */
export function isSvelteComponentEnhanced(component: any): boolean {
    if (!isSvelteComponent(component)) return false;

    // 检查是否有微应用增强功能
    return !!(component.microAppConfig || component.microAppHooks);
}

/**
 * 创建 Svelte 容器
 */
export function createSvelteContainer(appName: string, parentElement?: HTMLElement): HTMLElement {
    return createEnhancedContainer(appName, 'svelte', parentElement, {
        className: 'svelte-app-container',
        attributes: {
            'data-framework': 'svelte',
            'data-app': appName
        }
    });
}

/**
 * 挂载 Svelte 组件
 */
async function mountSvelteComponent(config: SvelteConfig, container: HTMLElement): Promise<SvelteComponentInstance> {
    const ComponentClass = config.component;

    if (!ComponentClass) {
        throw new Error('Svelte 组件不能为空');
    }

    // 创建组件实例
    const instance = new ComponentClass({
        target: container,
        props: config.props || {},
        ...config.svelte
    });

    return instance;
}

/**
 * 设置 Svelte 开发工具
 */
export function setupSvelteDevTools(appName: string, instance: SvelteComponentInstance): void {
    if (typeof window === 'undefined') return;

    try {
        // 注册到全局 Svelte 开发工具
        if (!(window as any).svelte) {
            (window as any).svelte = {};
        }

        (window as any).svelte[appName] = {
            instance,
            getComponent: () => instance,
            getProps: () => instance.$$.props || {}
        };
    } catch (error) {
        console.warn(`设置 Svelte 开发工具失败 (${appName}):`, error);
    }
}

/**
 * 格式化 Svelte 错误
 */
export function formatSvelteError(error: any, operation: string, appName: string): Error {
    return formatAdapterError(error, 'Svelte', operation, appName);
}

/**
 * 创建 Svelte 错误信息
 */
export function createSvelteErrorInfo(error: any, context: any): any {
    return createAdapterErrorInfo(error, 'Svelte', context);
}

/**
 * 合并 Svelte 配置
 */
export function mergeSvelteConfigs(base: Partial<SvelteConfig>, override: Partial<SvelteConfig>): SvelteConfig {
    return mergeConfigs(base, override);
}

/**
 * Svelte 微应用集成工具
 */
export class SvelteMicroAppIntegration {
    private instance: SvelteComponentInstance | null = null;
    private container: HTMLElement | null = null;

    constructor(private config: SvelteConfig) { }

    async mount(element: HTMLElement): Promise<void> {
        try {
            this.container = createSvelteContainer(this.config.name, element);
            this.instance = await mountSvelteComponent(this.config, this.container);

            // 设置开发工具
            if (this.config.svelte?.dev) {
                setupSvelteDevTools(this.config.name, this.instance);
            }

            // 触发生命周期钩子
            await this.config.lifecycle?.mounted?.(this.instance);
        } catch (error) {
            throw formatSvelteError(error, 'mount', this.config.name);
        }
    }

    async unmount(): Promise<void> {
        try {
            // 触发生命周期钩子
            await this.config.lifecycle?.beforeUnmount?.(this.instance);

            // 销毁组件实例
            if (this.instance) {
                this.instance.$destroy();
                this.instance = null;
            }

            // 清理容器
            if (this.container) {
                cleanupContainer(this.container);
                this.container = null;
            }

            // 触发生命周期钩子
            await this.config.lifecycle?.unmounted?.();
        } catch (error) {
            throw formatSvelteError(error, 'unmount', this.config.name);
        }
    }

    getInstance(): SvelteComponentInstance | null {
        return this.instance;
    }

    getContainer(): HTMLElement | null {
        return this.container;
    }

    async updateProps(props: Record<string, any>): Promise<void> {
        if (!this.instance) {
            throw new Error('组件未挂载，无法更新属性');
        }

        try {
            // 更新组件属性
            this.instance.$set(props);

            // 触发生命周期钩子
            await this.config.lifecycle?.updated?.(this.instance);
        } catch (error) {
            throw formatSvelteError(error, 'update', this.config.name);
        }
    }
}