/**
 * HTML Adapter Implementation
 * @description HTML 微前端适配器，基于新的 BaseAdapter 基础设施
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

import {
    BaseAdapter,
    BaseAdapterConfig
} from '@micro-core/shared/utils';

/**
 * HTML 适配器配置
 */
export interface HtmlAdapterConfig extends BaseAdapterConfig {
    /** HTML 内容或 URL */
    html?: string;
    /** 脚本 URL 列表 */
    scripts?: string[];
    /** 样式 URL 列表 */
    styles?: string[];
    /** 内联脚本 */
    inlineScripts?: string[];
    /** 内联样式 */
    inlineStyles?: string[];
    /** 是否自动清理资源 */
    autoCleanup?: boolean;
    /** 自定义初始化函数 */
    onInit?: () => void | Promise<void>;
    /** 自定义销毁函数 */
    onDestroy?: () => void | Promise<void>;
}

/**
 * HTML 应用实例
 */
export interface HtmlAppInstance {
    name: string;
    container?: HTMLElement;
    loadedScripts: HTMLScriptElement[];
    loadedStyles: HTMLLinkElement[];
    config: HtmlAdapterConfig;
    status: 'loaded' | 'mounted' | 'unmounted';
    createdAt: number;
    updatedAt: number;
}

/**
 * HTML 适配器类
 * @description 实现 HTML 微前端应用的加载、挂载、卸载等生命周期管理
 */
export class HtmlAdapter extends BaseAdapter {
    private appInstance: HtmlAppInstance | null = null;

    constructor(config: HtmlAdapterConfig) {
        super({
            name: (config as any).name || 'html-app',
            framework: 'html'
        });
    }

    /**
     * 检查是否能处理指定的应用配置
     * @param appConfig 应用配置
     * @returns 是否能处理
     */
    canHandle(appConfig: any): boolean {
        return isHtmlApp(appConfig);
    }

    /**
     * 加载 HTML 应用
     * @param config 应用配置
     */
    protected async doLoadApp(config: any): Promise<any> {
        // 验证配置
        validateHtmlConfig(config);

        // 创建应用实例
        this.appInstance = {
            name: config.name,
            loadedScripts: [],
            loadedStyles: [],
            config: {
                autoCleanup: true,
                ...config
            },
            status: 'loaded',
            createdAt: Date.now(),
            updatedAt: Date.now()
        } as HtmlAppInstance;

        return this.appInstance;
    }

    /**
     * 挂载 HTML 应用
     */
    protected async doMountApp(app: any): Promise<void> {
        // 准备容器
        app.container = this.prepareContainer();

        try {
            // 加载样式文件
            if (app.config.styles) {
                await this.loadStyles(app.config.styles, app);
            }

            // 添加内联样式
            if (app.config.inlineStyles) {
                this.addInlineStyles(app.config.inlineStyles);
            }

            // 加载脚本文件
            if (app.config.scripts) {
                await this.loadScripts(app.config.scripts, app);
            }

            // 执行内联脚本
            if (app.config.inlineScripts) {
                this.executeInlineScripts(app.config.inlineScripts, app);
            }

            // 设置 HTML 内容
            if (app.config.html) {
                if (this.isUrl(app.config.html)) {
                    // 从 URL 加载 HTML
                    const html = await this.fetchHtml(app.config.html);
                    app.container.innerHTML = html;
                } else {
                    // 直接设置 HTML 内容
                    app.container.innerHTML = app.config.html;
                }
            }

            // 执行自定义初始化
            if (app.config.onInit) {
                await app.config.onInit();
            }

            app.status = 'mounted';
            app.updatedAt = Date.now();
        } catch (error) {
            console.error(`HTML 应用 ${app.config.name} 挂载失败:`, error);
            throw error;
        }
    }

    /**
     * 卸载 HTML 应用
     */
    protected async doUnmountApp(app: any): Promise<void> {
        try {
            // 清理 DOM 内容
            if (app.container) {
                cleanupHtmlContainer(app.container);
            }

            // 执行自定义销毁函数
            if (app.config.onDestroy) {
                await app.config.onDestroy();
            }

            // 自动清理资源
            if (app.config.autoCleanup) {
                this.cleanup(app);
            }

            app.status = 'unmounted';
            app.container = undefined;
            app.updatedAt = Date.now();
        } catch (error) {
            console.error(`HTML 应用 ${app.config.name} 卸载失败:`, error);
            throw error;
        }
    }

    /**
     * 更新 HTML 应用属性
     */
    protected async doUpdateApp(app: any, props?: Record<string, any>): Promise<void> {
        if (!props) return;

        try {
            // HTML 应用的更新通常是重新设置内容
            if (app.config.html && app.container) {
                if (this.isUrl(app.config.html)) {
                    const html = await this.fetchHtml(app.config.html);
                    app.container.innerHTML = html;
                } else {
                    app.container.innerHTML = app.config.html;
                }
            }

            app.updatedAt = Date.now();
        } catch (error) {
            console.error(`HTML 应用 ${app.config.name} 更新失败:`, error);
            throw error;
        }
    }

    /**
     * 获取应用实例
     * @returns 应用实例
     */
    getAppInstance(): HtmlAppInstance | null {
        return this.appInstance;
    }

    /**
     * 准备容器元素
     * @returns 容器元素
     */
    protected prepareContainer(): HTMLElement {
        // 创建容器元素
        const container = document.createElement('div');
        container.id = `html-app-${Date.now()}`;
        container.className = 'html-app-container';
        container.setAttribute('data-framework', 'html');

        return container;
    }

    /**
     * 加载脚本文件
     */
    private async loadScripts(scripts: string[], app: HtmlAppInstance): Promise<void> {
        const promises = scripts.map(src => this.loadScript(src, app));
        await Promise.all(promises);
    }

    /**
     * 加载单个脚本文件
     */
    private loadScript(src: string, app: HtmlAppInstance): Promise<void> {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = () => resolve();
            script.onerror = () => reject(new Error(`加载脚本失败: ${src}`));

            document.head.appendChild(script);
            app.loadedScripts.push(script);
        });
    }

    /**
     * 加载样式文件
     */
    private async loadStyles(styles: string[], app: HtmlAppInstance): Promise<void> {
        const promises = styles.map(href => this.loadStyle(href, app));
        await Promise.all(promises);
    }

    /**
     * 加载单个样式文件
     */
    private loadStyle(href: string, app: HtmlAppInstance): Promise<void> {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.onload = () => resolve();
            link.onerror = () => reject(new Error(`加载样式失败: ${href}`));

            document.head.appendChild(link);
            app.loadedStyles.push(link);
        });
    }

    /**
     * 执行内联脚本
     */
    private executeInlineScripts(scripts: string[], app: HtmlAppInstance): void {
        scripts.forEach(scriptContent => {
            try {
                const script = document.createElement('script');
                script.textContent = scriptContent;
                document.head.appendChild(script);
                app.loadedScripts.push(script);
            } catch (error) {
                console.error(`执行内联脚本失败:`, error);
            }
        });
    }

    /**
     * 添加内联样式
     */
    private addInlineStyles(styles: string[]): void {
        styles.forEach(styleContent => {
            try {
                const style = document.createElement('style');
                style.textContent = styleContent;
                document.head.appendChild(style);
            } catch (error) {
                console.error(`添加内联样式失败:`, error);
            }
        });
    }

    /**
     * 从 URL 获取 HTML 内容
     */
    private async fetchHtml(url: string): Promise<string> {
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return await response.text();
        } catch (error) {
            console.error(`获取 HTML 内容失败: ${url}`, error);
            throw error;
        }
    }

    /**
     * 判断是否为 URL
     */
    private isUrl(str: string): boolean {
        try {
            new URL(str);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 清理加载的资源
     */
    private cleanup(app: HtmlAppInstance): void {
        // 移除加载的脚本
        app.loadedScripts.forEach(script => {
            if (script.parentNode) {
                script.parentNode.removeChild(script);
            }
        });
        app.loadedScripts = [];

        // 移除加载的样式
        app.loadedStyles.forEach(link => {
            if (link.parentNode) {
                link.parentNode.removeChild(link);
            }
        });
        app.loadedStyles = [];
    }
}

/**
 * 创建 HTML 适配器实例
 * @param config 应用配置
 * @returns HTML 适配器实例
 */
export function createHtmlAdapter(config: HtmlAdapterConfig): HtmlAdapter {
    return new HtmlAdapter(config);
}

/**
 * HTML 适配器工厂函数
 * @description 用于适配器注册表的工厂函数
 */
export const HtmlAdapterFactory = {
    type: 'html',
    create: createHtmlAdapter,
    canHandle: isHtmlApp,
    metadata: {
        name: 'HTML Adapter',
        version: '2.0.0',
        description: 'HTML 微前端适配器',
        supportedVersions: ['*'],
        author: 'Echo <<EMAIL>>'
    }
};

export default HtmlAdapter;