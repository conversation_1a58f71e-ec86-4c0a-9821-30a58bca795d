/**
 * React Adapter Implementation
 * @description React 微前端适配器，基于新的 BaseAdapter 基础设施
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

import {
  AdapterDependencies,
  AdapterStatus,
  BaseAdapter
} from '@micro-core/shared/utils';
import { ComponentType, createElement } from 'react';
import { createRoot, Root } from 'react-dom/client';
import { ReactComponentWrapper } from './component-wrapper';
import { ReactErrorBoundary } from './error-boundary';
import type {
  ReactAppConfig,
  ReactAppInstance
} from './types';
import {
  cleanupReactContainer,
  createReactErrorInfo,
  extractReactComponent,
  formatReactError,
  getReactVersion,
  isReactApp,
  isReactVersionCompatible,
  validateReactConfig
} from './utils';

/**
 * React 适配器类
 * @description 实现 React 微前端应用的加载、挂载、卸载等生命周期管理
 */
export class ReactAdapter extends BaseAdapter<ReactAppConfig> {
  private reactRoot: Root | null = null;
  private appInstance: ReactAppInstance | null = null;
  private component: ComponentType<any> | null = null;

  constructor(
    config: ReactAppConfig,
    dependencies: AdapterDependencies
  ) {
    super(config, dependencies);
  }

  /**
   * 检查是否能处理指定的应用配置
   * @param appConfig 应用配置
   * @returns 是否能处理
   */
  canHandle(appConfig: any): boolean {
    return isReactApp(appConfig);
  }

  /**
   * 加载 React 应用
   * @param appConfig 应用配置
   */
  async load(appConfig: ReactAppConfig): Promise<void> {
    return this.safeExecute(async () => {
      this.setStatus(AdapterStatus.LOADING);

      await this.executeLifecycleHook('beforeLoad', appConfig);

      // 验证配置
      validateReactConfig(appConfig);

      // 创建沙箱
      if (appConfig.sandbox) {
        await this.sandboxManager.createSandbox(appConfig.name, appConfig);
      }

      // 准备组件
      this.component = await this.prepareComponent(appConfig);

      // 准备容器
      this.prepareContainer();

      this.setStatus(AdapterStatus.LOADED);
    }, 'load');
  }

  /**
   * 挂载 React 应用
   */
  async mount(): Promise<void> {
    return this.safeExecute(async () => {
      if (this.getStatus() !== AdapterStatus.LOADED) {
        throw new Error('App must be loaded before mounting');
      }

      this.setStatus(AdapterStatus.MOUNTING);

      await this.executeLifecycleHook('beforeMount', this.config);

      if (!this.component || !this.container) {
        throw new Error('Component or container not ready');
      }

      // 创建 React root
      this.reactRoot = createRoot(this.container);

      // 创建包装组件
      const WrappedComponent = this.createWrappedComponent();

      // 渲染组件
      this.reactRoot.render(WrappedComponent);

      // 创建应用实例
      this.appInstance = {
        component: this.component,
        container: this.container,
        config: this.config,
        hooks: this.config.lifecycle || {},
        mount: () => this.mount(),
        unmount: () => this.unmount(),
        update: (props: any) => this.update(props)
      };

      this.setStatus(AdapterStatus.MOUNTED);

      await this.executeLifecycleHook('afterMount', this.config);
    }, 'mount');
  }

  /**
   * 卸载 React 应用
   */
  async unmount(): Promise<void> {
    return this.safeExecute(async () => {
      if (this.getStatus() !== AdapterStatus.MOUNTED) {
        return; // 已经卸载或未挂载
      }

      this.setStatus(AdapterStatus.UNMOUNTING);

      await this.executeLifecycleHook('beforeUnmount', this.config);

      // 卸载 React 组件
      if (this.reactRoot) {
        this.reactRoot.unmount();
        this.reactRoot = null;
      }

      // 清理容器
      if (this.container) {
        cleanupReactContainer(this.container);
      }

      // 销毁沙箱
      if (this.config.sandbox) {
        await this.sandboxManager.destroySandbox(this.config.name);
      }

      // 清理应用实例
      this.appInstance = null;
      this.component = null;

      this.setStatus(AdapterStatus.UNMOUNTED);

      await this.executeLifecycleHook('afterUnmount', this.config);
    }, 'unmount');
  }

  /**
   * 更新 React 应用属性
   * @param props 新的属性
   */
  async update(props: any): Promise<void> {
    return this.safeExecute(async () => {
      if (this.getStatus() !== AdapterStatus.MOUNTED) {
        throw new Error('App must be mounted before updating');
      }

      if (!this.reactRoot || !this.component) {
        throw new Error('React root or component not available');
      }

      // 更新配置中的 props
      this.config.props = { ...this.config.props, ...props };

      // 重新渲染组件
      const WrappedComponent = this.createWrappedComponent();
      this.reactRoot.render(WrappedComponent);

      // 触发通信事件
      this.communicationManager.emit('app:props-updated', {
        appName: this.config.name,
        props: this.config.props
      });
    }, 'update');
  }

  /**
   * 获取应用实例
   * @returns 应用实例
   */
  getAppInstance(): ReactAppInstance | null {
    return this.appInstance;
  }

  /**
   * 准备 React 组件
   * @param appConfig 应用配置
   * @returns React 组件
   */
  private async prepareComponent(appConfig: ReactAppConfig): Promise<ComponentType<any>> {
    if (appConfig.component) {
      // 直接使用提供的组件
      return appConfig.component;
    }

    if (appConfig.entry) {
      // 从入口加载组件
      const module = await this.loadModule(appConfig.entry);
      return extractReactComponent(module);
    }

    throw new Error('Either component or entry must be provided');
  }

  /**
   * 加载模块
   * @param entry 入口地址
   * @returns 模块对象
   */
  private async loadModule(entry: string): Promise<any> {
    try {
      // 动态导入模块
      const module = await import(entry);
      return module;
    } catch (error) {
      throw new Error(`Failed to load module from ${entry}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 创建包装组件
   * @returns 包装后的 React 组件
   */
  private createWrappedComponent(): React.ReactElement {
    if (!this.component) {
      throw new Error('Component not available');
    }

    const Component = this.component;
    const props = this.config.props || {};

    // 创建错误边界包装的组件
    return createElement(ReactErrorBoundary, {
      onError: (error: Error, errorInfo: any) => {
        const formattedError = formatReactError(error, {
          ...createReactErrorInfo(error, errorInfo.componentStack),
          appName: this.config.name,
          operation: 'render'
        });

        console.error(formattedError);

        this.communicationManager.emit('app:error', {
          appName: this.config.name,
          error: formattedError
        });
      },
      fallback: (error: Error) => this.createErrorFallback(error)
    }, createElement(ReactComponentWrapper, {
      component: Component,
      props,
      appName: this.config.name,
      onMount: () => {
        this.communicationManager.emit('app:component-mounted', {
          appName: this.config.name
        });
      },
      onUnmount: () => {
        this.communicationManager.emit('app:component-unmounted', {
          appName: this.config.name
        });
      }
    }));
  }

  /**
   * 创建错误回退组件
   * @param error 错误对象
   * @returns 错误回退元素
   */
  private createErrorFallback(error: Error): React.ReactElement {
    return createElement('div', {
      style: {
        padding: '20px',
        border: '1px solid #ff6b6b',
        borderRadius: '4px',
        backgroundColor: '#ffe0e0',
        color: '#d63031',
        fontFamily: 'monospace'
      }
    }, [
      createElement('h3', { key: 'title' }, 'React 应用加载失败'),
      createElement('p', { key: 'message' }, `错误: ${error.message}`),
      createElement('details', { key: 'details' }, [
        createElement('summary', { key: 'summary' }, '查看详细信息'),
        createElement('pre', {
          key: 'stack',
          style: {
            marginTop: '8px',
            padding: '12px',
            backgroundColor: '#f8f8f8',
            borderRadius: '4px',
            fontSize: '12px',
            overflow: 'auto'
          }
        }, error.stack || '无堆栈信息')
      ])
    ]);
  }

  /**
   * 检查 React 版本兼容性
   * @param requiredVersion 要求的版本
   * @returns 是否兼容
   */
  private checkVersionCompatibility(requiredVersion?: string): boolean {
    if (!requiredVersion) return true;

    const currentVersion = getReactVersion();
    if (!currentVersion) {
      console.warn('Cannot detect React version');
      return true; // 无法检测时假设兼容
    }

    const compatible = isReactVersionCompatible(currentVersion, requiredVersion);
    if (!compatible) {
      console.warn(
        `React version mismatch. Current: ${currentVersion}, Required: ${requiredVersion}`
      );
    }

    return compatible;
  }

  /**
   * 准备容器元素
   * @returns 容器元素
   */
  protected prepareContainer(): HTMLElement {
    const container = super.prepareContainer();

    // 添加 React 特定的属性
    container.className += ' react-app-container';
    container.setAttribute('data-framework', 'react');

    // 检查 React 版本兼容性
    const reactConfig = this.config.react;
    if (reactConfig?.reactVersion) {
      this.checkVersionCompatibility(reactConfig.reactVersion);
    }

    return container;
  }
}

/**
 * 创建 React 适配器实例
 * @param config 应用配置
 * @param dependencies 依赖注入
 * @returns React 适配器实例
 */
export function createReactAdapter(
  config: ReactAppConfig,
  dependencies: AdapterDependencies
): ReactAdapter {
  return new ReactAdapter(config, dependencies);
}

/**
 * React 适配器工厂函数
 * @description 用于适配器注册表的工厂函数
 */
export const ReactAdapterFactory = {
  type: 'react',
  create: createReactAdapter,
  canHandle: isReactApp,
  metadata: {
    name: 'React Adapter',
    version: '2.0.0',
    description: 'React 微前端适配器',
    supportedVersions: ['16.8+', '17.x', '18.x'],
    author: 'Echo <<EMAIL>>'
  }
};

export default ReactAdapter;