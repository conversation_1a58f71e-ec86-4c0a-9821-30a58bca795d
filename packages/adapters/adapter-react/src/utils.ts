/**
 * React Adapter Utilities
 * Helper functions for React micro-app integration
 */

import {
  cleanupContainer,
  createEnhancedContainer,
  mergeConfigs,
  formatReactError as sharedFormatReactError
} from '@micro-core/shared/utils';
import { ReactAdapter } from './react-adapter';
import type { ReactAdapterConfig, ReactAppConfig } from './types';

/**
 * Create a React adapter instance with default configuration
 */
export function createReactAdapter(
  config: ReactAdapterConfig = {}
): ReactAdapter {
  return new ReactAdapter(config);
}

/**
 * Check if an app configuration is for a React application
 */
export function isReactApp(config: any): config is ReactAppConfig {
  return !!(
    config.react ||
    config.component ||
    (config.entry && isReactEntry(config.entry))
  );
}

/**
 * Check if an entry point indicates a React application
 */
export function isReactEntry(entry: string): boolean {
  const reactIndicators = [
    'react',
    'jsx',
    'tsx',
    'react-dom',
    'react-app'
  ];

  const lowerEntry = entry.toLowerCase();
  return reactIndicators.some(indicator => lowerEntry.includes(indicator));
}

/**
 * Get React version from the environment
 */
export function getReactVersion(): string | null {
  try {
    if (typeof window !== 'undefined' && (window as any).React) {
      return (window as any).React.version;
    }

    // Try to get from package.json if available
    if (typeof require !== 'undefined') {
      try {
        const React = require('react');
        return React.version;
      } catch {
        // React not available
      }
    }

    return null;
  } catch {
    return null;
  }
}

/**
 * Check React version compatibility
 */
export function isReactVersionCompatible(version: string, minVersion: string = '16.8.0'): boolean {
  try {
    const parseVersion = (v: string) => v.split('.').map(Number);
    const current = parseVersion(version);
    const minimum = parseVersion(minVersion);

    for (let i = 0; i < Math.max(current.length, minimum.length); i++) {
      const currentPart = current[i] || 0;
      const minimumPart = minimum[i] || 0;

      if (currentPart > minimumPart) return true;
      if (currentPart < minimumPart) return false;
    }

    return true;
  } catch {
    return false;
  }
}

/**
 * Validate React app configuration
 */
export function validateReactConfig(config: ReactAppConfig): void {
  if (!config.name) {
    throw new Error('React app name is required');
  }

  if (!config.component && !config.entry) {
    throw new Error('Either component or entry must be specified for React app');
  }

  if (config.react?.reactVersion) {
    const currentVersion = getReactVersion();
    if (currentVersion && !isReactVersionCompatible(currentVersion, config.react.reactVersion)) {
      console.warn(
        `React version mismatch. Current: ${currentVersion}, Required: ${config.react.reactVersion}`
      );
    }
  }
}

/**
 * Create default React app configuration
 */
export function createDefaultReactConfig(overrides: Partial<ReactAppConfig> = {}): ReactAppConfig {
  return {
    name: 'react-app',
    react: {
      reactVersion: '18',
      enableDevTools: process.env.NODE_ENV === 'development',
      strictMode: false,
      sandbox: {
        isolateContext: true,
        preserveDevTools: true,
        contextProviders: []
      }
    },
    ...overrides
  };
}

/**
 * 检查首选名称的组件
 */
function checkPreferredComponent(module: any, preferredName: string, strictValidation: boolean): any | null {
  if (!preferredName || !module[preferredName]) {
    return null;
  }

  if (!strictValidation || isReactComponent(module[preferredName])) {
    return module[preferredName];
  }

  return null;
}

/**
 * 检查默认导出的组件
 */
function checkDefaultComponent(module: any, strictValidation: boolean): any | null {
  if (!module.default) {
    return null;
  }

  if (!strictValidation || isReactComponent(module.default)) {
    return module.default;
  }

  return null;
}

/**
 * 获取所有命名导出的组件
 */
function getNamedComponents(module: any, strictValidation: boolean): string[] {
  return Object.keys(module).filter(key => {
    const value = module[key];
    return key !== 'default' && (!strictValidation || isReactComponent(value));
  });
}

/**
 * 从多个组件中选择最佳组件
 */
function selectBestComponent(module: any, componentKeys: string[]): any {
  // 按优先级查找常见组件名称
  const commonNames = ['App', 'Main', 'Root', 'Component', 'Index', 'Default'];
  for (const name of commonNames) {
    if (componentKeys.includes(name)) {
      return module[name];
    }
  }

  // 按字母顺序返回第一个
  const sortedKeys = componentKeys.sort();
  console.warn(
    `Multiple React components found: ${componentKeys.join(', ')}. ` +
    `Using: ${sortedKeys[0]}. Consider specifying preferredName option.`
  );
  return module[sortedKeys[0]];
}

/**
 * Extract React component from module
 * @description 从模块中提取 React 组件，支持多种导出方式和组件类型
 * @param module 模块对象
 * @param options 提取选项
 * @returns React 组件
 */
export function extractReactComponent(
  module: any,
  options: {
    preferredName?: string;
    allowMultiple?: boolean;
    strictValidation?: boolean;
  } = {}
): any {
  if (!module) {
    throw new Error('Module is required for component extraction');
  }

  const { preferredName, allowMultiple = false, strictValidation = true } = options;

  // 1. 检查首选名称
  const preferredComponent = checkPreferredComponent(module, preferredName!, strictValidation);
  if (preferredComponent) {
    return preferredComponent;
  }

  // 2. 检查默认导出
  const defaultComponent = checkDefaultComponent(module, strictValidation);
  if (defaultComponent) {
    return defaultComponent;
  }

  // 3. 检查命名导出
  const componentKeys = getNamedComponents(module, strictValidation);

  if (componentKeys.length === 0) {
    throw new Error('No React component found in module');
  }

  if (componentKeys.length === 1) {
    return module[componentKeys[0]];
  }

  // 4. 处理多个组件的情况
  if (allowMultiple) {
    // 返回所有找到的组件
    return componentKeys.reduce((acc, key) => {
      acc[key] = module[key];
      return acc;
    }, {} as Record<string, any>);
  } else {
    // 选择最佳组件
    return selectBestComponent(module, componentKeys);
  }
}

/**
 * Check if a value is a React component
 */
export function isReactComponent(value: any): boolean {
  if (!value) return false;

  // Function component
  if (typeof value === 'function') {
    // Check if it has React component characteristics
    return (
      value.prototype === undefined || // Arrow function
      value.prototype.constructor === value || // Regular function
      value.prototype.isReactComponent || // Class component
      value.$$typeof === Symbol.for('react.forward_ref') || // Forward ref
      value.$$typeof === Symbol.for('react.memo') // Memo component
    );
  }

  // Class component
  if (typeof value === 'object' && value.prototype && value.prototype.isReactComponent) {
    return true;
  }

  return false;
}

/**
 * Create React app container element
 */
export function createReactContainer(appName: string, parentElement?: HTMLElement): HTMLElement {
  return createEnhancedContainer(appName, 'react', parentElement, {
    className: 'react-app-container'
  });
}

/**
 * Clean up React app container
 */
export function cleanupReactContainer(container: HTMLElement): void {
  cleanupContainer(container);
}

/**
 * Get React app container by name
 */
export function getReactContainer(appName: string): HTMLElement | null {
  return document.getElementById(`micro-app-${appName}`);
}

/**
 * Check if React DevTools is available
 */
export function isReactDevToolsAvailable(): boolean {
  return typeof window !== 'undefined' && !!(window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__;
}

/**
 * Enable React DevTools for a specific app
 */
export function enableReactDevTools(appName: string): void {
  if (isReactDevToolsAvailable()) {
    console.log(`React DevTools enabled for app: ${appName}`);
    // Additional DevTools setup can be added here
  }
}

/**
 * Create React error info object
 */
export function createReactErrorInfo(error: Error, componentStack?: string): any {
  return {
    componentStack: componentStack || '',
    errorBoundary: 'ReactAdapter',
    errorBoundaryStack: new Error().stack,
    timestamp: new Date().toISOString(),
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
    url: typeof window !== 'undefined' ? window.location.href : ''
  };
}

/**
 * Format React error for logging - 使用 shared 包的实现
 */
export function formatReactError(error: Error, errorInfo?: any): string {
  return sharedFormatReactError(error, errorInfo);
}

/**
 * Deep merge React configurations - 使用 shared 包的通用实现
 */
export function mergeReactConfigs(
  base: ReactAppConfig,
  override: Partial<ReactAppConfig>
): ReactAppConfig {
  return mergeConfigs(base, override);
}

/**
 * Enhanced React component detection
 * @description 增强的 React 组件检测函数，支持更多组件类型
 * @param value 待检查的值
 * @returns 是否为 React 组件
 */
export function isReactComponentEnhanced(value: any): boolean {
  if (!value) return false;

  // 检查 React 特殊类型标记
  if (value.$$typeof) {
    const reactSymbols = [
      Symbol.for('react.forward_ref'),
      Symbol.for('react.memo'),
      Symbol.for('react.lazy'),
      Symbol.for('react.suspense'),
      Symbol.for('react.fragment')
    ];

    if (reactSymbols.includes(value.$$typeof)) {
      return true;
    }
  }

  // 函数组件检查
  if (typeof value === 'function') {
    // 类组件检查
    if (value.prototype && value.prototype.isReactComponent) {
      return true;
    }

    // 函数组件检查
    if (value.prototype === undefined || value.prototype.constructor === value) {
      // 进一步验证：检查函数是否可能是 React 组件
      try {
        const funcStr = value.toString();

        // 检查是否包含 JSX 或 React 相关代码
        const reactPatterns = [
          /React\.createElement/,
          /jsx\(/,
          /jsxs\(/,
          /\.jsx/,
          /return\s+</, // JSX 返回
          /props\./,    // 使用 props
          /useState/,   // React Hooks
          /useEffect/,
          /useContext/,
          /useReducer/,
          /useMemo/,
          /useCallback/
        ];

        // 如果函数体包含 React 相关模式，认为是组件
        if (reactPatterns.some(pattern => pattern.test(funcStr))) {
          return true;
        }

        // 检查函数参数是否符合 React 组件模式
        if (value.length <= 2) { // React 组件通常接受 0-2 个参数 (props, context)
          return true;
        }
      } catch {
        // 如果无法解析函数字符串，使用基本检查
        return value.length <= 2;
      }
    }
  }

  // 对象形式的组件（如高阶组件返回的对象）
  if (typeof value === 'object' && value !== null) {
    // 检查是否有 render 方法（类组件）
    if (typeof value.render === 'function') {
      return true;
    }

    // 检查是否是 React 元素
    if (value.$$typeof === Symbol.for('react.element')) {
      return true;
    }
  }

  return false;
}

/**
 * React 适配器工具集合
 * @description 提供所有 React 适配器相关的工具函数
 */
export const ReactAdapterUtils = {
  // 组件相关
  extractReactComponent,
  isReactComponent,
  isReactComponentEnhanced,

  // 应用检测
  isReactApp,
  isReactEntry,

  // 版本管理
  getReactVersion,
  isReactVersionCompatible,

  // 配置管理
  validateReactConfig,
  createDefaultReactConfig,
  mergeReactConfigs,

  // 容器管理
  createReactContainer,
  cleanupReactContainer,
  getReactContainer,

  // 错误处理
  formatReactError,
  createReactErrorInfo,

  // 开发工具
  isReactDevToolsAvailable,
  enableReactDevTools,

  // 适配器创建
  createReactAdapter
} as const;