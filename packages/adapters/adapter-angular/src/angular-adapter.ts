/**
 * Angular Adapter Implementation
 * @description Angular 微前端适配器，基于新的 BaseAdapter 基础设施
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

import type { ApplicationRef, NgModuleRef, PlatformRef } from '@angular/core';
import { createApplication, platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import {
    BaseAdapter,
    BaseAdapterConfig
} from '@micro-core/shared/utils';

/**
 * Angular 适配器配置
 */
export interface AngularAdapterConfig extends BaseAdapterConfig {
    /** Angular 模块或组件 */
    module?: any;
    /** 根组件 */
    component?: any;
    /** 是否使用独立组件 */
    standalone?: boolean;
    /** 平台配置 */
    platformOptions?: any;
    /** 模块配置 */
    moduleOptions?: any;
    /** 应用配置 */
    appOptions?: any;
}

/**
 * Angular 应用实例
 */
export interface AngularAppInstance {
    name: string;
    platformRef?: PlatformRef;
    moduleRef?: NgModuleRef<any>;
    appRef?: ApplicationRef;
    config: AngularAdapterConfig;
    status: 'loaded' | 'mounted' | 'unmounted';
    createdAt: number;
    updatedAt: number;
}

/**
 * Angular 适配器类
 * @description 实现 Angular 微前端应用的加载、挂载、卸载等生命周期管理
 */
export class AngularAdapter extends BaseAdapter {
    private appInstance: AngularAppInstance | null = null;
    private component: any = null;

    constructor(config: AngularAdapterConfig) {
        super({
            name: config.name || 'angular-app',
            framework: 'angular'
        });
    }

    /**
     * 检查是否能处理指定的应用配置
     * @param appConfig 应用配置
     * @returns 是否能处理
     */
    canHandle(appConfig: any): boolean {
        return isAngularApp(appConfig);
    }

    /**
     * 加载 Angular 应用
     * @param config 应用配置
     */
    protected async doLoadApp(config: any): Promise<any> {
        // 验证配置
        validateAngularConfig(config);

        // 准备组件
        this.component = await this.prepareComponent(config);

        // 创建应用实例
        this.appInstance = {
            name: config.name,
            config,
            status: 'loaded',
            createdAt: Date.now(),
            updatedAt: Date.now()
        } as AngularAppInstance;

        return this.appInstance;
    }

    /**
     * 挂载 Angular 应用
     */
    protected async doMountApp(app: any): Promise<void> {
        if (!this.component) {
            throw new Error('组件未准备就绪');
        }

        // 准备容器
        app.container = this.prepareContainer();

        try {
            // 创建平台
            app.platformRef = platformBrowserDynamic(app.config.platformOptions);

            if (app.config.standalone && this.component) {
                // 独立组件模式
                app.appRef = await createApplication({
                    providers: app.config.appOptions?.providers || []
                });

                // 启动组件
                const componentRef = app.appRef.bootstrap(this.component);

                // 将组件插入到容器中
                if (componentRef.location?.nativeElement) {
                    app.container.appendChild(componentRef.location.nativeElement);
                }
            } else if (app.config.module) {
                // 模块模式
                app.moduleRef = await app.platformRef.bootstrapModule(
                    app.config.module,
                    app.config.moduleOptions
                );
                app.appRef = app.moduleRef.injector.get(ApplicationRef);

                // 启动组件
                if (this.component) {
                    const componentRef = app.appRef.bootstrap(this.component);

                    // 将组件插入到容器中
                    if (componentRef.location?.nativeElement) {
                        app.container.appendChild(componentRef.location.nativeElement);
                    }
                }
            } else {
                throw new Error('必须提供 Angular 模块或独立组件');
            }

            app.status = 'mounted';
            app.updatedAt = Date.now();
        } catch (error) {
            console.error(`Angular 应用 ${app.config.name} 挂载失败:`, error);
            throw error;
        }
    }

    /**
     * 卸载 Angular 应用
     */
    protected async doUnmountApp(app: any): Promise<void> {
        try {
            // 销毁应用
            if (app.appRef) {
                app.appRef.destroy();
                app.appRef = undefined;
            }

            // 销毁模块
            if (app.moduleRef) {
                app.moduleRef.destroy();
                app.moduleRef = undefined;
            }

            // 销毁平台
            if (app.platformRef) {
                app.platformRef.destroy();
                app.platformRef = undefined;
            }

            // 清理容器
            if (app.container) {
                cleanupAngularContainer(app.container);
            }

            app.status = 'unmounted';
            app.updatedAt = Date.now();
        } catch (error) {
            console.error(`Angular 应用 ${app.config.name} 卸载失败:`, error);
            throw error;
        }
    }

    /**
     * 更新 Angular 应用属性
     */
    protected async doUpdateApp(app: any, props?: Record<string, any>): Promise<void> {
        if (!props) return;

        try {
            // Angular 应用的更新通常通过服务或状态管理来处理
            // 这里可以触发变更检测
            if (app.appRef) {
                app.appRef.tick();
            }

            app.updatedAt = Date.now();
        } catch (error) {
            console.error(`Angular 应用 ${app.config.name} 更新失败:`, error);
            throw error;
        }
    }

    /**
     * 获取应用实例
     * @returns 应用实例
     */
    getAppInstance(): AngularAppInstance | null {
        return this.appInstance;
    }

    /**
     * 准备 Angular 组件
     * @param config 应用配置
     * @returns Angular 组件
     */
    private async prepareComponent(config: any): Promise<any> {
        if (config.component) {
            return config.component;
        }

        if (config.module) {
            // 从模块中提取组件
            return extractAngularComponent(config.module);
        }

        if (config.entry) {
            // 从入口加载组件
            const module = await this.loadModule(config.entry);
            return extractAngularComponent(module);
        }

        throw new Error('必须提供 component、module 或 entry');
    }

    /**
     * 加载模块
     * @param entry 入口地址
     * @returns 模块对象
     */
    private async loadModule(entry: string | any): Promise<any> {
        try {
            const entryUrl = typeof entry === 'string' ? entry : entry.url || entry.src;

            if (entryUrl.startsWith('http')) {
                // 远程模块
                return await this.loadRemoteModule(entryUrl);
            } else {
                // 本地模块
                return await import(entryUrl);
            }
        } catch (error) {
            throw new Error(`从 ${entry} 加载 Angular 模块失败: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * 加载远程模块
     * @param url 远程模块地址
     * @returns 模块对象
     */
    private async loadRemoteModule(url: string): Promise<any> {
        // 实现远程 Angular 模块加载
        const response = await fetch(url);
        const code = await response.text();

        // 创建沙箱并执行模块
        const moduleFunction = new Function('exports', 'require', 'module', 'ng', code);
        const module = { exports: {} };
        moduleFunction(module.exports, require, module, {});

        return module.exports;
    }

    /**
     * 准备容器元素
     * @returns 容器元素
     */
    protected prepareContainer(): HTMLElement {
        // 创建容器元素
        const container = document.createElement('div');
        container.id = `angular-app-${this.config.name}`;
        container.className = 'angular-app-container';
        container.setAttribute('data-framework', 'angular');

        // 检查 Angular 版本兼容性
        const angularConfig = (this.config as any).angular;
        if (angularConfig?.angularVersion) {
            this.checkVersionCompatibility(angularConfig.angularVersion);
        }

        return container;
    }

    /**
     * 检查 Angular 版本兼容性
     * @param requiredVersion 要求的版本
     * @returns 是否兼容
     */
    private checkVersionCompatibility(requiredVersion?: string): boolean {
        if (!requiredVersion) return true;

        const currentVersion = getAngularVersion();
        if (!currentVersion) {
            console.warn('无法检测 Angular 版本');
            return true; // 无法检测时假设兼容
        }

        const compatible = isAngularVersionCompatible(currentVersion, requiredVersion);
        if (!compatible) {
            console.warn(
                `Angular 版本不匹配。当前: ${currentVersion}, 要求: ${requiredVersion}`
            );
        }

        return compatible;
    }
}

/**
 * 创建 Angular 适配器实例
 * @param config 应用配置
 * @returns Angular 适配器实例
 */
export function createAngularAdapter(config: AngularAdapterConfig): AngularAdapter {
    return new AngularAdapter(config);
}

/**
 * Angular 适配器工厂函数
 * @description 用于适配器注册表的工厂函数
 */
export const AngularAdapterFactory = {
    type: 'angular',
    create: createAngularAdapter,
    canHandle: isAngularApp,
    metadata: {
        name: 'Angular Adapter',
        version: '2.0.0',
        description: 'Angular 微前端适配器',
        supportedVersions: ['12+', '13.x', '14.x', '15.x', '16.x', '17.x'],
        author: 'Echo <<EMAIL>>'
    }
};

export default AngularAdapter;