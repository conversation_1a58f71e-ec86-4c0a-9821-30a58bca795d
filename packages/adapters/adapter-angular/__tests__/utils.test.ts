/**
 * Angular 适配器工具函数测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
    AngularMicroAppIntegration,
    createAngularAdapter,
    createAngularContainer,
    createAngularErrorInfo,
    createDefaultAngularConfig,
    extractAngularComponent,
    formatAngularError,
    getAngularVersion,
    isAngularApp,
    isAngularComponent,
    isAngularComponentEnhanced,
    isAngularEntry,
    isAngularVersionCompatible,
    mergeAngularConfigs,
    setupAngularDevTools,
    validateAngularConfig
} from '../src/utils';

// Mock shared utils
vi.mock('@micro-core/shared/utils', () => ({
    mergeConfigs: vi.fn((base, override) => ({ ...base, ...override })),
    createEnhancedContainer: vi.fn((name, framework, parent, options) => {
        const div = document.createElement('div');
        div.id = `micro-app-${name}`;
        div.className = options?.className || '';
        return div;
    }),
    cleanupContainer: vi.fn(),
    formatAdapterError: vi.fn((error, framework, operation, appName) =>
        new Error(`${framework} ${operation} error in ${appName}: ${error.message}`)
    ),
    createAdapterErrorInfo: vi.fn((error, framework, context) => ({
        framework,
        error: error.message,
        context
    })),
    isObject: vi.fn((value) => value !== null && typeof value === 'object'),
    isFunction: vi.fn((value) => typeof value === 'function'),
    isString: vi.fn((value) => typeof value === 'string')
}));

// Mock Angular modules
vi.mock('@angular/platform-browser-dynamic', () => ({
    platformBrowserDynamic: vi.fn(() => ({
        bootstrapModule: vi.fn().mockResolvedValue({
            destroy: vi.fn(),
            injector: {
                get: vi.fn()
            }
        })
    }))
}));

vi.mock('@angular/core', () => ({
    enableProdMode: vi.fn(),
    VERSION: { full: '15.0.0' }
}));

describe('Angular 适配器工具函数', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        // 设置 DOM 环境
        Object.defineProperty(global, 'window', {
            value: {
                ng: { version: { full: '15.0.0' } }
            },
            writable: true
        });
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('createAngularAdapter', () => {
        it('应该创建 Angular 适配器', () => {
            const config = {
                name: 'test-app',
                entry: { AppModule: {} }
            };

            const adapter = createAngularAdapter(config);

            expect(adapter).toHaveProperty('name', 'test-app');
            expect(adapter).toHaveProperty('config');
            expect(adapter).toHaveProperty('mount');
            expect(adapter).toHaveProperty('unmount');
            expect(typeof adapter.mount).toBe('function');
            expect(typeof adapter.unmount).toBe('function');
        });

        it('应该使用默认配置', () => {
            const config = { name: 'test-app', entry: {} };
            const adapter = createAngularAdapter(config);

            expect(adapter.config).toMatchObject({
                name: 'test-app',
                angular: expect.objectContaining({
                    enableProdMode: false,
                    preserveWhitespaces: false,
                    enableIvy: true
                })
            });
        });
    });

    describe('isAngularApp', () => {
        it('应该识别 Angular 应用', () => {
            expect(isAngularApp({ ngModule: {} })).toBe(true);
            expect(isAngularApp({ moduleRef: {} })).toBe(true);
            expect(isAngularApp({ component: () => { } })).toBe(true);
            expect(isAngularApp({ platformBrowserDynamic: {} })).toBe(true);
        });

        it('应该拒绝非 Angular 应用', () => {
            expect(isAngularApp(null)).toBe(false);
            expect(isAngularApp(undefined)).toBe(false);
            expect(isAngularApp('string')).toBe(false);
            expect(isAngularApp({})).toBe(false);
            expect(isAngularApp({ randomProp: 'value' })).toBe(false);
        });
    });

    describe('isAngularEntry', () => {
        it('应该识别 Angular 入口', () => {
            expect(isAngularEntry({ AppModule: {} })).toBe(true);
            expect(isAngularEntry({ NgModule: {} })).toBe(true);
            expect(isAngularEntry({ AppComponent: {} })).toBe(true);
            expect(isAngularEntry({ Component: {} })).toBe(true);
            expect(isAngularEntry({ platformBrowserDynamic: {} })).toBe(true);
        });

        it('应该拒绝非 Angular 入口', () => {
            expect(isAngularEntry(null)).toBe(false);
            expect(isAngularEntry({})).toBe(false);
            expect(isAngularEntry({ randomExport: {} })).toBe(false);
        });
    });

    describe('getAngularVersion', () => {
        it('应该从全局对象获取版本', () => {
            expect(getAngularVersion()).toBe('15.0.0');
        });

        it('应该处理版本获取失败', () => {
            global.window = undefined as any;
            vi.doMock('@angular/core', () => {
                throw new Error('Module not found');
            });

            expect(getAngularVersion()).toBeNull();
        });
    });

    describe('isAngularVersionCompatible', () => {
        it('应该检查版本兼容性', () => {
            expect(isAngularVersionCompatible('15.0.0', '12.0.0')).toBe(true);
            expect(isAngularVersionCompatible('11.0.0', '12.0.0')).toBe(false);
            expect(isAngularVersionCompatible('12.0.0', '12.0.0')).toBe(true);
            expect(isAngularVersionCompatible('12.1.0', '12.0.0')).toBe(true);
        });

        it('应该处理无效版本', () => {
            expect(isAngularVersionCompatible('', '12.0.0')).toBe(false);
            expect(isAngularVersionCompatible(null as any, '12.0.0')).toBe(false);
        });
    });

    describe('validateAngularConfig', () => {
        it('应该验证有效配置', () => {
            const config = {
                name: 'test-app',
                entry: { AppModule: {} },
                angular: {
                    enableProdMode: true,
                    preserveWhitespaces: false
                }
            };

            const errors = validateAngularConfig(config);
            expect(errors).toHaveLength(0);
        });

        it('应该检测配置错误', () => {
            const config = {
                name: '',
                angular: {
                    enableProdMode: 'invalid' as any,
                    preserveWhitespaces: 'invalid' as any
                }
            };

            const errors = validateAngularConfig(config);
            expect(errors.length).toBeGreaterThan(0);
            expect(errors).toContain('配置中缺少有效的应用名称');
            expect(errors).toContain('配置中缺少入口模块或组件');
        });
    });

    describe('createDefaultAngularConfig', () => {
        it('应该创建默认配置', () => {
            const config = createDefaultAngularConfig({
                name: 'test-app',
                entry: {}
            });

            expect(config).toMatchObject({
                name: 'test-app',
                angular: {
                    enableProdMode: false,
                    preserveWhitespaces: false,
                    enableIvy: true,
                    strictTemplates: false
                },
                container: {
                    className: 'angular-app-container'
                }
            });
        });

        it('应该合并用户配置', () => {
            const userConfig = {
                name: 'custom-app',
                entry: {},
                angular: {
                    enableProdMode: true
                }
            };

            const config = createDefaultAngularConfig(userConfig);
            expect(config.angular?.enableProdMode).toBe(true);
        });
    });

    describe('extractAngularComponent', () => {
        it('应该提取首选组件', () => {
            const moduleExports = {
                PreferredComponent: { ɵcmp: {} },
                OtherComponent: { ɵcmp: {} }
            };

            const component = extractAngularComponent(moduleExports, 'PreferredComponent');
            expect(component).toBe(moduleExports.PreferredComponent);
        });

        it('应该提取默认导出', () => {
            const defaultComponent = { ɵcmp: {} };
            const moduleExports = {
                default: defaultComponent,
                OtherComponent: { ɵcmp: {} }
            };

            const component = extractAngularComponent(moduleExports);
            expect(component).toBe(defaultComponent);
        });

        it('应该提取命名导出', () => {
            const moduleExports = {
                AppModule: { ɵmod: {} },
                AppComponent: { ɵcmp: {} }
            };

            const component = extractAngularComponent(moduleExports);
            expect(component).toBe(moduleExports.AppModule); // 优先选择模块
        });

        it('应该抛出错误当没有找到组件时', () => {
            expect(() => extractAngularComponent({})).toThrow('未找到有效的 Angular 组件或模块');
            expect(() => extractAngularComponent(null)).toThrow('模块导出必须是对象');
        });
    });

    describe('isAngularComponent', () => {
        it('应该识别 Angular 模块', () => {
            expect(isAngularComponent({ ɵmod: {} })).toBe(true);
            expect(isAngularComponent({ ngModule: {} })).toBe(true);
            expect(isAngularComponent({ declarations: [], imports: [] })).toBe(true);
        });

        it('应该识别 Angular 组件', () => {
            expect(isAngularComponent({ ɵcmp: {} })).toBe(true);
            expect(isAngularComponent({ ngComponent: {} })).toBe(true);
            expect(isAngularComponent({ selector: 'app-test', template: '<div></div>' })).toBe(true);
        });

        it('应该拒绝非 Angular 组件', () => {
            expect(isAngularComponent(null)).toBe(false);
            expect(isAngularComponent({})).toBe(false);
            expect(isAngularComponent('string')).toBe(false);
            expect(isAngularComponent({ randomProp: 'value' })).toBe(false);
        });
    });

    describe('isAngularComponentEnhanced', () => {
        it('应该识别增强的 Angular 组件', () => {
            const enhancedComponent = {
                ɵcmp: {},
                microAppConfig: {},
                microAppHooks: {}
            };

            expect(isAngularComponentEnhanced(enhancedComponent)).toBe(true);
        });

        it('应该拒绝普通组件', () => {
            const normalComponent = { ɵcmp: {} };
            expect(isAngularComponentEnhanced(normalComponent)).toBe(false);
        });
    });

    describe('createAngularContainer', () => {
        it('应该创建 Angular 容器', () => {
            const container = createAngularContainer('test-app');

            expect(container).toBeDefined();
            expect(container.id).toBe('micro-app-test-app');
        });
    });

    describe('setupAngularDevTools', () => {
        it('应该设置开发工具', () => {
            const moduleRef = {
                injector: {
                    get: vi.fn()
                }
            } as any;

            setupAngularDevTools('test-app', moduleRef);

            expect((global.window as any).ng).toBeDefined();
            expect((global.window as any).ng['test-app']).toBeDefined();
        });

        it('应该处理设置失败', () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
            global.window = undefined as any;

            setupAngularDevTools('test-app', {} as any);

            expect(consoleSpy).not.toHaveBeenCalled(); // 因为 window 检查
            consoleSpy.mockRestore();
        });
    });

    describe('formatAngularError', () => {
        it('应该格式化错误', () => {
            const error = new Error('Test error');
            const formattedError = formatAngularError(error, 'mount', 'test-app');

            expect(formattedError.message).toContain('Angular');
            expect(formattedError.message).toContain('mount');
            expect(formattedError.message).toContain('test-app');
        });
    });

    describe('createAngularErrorInfo', () => {
        it('应该创建错误信息', () => {
            const error = new Error('Test error');
            const context = { operation: 'mount' };
            const errorInfo = createAngularErrorInfo(error, context);

            expect(errorInfo).toMatchObject({
                framework: 'Angular',
                error: 'Test error',
                context
            });
        });
    });

    describe('mergeAngularConfigs', () => {
        it('应该合并配置', () => {
            const base = { name: 'base', angular: { enableProdMode: false } };
            const override = { angular: { enableProdMode: true } };

            const merged = mergeAngularConfigs(base, override);

            expect(merged).toMatchObject({
                name: 'base',
                angular: { enableProdMode: true }
            });
        });
    });

    describe('AngularMicroAppIntegration', () => {
        let integration: AngularMicroAppIntegration;
        let mockElement: HTMLElement;

        beforeEach(() => {
            const config = {
                name: 'test-app',
                entry: { AppModule: {} },
                angular: { enableDevTools: true },
                lifecycle: {
                    mounted: vi.fn(),
                    beforeUnmount: vi.fn(),
                    unmounted: vi.fn()
                }
            };
            integration = new AngularMicroAppIntegration(config);
            mockElement = document.createElement('div');
        });

        describe('mount', () => {
            it('应该挂载应用', async () => {
                await integration.mount(mockElement);

                expect(integration.getContainer()).toBeDefined();
                expect(integration.getModuleRef()).toBeDefined();
            });

            it('应该处理挂载错误', async () => {
                vi.mocked(require('@angular/platform-browser-dynamic').platformBrowserDynamic).mockImplementation(() => {
                    throw new Error('Bootstrap failed');
                });

                await expect(integration.mount(mockElement)).rejects.toThrow();
            });
        });

        describe('unmount', () => {
            it('应该卸载应用', async () => {
                await integration.mount(mockElement);
                await integration.unmount();

                expect(integration.getContainer()).toBeNull();
                expect(integration.getModuleRef()).toBeNull();
            });
        });
    });
});
