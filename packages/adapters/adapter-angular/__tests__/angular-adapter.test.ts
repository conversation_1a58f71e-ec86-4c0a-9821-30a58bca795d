/**
 * Angular 适配器测试用例
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { AngularAdapter } from '../src';

// 模拟 Angular 依赖
vi.mock('@angular/core', () => ({
    NgModule: () => (cls: any) => cls,
    Component: () => (cls: any) => cls,
    ApplicationRef: class {
        attachView = vi.fn();
    },
    ComponentFactoryResolver: class {
        resolveComponentFactory = vi.fn();
    },
    Injector: class {
        get = vi.fn();
    },
    createPlatformFactory: vi.fn().mockReturnValue(() => ({
        bootstrapModule: vi.fn().mockResolvedValue({
            injector: {
                get: vi.fn().mockReturnValue({
                    bootstrap: vi.fn().mockReturnValue({
                        hostView: {},
                        instance: {},
                        changeDetectorRef: {
                            detectChanges: vi.fn()
                        },
                        destroy: vi.fn()
                    })
                })
            }
        })
    }))
}));

vi.mock('@angular/platform-browser-dynamic', () => ({
    platformBrowserDynamic: vi.fn().mockReturnValue({
        bootstrapModule: vi.fn().mockResolvedValue({
            injector: {
                get: vi.fn()
            }
        })
    })
}));

describe('AngularAdapter', () => {
    let adapter: AngularAdapter;
    let mockContainer: HTMLElement;
    let mockComponent: any;
    let mockProps: Record<string, any>;

    beforeEach(() => {
        // 重置模拟
        vi.resetAllMocks();

        // 创建模拟 DOM 元素
        mockContainer = document.createElement('div');
        document.body.appendChild(mockContainer);

        // 创建模拟组件和属性
        mockComponent = class TestComponent { };
        mockProps = { testProp: 'value' };

        // 创建适配器实例
        adapter = new AngularAdapter();
    });

    afterEach(() => {
        // 清理 DOM
        document.body.removeChild(mockContainer);
    });

    describe('挂载', () => {
        it('应该能够挂载 Angular 组件', async () => {
            const result = await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            expect(result).toBeDefined();
        });

        it('应该能够处理挂载选项', async () => {
            const modules = [class TestModule { }];
            const providers = [{ provide: 'TEST', useValue: 'test' }];

            const result = await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps,
                modules,
                providers
            });

            expect(result).toBeDefined();
        });

        it('应该在挂载失败时抛出错误', async () => {
            // 模拟挂载失败
            const error = new Error('挂载失败');
            vi.spyOn(adapter as any, 'createDynamicModule').mockImplementation(() => {
                throw error;
            });

            await expect(adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            })).rejects.toThrow('挂载失败');
        });
    });

    describe('卸载', () => {
        it('应该能够卸载 Angular 组件', async () => {
            // 先挂载组件
            const mountResult = await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 模拟组件引用
            const mockComponentRef = {
                destroy: vi.fn()
            };
            vi.spyOn(adapter as any, 'getComponentRef').mockReturnValue(mockComponentRef);

            // 然后卸载
            await adapter.unmount({ container: mockContainer });

            expect(mockComponentRef.destroy).toHaveBeenCalled();
        });

        it('应该在卸载失败时抛出错误', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 模拟组件引用
            const mockComponentRef = {
                destroy: vi.fn().mockImplementation(() => {
                    throw new Error('卸载失败');
                })
            };
            vi.spyOn(adapter as any, 'getComponentRef').mockReturnValue(mockComponentRef);

            // 然后卸载
            await expect(adapter.unmount({ container: mockContainer })).rejects.toThrow('卸载失败');
        });

        it('应该在容器不存在时不执行卸载', async () => {
            const destroySpy = vi.fn();
            vi.spyOn(adapter as any, 'getComponentRef').mockReturnValue({
                destroy: destroySpy
            });

            await adapter.unmount({ container: null as any });
            expect(destroySpy).not.toHaveBeenCalled();
        });
    });

    describe('更新', () => {
        it('应该能够更新 Angular 组件的属性', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 模拟组件实例和变更检测
            const mockComponentInstance = {};
            const mockChangeDetectorRef = {
                detectChanges: vi.fn()
            };

            vi.spyOn(adapter as any, 'getComponentInstance').mockReturnValue(mockComponentInstance);
            vi.spyOn(adapter as any, 'getChangeDetectorRef').mockReturnValue(mockChangeDetectorRef);

            // 然后更新属性
            const newProps = { testProp: 'newValue' };
            await adapter.update({
                container: mockContainer,
                props: newProps
            });

            expect(mockChangeDetectorRef.detectChanges).toHaveBeenCalled();
        });

        it('应该在更新失败时抛出错误', async () => {
            // 先挂载组件
            await adapter.mount({
                component: mockComponent,
                container: mockContainer,
                props: mockProps
            });

            // 模拟组件实例和变更检测失败
            vi.spyOn(adapter as any, 'getComponentInstance').mockReturnValue({});
            vi.spyOn(adapter as any, 'getChangeDetectorRef').mockImplementation(() => {
                throw new Error('更新失败');
            });

            // 然后更新属性
            const newProps = { testProp: 'newValue' };
            await expect(adapter.update({
                container: mockContainer,
                props: newProps
            })).rejects.toThrow('更新失败');
        });
    });

    describe('工具函数', () => {
        it('应该能够检查是否是 Angular 组件', () => {
            const angularComponent = class TestComponent { };
            const nonAngularComponent = { template: '<div></div>' };

            expect(adapter.isValidComponent(angularComponent)).toBe(true);
            expect(adapter.isValidComponent(nonAngularComponent)).toBe(false);
        });

        it('应该能够获取适配器名称', () => {
            expect(adapter.getName()).toBe('angular');
        });
    });
});