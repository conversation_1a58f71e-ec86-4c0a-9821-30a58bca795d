/**
 * Vue 2 Adapter Utilities
 * Helper functions for Vue 2 micro-app integration
 */

import {
  cleanupContainer,
  createEnhancedContainer,
  mergeConfigs,
  createVue2ErrorInfo as sharedCreateVue2ErrorInfo,
  formatVue2Error as sharedFormatVue2Error
} from '@micro-core/shared/utils';
import Vue from 'vue';
import type { Vue2AdapterConfig, Vue2AppConfig, Vue2ComponentOptions } from './types';
import { Vue2Adapter } from './vue2-adapter';

/**
 * Create a Vue 2 adapter instance with default configuration
 */
export function createVue2Adapter(
  config: Vue2AdapterConfig = {},
  dependencies?: {
    lifecycleManager?: any;
    sandboxManager?: any;
    communicationManager?: any;
    errorHandler?: any;
  }
): Vue2Adapter {
  const {
    lifecycleManager,
    sandboxManager,
    communicationManager,
    errorHandler
  } = dependencies || {};

  if (!lifecycleManager || !sandboxManager || !communicationManager || !errorHandler) {
    throw new Error('All dependencies are required to create Vue2Adapter');
  }

  return new Vue2Adapter(
    config,
    lifecycleManager,
    sandboxManager,
    communicationManager,
    errorHandler
  );
}

/**
 * Check if an app configuration is for a Vue 2 application
 */
export function isVue2App(config: any): config is Vue2AppConfig {
  return !!(
    config.vue2 ||
    config.component ||
    config.vueOptions ||
    (config.entry && isVue2Entry(config.entry))
  );
}

/**
 * Check if an entry point indicates a Vue 2 application
 */
export function isVue2Entry(entry: string): boolean {
  const vue2Indicators = [
    'vue',
    '.vue',
    'vue2',
    'vue-app'
  ];

  const lowerEntry = entry.toLowerCase();
  return vue2Indicators.some(indicator => lowerEntry.includes(indicator));
}

/**
 * Get Vue 2 version from the environment
 */
export function getVue2Version(): string | null {
  try {
    if (Vue && Vue.version) {
      return Vue.version;
    }

    // Try to get from package.json if available
    if (typeof require !== 'undefined') {
      try {
        const VuePackage = require('vue/package.json');
        return VuePackage.version;
      } catch {
        // Vue not available
      }
    }

    return null;
  } catch {
    return null;
  }
}

/**
 * Check Vue 2 version compatibility
 */
export function isVue2VersionCompatible(version: string, minVersion: string = '2.6.0'): boolean {
  try {
    const parseVersion = (v: string) => v.split('.').map(Number);
    const current = parseVersion(version);
    const minimum = parseVersion(minVersion);

    for (let i = 0; i < Math.max(current.length, minimum.length); i++) {
      const currentPart = current[i] || 0;
      const minimumPart = minimum[i] || 0;

      if (currentPart > minimumPart) return true;
      if (currentPart < minimumPart) return false;
    }

    return true;
  } catch {
    return false;
  }
}

/**
 * Validate Vue 2 app configuration
 */
export function validateVue2Config(config: Vue2AppConfig): void {
  if (!config.name) {
    throw new Error('Vue 2 app name is required');
  }

  if (!config.component && !config.vueOptions && !config.entry) {
    throw new Error('Either component, vueOptions, or entry must be specified for Vue 2 app');
  }

  if (config.vue2?.vueVersion) {
    const currentVersion = getVue2Version();
    if (currentVersion && !isVue2VersionCompatible(currentVersion, config.vue2.vueVersion)) {
      console.warn(
        `Vue 2 version mismatch. Current: ${currentVersion}, Required: ${config.vue2.vueVersion}`
      );
    }
  }
}

/**
 * Create default Vue 2 app configuration
 */
export function createDefaultVue2Config(overrides: Partial<Vue2AppConfig> = {}): Vue2AppConfig {
  return {
    name: 'vue2-app',
    vue2: {
      vueVersion: '2.7',
      enableDevTools: process.env.NODE_ENV === 'development',
      globalConfig: {
        productionTip: false
      },
      sandbox: {
        isolateVueGlobal: true,
        preserveDevTools: true,
        plugins: [],
        mixins: []
      }
    },
    ...overrides
  };
}

/**
 * 检查首选名称的组件
 */
function checkPreferredVue2Component(module: any, preferredName: string): Vue2ComponentOptions | null {
  if (!preferredName || !module[preferredName]) {
    return null;
  }

  if (isVue2Component(module[preferredName])) {
    return module[preferredName];
  }

  return null;
}

/**
 * 检查默认导出的组件
 */
function checkDefaultVue2Component(module: any): Vue2ComponentOptions | null {
  if (!module.default) {
    return null;
  }

  if (isVue2Component(module.default)) {
    return module.default;
  }

  return null;
}

/**
 * 获取所有命名导出的组件
 */
function getNamedVue2Components(module: any): string[] {
  return Object.keys(module).filter(key => {
    const value = module[key];
    return key !== 'default' && isVue2Component(value);
  });
}

/**
 * 从多个组件中选择最佳组件
 */
function selectBestVue2Component(module: any, componentKeys: string[]): Vue2ComponentOptions {
  // 按优先级查找常见组件名称
  const commonNames = ['App', 'Main', 'Root', 'Component', 'Index', 'Default'];
  for (const name of commonNames) {
    if (componentKeys.includes(name)) {
      return module[name];
    }
  }

  // 按字母顺序返回第一个
  const sortedKeys = componentKeys.sort();
  console.warn(
    `Multiple Vue 2 components found: ${componentKeys.join(', ')}. ` +
    `Using: ${sortedKeys[0]}. Consider specifying preferredName option.`
  );
  return module[sortedKeys[0]];
}

/**
 * Extract Vue 2 component from module
 * @description 从模块中提取 Vue 2 组件，支持多种导出方式和组件类型
 * @param module 模块对象
 * @param options 提取选项
 * @returns Vue 2 组件选项
 */
export function extractVue2Component(
  module: any,
  options: {
    preferredName?: string;
    allowMultiple?: boolean;
  } = {}
): Vue2ComponentOptions {
  if (!module) {
    throw new Error('Module is required for component extraction');
  }

  const { preferredName, allowMultiple = false } = options;

  // 1. 检查首选名称
  const preferredComponent = checkPreferredVue2Component(module, preferredName!);
  if (preferredComponent) {
    return preferredComponent;
  }

  // 2. 检查默认导出
  const defaultComponent = checkDefaultVue2Component(module);
  if (defaultComponent) {
    return defaultComponent;
  }

  // 3. 检查命名导出
  const componentKeys = getNamedVue2Components(module);

  if (componentKeys.length === 0) {
    // 4. 如果没有找到组件但有 Vue 组件属性，作为组件选项处理
    if (module.template || module.render || module.data) {
      return module;
    }
    throw new Error('No Vue 2 component found in module');
  }

  if (componentKeys.length === 1) {
    return module[componentKeys[0]];
  }

  // 5. 处理多个组件的情况
  if (allowMultiple) {
    // 返回所有找到的组件
    return componentKeys.reduce((acc, key) => {
      acc[key] = module[key];
      return acc;
    }, {} as Record<string, Vue2ComponentOptions>);
  } else {
    // 选择最佳组件
    return selectBestVue2Component(module, componentKeys);
  }
}

/**
 * Check if a value is a Vue 2 component
 */
export function isVue2Component(value: any): boolean {
  if (!value) return false;

  // Vue component options object
  if (typeof value === 'object' && (
    value.template ||
    value.render ||
    value.data ||
    value.computed ||
    value.methods ||
    value.props ||
    value.components
  )) {
    return true;
  }

  // Vue constructor
  if (typeof value === 'function' && value.cid !== undefined) {
    return true;
  }

  // Vue component instance
  if (value instanceof Vue) {
    return true;
  }

  return false;
}

/**
 * Create Vue 2 app container element - 使用 shared 工具
 */
export function createVue2Container(appName: string, parentElement?: HTMLElement): HTMLElement {
  return createEnhancedContainer(appName, 'vue2', parentElement, {
    className: 'vue2-app-container'
  });
}

/**
 * Clean up Vue 2 app container - 使用 shared 工具
 */
export function cleanupVue2Container(container: HTMLElement): void {
  cleanupContainer(container);
}

/**
 * Get Vue 2 app container by name
 */
export function getVue2Container(appName: string): HTMLElement | null {
  return document.getElementById(`micro-app-${appName}`);
}

/**
 * Check if Vue DevTools is available
 */
export function isVue2DevToolsAvailable(): boolean {
  return typeof window !== 'undefined' && !!(window as any).__VUE_DEVTOOLS_GLOBAL_HOOK__;
}

/**
 * Enable Vue DevTools for a specific app
 */
export function enableVue2DevTools(appName: string): void {
  if (isVue2DevToolsAvailable()) {
    console.log(`Vue 2 DevTools enabled for app: ${appName}`);
    Vue.config.devtools = true;
  }
}

/**
 * Create Vue 2 error info object - 使用 shared 工具
 */
export function createVue2ErrorInfo(error: Error, vm?: Vue, info?: string): any {
  const componentName = vm?.$options.name || (vm?.$options as any)._componentTag || 'Unknown';
  const componentStack = getComponentHierarchy(vm).join(' -> ');

  return sharedCreateVue2ErrorInfo(error, componentName, componentStack);
}

/**
 * Get Vue component hierarchy
 */
function getComponentHierarchy(vm?: Vue): string[] {
  const hierarchy: string[] = [];
  let current = vm;

  while (current) {
    const name = current.$options.name || (current.$options as any)._componentTag || 'Anonymous';
    hierarchy.push(name);
    current = current.$parent || undefined;
  }

  return hierarchy;
}

/**
 * Format Vue 2 error for logging - 使用 shared 工具
 */
export function formatVue2Error(error: Error, vm?: Vue, info?: string): string {
  const componentName = vm?.$options.name || (vm?.$options as any)._componentTag || 'Unknown';
  const componentStack = getComponentHierarchy(vm).join(' -> ');

  return sharedFormatVue2Error(error, {
    componentName,
    componentStack,
    appName: 'vue2-app',
    operation: info || 'unknown'
  });
}

/**
 * Deep merge Vue 2 configurations - 使用 shared 工具的通用实现
 */
export function mergeVue2Configs(
  base: Vue2AppConfig,
  override: Partial<Vue2AppConfig>
): Vue2AppConfig {
  return mergeConfigs(base, override);
}

/**
 * Create Vue 2 mixin for micro-app integration
 */
export function createVue2MicroAppMixin(context: any) {
  return {
    data() {
      return {
        $microApp: context
      };
    },
    methods: {
      $emitToParent(event: string, data?: any) {
        if ((this as any).$microApp?.communication) {
          (this as any).$microApp.communication.emit(event, data);
        }
      },
      $sendToApp(targetApp: string, message: any) {
        if ((this as any).$microApp?.communication) {
          (this as any).$microApp.communication.sendToApp(targetApp, message);
        }
      },
      $getGlobalState(key: string) {
        if ((this as any).$microApp?.communication) {
          return (this as any).$microApp.communication.getGlobalState(key);
        }
        return undefined;
      },
      $setGlobalState(state: any) {
        if ((this as any).$microApp?.communication) {
          (this as any).$microApp.communication.setGlobalState(state);
        }
      }
    }
  };
}

/**
 * Enhanced Vue 2 component detection
 * @description 增强的 Vue 2 组件检测函数，支持更多组件类型
 * @param value 待检查的值
 * @returns 是否为 Vue 2 组件
 */
export function isVue2ComponentEnhanced(value: any): boolean {
  if (!value) return false;

  // 标准 Vue 2 组件选项对象
  if (typeof value === 'object') {
    const hasVueOptions = !!(
      value.template ||
      value.render ||
      value.data ||
      value.computed ||
      value.methods ||
      value.props ||
      value.components ||
      value.watch ||
      value.mixins ||
      value.directives ||
      value.filters ||
      value.beforeCreate ||
      value.created ||
      value.beforeMount ||
      value.mounted ||
      value.beforeUpdate ||
      value.updated ||
      value.beforeDestroy ||
      value.destroyed
    );

    if (hasVueOptions) {
      return true;
    }
  }

  // Vue 构造函数
  if (typeof value === 'function') {
    // 检查是否有 Vue 构造函数的特征
    if (value.cid !== undefined || value.super === Vue) {
      return true;
    }

    // 检查是否是扩展的 Vue 构造函数
    if (value.prototype && value.prototype.$mount) {
      return true;
    }
  }

  // Vue 实例
  if (value instanceof Vue) {
    return true;
  }

  // 异步组件
  if (typeof value === 'function' && value.length === 2) {
    // 可能是异步组件 (resolve, reject) => {}
    return true;
  }

  return false;
}

/**
 * Vue2 适配器工具集合
 * @description 提供所有 Vue2 适配器相关的工具函数
 */
export const Vue2AdapterUtils = {
  // 组件相关
  extractVue2Component,
  isVue2Component,
  isVue2ComponentEnhanced,

  // 应用检测
  isVue2App,
  isVue2Entry,

  // 版本管理
  getVue2Version,
  isVue2VersionCompatible,

  // 配置管理
  validateVue2Config,
  createDefaultVue2Config,
  mergeVue2Configs,

  // 容器管理
  createVue2Container,
  cleanupVue2Container,
  getVue2Container,

  // 错误处理
  formatVue2Error,
  createVue2ErrorInfo,

  // 开发工具
  isVue2DevToolsAvailable,
  enableVue2DevTools,

  // 微应用集成
  createVue2MicroAppMixin,

  // 适配器创建
  createVue2Adapter
} as const;