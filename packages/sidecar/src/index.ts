/**
 * @fileoverview Micro-Core Sidecar 主入口
 * @description 提供零配置的微前端接入方案，支持一行代码接入
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type { MicroCoreOptions } from '@micro-core/core';
// 创建简单的错误创建函数和日志器
const createMicroCoreError = (code: string, message: string, context?: any) => {
    const error = new Error(message);
    (error as any).code = code;
    (error as any).context = context;
    return error;
};

const logger = {
    info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
    debug: (message: string, ...args: any[]) => console.log(`[DEBUG] ${message}`, ...args),
    warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args),
    error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args)
};

// 临时类型定义，直到 @micro-core/core 完善
export interface MicroAppConfig {
    name: string;
    entry: string;
    container?: string | HTMLElement;
    activeWhen?: string | ((location: Location) => boolean);
    customProps?: Record<string, any>;
}

// 核心模块
import { AutoConfig } from './auto-config';
import { AutoDiscovery } from './core/auto-discovery';
import { ConfigManager } from './core/config-manager';
import { SidecarManager } from './core/sidecar-manager';
import { FrameworkDetector } from './utils/framework-detector';

// 类型定义
import type {
    BridgeConfig,
    IsolationConfig,
    SidecarConfig,
    SidecarProxyConfig,
    SidecarStats,
    SidecarStatus
} from './types';

/**
 * Sidecar 初始化选项
 */
export interface SidecarInitOptions extends MicroCoreOptions {
    /** 应用名称 */
    name?: string;
    /** 应用信息 */
    app?: {
        name: string;
        version: string;
        entry: string;
        [key: string]: any;
    };
    /** 应用配置列表 */
    apps?: MicroAppConfig[];
    /** 是否自动启动 */
    autoStart?: boolean;
    /** 容器选择器 */
    container?: string | HTMLElement;
    /** 是否自动发现应用 */
    autoDiscovery?: boolean;
    /** 代理配置 */
    proxy?: SidecarProxyConfig;
    /** 隔离配置 */
    isolation?: IsolationConfig;
    /** 桥接配置 */
    bridge?: BridgeConfig;
    /** 调试模式 */
    debug?: boolean;
    /** 错误处理器 */
    errorHandler?: (error: Error) => void;
}

/**
 * Sidecar 实例状态
 */
interface SidecarInstance {
    manager: SidecarManager;
    status: SidecarStatus;
    config: SidecarConfig;
    stats: SidecarStats;
    createdAt: number;
}

/**
 * 全局 Sidecar 实例
 */
let globalSidecarInstance: SidecarInstance | null = null;

/**
 * 错误处理包装器
 */
function handleErrors<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    methodName: string
): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
        try {
            return await fn(...args);
        } catch (error) {
            const sidecarError = createMicroCoreError(
                'SIDECAR_ERROR',
                `Sidecar ${methodName} failed: ${error instanceof Error ? error.message : String(error)}`,
                { originalError: error, method: methodName, args }
            );

            logger.error(sidecarError.message, sidecarError);

            // 调用用户定义的错误处理器
            if (globalSidecarInstance?.config.errorHandler) {
                globalSidecarInstance.config.errorHandler(sidecarError);
            }

            throw sidecarError;
        }
    };
}

/**
 * 初始化 Sidecar
 * @param options 初始化选项
 * @returns Sidecar 管理器实例
 */
export const init = handleErrors(async (options: SidecarInitOptions = {}): Promise<SidecarManager> => {
    if (globalSidecarInstance) {
        logger.warn('Sidecar 已经初始化，返回现有实例');
        return globalSidecarInstance.manager;
    }

    logger.info('正在初始化 Micro-Core Sidecar...', { version: '0.1.0', options });

    try {
        // 创建配置管理器并加载配置
        const configManager = new ConfigManager();
        const baseConfig = await configManager.load();

        // 合并用户选项
        const finalConfig: SidecarConfig = {
            ...baseConfig,
            ...options,
            app: {
                name: options.name || 'sidecar-app',
                version: '0.1.0',
                entry: window.location.href,
                ...options.app
            },
            errorHandler: options.errorHandler || (() => { })
        };

        // 创建 Sidecar 管理器
        const manager = new SidecarManager(finalConfig);

        // 初始化管理器
        await manager.init();

        // 创建实例状态
        globalSidecarInstance = {
            manager,
            status: 'idle',
            config: finalConfig,
            stats: {
                startTime: Date.now(),
                uptime: 0,
                proxyRequests: 0,
                messagesSent: 0,
                messagesReceived: 0,
                errors: 0
            },
            createdAt: Date.now()
        };

        // 监听状态变化（如果管理器支持事件）
        if (typeof manager.on === 'function') {
            manager.on('status-change', (data: any) => {
                if (globalSidecarInstance && data && data.status) {
                    globalSidecarInstance.status = data.status;
                }
            });
        }

        // 自动启动
        if (options.autoStart !== false) {
            await start();
        }

        logger.info('Micro-Core Sidecar 初始化成功');
        return manager;

    } catch (error) {
        logger.error('Sidecar 初始化失败:', error);
        throw error;
    }
}, 'init');

/**
 * 获取 Sidecar 实例
 * @returns Sidecar 管理器实例或 null
 */
export function getSidecar(): SidecarManager | null {
    return globalSidecarInstance?.manager || null;
}

/**
 * 启动 Sidecar
 */
export const start = handleErrors(async (): Promise<void> => {
    if (!globalSidecarInstance) {
        throw createMicroCoreError(
            'SIDECAR_NOT_INITIALIZED',
            'Sidecar 未初始化，请先调用 init() 方法'
        );
    }

    logger.info('正在启动 Sidecar...');
    await globalSidecarInstance.manager.start();

    if (globalSidecarInstance) {
        globalSidecarInstance.status = 'running';
        globalSidecarInstance.stats.startTime = Date.now();
    }

    logger.info('Sidecar 启动成功');
}, 'start');

/**
 * 停止 Sidecar
 */
export const stop = handleErrors(async (): Promise<void> => {
    if (!globalSidecarInstance) {
        logger.warn('Sidecar 未初始化，无需停止');
        return;
    }

    logger.info('正在停止 Sidecar...');
    await globalSidecarInstance.manager.stop();

    globalSidecarInstance.status = 'stopped';
    logger.info('Sidecar 已停止');
}, 'stop');

/**
 * 重启 Sidecar
 */
export const restart = handleErrors(async (): Promise<void> => {
    logger.info('正在重启 Sidecar...');
    await stop();
    await start();
    logger.info('Sidecar 重启成功');
}, 'restart');

/**
 * 注册应用
 * @param config 应用配置
 */
export function registerApp(config: MicroAppConfig): void {
    if (!globalSidecarInstance) {
        throw createMicroCoreError(
            'SIDECAR_NOT_INITIALIZED',
            'Sidecar 未初始化，请先调用 init() 方法'
        );
    }

    logger.info(`注册应用: ${config.name}`, config);
    globalSidecarInstance.manager.registerApp(config);
}

/**
 * 卸载应用
 * @param name 应用名称
 */
export function unregisterApp(name: string): void {
    if (!globalSidecarInstance) {
        throw createMicroCoreError(
            'SIDECAR_NOT_INITIALIZED',
            'Sidecar 未初始化，请先调用 init() 方法'
        );
    }

    logger.info(`卸载应用: ${name}`);
    globalSidecarInstance.manager.unregisterApp(name);
}

/**
 * 获取 Sidecar 状态
 * @returns Sidecar 状态信息
 */
export function getStatus(): {
    status: SidecarStatus;
    stats: SidecarStats;
    config: SidecarConfig;
    uptime: number;
} | null {
    if (!globalSidecarInstance) {
        return null;
    }

    const now = Date.now();
    const uptime = now - globalSidecarInstance.createdAt;

    return {
        status: globalSidecarInstance.status,
        stats: {
            ...globalSidecarInstance.stats,
            uptime
        },
        config: globalSidecarInstance.config,
        uptime
    };
}

/**
 * 销毁 Sidecar 实例
 */
export const destroy = handleErrors(async (): Promise<void> => {
    if (!globalSidecarInstance) {
        return;
    }

    logger.info('正在销毁 Sidecar...');

    try {
        // 尝试调用 destroy 方法，如果不存在则调用 stop
        if ('destroy' in globalSidecarInstance.manager &&
            typeof (globalSidecarInstance.manager as any).destroy === 'function') {
            await (globalSidecarInstance.manager as any).destroy();
        } else {
            await globalSidecarInstance.manager.stop();
        }
    } finally {
        globalSidecarInstance = null;
    }

    logger.info('Sidecar 已销毁');
}, 'destroy');

/**
 * 创建 Sidecar 实例（工厂方法）
 * @param options 创建选项
 * @returns Sidecar 实例
 */
export async function createSidecar(options: SidecarInitOptions = {}): Promise<{
    manager: SidecarManager;
    init: () => Promise<void>;
    start: () => Promise<void>;
    stop: () => Promise<void>;
    destroy: () => Promise<void>;
}> {
    const manager = await init(options);

    return {
        manager,
        init: async () => { await init(options); },
        start,
        stop,
        destroy
    };
}

// 导出核心类
export { AutoConfig } from './auto-config';
export { AutoDiscovery } from './core/auto-discovery';
export { ConfigManager } from './core/config-manager';
export { SidecarManager } from './core/sidecar-manager';
export { FrameworkDetector } from './utils/framework-detector';

// 导出类型
export type {
    BridgeConfig,
    IsolationConfig,
    SidecarConfig,
    SidecarProxyConfig,
    SidecarStats,
    SidecarStatus
};

// 重新导出初始化选项类型
export type { SidecarInitOptions as InitOptions };

// 默认导出
export default {
    init,
    start,
    stop,
    restart,
    registerApp,
    unregisterApp,
    getSidecar,
    getStatus,
    destroy,
    createSidecar,

    // 工具类
    AutoDiscovery,
    ConfigManager,
    SidecarManager,
    FrameworkDetector,
    AutoConfig
};

// 全局暴露（用于 CDN 引入）
if (typeof window !== 'undefined') {
    (window as any).MicroCoreSidecar = {
        init,
        start,
        stop,
        restart,
        registerApp,
        unregisterApp,
        getSidecar,
        getStatus,
        destroy,
        createSidecar
    };
}
