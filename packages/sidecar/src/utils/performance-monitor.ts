/**
 * @fileoverview 性能监控工具
 * @description 提供 Sidecar 应用的性能监控和分析功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
    /** 应用启动时间 */
    startupTime: number;
    /** 内存使用量 */
    memoryUsage: number;
    /** 应用加载时间 */
    loadTime: number;
    /** 错误计数 */
    errorCount: number;
    /** 成功操作计数 */
    successCount: number;
    /** 平均响应时间 */
    averageResponseTime: number;
}

/**
 * 性能监控配置
 */
export interface PerformanceConfig {
    /** 是否启用监控 */
    enabled?: boolean;
    /** 采样率 (0-1) */
    sampleRate?: number;
    /** 监控间隔 (毫秒) */
    interval?: number;
    /** 是否收集内存信息 */
    collectMemory?: boolean;
    /** 是否收集网络信息 */
    collectNetwork?: boolean;
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
    private config: Required<PerformanceConfig>;
    private metrics: PerformanceMetrics;
    private startTime: number;
    private observers: PerformanceObserver[] = [];
    private timers: Map<string, number> = new Map();

    constructor(config: PerformanceConfig = {}) {
        this.config = {
            enabled: true,
            sampleRate: 1.0,
            interval: 5000,
            collectMemory: true,
            collectNetwork: true,
            ...config
        };

        this.startTime = performance.now();
        this.metrics = {
            startupTime: 0,
            memoryUsage: 0,
            loadTime: 0,
            errorCount: 0,
            successCount: 0,
            averageResponseTime: 0
        };

        if (this.config.enabled) {
            this.initialize();
        }
    }

    /**
     * 初始化性能监控
     */
    private initialize(): void {
        try {
            // 监控导航性能
            if ('PerformanceObserver' in window) {
                this.setupNavigationObserver();
                this.setupResourceObserver();
                this.setupMeasureObserver();
            }

            // 定期收集指标
            setInterval(() => {
                this.collectMetrics();
            }, this.config.interval);

            // 监听页面卸载事件，发送最终指标
            window.addEventListener('beforeunload', () => {
                this.sendMetrics();
            });

        } catch (error) {
            console.warn('Performance monitor initialization failed:', error);
        }
    }

    /**
     * 设置导航性能观察器
     */
    private setupNavigationObserver(): void {
        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach((entry) => {
                    if (entry.entryType === 'navigation') {
                        const navEntry = entry as PerformanceNavigationTiming;
                        this.metrics.loadTime = navEntry.loadEventEnd - navEntry.loadEventStart;
                        this.metrics.startupTime = navEntry.domContentLoadedEventEnd - navEntry.fetchStart;
                    }
                });
            });

            observer.observe({ entryTypes: ['navigation'] });
            this.observers.push(observer);
        } catch (error) {
            console.warn('Failed to setup navigation observer:', error);
        }
    }

    /**
     * 设置资源性能观察器
     */
    private setupResourceObserver(): void {
        if (!this.config.collectNetwork) return;

        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach((entry) => {
                    if (entry.entryType === 'resource') {
                        // 可以在这里收集资源加载性能数据
                        // 例如：慢资源、失败的资源等
                    }
                });
            });

            observer.observe({ entryTypes: ['resource'] });
            this.observers.push(observer);
        } catch (error) {
            console.warn('Failed to setup resource observer:', error);
        }
    }

    /**
     * 设置测量性能观察器
     */
    private setupMeasureObserver(): void {
        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach((entry) => {
                    if (entry.entryType === 'measure') {
                        // 处理自定义测量
                        this.updateAverageResponseTime(entry.duration);
                    }
                });
            });

            observer.observe({ entryTypes: ['measure'] });
            this.observers.push(observer);
        } catch (error) {
            console.warn('Failed to setup measure observer:', error);
        }
    }

    /**
     * 开始计时
     */
    startTimer(name: string): void {
        if (!this.config.enabled) return;

        this.timers.set(name, performance.now());
        performance.mark(`${name}-start`);
    }

    /**
     * 结束计时
     */
    endTimer(name: string): number {
        if (!this.config.enabled) return 0;

        const startTime = this.timers.get(name);
        if (!startTime) {
            console.warn(`Timer "${name}" was not started`);
            return 0;
        }

        const endTime = performance.now();
        const duration = endTime - startTime;

        performance.mark(`${name}-end`);
        performance.measure(name, `${name}-start`, `${name}-end`);

        this.timers.delete(name);
        return duration;
    }

    /**
     * 记录成功操作
     */
    recordSuccess(): void {
        if (!this.config.enabled) return;
        this.metrics.successCount++;
    }

    /**
     * 记录错误
     */
    recordError(): void {
        if (!this.config.enabled) return;
        this.metrics.errorCount++;
    }

    /**
     * 收集当前指标
     */
    private collectMetrics(): void {
        if (!this.shouldSample()) return;

        try {
            // 收集内存使用情况
            if (this.config.collectMemory && 'memory' in performance) {
                const memory = (performance as any).memory;
                this.metrics.memoryUsage = memory.usedJSHeapSize;
            }

            // 可以添加更多指标收集逻辑
        } catch (error) {
            console.warn('Failed to collect metrics:', error);
        }
    }

    /**
     * 更新平均响应时间
     */
    private updateAverageResponseTime(duration: number): void {
        const totalOperations = this.metrics.successCount + this.metrics.errorCount;
        if (totalOperations === 0) {
            this.metrics.averageResponseTime = duration;
        } else {
            this.metrics.averageResponseTime =
                (this.metrics.averageResponseTime * totalOperations + duration) / (totalOperations + 1);
        }
    }

    /**
     * 判断是否应该采样
     */
    private shouldSample(): boolean {
        return Math.random() < this.config.sampleRate;
    }

    /**
     * 获取当前指标
     */
    getMetrics(): PerformanceMetrics {
        return { ...this.metrics };
    }

    /**
     * 发送指标数据
     */
    private sendMetrics(): void {
        if (!this.shouldSample()) return;

        try {
            // 这里可以发送指标到监控服务
            // 例如：发送到 Google Analytics、自定义监控服务等
            console.debug('Performance metrics:', this.getMetrics());
        } catch (error) {
            console.warn('Failed to send metrics:', error);
        }
    }

    /**
     * 清理资源
     */
    destroy(): void {
        // 断开所有观察器
        this.observers.forEach(observer => {
            try {
                observer.disconnect();
            } catch (error) {
                console.warn('Failed to disconnect observer:', error);
            }
        });

        this.observers = [];
        this.timers.clear();

        // 发送最终指标
        this.sendMetrics();
    }

    /**
     * 生成性能报告
     */
    generateReport(): string {
        const metrics = this.getMetrics();
        const report = [
            '=== Sidecar Performance Report ===',
            `Startup Time: ${metrics.startupTime.toFixed(2)}ms`,
            `Load Time: ${metrics.loadTime.toFixed(2)}ms`,
            `Memory Usage: ${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`,
            `Success Count: ${metrics.successCount}`,
            `Error Count: ${metrics.errorCount}`,
            `Average Response Time: ${metrics.averageResponseTime.toFixed(2)}ms`,
            `Error Rate: ${((metrics.errorCount / (metrics.successCount + metrics.errorCount)) * 100).toFixed(2)}%`,
            '=================================='
        ].join('\n');

        return report;
    }
}

/**
 * 全局性能监控实例
 */
export const performanceMonitor = new PerformanceMonitor();

/**
 * 性能监控装饰器
 */
export function monitor(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = function (...args: any[]) {
        const timerName = `${target.constructor.name}.${propertyName}`;
        performanceMonitor.startTimer(timerName);

        try {
            const result = method.apply(this, args);

            // 处理异步方法
            if (result && typeof result.then === 'function') {
                return result
                    .then((value: any) => {
                        performanceMonitor.endTimer(timerName);
                        performanceMonitor.recordSuccess();
                        return value;
                    })
                    .catch((error: any) => {
                        performanceMonitor.endTimer(timerName);
                        performanceMonitor.recordError();
                        throw error;
                    });
            }

            // 处理同步方法
            performanceMonitor.endTimer(timerName);
            performanceMonitor.recordSuccess();
            return result;

        } catch (error) {
            performanceMonitor.endTimer(timerName);
            performanceMonitor.recordError();
            throw error;
        }
    };

    return descriptor;
}

export default PerformanceMonitor;
