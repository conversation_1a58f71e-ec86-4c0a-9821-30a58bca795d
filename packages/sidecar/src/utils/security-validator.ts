/**
 * @fileoverview 安全验证工具
 * @description 提供 Sidecar 应用的安全验证和防护功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 安全配置接口
 */
export interface SecurityConfig {
    /** 是否启用 CSP 检查 */
    enableCSP?: boolean;
    /** 允许的域名列表 */
    allowedDomains?: string[];
    /** 是否启用 XSS 防护 */
    enableXSSProtection?: boolean;
    /** 是否启用 CSRF 防护 */
    enableCSRFProtection?: boolean;
    /** 最大请求大小 (字节) */
    maxRequestSize?: number;
    /** 请求频率限制 (每分钟) */
    rateLimit?: number;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
    /** 是否通过验证 */
    isValid: boolean;
    /** 错误信息 */
    errors: string[];
    /** 警告信息 */
    warnings: string[];
}

/**
 * 安全验证器
 */
export class SecurityValidator {
    private config: Required<SecurityConfig>;
    private requestCounts: Map<string, { count: number; timestamp: number }> = new Map();

    constructor(config: SecurityConfig = {}) {
        this.config = {
            enableCSP: true,
            allowedDomains: ['localhost', '127.0.0.1'],
            enableXSSProtection: true,
            enableCSRFProtection: true,
            maxRequestSize: 10 * 1024 * 1024, // 10MB
            rateLimit: 100, // 每分钟100次请求
            ...config
        };
    }

    /**
     * 验证 URL 安全性
     */
    validateURL(url: string): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        try {
            const urlObj = new URL(url);

            // 检查协议
            if (!['http:', 'https:'].includes(urlObj.protocol)) {
                result.isValid = false;
                result.errors.push(`不安全的协议: ${urlObj.protocol}`);
            }

            // 检查域名白名单
            if (this.config.allowedDomains.length > 0) {
                const isAllowed = this.config.allowedDomains.some(domain => {
                    return urlObj.hostname === domain ||
                        urlObj.hostname.endsWith(`.${domain}`);
                });

                if (!isAllowed) {
                    result.isValid = false;
                    result.errors.push(`域名不在白名单中: ${urlObj.hostname}`);
                }
            }

            // 检查是否为本地文件
            if (urlObj.protocol === 'file:') {
                result.warnings.push('检测到本地文件协议，请确保文件来源可信');
            }

            // 检查端口
            if (urlObj.port && !this.isPortAllowed(parseInt(urlObj.port))) {
                result.warnings.push(`使用了非标准端口: ${urlObj.port}`);
            }

        } catch (error) {
            result.isValid = false;
            result.errors.push(`无效的 URL 格式: ${error.message}`);
        }

        return result;
    }

    /**
     * 验证 HTML 内容安全性
     */
    validateHTML(html: string): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        if (!this.config.enableXSSProtection) {
            return result;
        }

        // 检查危险的标签
        const dangerousTags = [
            /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
            /<iframe\b[^>]*>/gi,
            /<object\b[^>]*>/gi,
            /<embed\b[^>]*>/gi,
            /<form\b[^>]*>/gi
        ];

        dangerousTags.forEach((pattern, index) => {
            if (pattern.test(html)) {
                const tagNames = ['script', 'iframe', 'object', 'embed', 'form'];
                result.warnings.push(`检测到潜在危险标签: ${tagNames[index]}`);
            }
        });

        // 检查危险的属性
        const dangerousAttributes = [
            /on\w+\s*=/gi, // onclick, onload 等事件属性
            /javascript:/gi, // javascript: 协议
            /data:/gi, // data: 协议
            /vbscript:/gi // vbscript: 协议
        ];

        dangerousAttributes.forEach(pattern => {
            if (pattern.test(html)) {
                result.warnings.push('检测到潜在危险属性或协议');
            }
        });

        return result;
    }

    /**
     * 验证配置对象安全性
     */
    validateConfig(config: any): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        if (!config || typeof config !== 'object') {
            result.isValid = false;
            result.errors.push('配置必须是一个对象');
            return result;
        }

        // 检查是否包含函数（可能的代码注入）
        this.checkForFunctions(config, result, '');

        // 检查配置大小
        const configSize = JSON.stringify(config).length;
        if (configSize > this.config.maxRequestSize) {
            result.isValid = false;
            result.errors.push(`配置大小超过限制: ${configSize} > ${this.config.maxRequestSize}`);
        }

        // 检查敏感字段
        const sensitiveFields = ['password', 'token', 'secret', 'key', 'apiKey'];
        this.checkSensitiveFields(config, sensitiveFields, result, '');

        return result;
    }

    /**
     * 验证请求频率
     */
    validateRateLimit(identifier: string): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        const now = Date.now();
        const windowMs = 60 * 1000; // 1分钟窗口

        // 清理过期记录
        this.cleanupExpiredRequests(now, windowMs);

        // 获取或创建请求记录
        let requestInfo = this.requestCounts.get(identifier);
        if (!requestInfo) {
            requestInfo = { count: 0, timestamp: now };
            this.requestCounts.set(identifier, requestInfo);
        }

        // 检查是否在同一时间窗口内
        if (now - requestInfo.timestamp < windowMs) {
            requestInfo.count++;

            if (requestInfo.count > this.config.rateLimit) {
                result.isValid = false;
                result.errors.push(`请求频率超过限制: ${requestInfo.count} > ${this.config.rateLimit}/分钟`);
            } else if (requestInfo.count > this.config.rateLimit * 0.8) {
                result.warnings.push(`请求频率接近限制: ${requestInfo.count}/${this.config.rateLimit}`);
            }
        } else {
            // 新的时间窗口
            requestInfo.count = 1;
            requestInfo.timestamp = now;
        }

        return result;
    }

    /**
     * 验证 CSP 策略
     */
    validateCSP(): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        if (!this.config.enableCSP) {
            return result;
        }

        try {
            // 检查当前页面的 CSP 设置
            const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
            const cspContent = metaCSP?.getAttribute('content');

            if (!cspContent) {
                result.warnings.push('未检测到 CSP 策略，建议添加以提高安全性');
                return result;
            }

            // 解析 CSP 策略
            const policies = this.parseCSP(cspContent);

            // 检查关键策略
            if (!policies['script-src']) {
                result.warnings.push('CSP 中缺少 script-src 策略');
            } else if (policies['script-src'].includes("'unsafe-eval'")) {
                result.warnings.push('CSP 允许 unsafe-eval，存在安全风险');
            }

            if (!policies['object-src']) {
                result.warnings.push('CSP 中缺少 object-src 策略');
            }

            if (policies['script-src']?.includes("'unsafe-inline'")) {
                result.warnings.push('CSP 允许 unsafe-inline，存在 XSS 风险');
            }

        } catch (error) {
            result.warnings.push(`CSP 验证失败: ${error.message}`);
        }

        return result;
    }

    /**
     * 生成安全报告
     */
    generateSecurityReport(): string {
        const reports: string[] = ['=== Sidecar Security Report ==='];

        // CSP 检查
        const cspResult = this.validateCSP();
        reports.push('\n--- CSP 检查 ---');
        if (cspResult.errors.length > 0) {
            reports.push('错误:');
            cspResult.errors.forEach(error => reports.push(`  - ${error}`));
        }
        if (cspResult.warnings.length > 0) {
            reports.push('警告:');
            cspResult.warnings.forEach(warning => reports.push(`  - ${warning}`));
        }
        if (cspResult.errors.length === 0 && cspResult.warnings.length === 0) {
            reports.push('✅ CSP 配置正常');
        }

        // 请求统计
        reports.push('\n--- 请求统计 ---');
        reports.push(`活跃请求源: ${this.requestCounts.size}`);
        reports.push(`频率限制: ${this.config.rateLimit}/分钟`);

        // 安全配置
        reports.push('\n--- 安全配置 ---');
        reports.push(`XSS 防护: ${this.config.enableXSSProtection ? '启用' : '禁用'}`);
        reports.push(`CSRF 防护: ${this.config.enableCSRFProtection ? '启用' : '禁用'}`);
        reports.push(`域名白名单: ${this.config.allowedDomains.join(', ')}`);
        reports.push(`最大请求大小: ${(this.config.maxRequestSize / 1024 / 1024).toFixed(2)}MB`);

        reports.push('\n=====================================');

        return reports.join('\n');
    }

    /**
     * 检查端口是否被允许
     */
    private isPortAllowed(port: number): boolean {
        // 标准 HTTP/HTTPS 端口
        const standardPorts = [80, 443, 8080, 8443, 3000, 3001, 4200, 5000];
        return standardPorts.includes(port);
    }

    /**
     * 递归检查对象中的函数
     */
    private checkForFunctions(obj: any, result: ValidationResult, path: string): void {
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                const currentPath = path ? `${path}.${key}` : key;
                const value = obj[key];

                if (typeof value === 'function') {
                    result.warnings.push(`检测到函数属性: ${currentPath}`);
                } else if (typeof value === 'string' && this.containsJavaScript(value)) {
                    result.warnings.push(`检测到可能的脚本内容: ${currentPath}`);
                } else if (typeof value === 'object' && value !== null) {
                    this.checkForFunctions(value, result, currentPath);
                }
            }
        }
    }

    /**
     * 检查敏感字段
     */
    private checkSensitiveFields(obj: any, sensitiveFields: string[], result: ValidationResult, path: string): void {
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                const currentPath = path ? `${path}.${key}` : key;
                const lowerKey = key.toLowerCase();

                if (sensitiveFields.some(field => lowerKey.includes(field.toLowerCase()))) {
                    result.warnings.push(`检测到敏感字段: ${currentPath}`);
                }

                if (typeof obj[key] === 'object' && obj[key] !== null) {
                    this.checkSensitiveFields(obj[key], sensitiveFields, result, currentPath);
                }
            }
        }
    }

    /**
     * 检查字符串是否包含 JavaScript 代码
     */
    private containsJavaScript(str: string): boolean {
        const jsPatterns = [
            /function\s*\(/i,
            /=>\s*{/i,
            /eval\s*\(/i,
            /new\s+Function/i,
            /setTimeout\s*\(/i,
            /setInterval\s*\(/i
        ];

        return jsPatterns.some(pattern => pattern.test(str));
    }

    /**
     * 解析 CSP 策略
     */
    private parseCSP(cspContent: string): Record<string, string[]> {
        const policies: Record<string, string[]> = {};
        const directives = cspContent.split(';');

        directives.forEach(directive => {
            const parts = directive.trim().split(/\s+/);
            if (parts.length > 0) {
                const directiveName = parts[0];
                const sources = parts.slice(1);
                policies[directiveName] = sources;
            }
        });

        return policies;
    }

    /**
     * 清理过期的请求记录
     */
    private cleanupExpiredRequests(now: number, windowMs: number): void {
        for (const [identifier, requestInfo] of this.requestCounts.entries()) {
            if (now - requestInfo.timestamp > windowMs) {
                this.requestCounts.delete(identifier);
            }
        }
    }

    /**
     * 清理所有记录
     */
    cleanup(): void {
        this.requestCounts.clear();
    }
}

/**
 * 全局安全验证器实例
 */
export const securityValidator = new SecurityValidator();

/**
 * 安全验证装饰器
 */
export function secure(config?: SecurityConfig) {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
        const method = descriptor.value;
        const validator = new SecurityValidator(config);

        descriptor.value = function (...args: any[]) {
            // 验证参数安全性
            args.forEach((arg, index) => {
                if (typeof arg === 'string' && arg.startsWith('http')) {
                    const urlResult = validator.validateURL(arg);
                    if (!urlResult.isValid) {
                        throw new Error(`参数 ${index} URL 验证失败: ${urlResult.errors.join(', ')}`);
                    }
                } else if (typeof arg === 'object' && arg !== null) {
                    const configResult = validator.validateConfig(arg);
                    if (!configResult.isValid) {
                        throw new Error(`参数 ${index} 配置验证失败: ${configResult.errors.join(', ')}`);
                    }
                }
            });

            return method.apply(this, args);
        };

        return descriptor;
    };
}

export default SecurityValidator;
