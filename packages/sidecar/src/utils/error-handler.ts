/**
 * @fileoverview 统一错误处理工具
 * @description 提供 Sidecar 应用的统一错误处理和恢复机制
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 错误类型枚举
 */
export enum ErrorType {
    INITIALIZATION = 'INITIALIZATION',
    CONFIGURATION = 'CONFIGURATION',
    NETWORK = 'NETWORK',
    COMPATIBILITY = 'COMPATIBILITY',
    SECURITY = 'SECURITY',
    PERFORMANCE = 'PERFORMANCE',
    UNKNOWN = 'UNKNOWN'
}

/**
 * 错误严重程度枚举
 */
export enum ErrorSeverity {
    LOW = 'LOW',
    MEDIUM = 'MEDIUM',
    HIGH = 'HIGH',
    CRITICAL = 'CRITICAL'
}

/**
 * 错误上下文接口
 */
export interface ErrorContext {
    /** 错误发生的组件 */
    component?: string;
    /** 错误发生的方法 */
    method?: string;
    /** 用户代理信息 */
    userAgent?: string;
    /** 时间戳 */
    timestamp?: number;
    /** 额外的上下文数据 */
    data?: Record<string, any>;
}

/**
 * 错误恢复策略接口
 */
export interface ErrorRecoveryStrategy {
    /** 策略名称 */
    name: string;
    /** 是否可以恢复 */
    canRecover: (error: Error, context?: ErrorContext) => boolean;
    /** 恢复操作 */
    recover: (error: Error, context?: ErrorContext) => Promise<boolean>;
    /** 恢复优先级 */
    priority: number;
}

/**
 * 增强的错误类
 */
export class SidecarError extends Error {
    public readonly type: ErrorType;
    public readonly severity: ErrorSeverity;
    public readonly context: ErrorContext;
    public readonly timestamp: number;
    public readonly recoverable: boolean;

    constructor(
        message: string,
        type: ErrorType = ErrorType.UNKNOWN,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: ErrorContext = {},
        recoverable: boolean = false
    ) {
        super(message);
        this.name = 'SidecarError';
        this.type = type;
        this.severity = severity;
        this.context = {
            timestamp: Date.now(),
            userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
            ...context
        };
        this.timestamp = Date.now();
        this.recoverable = recoverable;

        // 保持堆栈跟踪
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, SidecarError);
        }
    }

    /**
     * 转换为 JSON 格式
     */
    toJSON(): Record<string, any> {
        return {
            name: this.name,
            message: this.message,
            type: this.type,
            severity: this.severity,
            context: this.context,
            timestamp: this.timestamp,
            recoverable: this.recoverable,
            stack: this.stack
        };
    }

    /**
     * 创建初始化错误
     */
    static initialization(message: string, context?: ErrorContext): SidecarError {
        return new SidecarError(message, ErrorType.INITIALIZATION, ErrorSeverity.HIGH, context, true);
    }

    /**
     * 创建配置错误
     */
    static configuration(message: string, context?: ErrorContext): SidecarError {
        return new SidecarError(message, ErrorType.CONFIGURATION, ErrorSeverity.MEDIUM, context, true);
    }

    /**
     * 创建网络错误
     */
    static network(message: string, context?: ErrorContext): SidecarError {
        return new SidecarError(message, ErrorType.NETWORK, ErrorSeverity.MEDIUM, context, true);
    }

    /**
     * 创建兼容性错误
     */
    static compatibility(message: string, context?: ErrorContext): SidecarError {
        return new SidecarError(message, ErrorType.COMPATIBILITY, ErrorSeverity.LOW, context, true);
    }

    /**
     * 创建安全错误
     */
    static security(message: string, context?: ErrorContext): SidecarError {
        return new SidecarError(message, ErrorType.SECURITY, ErrorSeverity.CRITICAL, context, false);
    }

    /**
     * 创建性能错误
     */
    static performance(message: string, context?: ErrorContext): SidecarError {
        return new SidecarError(message, ErrorType.PERFORMANCE, ErrorSeverity.LOW, context, true);
    }
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
    private strategies: ErrorRecoveryStrategy[] = [];
    private errorHistory: SidecarError[] = [];
    private maxHistorySize = 100;
    private onError?: (error: SidecarError) => void;
    private onRecovery?: (error: SidecarError, strategy: string) => void;

    constructor() {
        this.setupDefaultStrategies();
        this.setupGlobalErrorHandling();
    }

    /**
     * 设置错误回调
     */
    setErrorCallback(callback: (error: SidecarError) => void): void {
        this.onError = callback;
    }

    /**
     * 设置恢复回调
     */
    setRecoveryCallback(callback: (error: SidecarError, strategy: string) => void): void {
        this.onRecovery = callback;
    }

    /**
     * 注册恢复策略
     */
    registerStrategy(strategy: ErrorRecoveryStrategy): void {
        this.strategies.push(strategy);
        this.strategies.sort((a, b) => b.priority - a.priority);
    }

    /**
     * 处理错误
     */
    async handleError(error: Error | SidecarError, context?: ErrorContext): Promise<boolean> {
        let sidecarError: SidecarError;

        if (error instanceof SidecarError) {
            sidecarError = error;
        } else {
            sidecarError = new SidecarError(
                error.message,
                ErrorType.UNKNOWN,
                ErrorSeverity.MEDIUM,
                context
            );
        }

        // 记录错误
        this.recordError(sidecarError);

        // 触发错误回调
        if (this.onError) {
            try {
                this.onError(sidecarError);
            } catch (callbackError) {
                console.error('Error in error callback:', callbackError);
            }
        }

        // 尝试恢复
        if (sidecarError.recoverable) {
            return await this.attemptRecovery(sidecarError);
        }

        return false;
    }

    /**
     * 尝试错误恢复
     */
    private async attemptRecovery(error: SidecarError): Promise<boolean> {
        for (const strategy of this.strategies) {
            if (strategy.canRecover(error, error.context)) {
                try {
                    const recovered = await strategy.recover(error, error.context);
                    if (recovered) {
                        console.info(`Error recovered using strategy: ${strategy.name}`);

                        if (this.onRecovery) {
                            this.onRecovery(error, strategy.name);
                        }

                        return true;
                    }
                } catch (recoveryError) {
                    console.warn(`Recovery strategy ${strategy.name} failed:`, recoveryError);
                }
            }
        }

        return false;
    }

    /**
     * 记录错误
     */
    private recordError(error: SidecarError): void {
        this.errorHistory.push(error);

        // 限制历史记录大小
        if (this.errorHistory.length > this.maxHistorySize) {
            this.errorHistory.shift();
        }
    }

    /**
     * 获取错误历史
     */
    getErrorHistory(): SidecarError[] {
        return [...this.errorHistory];
    }

    /**
     * 获取错误统计
     */
    getErrorStats(): Record<string, any> {
        const stats = {
            total: this.errorHistory.length,
            byType: {} as Record<string, number>,
            bySeverity: {} as Record<string, number>,
            recoverable: 0,
            recent: this.errorHistory.slice(-10)
        };

        this.errorHistory.forEach(error => {
            stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
            stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
            if (error.recoverable) {
                stats.recoverable++;
            }
        });

        return stats;
    }

    /**
     * 清理错误历史
     */
    clearHistory(): void {
        this.errorHistory = [];
    }

    /**
     * 设置默认恢复策略
     */
    private setupDefaultStrategies(): void {
        // 网络重试策略
        this.registerStrategy({
            name: 'NetworkRetry',
            priority: 10,
            canRecover: (error) => error instanceof SidecarError && error.type === ErrorType.NETWORK,
            recover: async (error, context) => {
                const maxRetries = 3;
                const retryDelay = 1000;

                for (let i = 0; i < maxRetries; i++) {
                    await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, i)));

                    try {
                        // 这里应该重新执行失败的网络操作
                        // 具体实现取决于上下文
                        return true;
                    } catch (retryError) {
                        if (i === maxRetries - 1) {
                            return false;
                        }
                    }
                }

                return false;
            }
        });

        // 配置重置策略
        this.registerStrategy({
            name: 'ConfigurationReset',
            priority: 5,
            canRecover: (error) => error instanceof SidecarError && error.type === ErrorType.CONFIGURATION,
            recover: async (error, context) => {
                try {
                    // 重置为默认配置
                    console.warn('Resetting to default configuration due to error:', error.message);
                    return true;
                } catch (resetError) {
                    return false;
                }
            }
        });

        // 兼容性降级策略
        this.registerStrategy({
            name: 'CompatibilityFallback',
            priority: 8,
            canRecover: (error) => error instanceof SidecarError && error.type === ErrorType.COMPATIBILITY,
            recover: async (error, context) => {
                try {
                    console.warn('Falling back to compatibility mode due to error:', error.message);
                    // 启用兼容模式
                    return true;
                } catch (fallbackError) {
                    return false;
                }
            }
        });
    }

    /**
     * 设置全局错误处理
     */
    private setupGlobalErrorHandling(): void {
        // 处理未捕获的错误
        if (typeof window !== 'undefined') {
            window.addEventListener('error', (event) => {
                this.handleError(
                    new SidecarError(
                        event.error?.message || 'Unknown error',
                        ErrorType.UNKNOWN,
                        ErrorSeverity.HIGH,
                        {
                            component: 'Global',
                            method: 'window.onerror',
                            data: {
                                filename: event.filename,
                                lineno: event.lineno,
                                colno: event.colno
                            }
                        }
                    )
                );
            });

            // 处理未捕获的 Promise 拒绝
            window.addEventListener('unhandledrejection', (event) => {
                this.handleError(
                    new SidecarError(
                        event.reason?.message || 'Unhandled promise rejection',
                        ErrorType.UNKNOWN,
                        ErrorSeverity.HIGH,
                        {
                            component: 'Global',
                            method: 'unhandledrejection',
                            data: { reason: event.reason }
                        }
                    )
                );
            });
        }
    }
}

/**
 * 全局错误处理器实例
 */
export const errorHandler = new ErrorHandler();

/**
 * 错误处理装饰器
 */
export function handleErrors(
    errorType: ErrorType = ErrorType.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM
) {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
        const method = descriptor.value;

        descriptor.value = async function (...args: any[]) {
            try {
                const result = method.apply(this, args);

                // 处理异步方法
                if (result && typeof result.then === 'function') {
                    return result.catch((error: Error) => {
                        const sidecarError = new SidecarError(
                            error.message,
                            errorType,
                            severity,
                            {
                                component: target.constructor.name,
                                method: propertyName,
                                data: { args }
                            },
                            true
                        );

                        errorHandler.handleError(sidecarError);
                        throw sidecarError;
                    });
                }

                return result;
            } catch (error: any) {
                const sidecarError = new SidecarError(
                    error?.message || 'Unknown error',
                    errorType,
                    severity,
                    {
                        component: target.constructor.name,
                        method: propertyName,
                        data: { args }
                    },
                    true
                );

                errorHandler.handleError(sidecarError);
                throw sidecarError;
            }
        };

        return descriptor;
    };
}

export default ErrorHandler;
