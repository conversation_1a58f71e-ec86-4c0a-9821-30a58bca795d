/**
 * @fileoverview 事件隔离器
 * @description 提供DOM事件和自定义事件隔离功能
 * <AUTHOR> <<EMAIL>>
 */

import { logger } from '@micro-core/shared';
import type { EventIsolationConfig } from '../types';
import { SidecarError } from '../utils/error-handler';

/**
 * 事件隔离器
 */
export class EventIsolator {
    private config: Required<EventIsolationConfig>;
    private isolatedEvents = new Map<string, Set<EventListener>>();
    private originalAddEventListener?: typeof EventTarget.prototype.addEventListener;
    private originalRemoveEventListener?: typeof EventTarget.prototype.removeEventListener;
    private originalDispatchEvent?: typeof EventTarget.prototype.dispatchEvent;
    private isInitialized = false;

    constructor(config: EventIsolationConfig) {
        this.config = {
            dom: true,
            custom: true,
            namespace: 'micro-app',
            ...config
        };
    }

    /**
     * 初始化隔离器
     */
    /**
     * 初始化事件隔离器
     * Initialize the event isolator
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }
        logger.debug('事件隔离器初始化 (Initializing EventIsolator)');
        try {
            // 保存原始方法
            this.originalAddEventListener = EventTarget.prototype.addEventListener;
            this.originalRemoveEventListener = EventTarget.prototype.removeEventListener;
            this.originalDispatchEvent = EventTarget.prototype.dispatchEvent;

            // 启用 DOM 事件隔离
            if (this.config.dom) {
                this.setupDOMEventIsolation();
            }

            // 启用自定义事件隔离
            if (this.config.custom) {
                this.setupCustomEventIsolation();
            }

            this.isInitialized = true;
        } catch (error) {
            logger.error(new SidecarError({
                code: 'EVENT_ISOLATOR_INIT_ERROR',
                message: '事件隔离器初始化失败 (Failed to initialize EventIsolator)',
                cause: error,
                context: { config: this.config }
            }));
            throw error;
        }
    }

    /**
     * 销毁隔离器
     */
    /**
     * 销毁事件隔离器，恢复原始事件方法并清理所有隔离事件
     * Destroy the event isolator, restore original event methods, and cleanup all isolated events
     */
    async destroy(): Promise<void> {
        if (!this.isInitialized) {
            return;
        }
        logger.debug('销毁事件隔离器 (Destroying EventIsolator)');
        try {
            // 恢复原始方法
            if (this.originalAddEventListener) {
                EventTarget.prototype.addEventListener = this.originalAddEventListener;
            }
            if (this.originalRemoveEventListener) {
                EventTarget.prototype.removeEventListener = this.originalRemoveEventListener;
            }
            if (this.originalDispatchEvent) {
                EventTarget.prototype.dispatchEvent = this.originalDispatchEvent;
            }
        } catch (error) {
            logger.error(new SidecarError({
                code: 'EVENT_ISOLATOR_RESTORE_ERROR',
                message: '恢复事件原型方法失败 (Failed to restore event prototypes)',
                cause: error,
                context: {
                    add: !!this.originalAddEventListener,
                    remove: !!this.originalRemoveEventListener,
                    dispatch: !!this.originalDispatchEvent
                }
            }));
        } finally {
            // 清理隔离事件
            this.isolatedEvents.clear();
            this.isInitialized = false;
        }
    }

    /**
     * 添加隔离事件监听器
     */
    /**
     * 添加隔离事件监听器
     * Add an isolated event listener
     */
    addEventListener(
        target: EventTarget,
        type: string,
        listener: EventListener,
        options?: boolean | AddEventListenerOptions
    ): void {
        const namespacedType = this.getNamespacedEventType(type);
        const eventKey = this.getEventKey(target, namespacedType);
        try {
            // 记录隔离事件
            if (!this.isolatedEvents.has(eventKey)) {
                this.isolatedEvents.set(eventKey, new Set());
            }
            this.isolatedEvents.get(eventKey)!.add(listener);
            // 添加事件监听器
            if (this.originalAddEventListener) {
                this.originalAddEventListener.call(target, namespacedType, listener, options);
            } else {
                throw new SidecarError({
                    code: 'EVENT_ISOLATOR_NO_ORIGINAL_ADD',
                    message: '原始 addEventListener 不存在 (Original addEventListener missing)',
                    context: { type, target }
                });
            }
            logger.debug(`添加隔离事件监听器: ${type} -> ${namespacedType}`);
        } catch (error) {
            logger.error(new SidecarError({
                code: 'EVENT_ISOLATOR_ADD_EVENT_ERROR',
                message: `添加事件监听器失败 (Failed to add event listener): ${type}`,
                cause: error,
                context: { type, target, listener }
            }));
            throw error;
        }
    }

    /**
     * 移除隔离事件监听器
     */
    /**
     * 移除隔离事件监听器
     * Remove an isolated event listener
     */
    removeEventListener(
        target: EventTarget,
        type: string,
        listener: EventListener,
        options?: boolean | EventListenerOptions
    ): void {
        const namespacedType = this.getNamespacedEventType(type);
        const eventKey = this.getEventKey(target, namespacedType);
        try {
            // 移除隔离事件记录
            const listeners = this.isolatedEvents.get(eventKey);
            if (listeners) {
                listeners.delete(listener);
                if (listeners.size === 0) {
                    this.isolatedEvents.delete(eventKey);
                }
            }
            // 移除事件监听器
            if (this.originalRemoveEventListener) {
                this.originalRemoveEventListener.call(target, namespacedType, listener, options);
            } else {
                throw new SidecarError({
                    code: 'EVENT_ISOLATOR_NO_ORIGINAL_REMOVE',
                    message: '原始 removeEventListener 不存在 (Original removeEventListener missing)',
                    context: { type, target }
                });
            }
            logger.debug(`移除隔离事件监听器: ${type} -> ${namespacedType}`);
        } catch (error) {
            logger.error(new SidecarError({
                code: 'EVENT_ISOLATOR_REMOVE_EVENT_ERROR',
                message: `移除事件监听器失败 (Failed to remove event listener): ${type}`,
                cause: error,
                context: { type, target, listener }
            }));
            throw error;
        }
    }

    /**
     * 分发隔离事件
     */
    /**
     * 分发隔离事件
     * Dispatch an isolated event
     */
    dispatchEvent(target: EventTarget, event: Event): boolean {
        try {
            // 如果是自定义事件，添加命名空间
            if (this.config.custom && event instanceof CustomEvent) {
                const namespacedEvent = this.createNamespacedEvent(event);
                if (this.originalDispatchEvent) {
                    return this.originalDispatchEvent.call(target, namespacedEvent);
                } else {
                    throw new SidecarError({
                        code: 'EVENT_ISOLATOR_NO_ORIGINAL_DISPATCH',
                        message: '原始 dispatchEvent 不存在 (Original dispatchEvent missing)',
                        context: { target, event }
                    });
                }
            }
            // 普通事件直接分发
            if (this.originalDispatchEvent) {
                return this.originalDispatchEvent.call(target, event);
            } else {
                throw new SidecarError({
                    code: 'EVENT_ISOLATOR_NO_ORIGINAL_DISPATCH',
                    message: '原始 dispatchEvent 不存在 (Original dispatchEvent missing)',
                    context: { target, event }
                });
            }
        } catch (error) {
            logger.error(new SidecarError({
                code: 'EVENT_ISOLATOR_DISPATCH_EVENT_ERROR',
                message: '分发事件失败 (Failed to dispatch event)',
                cause: error,
                context: { target, event }
            }));
            return false;
        }
    }

    /**
     * 清理所有隔离事件
     */
    /**
     * 清理所有隔离事件
     * Clear all isolated events
     */
    clearAllEvents(): void {
        try {
            this.isolatedEvents.clear();
            logger.debug('清理所有隔离事件 (Cleared all isolated events)');
        } catch (error) {
            logger.error(new SidecarError({
                code: 'EVENT_ISOLATOR_CLEAR_EVENTS_ERROR',
                message: '清理隔离事件失败 (Failed to clear isolated events)',
                cause: error
            }));
        }
    }

    /**
     * 设置DOM事件隔离
     */
    /**
     * 设置 DOM 事件隔离，重写 add/removeEventListener 原型方法
     * Setup DOM event isolation by overriding add/removeEventListener prototypes
     */
    private setupDOMEventIsolation(): void {
        const self = this;
        try {
            // 重写 addEventListener
            EventTarget.prototype.addEventListener = function (
                type: string,
                listener: EventListener | EventListenerObject | null,
                options?: boolean | AddEventListenerOptions
            ) {
                try {
                    if (listener && typeof listener === 'function') {
                        self.addEventListener(this, type, listener, options);
                    } else if (listener && typeof listener === 'object' && listener.handleEvent) {
                        self.addEventListener(this, type, listener.handleEvent.bind(listener), options);
                    }
                } catch (error) {
                    logger.error(new SidecarError({
                        code: 'EVENT_ISOLATOR_ADD_LISTENER_ERROR',
                        message: `添加隔离事件监听器失败 (Failed to add isolated event listener): ${type}`,
                        cause: error,
                        context: { type, listener }
                    }));
                }
            };
            // 重写 removeEventListener
            EventTarget.prototype.removeEventListener = function (
                type: string,
                listener: EventListener | EventListenerObject | null,
                options?: boolean | EventListenerOptions
            ) {
                try {
                    if (listener && typeof listener === 'function') {
                        self.removeEventListener(this, type, listener, options);
                    } else if (listener && typeof listener === 'object' && listener.handleEvent) {
                        self.removeEventListener(this, type, listener.handleEvent.bind(listener), options);
                    }
                } catch (error) {
                    logger.error(new SidecarError({
                        code: 'EVENT_ISOLATOR_REMOVE_LISTENER_ERROR',
                        message: `移除隔离事件监听器失败 (Failed to remove isolated event listener): ${type}`,
                        cause: error,
                        context: { type, listener }
                    }));
                }
            };
            logger.debug('DOM事件隔离已设置 (DOM event isolation set)');
        } catch (error) {
            logger.error(new SidecarError({
                code: 'EVENT_ISOLATOR_DOM_PATCH_ERROR',
                message: '设置 DOM 事件隔离失败 (Failed to patch DOM event prototypes)',
                cause: error
            }));
        }
    }

    /**
     * 设置自定义事件隔离
     */
    /**
     * 设置自定义事件隔离，重写 dispatchEvent 原型方法
     * Setup custom event isolation by overriding dispatchEvent prototype
     */
    private setupCustomEventIsolation(): void {
        const self = this;
        try {
            // 重写 dispatchEvent
            EventTarget.prototype.dispatchEvent = function (event: Event) {
                try {
                    return self.dispatchEvent(this, event);
                } catch (error) {
                    logger.error(new SidecarError({
                        code: 'EVENT_ISOLATOR_DISPATCH_EVENT_ERROR',
                        message: '分发隔离事件失败 (Failed to dispatch isolated event)',
                        cause: error,
                        context: { event }
                    }));
                    return false;
                }
            };
            logger.debug('自定义事件隔离已设置 (Custom event isolation set)');
        } catch (error) {
            logger.error(new SidecarError({
                code: 'EVENT_ISOLATOR_CUSTOM_PATCH_ERROR',
                message: '设置自定义事件隔离失败 (Failed to patch custom event prototypes)',
                cause: error
            }));
        }
    }

    /**
     * 获取命名空间事件类型
     */
    private getNamespacedEventType(type: string): string {
        if (this.config.namespace && !type.startsWith(`${this.config.namespace}:`)) {
            return `${this.config.namespace}:${type}`;
        }
        return type;
    }

    /**
     * 创建命名空间事件
     */
    private createNamespacedEvent(originalEvent: CustomEvent): CustomEvent {
        const namespacedType = this.getNamespacedEventType(originalEvent.type);

        return new CustomEvent(namespacedType, {
            detail: originalEvent.detail,
            bubbles: originalEvent.bubbles,
            cancelable: originalEvent.cancelable,
            composed: originalEvent.composed
        });
    }

    /**
     * 获取事件键
     */
    private getEventKey(target: EventTarget, type: string): string {
        // 使用目标对象的构造函数名和事件类型作为键
        const targetName = target.constructor.name || 'Unknown';
        return `${targetName}:${type}`;
    }

    /**
     * 获取统计信息
     */
    /**
     * 获取统计信息
     * Get stats for the event isolator
     */
    getStats(): {
        totalEvents: number;
        eventTypes: string[];
        domIsolation: boolean;
        customIsolation: boolean;
    } {
        try {
            const eventTypes = Array.from(this.isolatedEvents.keys());
            return {
                totalEvents: this.isolatedEvents.size,
                eventTypes,
                domIsolation: this.config.dom,
                customIsolation: this.config.custom
            };
        } catch (error) {
            logger.error(new SidecarError({
                code: 'EVENT_ISOLATOR_GET_STATS_ERROR',
                message: '获取事件隔离器统计信息失败 (Failed to get event isolator stats)',
                cause: error
            }));
            return {
                totalEvents: 0,
                eventTypes: [],
                domIsolation: false,
                customIsolation: false
            };
        }
    }
}

export default EventIsolator;