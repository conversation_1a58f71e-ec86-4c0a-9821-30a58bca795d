/**
 * @fileoverview 脚本隔离器
 * @description 提供JavaScript代码执行隔离功能
 * <AUTHOR> <<EMAIL>>
 */

import { logger } from '@micro-core/shared';
import { SidecarError } from '../utils/error-handler';
import type { ScriptIsolationConfig } from '../types';

/**
 * 脚本隔离器
 */
export class ScriptIsolator {
    private config: Required<ScriptIsolationConfig>;
    /**
     * 沙箱窗口对象（根据隔离模式为 Proxy/iframe/window 快照）
     * Sandbox window object (Proxy/iframe/snapshot)
     */
    private sandboxWindow?: Window | Record<string, unknown>;
    /**
     * 原始窗口快照（仅 snapshot 模式）
     * Original window snapshot (snapshot mode only)
     */
    private originalWindow?: Record<string, unknown>;
    private isInitialized = false;

    constructor(config: ScriptIsolationConfig) {
        this.config = {
            mode: 'proxy',
            whitelist: ['console', 'setTimeout', 'setInterval', 'clearTimeout', 'clearInterval'],
            blacklist: ['eval', 'Function', 'XMLHttpRequest', 'fetch'],
            strict: true,
            ...config
        };
    }

    /**
     * 初始化隔离器
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        logger.debug(`脚本隔离器初始化 (模式: ${this.config.mode})`);

        switch (this.config.mode) {
            case 'proxy':
                await this.initializeProxyMode();
                break;
            case 'snapshot':
                await this.initializeSnapshotMode();
                break;
            case 'iframe':
                await this.initializeIframeMode();
                break;
        }

        this.isInitialized = true;
    }

    /**
     * 销毁隔离器
     */
    async destroy(): Promise<void> {
        if (!this.isInitialized) {
            return;
        }

        logger.debug('销毁脚本隔离器');

        // 恢复原始环境
        if (this.originalWindow) {
            Object.keys(this.originalWindow).forEach(key => {
                try {
                    (window as any)[key] = this.originalWindow[key];
                } catch (error) {
                    // 忽略无法恢复的属性
                }
            });
        }

        this.sandboxWindow = undefined;
        this.originalWindow = undefined;
        this.isInitialized = false;
    }

    /**
     * 执行代码
     */
    async execute(code: string): Promise<any> {
        if (!this.isInitialized) {
            throw new SidecarError({
                code: 'ISOLATOR_NOT_INITIALIZED',
                message: '脚本隔离器未初始化 (ScriptIsolator not initialized)',
                context: { mode: this.config.mode }
            });
        }

        try {
            logger.debug('在隔离环境中执行脚本', { codeLength: code.length });

            switch (this.config.mode) {
                case 'proxy':
                    return this.executeInProxy(code);
                case 'snapshot':
                    return this.executeInSnapshot(code);
                case 'iframe':
                    return this.executeInIframe(code);
                default:
                    throw new SidecarError({
                        code: 'UNKNOWN_ISOLATION_MODE',
                        message: `未知的隔离模式: ${this.config.mode}`,
                        context: { mode: this.config.mode }
                    });
                default:
                    throw createMicroCoreError(
                        'UNKNOWN_ISOLATION_MODE',
                        `未知的隔离模式: ${this.config.mode}`
                    );
            }
        } catch (error) {
            logger.error('脚本执行失败 (Script execution failed):', error);
            throw new SidecarError({
                code: 'SCRIPT_EXECUTION_FAILED',
                message: '脚本隔离器执行代码失败 (ScriptIsolator execution failed)',
                cause: error,
                context: { mode: this.config.mode, codeLength: code.length }
            });
        }
    }

    /**
     * 初始化代理模式
     */
    /**
     * 初始化代理模式 (Proxy Mode Initialization)
     * @description 使用 Proxy 拦截访问，实现属性白名单/黑名单控制
     */
    private async initializeProxyMode(): Promise<void> {
        this.sandboxWindow = new Proxy(window, {
            get: (target: Window, prop: string | symbol) => {
                // 黑名单校验
                if (this.config.blacklist.includes(String(prop))) {
                    throw new SidecarError({
                        code: 'ACCESS_DENIED',
                        message: `访问被禁止的属性 (Access denied property): ${String(prop)}`,
                        context: { prop }
                    });
                }
                // 白名单校验
                if (this.config.whitelist.length > 0 &&
                    !this.config.whitelist.includes(String(prop))) {
                    if (this.config.strict) {
                        throw new SidecarError({
                            code: 'ACCESS_DENIED',
                            message: `访问未授权的属性 (Access to non-whitelisted property): ${String(prop)}`,
                            context: { prop }
                        });
                    }
                    return undefined;
                }
                return (target as any)[prop];
            },
            set: (target: Window, prop: string | symbol, value: unknown) => {
                if (this.config.blacklist.includes(String(prop))) {
                    throw new SidecarError({
                        code: 'ACCESS_DENIED',
                        message: `设置被禁止的属性 (Set denied property): ${String(prop)}`,
                        context: { prop }
                    });
                }
                (this.sandboxWindow as Record<string, unknown>)[prop as string] = value;
                return true;
            },
            has: (target: Window, prop: string | symbol) => {
                return prop in target;
            }
        });
    }

    /**
     * 初始化快照模式
     */
    /**
     * 初始化快照模式 (Snapshot Mode Initialization)
     * @description 保存 window 的所有属性快照，便于执行后恢复
     */
    private async initializeSnapshotMode(): Promise<void> {
        this.originalWindow = {};
        Object.keys(window).forEach(key => {
            try {
                this.originalWindow![key] = (window as Record<string, unknown>)[key];
            } catch {
                // 忽略无法访问的属性 (ignore inaccessible)
            }
        });
        logger.debug('窗口快照已创建 (Window snapshot created)');
    }

    /**
     * 初始化iframe模式
     */
    /**
     * 初始化 iframe 模式 (Iframe Mode Initialization)
     * @description 创建隐藏 iframe 作为沙箱环境
     */
    private async initializeIframeMode(): Promise<void> {
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = 'about:blank';
        document.body.appendChild(iframe);
        await new Promise<void>((resolve) => {
            iframe.onload = () => resolve();
        });
        this.sandboxWindow = iframe.contentWindow ?? undefined;
        logger.debug('Iframe 沙箱已创建 (Iframe sandbox created)');
    }

    /**
     * 在代理环境中执行代码
     */
    /**
     * 在代理环境中执行代码
     * Execute code in proxy sandbox
     */
    private executeInProxy<T = unknown>(code: string): T {
        const executeFunction = new Function('window', 'global', code);
        return executeFunction.call(this.sandboxWindow, this.sandboxWindow, this.sandboxWindow) as T;
    }

    /**
     * 在快照环境中执行代码
     */
    /**
     * 在快照环境中执行代码
     * Execute code in snapshot sandbox
     */
    private executeInSnapshot<T = unknown>(code: string): T {
        try {
            const result = eval(code) as T;
            this.restoreWindowSnapshot();
            return result;
        } catch (error) {
            this.restoreWindowSnapshot();
            throw new SidecarError({
                code: 'SNAPSHOT_EXECUTION_FAILED',
                message: '快照模式执行失败 (Snapshot mode execution failed)',
                cause: error,
                context: { codeLength: code.length }
            });
        }
    }

    /**
     * 在iframe环境中执行代码
     */
    /**
     * 在 iframe 环境中执行代码
     * Execute code in iframe sandbox
     */
    private executeInIframe<T = unknown>(code: string): T {
        if (!this.sandboxWindow || typeof (this.sandboxWindow as Window).eval !== 'function') {
            throw new SidecarError({
                code: 'IFRAME_NOT_READY',
                message: 'Iframe 沙箱未准备就绪 (Iframe sandbox not ready)',
                context: {}
            });
        }
        return (this.sandboxWindow as Window).eval(code) as T;
    }

    /**
     * 恢复窗口快照
     */
    /**
     * 恢复窗口快照 (Restore window snapshot)
     */
    private restoreWindowSnapshot(): void {
        if (!this.originalWindow) return;
        // 移除新增属性
        Object.keys(window).forEach(key => {
            if (!(key in this.originalWindow!)) {
                try {
                    delete (window as Record<string, unknown>)[key];
                } catch {
                    // 忽略无法删除的属性
                }
            }
        });
        // 恢复被修改的属性
        Object.keys(this.originalWindow).forEach(key => {
            try {
                if ((window as Record<string, unknown>)[key] !== this.originalWindow![key]) {
                    (window as Record<string, unknown>)[key] = this.originalWindow![key];
                }
            } catch {
                // 忽略无法恢复的属性
            }
        });
    }

    /**
     * 获取沙箱窗口
     */
    /**
     * 获取沙箱窗口对象
     * Get sandbox window object
     */
    getSandboxWindow(): Window | Record<string, unknown> | undefined {
        return this.sandboxWindow;
    }
}

export default ScriptIsolator;