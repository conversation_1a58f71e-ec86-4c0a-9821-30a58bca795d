/**
 * @fileoverview 隔离容器
 * @description 提供应用运行时隔离环境
 * <AUTHOR> <<EMAIL>>
 */

import { MicroCoreError, logger } from '@micro-core/shared';
import { EventEmitter } from 'events';
import type { IIsolationContainer, IsolationConfig } from '../types';
import { EventIsolator } from './event-isolator';
import { GlobalIsolator } from './global-isolator';
import { ScriptIsolator } from './script-isolator';
import { StyleIsolator } from './style-isolator';

/**
 * 隔离容器
 */
export class IsolationContainer extends EventEmitter implements IIsolationContainer {
    private config: Required<IsolationConfig>;
    private styleIsolator: StyleIsolator | undefined;
    private scriptIsolator: ScriptIsolator | undefined;
    private globalIsolator: GlobalIsolator | undefined;
    private eventIsolator: EventIsolator | undefined;
    private isInitialized = false;
    private isDestroyed = false;

    constructor(config: IsolationConfig = {}) {
        super();

        this.config = {
            style: {
                mode: 'scoped',
                prefix: 'micro-app',
                excludeSelectors: [],
                isolateGlobal: true
            },
            script: {
                mode: 'proxy',
                whitelist: ['console', 'setTimeout', 'setInterval'],
                blacklist: ['eval', 'Function'],
                strict: true
            },
            global: {
                shared: ['console', 'document', 'window'],
                private: [],
                mapping: {}
            },
            event: {
                dom: true,
                custom: true,
                namespace: 'micro-app'
            },
            ...config
        } as Required<IsolationConfig>;
    }

    /**
     * 初始化容器
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        if (this.isDestroyed) {
            throw createMicroCoreError(
                'CONTAINER_DESTROYED',
                '隔离容器已被销毁，无法重新初始化'
            );
        }

        logger.info('正在初始化隔离容器...');

        try {
            // 初始化样式隔离器
            if (this.config.style) {
                this.styleIsolator = new StyleIsolator(this.config.style);
                await this.styleIsolator.initialize();
            }

            // 初始化脚本隔离器
            if (this.config.script) {
                this.scriptIsolator = new ScriptIsolator(this.config.script);
                await this.scriptIsolator.initialize();
            }

            // 初始化全局变量隔离器
            if (this.config.global) {
                this.globalIsolator = new GlobalIsolator(this.config.global);
                await this.globalIsolator.initialize();
            }

            // 初始化事件隔离器
            if (this.config.event) {
                this.eventIsolator = new EventIsolator(this.config.event);
                await this.eventIsolator.initialize();
            }

            this.isInitialized = true;
            logger.info('隔离容器初始化成功');
            this.emit('initialized');

        } catch (error) {
            logger.error('隔离容器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 销毁容器
     */
    async destroy(): Promise<void> {
        if (this.isDestroyed) {
            return;
        }

        logger.info('正在销毁隔离容器...');

        try {
            // 销毁各个隔离器
            if (this.eventIsolator) {
                await this.eventIsolator.destroy();
                this.eventIsolator = undefined;
            }

            if (this.globalIsolator) {
                await this.globalIsolator.destroy();
                this.globalIsolator = undefined;
            }

            if (this.scriptIsolator) {
                await this.scriptIsolator.destroy();
                this.scriptIsolator = undefined;
            }

            if (this.styleIsolator) {
                await this.styleIsolator.destroy();
                this.styleIsolator = undefined;
            }

            this.isDestroyed = true;
            this.isInitialized = false;
            this.removeAllListeners();

            logger.info('隔离容器已销毁');

        } catch (error) {
            logger.error('隔离容器销毁失败:', error);
            throw error;
        }
    }

    /**
     * 执行代码
     */
    /**
     * 执行代码
     * @param code 要执行的代码字符串
     * @returns 执行结果（类型安全：string | number | boolean | object | void）
     */
    async execute<T = unknown>(code: string): Promise<T> {
        if (!this.isInitialized) {
            throw createMicroCoreError(
                'CONTAINER_NOT_INITIALIZED',
                '隔离容器未初始化'
            );
        }

        if (this.isDestroyed) {
            throw createMicroCoreError(
                'CONTAINER_DESTROYED',
                '隔离容器已被销毁'
            );
        }

        try {
            logger.debug('在隔离环境中执行代码', { codeLength: code.length });

            // 使用脚本隔离器执行代码
            if (this.scriptIsolator) {
                return await this.scriptIsolator.execute(code);
            }

            // 如果没有脚本隔离器，直接执行
            return eval(code);

        } catch (error) {
            logger.error('代码执行失败:', error);
            this.emit('execution-error', { error, code });
            throw error;
        }
    }

    /**
     * 设置全局变量
     */
    /**
     * 设置全局变量（类型安全）
     * @param name 变量名
     * @param value 变量值
     */
    setGlobal<T = unknown>(name: string, value: T): void {
        if (this.globalIsolator) {
            this.globalIsolator.setGlobal(name, value);
        } else {
            (window as any)[name] = value;
        }
    }

    /**
     * 获取全局变量
     */
    /**
     * 获取全局变量（类型安全）
     * @param name 变量名
     * @returns 变量值
     */
    getGlobal<T = unknown>(name: string): T | undefined {
        if (this.globalIsolator) {
            return this.globalIsolator.getGlobal(name);
        } else {
            return (window as any)[name];
        }
    }

    /**
     * 清理全局变量
     */
    clearGlobals(): void {
        if (this.globalIsolator) {
            this.globalIsolator.clearGlobals();
        }
    }

    /**
     * 应用样式隔离
     */
    applyStyleIsolation(element: HTMLElement): void {
        if (this.styleIsolator) {
            this.styleIsolator.isolateElement(element);
        }
    }

    /**
     * 移除样式隔离
     */
    removeStyleIsolation(element: HTMLElement): void {
        if (this.styleIsolator) {
            this.styleIsolator.restoreElement(element);
        }
    }

    /**
     * 获取隔离统计信息
     */
    getStats(): {
        initialized: boolean;
        destroyed: boolean;
        isolators: {
            style: boolean;
            script: boolean;
            global: boolean;
            event: boolean;
        };
    } {
        return {
            initialized: this.isInitialized,
            destroyed: this.isDestroyed,
            isolators: {
                style: !!this.styleIsolator,
                script: !!this.scriptIsolator,
                global: !!this.globalIsolator,
                event: !!this.eventIsolator
            }
        };
    }
}

export default IsolationContainer;