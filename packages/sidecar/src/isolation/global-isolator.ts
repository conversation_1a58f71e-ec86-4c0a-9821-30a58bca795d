/**
 * @fileoverview 全局变量隔离器
 * @description 提供全局变量隔离和管理功能
 * <AUTHOR> <<EMAIL>>
 */

import { logger } from '@micro-core/shared';
import type { GlobalIsolationConfig } from '../types';
import { SidecarError } from '../utils/error-handler';

/**
 * 全局变量隔离器
 */
export class GlobalIsolator {
    private config: Required<GlobalIsolationConfig>;
    /**
     * 隔离的全局变量集合
     * Isolated globals map
     */
    private isolatedGlobals: Map<string, unknown> = new Map();
    /**
     * 原始全局变量快照
     * Snapshot of original globals
     */
    private originalGlobals: Map<string, unknown> = new Map();
    private isInitialized = false;

    constructor(config: GlobalIsolationConfig) {
        this.config = {
            shared: ['console', 'document', 'window', 'navigator'],
            private: [],
            mapping: {},
            ...config
        };
    }

    /**
     * 初始化隔离器
     */
    /**
     * 初始化隔离器
     * Initialize the global isolator
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }
        logger.debug('全局变量隔离器初始化 (Initializing GlobalIsolator)');
        try {
            this.saveOriginalGlobals();
            this.setupVariableMapping();
            this.isInitialized = true;
        } catch (error) {
            throw new SidecarError({
                code: 'GLOBAL_ISOLATOR_INIT_FAILED',
                message: '全局变量隔离器初始化失败 (Failed to initialize GlobalIsolator)',
                cause: error,
                context: { config: this.config }
            });
        }
    }

    /**
     * 销毁隔离器
     */
    /**
     * 销毁隔离器，恢复所有全局变量
     * Destroy the isolator and restore all globals
     */
    async destroy(): Promise<void> {
        if (!this.isInitialized) {
            return;
        }
        logger.debug('销毁全局变量隔离器 (Destroying GlobalIsolator)');
        try {
            this.restoreOriginalGlobals();
        } catch (error) {
            throw new SidecarError({
                code: 'GLOBAL_ISOLATOR_RESTORE_FAILED',
                message: '恢复原始全局变量失败 (Failed to restore original globals)',
                cause: error,
                context: { originalGlobals: Array.from(this.originalGlobals.keys()) }
            });
        } finally {
            this.isolatedGlobals.clear();
            this.originalGlobals.clear();
            this.isInitialized = false;
        }
    }

    /**
     * 设置全局变量
     */
    /**
     * 设置全局变量
     * Set global variable
     */
    setGlobal(name: string, value: unknown): void {
        // 检查是否为共享变量
        if (this.config.shared.includes(name)) {
            (window as any)[name] = value;
            logger.debug(`设置共享全局变量: ${name}`);
            return;
        }

        // 检查是否为私有变量
        if (this.config.private.includes(name)) {
            this.isolatedGlobals.set(name, value);
            logger.debug(`设置私有全局变量: ${name}`);
            return;
        }

        // 检查变量映射
        const mappedName = this.config.mapping[name];
        if (mappedName) {
            this.isolatedGlobals.set(mappedName, value);
            logger.debug(`设置映射全局变量: ${name} -> ${mappedName}`);
            return;
        }

        // 默认设置为隔离变量
        this.isolatedGlobals.set(name, value);
        logger.debug(`设置隔离全局变量: ${name}`);
    }

    /**
     * 获取全局变量
     */
    /**
     * 获取全局变量
     * Get global variable
     */
    getGlobal(name: string): unknown {
        // 检查是否为共享变量
        if (this.config.shared.includes(name)) {
            return (window as any)[name];
        }

        // 检查是否为私有变量
        if (this.config.private.includes(name)) {
            return this.isolatedGlobals.get(name);
        }

        // 检查变量映射
        const mappedName = this.config.mapping[name];
        if (mappedName) {
            return this.isolatedGlobals.get(mappedName);
        }

        // 检查隔离变量
        if (this.isolatedGlobals.has(name)) {
            return this.isolatedGlobals.get(name);
        }

        // 返回原始全局变量
        return (window as any)[name];
    }

    /**
     * 删除全局变量
     */
    /**
     * 删除全局变量
     * Delete global variable
     */
    deleteGlobal(name: string): boolean {
        // 不能删除共享变量
        if (this.config.shared.includes(name)) {
            logger.warn(`无法删除共享全局变量: ${name}`);
            return false;
        }

        // 删除私有变量
        if (this.config.private.includes(name)) {
            return this.isolatedGlobals.delete(name);
        }

        // 删除映射变量
        const mappedName = this.config.mapping[name];
        if (mappedName) {
            return this.isolatedGlobals.delete(mappedName);
        }

        // 删除隔离变量
        if (this.isolatedGlobals.has(name)) {
            return this.isolatedGlobals.delete(name);
        }

        return false;
    }

    /**
     * 清理全局变量
     */
    /**
     * 清理隔离的全局变量
     * Clear all isolated globals
     */
    clearGlobals(): void {
        logger.debug('清理隔离的全局变量');
        this.isolatedGlobals.clear();
    }

    /**
     * 获取所有隔离变量
     */
    /**
     * 获取所有隔离变量
     * Get all isolated globals
     */
    getIsolatedGlobals(): Map<string, unknown> {
        return new Map(this.isolatedGlobals);
    }

    /**
     * 检查变量是否存在
     */
    /**
     * 检查变量是否存在
     * Check if global exists
     */
    hasGlobal(name: string): boolean {
        // 检查共享变量
        if (this.config.shared.includes(name)) {
            return name in window;
        }

        // 检查私有变量
        if (this.config.private.includes(name)) {
            return this.isolatedGlobals.has(name);
        }

        // 检查映射变量
        const mappedName = this.config.mapping[name];
        if (mappedName) {
            return this.isolatedGlobals.has(mappedName);
        }

        // 检查隔离变量
        return this.isolatedGlobals.has(name) || (name in window);
    }

    /**
     * 保存原始全局变量
     */
    /**
     * 保存原始全局变量快照
     * Save snapshot of original globals
     */
    private saveOriginalGlobals(): void {
        // 保存需要隔离的全局变量
        Object.keys(window).forEach((key: string) => {
            if (!this.config.shared.includes(key)) {
                try {
                    // 尽量保证类型安全
                    this.originalGlobals.set(key, (window as unknown as Record<string, unknown>)[key]);
                } catch (error) {
                    // 使用 SidecarError 记录无法访问的属性
                    logger.warn(new SidecarError({
                        code: 'GLOBAL_ISOLATOR_SNAPSHOT_ERROR',
                        message: `无法保存全局变量: ${key} (Failed to snapshot global: ${key})`,
                        cause: error,
                        context: { key }
                    }));
                }
            }
        });

        logger.debug(`已保存 ${this.originalGlobals.size} 个原始全局变量`);
    }

    /**
     * 恢复原始全局变量
     */
    /**
     * 恢复原始全局变量快照
     * Restore snapshot of original globals
     */
    private restoreOriginalGlobals(): void {
        this.originalGlobals.forEach((value, key) => {
            try {
                (window as unknown as Record<string, unknown>)[key] = value;
            } catch (error) {
                logger.warn(new SidecarError({
                    code: 'GLOBAL_ISOLATOR_RESTORE_ERROR',
                    message: `无法恢复全局变量: ${key} (Failed to restore global: ${key})`,
                    cause: error,
                    context: { key, value }
                }));
            }
        });
        logger.debug(`已恢复 ${this.originalGlobals.size} 个原始全局变量`);
    }

    /**
     * 设置变量映射
     */
    /**
     * 设置变量映射
     * Setup variable mapping
     */
    private setupVariableMapping(): void {
        Object.entries(this.config.mapping).forEach(([from, to]) => {
            try {
                const value = (window as unknown as Record<string, unknown>)[from];
                if (value !== undefined) {
                    this.isolatedGlobals.set(to, value);
                    logger.debug(`设置变量映射: ${from} -> ${to}`);
                }
            } catch (error) {
                logger.warn(new SidecarError({
                    code: 'GLOBAL_ISOLATOR_MAPPING_ERROR',
                    message: `变量映射失败: ${from} -> ${to} (Failed to map variable: ${from} -> ${to})`,
                    cause: error,
                    context: { from, to }
                }));
            }
        });
    }

    /**
     * 获取统计信息
     */
    /**
     * 获取隔离器统计信息
     * Get stats of the isolator
     */
    getStats(): {
        isolatedCount: number;
        sharedCount: number;
        privateCount: number;
        mappingCount: number;
    } {
        return {
            isolatedCount: this.isolatedGlobals.size,
            sharedCount: this.config.shared.length,
            privateCount: this.config.private.length,
            mappingCount: Object.keys(this.config.mapping).length
        };
    }
}

export default GlobalIsolator;