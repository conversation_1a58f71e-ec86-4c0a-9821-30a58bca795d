/**
 * @fileoverview Sidecar 核心功能测试
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { Sidecar } from '../../src/sidecar';
import type { SidecarApp, SidecarConfig } from '../../src/types';

describe('Sidecar 核心功能测试', () => {
    let sidecar: Sidecar;
    let mockContainer: HTMLElement;

    beforeEach(() => {
        // 创建模拟容器
        mockContainer = document.createElement('div');
        mockContainer.id = 'sidecar-container';
        document.body.appendChild(mockContainer);

        // 初始化 sidecar
        const config: SidecarConfig = {
            container: '#sidecar-container',
            isolation: {
                css: true,
                js: true,
                events: true
            },
            bridge: {
                type: 'postMessage',
                timeout: 5000
            }
        };

        sidecar = new Sidecar(config);
    });

    afterEach(() => {
        // 清理
        if (mockContainer && mockContainer.parentNode) {
            mockContainer.parentNode.removeChild(mockContainer);
        }
        if (sidecar) {
            sidecar.destroy();
        }
        vi.clearAllMocks();
    });

    describe('初始化和配置', () => {
        it('应该正确初始化 Sidecar 实例', () => {
            expect(sidecar).toBeInstanceOf(Sidecar);
            expect(sidecar.isInitialized()).toBe(true);
        });

        it('应该正确设置默认配置', () => {
            const defaultSidecar = new Sidecar();
            expect(defaultSidecar.getConfig()).toMatchObject({
                container: 'body',
                isolation: {
                    css: true,
                    js: true,
                    events: false
                }
            });
            defaultSidecar.destroy();
        });

        it('应该合并用户配置和默认配置', () => {
            const customConfig: Partial<SidecarConfig> = {
                isolation: {
                    css: false
                }
            };

            const customSidecar = new Sidecar(customConfig);
            const config = customSidecar.getConfig();

            expect(config.isolation.css).toBe(false);
            expect(config.isolation.js).toBe(true); // 默认值
            customSidecar.destroy();
        });

        it('应该验证配置的有效性', () => {
            expect(() => {
                new Sidecar({
                    container: '', // 无效容器
                    bridge: {
                        type: 'invalid' as any // 无效桥接类型
                    }
                });
            }).toThrow();
        });
    });

    describe('应用注册和管理', () => {
        it('应该成功注册应用', async () => {
            const app: SidecarApp = {
                name: 'test-app',
                entry: 'https://example.com/app.js',
                container: '#app-container',
                props: { theme: 'dark' }
            };

            await sidecar.registerApp(app);

            const registeredApps = sidecar.getRegisteredApps();
            expect(registeredApps).toHaveLength(1);
            expect(registeredApps[0].name).toBe('test-app');
        });

        it('应该防止重复注册同名应用', async () => {
            const app: SidecarApp = {
                name: 'duplicate-app',
                entry: 'https://example.com/app.js',
                container: '#app-container'
            };

            await sidecar.registerApp(app);

            await expect(sidecar.registerApp(app)).rejects.toThrow('应用已存在');
        });

        it('应该成功注销应用', async () => {
            const app: SidecarApp = {
                name: 'test-app',
                entry: 'https://example.com/app.js',
                container: '#app-container'
            };

            await sidecar.registerApp(app);
            await sidecar.unregisterApp('test-app');

            const registeredApps = sidecar.getRegisteredApps();
            expect(registeredApps).toHaveLength(0);
        });

        it('应该处理注销不存在的应用', async () => {
            await expect(sidecar.unregisterApp('non-existent')).rejects.toThrow('应用不存在');
        });
    });

    describe('应用生命周期管理', () => {
        let testApp: SidecarApp;

        beforeEach(() => {
            testApp = {
                name: 'lifecycle-app',
                entry: 'https://example.com/app.js',
                container: '#app-container',
                lifecycle: {
                    beforeLoad: vi.fn(),
                    afterLoad: vi.fn(),
                    beforeMount: vi.fn(),
                    afterMount: vi.fn(),
                    beforeUnmount: vi.fn(),
                    afterUnmount: vi.fn()
                }
            };
        });

        it('应该按正确顺序执行加载生命周期', async () => {
            await sidecar.registerApp(testApp);
            await sidecar.loadApp('lifecycle-app');

            expect(testApp.lifecycle?.beforeLoad).toHaveBeenCalledBefore(testApp.lifecycle?.afterLoad as any);
        });

        it('应该按正确顺序执行挂载生命周期', async () => {
            await sidecar.registerApp(testApp);
            await sidecar.loadApp('lifecycle-app');
            await sidecar.mountApp('lifecycle-app');

            expect(testApp.lifecycle?.beforeMount).toHaveBeenCalledBefore(testApp.lifecycle?.afterMount as any);
        });

        it('应该按正确顺序执行卸载生命周期', async () => {
            await sidecar.registerApp(testApp);
            await sidecar.loadApp('lifecycle-app');
            await sidecar.mountApp('lifecycle-app');
            await sidecar.unmountApp('lifecycle-app');

            expect(testApp.lifecycle?.beforeUnmount).toHaveBeenCalledBefore(testApp.lifecycle?.afterUnmount as any);
        });

        it('应该处理生命周期钩子中的错误', async () => {
            testApp.lifecycle!.beforeLoad = vi.fn().mockRejectedValue(new Error('加载前错误'));

            await sidecar.registerApp(testApp);

            await expect(sidecar.loadApp('lifecycle-app')).rejects.toThrow('加载前错误');
        });
    });

    describe('隔离功能', () => {
        it('应该启用 CSS 隔离', async () => {
            const isolatedSidecar = new Sidecar({
                isolation: { css: true, js: false, events: false }
            });

            const app: SidecarApp = {
                name: 'css-isolated-app',
                entry: 'https://example.com/app.js',
                container: '#app-container'
            };

            await isolatedSidecar.registerApp(app);
            await isolatedSidecar.loadApp('css-isolated-app');

            // 验证 CSS 隔离是否生效
            const appContainer = document.querySelector('#app-container');
            expect(appContainer?.getAttribute('data-css-isolated')).toBe('true');

            isolatedSidecar.destroy();
        });

        it('应该启用 JavaScript 隔离', async () => {
            const isolatedSidecar = new Sidecar({
                isolation: { css: false, js: true, events: false }
            });

            const app: SidecarApp = {
                name: 'js-isolated-app',
                entry: 'https://example.com/app.js',
                container: '#app-container'
            };

            await isolatedSidecar.registerApp(app);
            await isolatedSidecar.loadApp('js-isolated-app');

            // 验证 JavaScript 隔离是否生效
            const appContainer = document.querySelector('#app-container');
            expect(appContainer?.getAttribute('data-js-isolated')).toBe('true');

            isolatedSidecar.destroy();
        });

        it('应该启用事件隔离', async () => {
            const isolatedSidecar = new Sidecar({
                isolation: { css: false, js: false, events: true }
            });

            const app: SidecarApp = {
                name: 'event-isolated-app',
                entry: 'https://example.com/app.js',
                container: '#app-container'
            };

            await isolatedSidecar.registerApp(app);
            await isolatedSidecar.loadApp('event-isolated-app');

            // 验证事件隔离是否生效
            const appContainer = document.querySelector('#app-container');
            expect(appContainer?.getAttribute('data-event-isolated')).toBe('true');

            isolatedSidecar.destroy();
        });
    });

    describe('通信桥接', () => {
        it('应该建立 PostMessage 桥接', async () => {
            const bridgeSidecar = new Sidecar({
                bridge: { type: 'postMessage', timeout: 3000 }
            });

            const app: SidecarApp = {
                name: 'bridge-app',
                entry: 'https://example.com/app.js',
                container: '#app-container'
            };

            await bridgeSidecar.registerApp(app);
            await bridgeSidecar.loadApp('bridge-app');

            // 验证桥接是否建立
            expect(bridgeSidecar.getBridge('bridge-app')).toBeDefined();
            expect(bridgeSidecar.getBridge('bridge-app')?.getType()).toBe('postMessage');

            bridgeSidecar.destroy();
        });

        it('应该支持自定义事件桥接', async () => {
            const bridgeSidecar = new Sidecar({
                bridge: { type: 'customEvent', timeout: 3000 }
            });

            const app: SidecarApp = {
                name: 'custom-bridge-app',
                entry: 'https://example.com/app.js',
                container: '#app-container'
            };

            await bridgeSidecar.registerApp(app);
            await bridgeSidecar.loadApp('custom-bridge-app');

            const bridge = bridgeSidecar.getBridge('custom-bridge-app');
            expect(bridge?.getType()).toBe('customEvent');

            bridgeSidecar.destroy();
        });

        it('应该处理桥接超时', async () => {
            const timeoutSidecar = new Sidecar({
                bridge: { type: 'postMessage', timeout: 100 }
            });

            const app: SidecarApp = {
                name: 'timeout-app',
                entry: 'https://example.com/slow-app.js', // 模拟慢加载
                container: '#app-container'
            };

            await timeoutSidecar.registerApp(app);

            // 模拟网络延迟
            vi.spyOn(global, 'fetch').mockImplementation(() =>
                new Promise(resolve => setTimeout(resolve, 200))
            );

            await expect(timeoutSidecar.loadApp('timeout-app')).rejects.toThrow('桥接超时');

            timeoutSidecar.destroy();
        });
    });

    describe('错误处理', () => {
        it('应该处理应用加载错误', async () => {
            const app: SidecarApp = {
                name: 'error-app',
                entry: 'https://invalid-url/app.js',
                container: '#app-container'
            };

            await sidecar.registerApp(app);

            await expect(sidecar.loadApp('error-app')).rejects.toThrow();
        });

        it('应该处理应用挂载错误', async () => {
            const app: SidecarApp = {
                name: 'mount-error-app',
                entry: 'https://example.com/app.js',
                container: '#non-existent-container' // 不存在的容器
            };

            await sidecar.registerApp(app);

            await expect(sidecar.mountApp('mount-error-app')).rejects.toThrow('容器不存在');
        });

        it('应该提供详细的错误信息', async () => {
            const app: SidecarApp = {
                name: 'detailed-error-app',
                entry: 'https://example.com/app.js',
                container: '#app-container'
            };

            await sidecar.registerApp(app);

            try {
                await sidecar.loadApp('detailed-error-app');
            } catch (error: any) {
                expect(error.message).toContain('详细错误信息');
                expect(error.appName).toBe('detailed-error-app');
                expect(error.timestamp).toBeDefined();
            }
        });
    });

    describe('性能监控', () => {
        it('应该记录应用加载时间', async () => {
            const app: SidecarApp = {
                name: 'perf-app',
                entry: 'https://example.com/app.js',
                container: '#app-container'
            };

            await sidecar.registerApp(app);
            await sidecar.loadApp('perf-app');

            const metrics = sidecar.getPerformanceMetrics('perf-app');
            expect(metrics.loadTime).toBeGreaterThan(0);
        });

        it('应该记录应用挂载时间', async () => {
            const app: SidecarApp = {
                name: 'mount-perf-app',
                entry: 'https://example.com/app.js',
                container: '#app-container'
            };

            await sidecar.registerApp(app);
            await sidecar.loadApp('mount-perf-app');
            await sidecar.mountApp('mount-perf-app');

            const metrics = sidecar.getPerformanceMetrics('mount-perf-app');
            expect(metrics.mountTime).toBeGreaterThan(0);
        });

        it('应该监控内存使用情况', async () => {
            const app: SidecarApp = {
                name: 'memory-app',
                entry: 'https://example.com/app.js',
                container: '#app-container'
            };

            await sidecar.registerApp(app);
            await sidecar.loadApp('memory-app');

            const metrics = sidecar.getPerformanceMetrics('memory-app');
            expect(metrics.memoryUsage).toBeDefined();
            expect(typeof metrics.memoryUsage.used).toBe('number');
        });
    });

    describe('清理和销毁', () => {
        it('应该正确清理所有资源', async () => {
            const app: SidecarApp = {
                name: 'cleanup-app',
                entry: 'https://example.com/app.js',
                container: '#app-container'
            };

            await sidecar.registerApp(app);
            await sidecar.loadApp('cleanup-app');
            await sidecar.mountApp('cleanup-app');

            sidecar.destroy();

            expect(sidecar.getRegisteredApps()).toHaveLength(0);
            expect(sidecar.isInitialized()).toBe(false);
        });

        it('应该移除所有事件监听器', async () => {
            const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener');

            sidecar.destroy();

            expect(removeEventListenerSpy).toHaveBeenCalled();
        });

        it('应该清理所有 DOM 元素', async () => {
            const app: SidecarApp = {
                name: 'dom-cleanup-app',
                entry: 'https://example.com/app.js',
                container: '#app-container'
            };

            await sidecar.registerApp(app);
            await sidecar.loadApp('dom-cleanup-app');

            const appElements = document.querySelectorAll('[data-sidecar-app]');
            expect(appElements.length).toBeGreaterThan(0);

            sidecar.destroy();

            const remainingElements = document.querySelectorAll('[data-sidecar-app]');
            expect(remainingElements.length).toBe(0);
        });
    });

    describe('兼容性模式', () => {
        it('应该支持 jQuery 应用', async () => {
            const jqueryApp: SidecarApp = {
                name: 'jquery-app',
                entry: 'https://example.com/jquery-app.js',
                container: '#app-container',
                framework: 'jquery',
                compatMode: true
            };

            await sidecar.registerApp(jqueryApp);
            await sidecar.loadApp('jquery-app');

            expect(sidecar.getAppInfo('jquery-app')?.framework).toBe('jquery');
            expect(sidecar.getAppInfo('jquery-app')?.compatMode).toBe(true);
        });

        it('应该支持原生 JavaScript 应用', async () => {
            const vanillaApp: SidecarApp = {
                name: 'vanilla-app',
                entry: 'https://example.com/vanilla-app.js',
                container: '#app-container',
                framework: 'vanilla',
                compatMode: true
            };

            await sidecar.registerApp(vanillaApp);
            await sidecar.loadApp('vanilla-app');

            expect(sidecar.getAppInfo('vanilla-app')?.framework).toBe('vanilla');
        });

        it('应该自动检测应用框架', async () => {
            const autoDetectApp: SidecarApp = {
                name: 'auto-detect-app',
                entry: 'https://example.com/app.js',
                container: '#app-container',
                autoDetectFramework: true
            };

            await sidecar.registerApp(autoDetectApp);
            await sidecar.loadApp('auto-detect-app');

            const appInfo = sidecar.getAppInfo('auto-detect-app');
            expect(appInfo?.detectedFramework).toBeDefined();
        });
    });
});