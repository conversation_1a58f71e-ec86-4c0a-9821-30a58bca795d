/**
 * @fileoverview 隔离功能测试
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { EventIsolator } from '../../src/isolation/event-isolator';
import { GlobalIsolator } from '../../src/isolation/global-isolator';
import { IsolationContainer } from '../../src/isolation/isolation-container';
import { ScriptIsolator } from '../../src/isolation/script-isolator';
import { StyleIsolator } from '../../src/isolation/style-isolator';
import type { IsolationConfig } from '../../src/types';

describe('隔离功能测试', () => {
    let mockDocument: any;
    let mockWindow: any;

    beforeEach(() => {
        // 模拟 document 对象
        mockDocument = {
            createElement: vi.fn().mockImplementation((tagName: string) => ({
                tagName: tagName.toUpperCase(),
                setAttribute: vi.fn(),
                getAttribute: vi.fn(),
                appendChild: vi.fn(),
                removeChild: vi.fn(),
                addEventListener: vi.fn(),
                removeEventListener: vi.fn(),
                style: {},
                innerHTML: '',
                textContent: '',
                parentNode: null,
                childNodes: [],
                querySelectorAll: vi.fn().mockReturnValue([]),
                querySelector: vi.fn()
            })),
            head: {
                appendChild: vi.fn(),
                removeChild: vi.fn(),
                querySelectorAll: vi.fn().mockReturnValue([])
            },
            body: {
                appendChild: vi.fn(),
                removeChild: vi.fn(),
                addEventListener: vi.fn(),
                removeEventListener: vi.fn()
            },
            addEventListener: vi.fn(),
            removeEventListener: vi.fn(),
            querySelectorAll: vi.fn().mockReturnValue([]),
            querySelector: vi.fn()
        };

        // 模拟 window 对象
        mockWindow = {
            addEventListener: vi.fn(),
            removeEventListener: vi.fn(),
            document: mockDocument,
            location: { href: 'http://localhost:3000' },
            history: { pushState: vi.fn(), replaceState: vi.fn() },
            localStorage: {
                getItem: vi.fn(),
                setItem: vi.fn(),
                removeItem: vi.fn(),
                clear: vi.fn()
            },
            sessionStorage: {
                getItem: vi.fn(),
                setItem: vi.fn(),
                removeItem: vi.fn(),
                clear: vi.fn()
            }
        };

        // 设置全局对象
        Object.defineProperty(global, 'document', {
            value: mockDocument,
            writable: true
        });

        Object.defineProperty(global, 'window', {
            value: mockWindow,
            writable: true
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('IsolationContainer', () => {
        let container: IsolationContainer;
        let config: IsolationConfig;

        beforeEach(() => {
            config = {
                css: true,
                js: true,
                events: true,
                storage: true,
                history: true
            };
            container = new IsolationContainer('test-app', config);
        });

        it('应该正确初始化隔离容器', () => {
            expect(container).toBeInstanceOf(IsolationContainer);
            expect(container.getAppName()).toBe('test-app');
            expect(container.getConfig()).toEqual(config);
        });

        it('应该能够创建隔离环境', async () => {
            await container.create();

            expect(container.isCreated()).toBe(true);
            expect(container.getIsolatedWindow()).toBeDefined();
            expect(container.getIsolatedDocument()).toBeDefined();
        });

        it('应该能够销毁隔离环境', async () => {
            await container.create();
            await container.destroy();

            expect(container.isCreated()).toBe(false);
        });

        it('应该支持部分隔离配置', async () => {
            const partialConfig: IsolationConfig = {
                css: true,
                js: false,
                events: false
            };

            const partialContainer = new IsolationContainer('partial-app', partialConfig);
            await partialContainer.create();

            expect(partialContainer.getConfig().css).toBe(true);
            expect(partialContainer.getConfig().js).toBe(false);
            expect(partialContainer.getConfig().events).toBe(false);
        });

        it('应该能够获取隔离统计信息', async () => {
            await container.create();

            const stats = container.getStats();
            expect(stats).toHaveProperty('createdAt');
            expect(stats).toHaveProperty('isolatedElements');
            expect(stats).toHaveProperty('isolatedEvents');
            expect(stats).toHaveProperty('isolatedGlobals');
        });
    });

    describe('StyleIsolator', () => {
        let isolator: StyleIsolator;

        beforeEach(() => {
            isolator = new StyleIsolator('test-app');
        });

        it('应该正确初始化样式隔离器', () => {
            expect(isolator).toBeInstanceOf(StyleIsolator);
            expect(isolator.getAppName()).toBe('test-app');
        });

        it('应该能够隔离样式', async () => {
            const cssText = `
                .test-class {
                    color: red;
                    background: blue;
                }
                #test-id {
                    font-size: 16px;
                }
            `;

            await isolator.isolate(cssText);

            const isolatedCSS = isolator.getIsolatedCSS();
            expect(isolatedCSS).toContain('[data-micro-app="test-app"]');
            expect(isolatedCSS).toContain('.test-class');
            expect(isolatedCSS).toContain('#test-id');
        });

        it('应该能够添加样式前缀', async () => {
            const cssText = '.button { padding: 10px; }';

            await isolator.isolate(cssText, { prefix: 'micro-app-test-app' });

            const isolatedCSS = isolator.getIsolatedCSS();
            expect(isolatedCSS).toContain('.micro-app-test-app .button');
        });

        it('应该能够处理媒体查询', async () => {
            const cssText = `
                @media (max-width: 768px) {
                    .responsive {
                        display: none;
                    }
                }
            `;

            await isolator.isolate(cssText);

            const isolatedCSS = isolator.getIsolatedCSS();
            expect(isolatedCSS).toContain('@media (max-width: 768px)');
            expect(isolatedCSS).toContain('[data-micro-app="test-app"]');
        });

        it('应该能够处理关键帧动画', async () => {
            const cssText = `
                @keyframes slideIn {
                    from { transform: translateX(-100%); }
                    to { transform: translateX(0); }
                }
                .animated {
                    animation: slideIn 0.3s ease-in-out;
                }
            `;

            await isolator.isolate(cssText);

            const isolatedCSS = isolator.getIsolatedCSS();
            expect(isolatedCSS).toContain('@keyframes slideIn');
            expect(isolatedCSS).toContain('.animated');
        });

        it('应该能够移除隔离的样式', async () => {
            const cssText = '.test { color: red; }';

            await isolator.isolate(cssText);
            expect(isolator.getIsolatedCSS()).toBeTruthy();

            await isolator.remove();
            expect(isolator.getIsolatedCSS()).toBe('');
        });

        it('应该能够处理样式冲突', async () => {
            const cssText1 = '.conflict { color: red; }';
            const cssText2 = '.conflict { color: blue; }';

            await isolator.isolate(cssText1);
            await isolator.isolate(cssText2);

            const isolatedCSS = isolator.getIsolatedCSS();
            // 后添加的样式应该覆盖前面的
            expect(isolatedCSS).toContain('color: blue');
        });
    });

    describe('ScriptIsolator', () => {
        let isolator: ScriptIsolator;

        beforeEach(() => {
            isolator = new ScriptIsolator('test-app');
        });

        it('应该正确初始化脚本隔离器', () => {
            expect(isolator).toBeInstanceOf(ScriptIsolator);
            expect(isolator.getAppName()).toBe('test-app');
        });

        it('应该能够创建隔离的执行环境', async () => {
            await isolator.createSandbox();

            const sandbox = isolator.getSandbox();
            expect(sandbox).toBeDefined();
            expect(sandbox.window).toBeDefined();
            expect(sandbox.document).toBeDefined();
        });

        it('应该能够执行隔离的脚本', async () => {
            await isolator.createSandbox();

            const scriptCode = `
                var testVar = 'isolated';
                function testFunction() {
                    return 'isolated function';
                }
                window.testGlobal = 'isolated global';
            `;

            const result = await isolator.execute(scriptCode);

            expect(result.success).toBe(true);
            expect(result.error).toBeUndefined();
        });

        it('应该能够处理脚本错误', async () => {
            await isolator.createSandbox();

            const errorScript = `
                throw new Error('测试错误');
            `;

            const result = await isolator.execute(errorScript);

            expect(result.success).toBe(false);
            expect(result.error).toBeDefined();
            expect(result.error?.message).toContain('测试错误');
        });

        it('应该能够隔离全局变量', async () => {
            await isolator.createSandbox();

            const script1 = 'var globalVar = "app1";';
            const script2 = 'var globalVar = "app2";';

            await isolator.execute(script1);

            // 创建另一个隔离器
            const isolator2 = new ScriptIsolator('test-app-2');
            await isolator2.createSandbox();
            await isolator2.execute(script2);

            // 两个隔离器的全局变量应该不互相影响
            const sandbox1 = isolator.getSandbox();
            const sandbox2 = isolator2.getSandbox();

            expect(sandbox1.window.globalVar).toBe('app1');
            expect(sandbox2.window.globalVar).toBe('app2');
        });

        it('应该能够销毁沙箱', async () => {
            await isolator.createSandbox();
            expect(isolator.getSandbox()).toBeDefined();

            await isolator.destroy();
            expect(isolator.getSandbox()).toBeNull();
        });

        it('应该能够处理异步脚本', async () => {
            await isolator.createSandbox();

            const asyncScript = `
                new Promise((resolve) => {
                    setTimeout(() => {
                        window.asyncResult = 'completed';
                        resolve('done');
                    }, 10);
                });
            `;

            const result = await isolator.execute(asyncScript);

            expect(result.success).toBe(true);

            // 等待异步操作完成
            await new Promise(resolve => setTimeout(resolve, 20));

            const sandbox = isolator.getSandbox();
            expect(sandbox.window.asyncResult).toBe('completed');
        });
    });

    describe('EventIsolator', () => {
        let isolator: EventIsolator;

        beforeEach(() => {
            isolator = new EventIsolator('test-app');
        });

        it('应该正确初始化事件隔离器', () => {
            expect(isolator).toBeInstanceOf(EventIsolator);
            expect(isolator.getAppName()).toBe('test-app');
        });

        it('应该能够隔离事件监听器', async () => {
            await isolator.isolate();

            const handler = vi.fn();
            const mockElement = mockDocument.createElement('div');

            isolator.addEventListenerToElement(mockElement, 'click', handler);

            // 模拟事件触发
            const clickEvent = new Event('click');
            mockElement.dispatchEvent(clickEvent);

            expect(handler).toHaveBeenCalled();
        });

        it('应该能够移除事件监听器', async () => {
            await isolator.isolate();

            const handler = vi.fn();
            const mockElement = mockDocument.createElement('div');

            isolator.addEventListenerToElement(mockElement, 'click', handler);
            isolator.removeEventListenerFromElement(mockElement, 'click', handler);

            // 模拟事件触发
            const clickEvent = new Event('click');
            mockElement.dispatchEvent(clickEvent);

            expect(handler).not.toHaveBeenCalled();
        });

        it('应该能够隔离不同应用的事件', async () => {
            const isolator2 = new EventIsolator('test-app-2');

            await isolator.isolate();
            await isolator2.isolate();

            const handler1 = vi.fn();
            const handler2 = vi.fn();
            const element1 = mockDocument.createElement('div');
            const element2 = mockDocument.createElement('div');

            isolator.addEventListenerToElement(element1, 'click', handler1);
            isolator2.addEventListenerToElement(element2, 'click', handler2);

            // 触发第一个应用的事件
            element1.dispatchEvent(new Event('click'));
            expect(handler1).toHaveBeenCalled();
            expect(handler2).not.toHaveBeenCalled();

            // 重置 mock
            handler1.mockClear();
            handler2.mockClear();

            // 触发第二个应用的事件
            element2.dispatchEvent(new Event('click'));
            expect(handler1).not.toHaveBeenCalled();
            expect(handler2).toHaveBeenCalled();
        });

        it('应该能够禁用事件隔离', async () => {
            await isolator.isolate();
            expect(isolator.isIsolated()).toBe(true);

            await isolator.restore();
            expect(isolator.isIsolated()).toBe(false);
        });

        it('应该能够获取事件统计信息', async () => {
            await isolator.isolate();

            const handler = vi.fn();
            const element = mockDocument.createElement('div');
            isolator.addEventListenerToElement(element, 'click', handler);
            isolator.addEventListenerToElement(element, 'keydown', handler);

            const stats = isolator.getStats();
            expect(stats.totalEvents).toBeGreaterThan(0);
            expect(stats.eventTypes).toContain('click');
            expect(stats.eventTypes).toContain('keydown');
        });
    });

    describe('GlobalIsolator', () => {
        let isolator: GlobalIsolator;

        beforeEach(() => {
            const config = {
                isolateGlobals: true,
                isolateStorage: true,
                isolateHistory: true
            };
            isolator = new GlobalIsolator(config);
        });

        it('应该正确初始化全局变量隔离器', () => {
            expect(isolator).toBeInstanceOf(GlobalIsolator);
        });

        it('应该能够隔离全局变量', async () => {
            await isolator.isolate();

            // 设置隔离的全局变量
            const isolatedWindow = isolator.getIsolatedWindow();
            isolatedWindow.testVar = 'isolated value';
            expect(isolatedWindow.testVar).toBe('isolated value');

            // 全局变量不应该污染真实的 window
            expect((global as any).testVar).toBeUndefined();
        });

        it('应该能够隔离 localStorage', async () => {
            await isolator.isolate();

            const isolatedStorage = isolator.getIsolatedStorage();
            isolatedStorage.localStorage.setItem('testKey', 'testValue');
            expect(isolatedStorage.localStorage.getItem('testKey')).toBe('testValue');

            // 不应该影响真实的 localStorage
            expect(mockWindow.localStorage.getItem).not.toHaveBeenCalledWith('testKey');
        });

        it('应该能够隔离 sessionStorage', async () => {
            await isolator.isolate();

            const isolatedStorage = isolator.getIsolatedStorage();
            isolatedStorage.sessionStorage.setItem('sessionKey', 'sessionValue');
            expect(isolatedStorage.sessionStorage.getItem('sessionKey')).toBe('sessionValue');

            // 不应该影响真实的 sessionStorage
            expect(mockWindow.sessionStorage.getItem).not.toHaveBeenCalledWith('sessionKey');
        });

        it('应该能够隔离 history API', async () => {
            await isolator.isolate();

            const isolatedHistory = isolator.getIsolatedHistory();
            const originalPushState = mockWindow.history.pushState;

            isolatedHistory.pushState({ test: 'data' }, 'Test', '/test');

            // 应该使用隔离的 history
            expect(originalPushState).not.toHaveBeenCalled();
        });

        it('应该能够恢复全局变量', async () => {
            await isolator.isolate();

            // 设置一些隔离的全局变量
            const isolatedWindow = isolator.getIsolatedWindow();
            isolatedWindow.var1 = 'value1';
            isolatedWindow.var2 = 'value2';

            await isolator.restore();

            // 隔离的变量应该被清除
            expect(isolatedWindow.var1).toBeUndefined();
            expect(isolatedWindow.var2).toBeUndefined();
        });

        it('应该能够获取隔离统计信息', async () => {
            await isolator.isolate();

            const isolatedWindow = isolator.getIsolatedWindow();
            const isolatedStorage = isolator.getIsolatedStorage();

            isolatedWindow.var1 = 'value1';
            isolatedStorage.localStorage.setItem('key1', 'value1');
            isolatedStorage.sessionStorage.setItem('key2', 'value2');

            const stats = isolator.getStats();
            expect(stats.isolatedCount).toBeGreaterThan(0);
            expect(stats.sharedCount).toBeGreaterThanOrEqual(0);
            expect(stats.privateCount).toBeGreaterThanOrEqual(0);
        });
    });

    describe('隔离功能集成测试', () => {
        let container: IsolationContainer;

        beforeEach(() => {
            const config = {
                isolateStyle: true,
                isolateScript: true,
                isolateEvent: true,
                isolateGlobal: true
            };
            container = new IsolationContainer(config);
        });

        it('应该能够同时启用多种隔离', async () => {
            await container.isolate();

            expect(container.getStyleIsolator()).toBeDefined();
            expect(container.getScriptIsolator()).toBeDefined();
            expect(container.getEventIsolator()).toBeDefined();
            expect(container.getGlobalIsolator()).toBeDefined();
        });

        it('应该能够处理隔离冲突', async () => {
            await container.isolate();

            // 同时设置样式和脚本
            const styleIsolator = container.getStyleIsolator();
            const scriptIsolator = container.getScriptIsolator();

            await styleIsolator.isolate('.test { color: red; }');
            await scriptIsolator.createSandbox();

            // 两种隔离应该能够共存
            expect(styleIsolator.getIsolatedCSS()).toBeTruthy();
            expect(scriptIsolator.getSandbox()).toBeDefined();
        });

        it('应该能够批量清理隔离资源', async () => {
            await container.isolate();

            // 创建一些隔离资源
            const styleIsolator = container.getStyleIsolator();
            const scriptIsolator = container.getScriptIsolator();
            const eventIsolator = container.getEventIsolator();

            await styleIsolator.isolate('.test { color: red; }');
            await scriptIsolator.createSandbox();
            await eventIsolator.isolate();

            // 批量清理
            await container.restore();

            expect(container.isIsolated()).toBe(false);
        });

        it('应该能够获取综合统计信息', async () => {
            await container.isolate();

            const stats = container.getStats();
            expect(stats).toHaveProperty('initialized');
            expect(stats).toHaveProperty('destroyed');
            expect(stats).toHaveProperty('isolators');
        });

        it('应该能够处理隔离错误', async () => {
            const errorContainer = new IsolationContainer({
                isolateStyle: true,
                isolateScript: true,
                isolateEvent: true
            });

            // 模拟创建错误
            vi.spyOn(errorContainer, 'isolate').mockRejectedValue(new Error('隔离创建失败'));

            await expect(errorContainer.isolate()).rejects.toThrow('隔离创建失败');
        });

        it('应该支持动态配置更新', async () => {
            await container.isolate();

            // 更新配置
            const newConfig = {
                isolateStyle: false,
                isolateScript: true,
                isolateEvent: false
            };

            container.updateConfig(newConfig);

            expect(container.getConfig().isolateStyle).toBe(false);
            expect(container.getConfig().isolateScript).toBe(true);
            expect(container.getConfig().isolateEvent).toBe(false);
        });

        it('应该能够处理内存泄漏', async () => {
            await container.isolate();

            // 创建大量隔离资源
            const styleIsolator = container.getStyleIsolator();
            for (let i = 0; i < 100; i++) {
                await styleIsolator.isolate(`.test-${i} { color: red; }`);
            }

            const initialStats = container.getStats();

            // 清理资源
            await container.restore();

            // 隔离状态应该被重置
            expect(container.isIsolated()).toBe(false);
        });
    });
});
