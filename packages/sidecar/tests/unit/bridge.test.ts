/**
 * @fileoverview 桥接功能测试
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { CustomEventBridge } from '../../src/bridge/custom-event-bridge';
import { MessageBridge } from '../../src/bridge/message-bridge';
import { PostMessageBridge } from '../../src/bridge/post-message-bridge';
import type { BridgeConfig, BridgeMessage } from '../../src/types';

describe('桥接功能测试', () => {
    let mockWindow: any;

    beforeEach(() => {
        // 模拟 window 对象
        mockWindow = {
            addEventListener: vi.fn(),
            removeEventListener: vi.fn(),
            postMessage: vi.fn(),
            dispatchEvent: vi.fn(),
            CustomEvent: vi.fn().mockImplementation((type, options) => ({
                type,
                detail: options?.detail,
                preventDefault: vi.fn(),
                stopPropagation: vi.fn()
            }))
        };

        // 设置全局 window
        Object.defineProperty(global, 'window', {
            value: mockWindow,
            writable: true
        });

        Object.defineProperty(global, 'CustomEvent', {
            value: mockWindow.CustomEvent,
            writable: true
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('MessageBridge 基础类', () => {
        let bridge: MessageBridge;
        let config: BridgeConfig;

        beforeEach(() => {
            config = {
                type: 'postMessage',
                timeout: 5000,
                retries: 3
            };
            bridge = new MessageBridge(config);
        });

        it('应该正确初始化桥接', () => {
            expect(bridge).toBeInstanceOf(MessageBridge);
            expect(bridge.getType()).toBe('postMessage');
            expect(bridge.getConfig()).toEqual(config);
        });

        it('应该正确设置默认配置', () => {
            const defaultBridge = new MessageBridge();
            const defaultConfig = defaultBridge.getConfig();

            expect(defaultConfig.timeout).toBe(30000);
            expect(defaultConfig.retries).toBe(3);
            expect(defaultConfig.type).toBe('postMessage');
        });

        it('应该能够连接和断开', async () => {
            await bridge.connect();
            expect(bridge.isConnected()).toBe(true);

            await bridge.disconnect();
            expect(bridge.isConnected()).toBe(false);
        });

        it('应该能够发送消息', async () => {
            const message: BridgeMessage = {
                id: 'test-message-1',
                type: 'request',
                action: 'test-action',
                data: { test: 'data' },
                timestamp: Date.now()
            };

            await bridge.connect();
            const result = await bridge.send(message);

            expect(result).toBeDefined();
            expect(result.success).toBe(true);
        });

        it('应该能够监听消息', async () => {
            const messageHandler = vi.fn();

            await bridge.connect();
            bridge.on('message', messageHandler);

            // 模拟接收消息
            const testMessage: BridgeMessage = {
                id: 'received-message-1',
                type: 'response',
                action: 'test-response',
                data: { response: 'data' },
                timestamp: Date.now()
            };

            bridge.emit('message', testMessage);

            expect(messageHandler).toHaveBeenCalledWith(testMessage);
        });

        it('应该处理发送超时', async () => {
            const timeoutBridge = new MessageBridge({ timeout: 100 });

            const message: BridgeMessage = {
                id: 'timeout-message',
                type: 'request',
                action: 'slow-action',
                data: {},
                timestamp: Date.now()
            };

            await timeoutBridge.connect();

            // 模拟慢响应
            vi.spyOn(timeoutBridge, 'send').mockImplementation(() =>
                new Promise(resolve => setTimeout(resolve, 200))
            );

            await expect(timeoutBridge.send(message)).rejects.toThrow('发送超时');
        });

        it('应该支持重试机制', async () => {
            const retryBridge = new MessageBridge({ retries: 2 });
            let attemptCount = 0;

            vi.spyOn(retryBridge, 'send').mockImplementation(async () => {
                attemptCount++;
                if (attemptCount < 3) {
                    throw new Error('发送失败');
                }
                return { success: true, data: null };
            });

            const message: BridgeMessage = {
                id: 'retry-message',
                type: 'request',
                action: 'retry-action',
                data: {},
                timestamp: Date.now()
            };

            await retryBridge.connect();
            const result = await retryBridge.send(message);

            expect(attemptCount).toBe(3);
            expect(result.success).toBe(true);
        });
    });

    describe('PostMessageBridge', () => {
        let bridge: PostMessageBridge;

        beforeEach(() => {
            bridge = new PostMessageBridge({
                type: 'postMessage',
                timeout: 5000,
                targetOrigin: '*'
            });
        });

        it('应该正确初始化 PostMessage 桥接', () => {
            expect(bridge).toBeInstanceOf(PostMessageBridge);
            expect(bridge.getType()).toBe('postMessage');
        });

        it('应该能够连接', async () => {
            await bridge.connect();

            expect(mockWindow.addEventListener).toHaveBeenCalledWith(
                'message',
                expect.any(Function),
                false
            );
            expect(bridge.isConnected()).toBe(true);
        });

        it('应该能够断开连接', async () => {
            await bridge.connect();
            await bridge.disconnect();

            expect(mockWindow.removeEventListener).toHaveBeenCalledWith(
                'message',
                expect.any(Function),
                false
            );
            expect(bridge.isConnected()).toBe(false);
        });

        it('应该能够发送 PostMessage', async () => {
            const message: BridgeMessage = {
                id: 'post-message-1',
                type: 'request',
                action: 'test-post',
                data: { test: 'postMessage' },
                timestamp: Date.now()
            };

            await bridge.connect();
            await bridge.send(message);

            expect(mockWindow.postMessage).toHaveBeenCalledWith(
                expect.objectContaining({
                    id: message.id,
                    type: message.type,
                    action: message.action,
                    data: message.data
                }),
                '*'
            );
        });

        it('应该能够接收 PostMessage', async () => {
            const messageHandler = vi.fn();

            await bridge.connect();
            bridge.on('message', messageHandler);

            // 模拟接收到的 postMessage 事件
            const messageEvent = {
                data: {
                    id: 'received-post-message',
                    type: 'response',
                    action: 'test-response',
                    data: { response: 'postMessage' },
                    timestamp: Date.now()
                },
                origin: 'http://localhost:3000',
                source: mockWindow
            };

            // 触发消息处理器
            const messageListener = mockWindow.addEventListener.mock.calls
                .find(call => call[0] === 'message')?.[1];

            if (messageListener) {
                messageListener(messageEvent);
            }

            expect(messageHandler).toHaveBeenCalledWith(
                expect.objectContaining({
                    id: 'received-post-message',
                    type: 'response',
                    action: 'test-response'
                })
            );
        });

        it('应该验证消息来源', async () => {
            const originBridge = new PostMessageBridge({
                type: 'postMessage',
                targetOrigin: 'http://trusted-origin.com'
            });

            const messageHandler = vi.fn();

            await originBridge.connect();
            originBridge.on('message', messageHandler);

            // 模拟来自不信任来源的消息
            const untrustedMessageEvent = {
                data: {
                    id: 'untrusted-message',
                    type: 'request',
                    action: 'malicious-action',
                    data: {},
                    timestamp: Date.now()
                },
                origin: 'http://malicious-site.com',
                source: mockWindow
            };

            const messageListener = mockWindow.addEventListener.mock.calls
                .find(call => call[0] === 'message')?.[1];

            if (messageListener) {
                messageListener(untrustedMessageEvent);
            }

            // 不应该处理来自不信任来源的消息
            expect(messageHandler).not.toHaveBeenCalled();
        });
    });

    describe('CustomEventBridge', () => {
        let bridge: CustomEventBridge;

        beforeEach(() => {
            bridge = new CustomEventBridge({
                type: 'customEvent',
                timeout: 5000,
                eventPrefix: 'micro-core'
            });
        });

        it('应该正确初始化 CustomEvent 桥接', () => {
            expect(bridge).toBeInstanceOf(CustomEventBridge);
            expect(bridge.getType()).toBe('customEvent');
        });

        it('应该能够连接', async () => {
            await bridge.connect();

            expect(mockWindow.addEventListener).toHaveBeenCalledWith(
                'micro-core:message',
                expect.any(Function),
                false
            );
            expect(bridge.isConnected()).toBe(true);
        });

        it('应该能够断开连接', async () => {
            await bridge.connect();
            await bridge.disconnect();

            expect(mockWindow.removeEventListener).toHaveBeenCalledWith(
                'micro-core:message',
                expect.any(Function),
                false
            );
            expect(bridge.isConnected()).toBe(false);
        });

        it('应该能够发送自定义事件', async () => {
            const message: BridgeMessage = {
                id: 'custom-event-1',
                type: 'request',
                action: 'test-custom',
                data: { test: 'customEvent' },
                timestamp: Date.now()
            };

            await bridge.connect();
            await bridge.send(message);

            expect(mockWindow.CustomEvent).toHaveBeenCalledWith(
                'micro-core:message',
                {
                    detail: expect.objectContaining({
                        id: message.id,
                        type: message.type,
                        action: message.action,
                        data: message.data
                    })
                }
            );

            expect(mockWindow.dispatchEvent).toHaveBeenCalled();
        });

        it('应该能够接收自定义事件', async () => {
            const messageHandler = vi.fn();

            await bridge.connect();
            bridge.on('message', messageHandler);

            // 模拟接收到的自定义事件
            const customEvent = {
                type: 'micro-core:message',
                detail: {
                    id: 'received-custom-event',
                    type: 'response',
                    action: 'test-response',
                    data: { response: 'customEvent' },
                    timestamp: Date.now()
                }
            };

            // 触发事件处理器
            const eventListener = mockWindow.addEventListener.mock.calls
                .find(call => call[0] === 'micro-core:message')?.[1];

            if (eventListener) {
                eventListener(customEvent);
            }

            expect(messageHandler).toHaveBeenCalledWith(
                expect.objectContaining({
                    id: 'received-custom-event',
                    type: 'response',
                    action: 'test-response'
                })
            );
        });

        it('应该支持自定义事件前缀', async () => {
            const customPrefixBridge = new CustomEventBridge({
                type: 'customEvent',
                eventPrefix: 'my-app'
            });

            await customPrefixBridge.connect();

            expect(mockWindow.addEventListener).toHaveBeenCalledWith(
                'my-app:message',
                expect.any(Function),
                false
            );
        });
    });

    describe('桥接工厂和管理', () => {
        it('应该能够创建不同类型的桥接', () => {
            const postMessageBridge = new PostMessageBridge();
            const customEventBridge = new CustomEventBridge();

            expect(postMessageBridge.getType()).toBe('postMessage');
            expect(customEventBridge.getType()).toBe('customEvent');
        });

        it('应该能够管理多个桥接实例', async () => {
            const bridge1 = new PostMessageBridge({ type: 'postMessage' });
            const bridge2 = new CustomEventBridge({ type: 'customEvent' });

            await bridge1.connect();
            await bridge2.connect();

            expect(bridge1.isConnected()).toBe(true);
            expect(bridge2.isConnected()).toBe(true);

            await bridge1.disconnect();
            await bridge2.disconnect();

            expect(bridge1.isConnected()).toBe(false);
            expect(bridge2.isConnected()).toBe(false);
        });

        it('应该能够处理桥接错误', async () => {
            const errorBridge = new MessageBridge();

            // 模拟连接错误
            vi.spyOn(errorBridge, 'connect').mockRejectedValue(new Error('连接失败'));

            await expect(errorBridge.connect()).rejects.toThrow('连接失败');
        });
    });

    describe('消息序列化和过滤', () => {
        let bridge: MessageBridge;

        beforeEach(() => {
            bridge = new MessageBridge();
        });

        it('应该能够序列化复杂消息', async () => {
            const complexMessage: BridgeMessage = {
                id: 'complex-message',
                type: 'request',
                action: 'complex-action',
                data: {
                    nested: {
                        array: [1, 2, 3],
                        object: { key: 'value' },
                        date: new Date(),
                        function: () => 'test'
                    }
                },
                timestamp: Date.now()
            };

            await bridge.connect();

            // 应该能够处理复杂数据结构
            expect(() => bridge.send(complexMessage)).not.toThrow();
        });

        it('应该能够过滤无效消息', async () => {
            const messageHandler = vi.fn();

            await bridge.connect();
            bridge.on('message', messageHandler);

            // 模拟无效消息
            const invalidMessages = [
                null,
                undefined,
                '',
                123,
                { invalid: 'message' },
                { id: '', type: 'invalid' }
            ];

            invalidMessages.forEach(msg => {
                bridge.emit('message', msg as any);
            });

            // 不应该处理无效消息
            expect(messageHandler).not.toHaveBeenCalled();
        });

        it('应该验证消息格式', () => {
            const validMessage: BridgeMessage = {
                id: 'valid-message',
                type: 'request',
                action: 'test-action',
                data: {},
                timestamp: Date.now()
            };

            const invalidMessages = [
                { type: 'request', action: 'test' }, // 缺少 id
                { id: 'test', action: 'test' }, // 缺少 type
                { id: 'test', type: 'request' }, // 缺少 action
                { id: 'test', type: 'invalid-type', action: 'test' } // 无效 type
            ];

            expect(bridge.validateMessage(validMessage)).toBe(true);

            invalidMessages.forEach(msg => {
                expect(bridge.validateMessage(msg as any)).toBe(false);
            });
        });
    });
});