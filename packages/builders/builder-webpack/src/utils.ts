/**
 * @fileoverview Webpack Builder Utilities
 * @description Webpack 构建器工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { formatBytes, formatTime } from '@micro-core/shared/utils';
import type { WebpackBuilderConfig } from './types';

/**
 * 验证 Webpack 构建器配置
 */
export function validateWebpackConfig(config: WebpackBuilderConfig): void {
    if (!config.name) {
        throw new Error('构建器名称是必需的');
    }

    if (!config.entry) {
        throw new Error('入口文件是必需的');
    }

    if (config.devServer && !config.devServerConfig?.port) {
        console.warn('开发服务器端口未指定，将使用默认端口 3000');
    }
}

/**
 * 检查是否为 Webpack 应用
 */
export function isWebpackApp(config: any): boolean {
    return !!(
        config.webpack ||
        config.webpackConfig ||
        (config.entry && typeof config.entry === 'string') ||
        config.builder === 'webpack'
    );
}

/**
 * 获取 Webpack 版本
 */
export function getWebpackVersion(): string | null {
    try {
        return require('webpack/package.json').version;
    } catch {
        return null;
    }
}

/**
 * 检查 Webpack 版本兼容性
 */
export function isWebpackVersionCompatible(version: string, minVersion: string = '5.0.0'): boolean {
    try {
        const parseVersion = (v: string) => v.split('.').map(Number);
        const current = parseVersion(version);
        const minimum = parseVersion(minVersion);

        for (let i = 0; i < Math.max(current.length, minimum.length); i++) {
            const currentPart = current[i] || 0;
            const minimumPart = minimum[i] || 0;

            if (currentPart > minimumPart) return true;
            if (currentPart < minimumPart) return false;
        }

        return true;
    } catch {
        return false;
    }
}

/**
 * 创建默认 Webpack 配置
 */
export function createDefaultWebpackConfig(overrides: Partial<WebpackBuilderConfig> = {}): WebpackBuilderConfig {
    return {
        name: 'webpack-app',
        framework: 'webpack',
        mode: 'production',
        outputDir: 'dist',
        publicPath: '/',
        sourceMap: false,
        minify: true,
        analyze: false,
        hmr: true,
        devServer: false,
        ...overrides
    };
}

/**
 * 格式化 Webpack 错误信息
 */
export function formatWebpackError(error: Error, context?: {
    builderName?: string;
    appName?: string;
    operation?: string;
}): string {
    const timestamp = new Date().toISOString();
    const builderName = context?.builderName || 'webpack';
    const appName = context?.appName || 'unknown';
    const operation = context?.operation || 'unknown';

    return `[${timestamp}] [${builderName}] [${appName}] [${operation}] ${error.name}: ${error.message}${error.stack ? '\n' + error.stack : ''
        }`;
}

/**
 * 计算构建统计信息
 */
export function calculateBuildStats(stats: any): {
    duration: string;
    totalSize: string;
    chunkCount: number;
    assetCount: number;
    errors: number;
    warnings: number;
} {
    const statsJson = stats.toJson();

    return {
        duration: formatTime(stats.endTime - stats.startTime),
        totalSize: formatBytes(
            (statsJson.assets || []).reduce((sum: number, asset: any) => sum + (asset.size || 0), 0)
        ),
        chunkCount: (statsJson.chunks || []).length,
        assetCount: (statsJson.assets || []).length,
        errors: (statsJson.errors || []).length,
        warnings: (statsJson.warnings || []).length
    };
}

/**
 * 获取资源类型
 */
export function getAssetType(filename: string): 'javascript' | 'stylesheet' | 'html' | 'image' | 'font' | 'other' {
    const ext = filename.split('.').pop()?.toLowerCase();

    switch (ext) {
        case 'js':
        case 'mjs':
        case 'jsx':
        case 'ts':
        case 'tsx':
            return 'javascript';
        case 'css':
        case 'scss':
        case 'sass':
        case 'less':
            return 'stylesheet';
        case 'html':
        case 'htm':
            return 'html';
        case 'png':
        case 'jpg':
        case 'jpeg':
        case 'gif':
        case 'svg':
        case 'webp':
            return 'image';
        case 'woff':
        case 'woff2':
        case 'eot':
        case 'ttf':
        case 'otf':
            return 'font';
        default:
            return 'other';
    }
}

/**
 * 创建 Webpack 加载器规则
 */
export function createLoaderRules(): any[] {
    return [
        // TypeScript/JavaScript
        {
            test: /\.(ts|tsx|js|jsx)$/,
            exclude: /node_modules/,
            use: {
                loader: 'babel-loader',
                options: {
                    presets: [
                        ['@babel/preset-env', { targets: 'defaults' }],
                        '@babel/preset-typescript',
                        ['@babel/preset-react', { runtime: 'automatic' }]
                    ],
                    plugins: [
                        '@babel/plugin-proposal-class-properties',
                        '@babel/plugin-proposal-object-rest-spread'
                    ]
                }
            }
        },
        // CSS
        {
            test: /\.css$/,
            use: ['style-loader', 'css-loader', 'postcss-loader']
        },
        // SCSS/SASS
        {
            test: /\.(scss|sass)$/,
            use: ['style-loader', 'css-loader', 'postcss-loader', 'sass-loader']
        },
        // Less
        {
            test: /\.less$/,
            use: ['style-loader', 'css-loader', 'postcss-loader', 'less-loader']
        },
        // 图片资源
        {
            test: /\.(png|jpg|jpeg|gif|svg|webp)$/,
            type: 'asset/resource',
            generator: {
                filename: 'images/[name].[hash][ext]'
            }
        },
        // 字体资源
        {
            test: /\.(woff|woff2|eot|ttf|otf)$/,
            type: 'asset/resource',
            generator: {
                filename: 'fonts/[name].[hash][ext]'
            }
        },
        // 其他资源
        {
            test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)$/,
            type: 'asset/resource',
            generator: {
                filename: 'media/[name].[hash][ext]'
            }
        }
    ];
}

/**
 * 创建 Webpack 优化配置
 */
export function createOptimizationConfig(mode: 'development' | 'production'): any {
    const isDev = mode === 'development';

    if (isDev) {
        return {
            splitChunks: {
                chunks: 'all',
                cacheGroups: {
                    vendor: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        chunks: 'all'
                    }
                }
            }
        };
    }

    return {
        minimize: true,
        minimizer: [
            new (require('terser-webpack-plugin'))({
                terserOptions: {
                    compress: {
                        drop_console: true,
                        drop_debugger: true
                    },
                    format: {
                        comments: false
                    }
                },
                extractComments: false
            }),
            new (require('css-minimizer-webpack-plugin'))()
        ],
        splitChunks: {
            chunks: 'all',
            cacheGroups: {
                vendor: {
                    test: /[\\/]node_modules[\\/]/,
                    name: 'vendors',
                    chunks: 'all',
                    priority: 10
                },
                common: {
                    name: 'common',
                    minChunks: 2,
                    chunks: 'all',
                    priority: 5,
                    reuseExistingChunk: true
                }
            }
        },
        runtimeChunk: {
            name: 'runtime'
        }
    };
}

/**
 * Webpack 构建器工具集合
 */
export const WebpackBuilderUtils = {
    // 配置相关
    validateWebpackConfig,
    createDefaultWebpackConfig,
    isWebpackApp,

    // 版本管理
    getWebpackVersion,
    isWebpackVersionCompatible,

    // 错误处理
    formatWebpackError,

    // 统计信息
    calculateBuildStats,
    getAssetType,

    // 配置生成
    createLoaderRules,
    createOptimizationConfig
} as const;