/**
 * @fileoverview Webpack Builder Plugins
 * @description Webpack 构建器插件配置
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import webpack from 'webpack';
import type { WebpackBuilderConfig, WebpackPluginConfig } from './types';

/**
 * 创建 HTML 插件
 */
export function createHtmlPlugin(config: WebpackBuilderConfig): any {
    const HtmlWebpackPlugin = require('html-webpack-plugin');

    return new HtmlWebpackPlugin({
        template: config.template || 'public/index.html',
        filename: 'index.html',
        inject: true,
        minify: config.mode === 'production' ? {
            removeComments: true,
            collapseWhitespace: true,
            removeRedundantAttributes: true,
            useShortDoctype: true,
            removeEmptyAttributes: true,
            removeStyleLinkTypeAttributes: true,
            keepClosingSlash: true,
            minifyJS: true,
            minifyCSS: true,
            minifyURLs: true
        } : false,
        meta: {
            viewport: 'width=device-width, initial-scale=1, shrink-to-fit=no',
            'theme-color': '#000000'
        }
    });
}

/**
 * 创建环境变量插件
 */
export function createDefinePlugin(config: WebpackBuilderConfig): webpack.DefinePlugin {
    return new webpack.DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify(config.mode || 'production'),
        'process.env.PUBLIC_PATH': JSON.stringify(config.publicPath || '/'),
        '__MICRO_CORE_DEV__': config.mode === 'development',
        '__MICRO_CORE_PROD__': config.mode === 'production'
    });
}

/**
 * 创建 CSS 提取插件
 */
export function createMiniCssExtractPlugin(config: WebpackBuilderConfig): any {
    const MiniCssExtractPlugin = require('mini-css-extract-plugin');

    return new MiniCssExtractPlugin({
        filename: config.mode === 'development' ? '[name].css' : '[name].[contenthash].css',
        chunkFilename: config.mode === 'development' ? '[id].css' : '[id].[contenthash].css',
        ignoreOrder: false
    });
}

/**
 * 创建复制插件
 */
export function createCopyPlugin(patterns: Array<{ from: string; to: string }>): any {
    const CopyWebpackPlugin = require('copy-webpack-plugin');

    return new CopyWebpackPlugin({
        patterns: patterns.map(pattern => ({
            from: pattern.from,
            to: pattern.to,
            noErrorOnMissing: true
        }))
    });
}

/**
 * 创建包分析插件
 */
export function createBundleAnalyzerPlugin(): any {
    const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');

    return new BundleAnalyzerPlugin({
        analyzerMode: 'static',
        openAnalyzer: false,
        reportFilename: 'bundle-report.html'
    });
}

/**
 * 创建进度插件
 */
export function createProgressPlugin(): webpack.ProgressPlugin {
    return new webpack.ProgressPlugin({
        activeModules: false,
        entries: true,
        handler(percentage, message, ...args) {
            const percent = Math.round(percentage * 100);
            if (percent % 10 === 0) {
                console.log(`构建进度: ${percent}% ${message}`);
            }
        },
        modules: true,
        modulesCount: 5000,
        profile: false,
        dependencies: true,
        dependenciesCount: 10000
    });
}

/**
 * 创建热更新插件
 */
export function createHotModuleReplacementPlugin(): webpack.HotModuleReplacementPlugin {
    return new webpack.HotModuleReplacementPlugin();
}

/**
 * 创建清理插件
 */
export function createCleanWebpackPlugin(): any {
    const { CleanWebpackPlugin } = require('clean-webpack-plugin');

    return new CleanWebpackPlugin({
        cleanStaleWebpackAssets: false,
        cleanOnceBeforeBuildPatterns: ['**/*', '!static-files*'],
        cleanAfterEveryBuildPatterns: ['!static-files*']
    });
}

/**
 * 创建 ESLint 插件
 */
export function createESLintPlugin(): any {
    const ESLintPlugin = require('eslint-webpack-plugin');

    return new ESLintPlugin({
        extensions: ['js', 'jsx', 'ts', 'tsx'],
        exclude: 'node_modules',
        emitWarning: true,
        emitError: false,
        failOnError: false,
        failOnWarning: false
    });
}

/**
 * 创建 Fork TS Checker 插件
 */
export function createForkTsCheckerPlugin(): any {
    const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');

    return new ForkTsCheckerWebpackPlugin({
        async: true,
        typescript: {
            diagnosticOptions: {
                semantic: true,
                syntactic: true
            },
            mode: 'write-references'
        },
        eslint: {
            files: './src/**/*.{ts,tsx,js,jsx}'
        }
    });
}

/**
 * 创建 PWA 插件
 */
export function createWorkboxPlugin(): any {
    const { GenerateSW } = require('workbox-webpack-plugin');

    return new GenerateSW({
        clientsClaim: true,
        exclude: [/\.map$/, /manifest$/, /\.htaccess$/],
        importWorkboxFrom: 'cdn',
        navigateFallback: '/index.html',
        navigateFallbackBlacklist: [/^\/_/, /\/[^/?]+\.[^/]+$/]
    });
}

/**
 * 根据配置创建插件列表
 */
export function createPlugins(config: WebpackBuilderConfig, pluginConfig?: WebpackPluginConfig): webpack.WebpackPluginInstance[] {
    const plugins: webpack.WebpackPluginInstance[] = [];
    const isDev = config.mode === 'development';
    const isProd = config.mode === 'production';

    // 基础插件
    plugins.push(createDefinePlugin(config));
    plugins.push(createProgressPlugin());

    // HTML 插件
    if (pluginConfig?.html !== false) {
        plugins.push(createHtmlPlugin(config));
    }

    // 开发环境插件
    if (isDev) {
        if (config.hmr) {
            plugins.push(createHotModuleReplacementPlugin());
        }
    }

    // 生产环境插件
    if (isProd) {
        plugins.push(createCleanWebpackPlugin());

        // CSS 提取插件
        if (pluginConfig?.extractCss !== false) {
            plugins.push(createMiniCssExtractPlugin(config));
        }

        // 包分析插件
        if (config.analyze) {
            plugins.push(createBundleAnalyzerPlugin());
        }
    }

    // 复制插件
    if (pluginConfig?.copy && pluginConfig.copy.length > 0) {
        plugins.push(createCopyPlugin(pluginConfig.copy));
    }

    // TypeScript 检查插件
    plugins.push(createForkTsCheckerPlugin());

    // ESLint 插件
    plugins.push(createESLintPlugin());

    return plugins;
}

/**
 * Webpack 插件工具集合
 */
export const WebpackPluginUtils = {
    // 单个插件创建
    createHtmlPlugin,
    createDefinePlugin,
    createMiniCssExtractPlugin,
    createCopyPlugin,
    createBundleAnalyzerPlugin,
    createProgressPlugin,
    createHotModuleReplacementPlugin,
    createCleanWebpackPlugin,
    createESLintPlugin,
    createForkTsCheckerPlugin,
    createWorkboxPlugin,

    // 插件列表创建
    createPlugins
} as const;