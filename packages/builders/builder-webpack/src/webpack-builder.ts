/**
 * @fileoverview Webpack Builder Implementation
 * @description Webpack 构建器实现，提供 Webpack 构建支持
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { BaseBuilderConfig, BuildResult } from '@micro-core/shared/types';
import { formatBytes, formatTime } from '@micro-core/shared/utils';
import webpack, { Configuration, Stats } from 'webpack';

/**
 * Webpack 构建器配置
 */
export interface WebpackBuilderConfig extends BaseBuilderConfig {
  /** Webpack 配置 */
  webpackConfig?: Configuration;
  /** 是否启用开发服务器 */
  devServer?: boolean;
  /** 开发服务器配置 */
  devServerConfig?: any;
  /** 是否启用热更新 */
  hmr?: boolean;
  /** 构建模式 */
  mode?: 'development' | 'production';
  /** 输出目录 */
  outputDir?: string;
  /** 公共路径 */
  publicPath?: string;
  /** 是否生成 source map */
  sourceMap?: boolean;
  /** 是否压缩代码 */
  minify?: boolean;
  /** 是否分析包大小 */
  analyze?: boolean;
}

/**
 * Webpack 构建器类
 */
export class WebpackBuilder {
  private config: WebpackBuilderConfig;
  private compiler: webpack.Compiler | null = null;

  constructor(config: WebpackBuilderConfig) {
    this.config = {
      mode: 'production',
      outputDir: 'dist',
      publicPath: '/',
      sourceMap: false,
      minify: true,
      analyze: false,
      ...config
    };
  }

  /**
   * 构建应用
   */
  async build(): Promise<BuildResult> {
    const startTime = Date.now();

    try {
      // 创建 Webpack 配置
      const webpackConfig = this.createWebpackConfig();

      // 创建编译器
      this.compiler = webpack(webpackConfig);

      // 执行构建
      const stats = await this.runBuild();

      // 处理构建结果
      const result = this.processBuildResult(stats, startTime);

      return result;
    } catch (error) {
      throw new Error(`Webpack 构建失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 启动开发服务器
   */
  async serve(): Promise<void> {
    if (!this.config.devServer) {
      throw new Error('开发服务器未启用');
    }

    try {
      // 创建开发配置
      const webpackConfig = this.createWebpackConfig('development');

      // 启动开发服务器
      const WebpackDevServer = require('webpack-dev-server');
      const devServerConfig = this.createDevServerConfig();

      this.compiler = webpack(webpackConfig);
      const server = new WebpackDevServer(devServerConfig, this.compiler);

      await server.start();
      console.log('Webpack 开发服务器已启动');
    } catch (error) {
      throw new Error(`启动开发服务器失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 停止构建器
   */
  async stop(): Promise<void> {
    if (this.compiler) {
      this.compiler.close(() => {
        console.log('Webpack 编译器已关闭');
      });
      this.compiler = null;
    }
  }

  /**
   * 创建 Webpack 配置
   */
  private createWebpackConfig(mode?: string): Configuration {
    const buildMode = mode || this.config.mode || 'production';
    const isDev = buildMode === 'development';

    const baseConfig: Configuration = {
      mode: buildMode,
      entry: this.config.entry || './src/index.js',
      output: {
        path: this.config.outputDir,
        filename: isDev ? '[name].js' : '[name].[contenthash].js',
        publicPath: this.config.publicPath,
        clean: true
      },
      resolve: {
        extensions: ['.ts', '.tsx', '.js', '.jsx', '.json'],
        alias: this.config.alias || {}
      },
      module: {
        rules: [
          // TypeScript/JavaScript
          {
            test: /\.(ts|tsx|js|jsx)$/,
            exclude: /node_modules/,
            use: {
              loader: 'babel-loader',
              options: {
                presets: [
                  '@babel/preset-env',
                  '@babel/preset-typescript',
                  '@babel/preset-react'
                ]
              }
            }
          },
          // CSS
          {
            test: /\.css$/,
            use: ['style-loader', 'css-loader']
          },
          // SCSS/SASS
          {
            test: /\.(scss|sass)$/,
            use: ['style-loader', 'css-loader', 'sass-loader']
          },
          // 静态资源
          {
            test: /\.(png|jpg|jpeg|gif|svg|woff|woff2|eot|ttf|otf)$/,
            type: 'asset/resource'
          }
        ]
      },
      plugins: this.createPlugins(buildMode),
      devtool: this.config.sourceMap ? (isDev ? 'eval-source-map' : 'source-map') : false,
      optimization: this.createOptimization(buildMode)
    };

    // 合并用户自定义配置
    if (this.config.webpackConfig) {
      return this.mergeWebpackConfig(baseConfig, this.config.webpackConfig);
    }

    return baseConfig;
  }

  /**
   * 创建插件配置
   */
  private createPlugins(mode: string): webpack.WebpackPluginInstance[] {
    const plugins: webpack.WebpackPluginInstance[] = [];

    // HTML 插件
    const HtmlWebpackPlugin = require('html-webpack-plugin');
    plugins.push(new HtmlWebpackPlugin({
      template: this.config.template || 'public/index.html',
      filename: 'index.html',
      inject: true
    }));

    // 环境变量插件
    plugins.push(new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(mode)
    }));

    // 开发模式插件
    if (mode === 'development' && this.config.hmr) {
      plugins.push(new webpack.HotModuleReplacementPlugin());
    }

    // 生产模式插件
    if (mode === 'production') {
      // CSS 提取插件
      const MiniCssExtractPlugin = require('mini-css-extract-plugin');
      plugins.push(new MiniCssExtractPlugin({
        filename: '[name].[contenthash].css'
      }));

      // 包分析插件
      if (this.config.analyze) {
        const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
        plugins.push(new BundleAnalyzerPlugin());
      }
    }

    return plugins;
  }

  /**
   * 创建优化配置
   */
  private createOptimization(mode: string): Configuration['optimization'] {
    if (mode === 'development') {
      return {
        splitChunks: {
          chunks: 'all'
        }
      };
    }

    return {
      minimize: this.config.minify,
      minimizer: this.config.minify ? [
        new (require('terser-webpack-plugin'))({
          terserOptions: {
            compress: {
              drop_console: true,
              drop_debugger: true
            }
          }
        }),
        new (require('css-minimizer-webpack-plugin'))()
      ] : [],
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all'
          }
        }
      }
    };
  }

  /**
   * 创建开发服务器配置
   */
  private createDevServerConfig(): any {
    return {
      port: 3000,
      hot: this.config.hmr,
      open: true,
      historyApiFallback: true,
      static: {
        directory: 'public'
      },
      ...this.config.devServerConfig
    };
  }

  /**
   * 执行构建
   */
  private runBuild(): Promise<Stats> {
    return new Promise((resolve, reject) => {
      if (!this.compiler) {
        reject(new Error('编译器未初始化'));
        return;
      }

      this.compiler.run((err, stats) => {
        if (err) {
          reject(err);
          return;
        }

        if (!stats) {
          reject(new Error('构建统计信息为空'));
          return;
        }

        if (stats.hasErrors()) {
          const errors = stats.toJson().errors || [];
          reject(new Error(`构建错误: ${errors.map(e => e.message).join('\n')}`));
          return;
        }

        resolve(stats);
      });
    });
  }

  /**
   * 处理构建结果
   */
  private processBuildResult(stats: Stats, startTime: number): BuildResult {
    const endTime = Date.now();
    const buildTime = endTime - startTime;
    const statsJson = stats.toJson();

    // 计算包大小
    const assets = statsJson.assets || [];
    const totalSize = assets.reduce((sum, asset) => sum + (asset.size || 0), 0);

    return {
      success: true,
      buildTime: formatTime(buildTime),
      outputSize: formatBytes(totalSize),
      assets: assets.map(asset => ({
        name: asset.name || '',
        size: formatBytes(asset.size || 0),
        type: this.getAssetType(asset.name || '')
      })),
      warnings: statsJson.warnings?.map(w => w.message) || [],
      errors: [],
      metadata: {
        webpack: {
          version: require('webpack/package.json').version,
          mode: this.config.mode,
          chunks: statsJson.chunks?.length || 0,
          modules: statsJson.modules?.length || 0
        }
      }
    };
  }

  /**
   * 获取资源类型
   */
  private getAssetType(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'js':
        return 'javascript';
      case 'css':
        return 'stylesheet';
      case 'html':
        return 'html';
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return 'image';
      case 'woff':
      case 'woff2':
      case 'eot':
      case 'ttf':
        return 'font';
      default:
        return 'other';
    }
  }

  /**
   * 合并 Webpack 配置
   */
  private mergeWebpackConfig(base: Configuration, custom: Configuration): Configuration {
    // 简单的深度合并实现
    const merge = (target: any, source: any): any => {
      for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          target[key] = merge(target[key] || {}, source[key]);
        } else {
          target[key] = source[key];
        }
      }
      return target;
    };

    return merge({ ...base }, custom);
  }

  /**
   * 获取构建器信息
   */
  getInfo(): any {
    return {
      name: 'WebpackBuilder',
      version: '1.0.0',
      type: 'webpack',
      supportedFormats: ['js', 'ts', 'jsx', 'tsx', 'css', 'scss', 'sass'],
      features: [
        'Hot Module Replacement',
        'Code Splitting',
        'Tree Shaking',
        'Bundle Analysis',
        'Source Maps',
        'Asset Optimization'
      ]
    };
  }
}

/**
 * 创建 Webpack 构建器实例
 */
export function createWebpackBuilder(config: WebpackBuilderConfig): WebpackBuilder {
  return new WebpackBuilder(config);
}

/**
 * Webpack 构建器工厂
 */
export const WebpackBuilderFactory = {
  type: 'webpack',
  create: createWebpackBuilder,
  metadata: {
    name: 'Webpack Builder',
    version: '1.0.0',
    description: 'Webpack 构建器，支持现代前端应用构建',
    author: 'Echo <<EMAIL>>'
  }
};

export default WebpackBuilder;