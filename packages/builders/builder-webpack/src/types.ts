/**
 * @fileoverview Webpack Builder Types
 * @description Webpack 构建器类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { BaseBuilderConfig, BuildResult } from '@micro-core/shared/types';
import type { Configuration } from 'webpack';

/**
 * Webpack 构建器配置接口
 */
export interface WebpackBuilderConfig extends BaseBuilderConfig {
    /** Webpack 配置 */
    webpackConfig?: Configuration;
    /** 是否启用开发服务器 */
    devServer?: boolean;
    /** 开发服务器配置 */
    devServerConfig?: WebpackDevServerConfig;
    /** 是否启用热更新 */
    hmr?: boolean;
    /** 构建模式 */
    mode?: 'development' | 'production';
    /** 输出目录 */
    outputDir?: string;
    /** 公共路径 */
    publicPath?: string;
    /** 是否生成 source map */
    sourceMap?: boolean;
    /** 是否压缩代码 */
    minify?: boolean;
    /** 是否分析包大小 */
    analyze?: boolean;
    /** HTML 模板路径 */
    template?: string;
    /** 路径别名 */
    alias?: Record<string, string>;
}

/**
 * Webpack 开发服务器配置
 */
export interface WebpackDevServerConfig {
    /** 端口号 */
    port?: number;
    /** 主机地址 */
    host?: string;
    /** 是否启用热更新 */
    hot?: boolean;
    /** 是否自动打开浏览器 */
    open?: boolean;
    /** 是否启用 HTTPS */
    https?: boolean;
    /** 静态文件目录 */
    static?: {
        directory: string;
        publicPath?: string;
    };
    /** 代理配置 */
    proxy?: Record<string, any>;
    /** 历史路由回退 */
    historyApiFallback?: boolean | any;
    /** 压缩 */
    compress?: boolean;
    /** 客户端配置 */
    client?: {
        logging?: 'none' | 'error' | 'warn' | 'info' | 'log' | 'verbose';
        overlay?: boolean | {
            errors?: boolean;
            warnings?: boolean;
        };
        progress?: boolean;
    };
}

/**
 * Webpack 构建结果
 */
export interface WebpackBuildResult extends BuildResult {
    /** Webpack 特定元数据 */
    metadata: {
        webpack: {
            version: string;
            mode: string;
            chunks: number;
            modules: number;
        };
    };
}

/**
 * Webpack 资源信息
 */
export interface WebpackAsset {
    /** 资源名称 */
    name: string;
    /** 资源大小 */
    size: string;
    /** 资源类型 */
    type: 'javascript' | 'stylesheet' | 'html' | 'image' | 'font' | 'other';
    /** 是否为入口文件 */
    isEntry?: boolean;
    /** 是否为初始文件 */
    isInitial?: boolean;
}

/**
 * Webpack 构建统计信息
 */
export interface WebpackStats {
    /** 构建时间 */
    time: number;
    /** 资源列表 */
    assets: WebpackAsset[];
    /** 代码块数量 */
    chunks: number;
    /** 模块数量 */
    modules: number;
    /** 错误信息 */
    errors: string[];
    /** 警告信息 */
    warnings: string[];
}

/**
 * Webpack 插件配置
 */
export interface WebpackPluginConfig {
    /** HTML 插件配置 */
    html?: {
        template?: string;
        filename?: string;
        inject?: boolean | 'head' | 'body';
        minify?: boolean;
    };
    /** CSS 提取插件配置 */
    extractCss?: {
        filename?: string;
        chunkFilename?: string;
    };
    /** 环境变量插件配置 */
    define?: Record<string, any>;
    /** 复制插件配置 */
    copy?: Array<{
        from: string;
        to: string;
    }>;
}

/**
 * Webpack 优化配置
 */
export interface WebpackOptimizationConfig {
    /** 是否压缩 */
    minimize?: boolean;
    /** 代码分割配置 */
    splitChunks?: {
        chunks?: 'all' | 'async' | 'initial';
        minSize?: number;
        maxSize?: number;
        cacheGroups?: Record<string, any>;
    };
    /** 运行时代码分割 */
    runtimeChunk?: boolean | 'single' | 'multiple';
}

/**
 * Webpack 模块规则
 */
export interface WebpackModuleRule {
    /** 测试正则 */
    test: RegExp;
    /** 排除路径 */
    exclude?: RegExp | string | string[];
    /** 包含路径 */
    include?: RegExp | string | string[];
    /** 使用的加载器 */
    use: string | any[] | any;
    /** 规则类型 */
    type?: string;
}

/**
 * Webpack 构建器选项
 */
export interface WebpackBuilderOptions {
    /** 配置文件路径 */
    configFile?: string;
    /** 是否监听文件变化 */
    watch?: boolean;
    /** 监听选项 */
    watchOptions?: {
        ignored?: RegExp | string | string[];
        poll?: boolean | number;
    };
    /** 是否显示进度 */
    progress?: boolean;
    /** 统计信息配置 */
    stats?: 'none' | 'errors-only' | 'minimal' | 'normal' | 'verbose' | any;
}