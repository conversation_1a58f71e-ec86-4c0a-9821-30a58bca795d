/**
 * @fileoverview Unit tests for WebpackBuilder
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import { BaseBuilderConfig } from '@micro-core/shared/types';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { WebpackBuilder } from '../../src/webpack-builder';

// Mock webpack
vi.mock('webpack', () => ({
  default: vi.fn(),
  webpack: vi.fn()
}));

// Mock shared utilities
vi.mock('../../../shared/utils', () => ({
  Logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn()
  },
  ConfigMerger: {
    deepMerge: vi.fn((a, b) => ({ ...a, ...b }))
  },
  PerformanceMonitor: {
    startTimer: vi.fn(),
    endTimer: vi.fn(() => 100)
  },
  ErrorHandler: {
    formatError: vi.fn((error) => ({ message: error.message }))
  }
}));

describe('WebpackBuilder', () => {
  let builder: WebpackBuilder;
  let mockConfig: BaseBuilderConfig;

  beforeEach(() => {
    builder = new WebpackBuilder();
    mockConfig = {
      id: 'test-app',
      name: 'test-app',
      entry: './src/index.js',
      outDir: './dist',
      mode: 'development'
    };

    // Clear all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Constructor', () => {
    it('should create WebpackBuilder instance with correct name and version', () => {
      expect(builder.name).toBe('webpack');
      expect(builder.version).toBeDefined();
      expect(typeof builder.version).toBe('string');
    });

    it('should initialize with default options', () => {
      const builderWithOptions = new WebpackBuilder({
        logLevel: 'info'
      });
      expect(builderWithOptions).toBeInstanceOf(WebpackBuilder);
    });
  });

  describe('createBuilderConfig', () => {
    it('should create valid webpack configuration', () => {
      const webpackConfig = builder.createBuilderConfig(mockConfig);

      expect(webpackConfig).toBeDefined();
      expect(webpackConfig.entry).toBe(mockConfig.entry);
      expect(webpackConfig.mode).toBe(mockConfig.mode);
      expect(webpackConfig.output).toBeDefined();
      expect(webpackConfig.output.path).toContain('dist');
    });

    it('should handle production mode configuration', () => {
      const prodConfig = { ...mockConfig, mode: 'production' as const };
      const webpackConfig = builder.createBuilderConfig(prodConfig);

      expect(webpackConfig.mode).toBe('production');
      expect(webpackConfig.optimization).toBeDefined();
      expect(webpackConfig.optimization.minimize).toBe(true);
    });

    it('should include micro-frontend specific plugins', () => {
      const webpackConfig = builder.createBuilderConfig(mockConfig);

      expect(webpackConfig.plugins).toBeDefined();
      expect(Array.isArray(webpackConfig.plugins)).toBe(true);
      expect(webpackConfig.plugins.length).toBeGreaterThan(0);
    });

    it('should handle custom webpack configuration merge', () => {
      const configWithCustomWebpack = {
        ...mockConfig,
        webpackConfig: {
          resolve: {
            alias: {
              '@custom': './src/custom'
            }
          }
        }
      };

      const webpackConfig = builder.createBuilderConfig(configWithCustomWebpack);
      expect(webpackConfig.resolve.alias['@custom']).toBe('./src/custom');
    });
  });

  describe('executeBuild', () => {
    it('should execute webpack build successfully', async () => {
      const mockWebpack = vi.fn().mockImplementation((config, callback) => {
        // Simulate successful build
        setTimeout(() => {
          callback(null, {
            compilation: {
              assets: {
                'main.js': { size: () => 1024 },
                'main.css': { size: () => 512 }
              },
              errors: [],
              warnings: []
            }
          });
        }, 10);
        return { run: vi.fn() };
      });

      vi.doMock('webpack', () => ({ default: mockWebpack }));

      const webpackConfig = builder.createBuilderConfig(mockConfig);
      const result = await builder.executeBuild(webpackConfig);

      expect(result.success).toBe(true);
      expect(result.outputs).toBeDefined();
      expect(result.stats).toBeDefined();
      expect(result.stats.duration).toBeGreaterThan(0);
    });

    it('should handle build errors gracefully', async () => {
      const mockWebpack = vi.fn().mockImplementation((config, callback) => {
        setTimeout(() => {
          callback(new Error('Build failed'), null);
        }, 10);
        return { run: vi.fn() };
      });

      vi.doMock('webpack', () => ({ default: mockWebpack }));

      const webpackConfig = builder.createBuilderConfig(mockConfig);
      const result = await builder.executeBuild(webpackConfig);

      expect(result.success).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should handle compilation warnings', async () => {
      const mockWebpack = vi.fn().mockImplementation((config, callback) => {
        setTimeout(() => {
          callback(null, {
            compilation: {
              assets: {},
              errors: [],
              warnings: [{ message: 'Test warning' }]
            }
          });
        }, 10);
        return { run: vi.fn() };
      });

      vi.doMock('webpack', () => ({ default: mockWebpack }));

      const webpackConfig = builder.createBuilderConfig(mockConfig);
      const result = await builder.executeBuild(webpackConfig);

      expect(result.success).toBe(true);
      expect(result.warnings).toBeDefined();
      expect(result.warnings.length).toBeGreaterThan(0);
    });
  });

  describe('startDevServer', () => {
    it('should start webpack dev server successfully', async () => {
      const mockDevServer = {
        listen: vi.fn((port, host, callback) => {
          setTimeout(() => callback(), 10);
        }),
        close: vi.fn((callback) => {
          setTimeout(() => callback(), 10);
        })
      };

      const mockWebpack = vi.fn().mockReturnValue({
        run: vi.fn()
      });

      vi.doMock('webpack', () => ({ default: mockWebpack }));
      vi.doMock('webpack-dev-server', () => ({
        default: vi.fn().mockImplementation(() => mockDevServer)
      }));

      const webpackConfig = builder.createBuilderConfig(mockConfig);
      const devServerConfig = {
        port: 3000,
        host: 'localhost',
        hot: true
      };

      const server = await builder.startDevServer(webpackConfig, devServerConfig);

      expect(server).toBeDefined();
      expect(mockDevServer.listen).toHaveBeenCalled();
    });

    it('should handle dev server start errors', async () => {
      const mockDevServer = {
        listen: vi.fn((port, host, callback) => {
          setTimeout(() => callback(new Error('Port already in use')), 10);
        })
      };

      vi.doMock('webpack-dev-server', () => ({
        default: vi.fn().mockImplementation(() => mockDevServer)
      }));

      const webpackConfig = builder.createBuilderConfig(mockConfig);
      const devServerConfig = { port: 3000 };

      await expect(builder.startDevServer(webpackConfig, devServerConfig))
        .rejects.toThrow('Port already in use');
    });
  });

  describe('stopDevServer', () => {
    it('should stop dev server successfully', async () => {
      const mockServer = {
        close: vi.fn((callback) => {
          setTimeout(() => callback(), 10);
        })
      };

      await expect(builder.stopDevServer(mockServer)).resolves.not.toThrow();
      expect(mockServer.close).toHaveBeenCalled();
    });

    it('should handle server stop errors', async () => {
      const mockServer = {
        close: vi.fn((callback) => {
          setTimeout(() => callback(new Error('Failed to close server')), 10);
        })
      };

      await expect(builder.stopDevServer(mockServer))
        .rejects.toThrow('Failed to close server');
    });
  });

  describe('Performance and Edge Cases', () => {
    it('should handle large number of assets efficiently', async () => {
      const largeAssetMap = {};
      for (let i = 0; i < 1000; i++) {
        largeAssetMap[`chunk-${i}.js`] = { size: () => 1024 };
      }

      const mockWebpack = vi.fn().mockImplementation((config, callback) => {
        setTimeout(() => {
          callback(null, {
            compilation: {
              assets: largeAssetMap,
              errors: [],
              warnings: []
            }
          });
        }, 10);
        return { run: vi.fn() };
      });

      vi.doMock('webpack', () => ({ default: mockWebpack }));

      const webpackConfig = builder.createBuilderConfig(mockConfig);
      const startTime = Date.now();
      const result = await builder.executeBuild(webpackConfig);
      const duration = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(result.outputs.length).toBe(1000);
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should handle empty configuration gracefully', () => {
      const emptyConfig = {
        id: '',
        name: '',
        entry: '',
        outDir: '',
        mode: 'development' as const
      };

      expect(() => builder.createBuilderConfig(emptyConfig)).not.toThrow();
    });

    it('should validate configuration parameters', () => {
      const invalidConfig = {
        ...mockConfig,
        mode: 'invalid-mode' as any
      };

      // Should handle invalid mode gracefully
      const webpackConfig = builder.createBuilderConfig(invalidConfig);
      expect(webpackConfig.mode).toBe('development'); // Should fallback to default
    });
  });

  describe('Integration with BaseBuilder', () => {
    it('should properly extend BaseBuilder functionality', () => {
      expect(builder).toHaveProperty('name');
      expect(builder).toHaveProperty('version');
      expect(builder).toHaveProperty('build');
      expect(builder).toHaveProperty('serve');
      expect(builder).toHaveProperty('stop');
    });

    it('should emit events during build process', async () => {
      const eventSpy = vi.spyOn(builder as any, 'emit');

      const mockWebpack = vi.fn().mockImplementation((config, callback) => {
        setTimeout(() => {
          callback(null, {
            compilation: {
              assets: {},
              errors: [],
              warnings: []
            }
          });
        }, 10);
        return { run: vi.fn() };
      });

      vi.doMock('webpack', () => ({ default: mockWebpack }));

      await builder.build(mockConfig);

      expect(eventSpy).toHaveBeenCalledWith('build:start', expect.any(Object));
      expect(eventSpy).toHaveBeenCalledWith('build:complete', expect.any(Object));
    });
  });
});
