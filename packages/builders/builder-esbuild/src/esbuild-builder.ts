/**
 * @fileoverview ESBuild Builder Implementation
 * @description ESBuild 构建器实现，提供快速构建支持
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { BaseBuilderConfig, BuildResult } from '@micro-core/shared/types';
import { formatBytes, formatTime } from '@micro-core/shared/utils';
import * as esbuild from 'esbuild';

/**
 * ESBuild 构建器配置
 */
export interface ESBuildBuilderConfig extends BaseBuilderConfig {
  /** ESBuild 配置 */
  esbuildConfig?: esbuild.BuildOptions;
  /** 是否启用监听模式 */
  watch?: boolean;
  /** 是否启用开发服务器 */
  serve?: boolean;
  /** 开发服务器配置 */
  serveOptions?: esbuild.ServeOptions;
  /** 构建模式 */
  mode?: 'development' | 'production';
  /** 输出目录 */
  outdir?: string;
  /** 输出文件 */
  outfile?: string;
  /** 目标环境 */
  target?: string | string[];
  /** 输出格式 */
  format?: 'iife' | 'cjs' | 'esm';
  /** 是否生成 source map */
  sourcemap?: boolean | 'inline' | 'external' | 'both';
  /** 是否压缩代码 */
  minify?: boolean;
  /** 是否分割代码 */
  splitting?: boolean;
  /** 外部依赖 */
  external?: string[];
  /** 平台 */
  platform?: 'browser' | 'node' | 'neutral';
}

/**
 * ESBuild 构建器类
 */
export class ESBuildBuilder {
  private config: ESBuildBuilderConfig;
  private context: esbuild.BuildContext | null = null;

  constructor(config: ESBuildBuilderConfig) {
    this.config = {
      mode: 'production',
      outdir: 'dist',
      target: 'es2020',
      format: 'esm',
      sourcemap: false,
      minify: true,
      splitting: false,
      platform: 'browser',
      watch: false,
      serve: false,
      ...config
    };
  }

  /**
   * 构建应用
   */
  async build(): Promise<BuildResult> {
    const startTime = Date.now();

    try {
      // 创建 ESBuild 配置
      const buildOptions = this.createBuildOptions();

      // 执行构建
      const result = await esbuild.build(buildOptions);

      // 处理构建结果
      const buildResult = this.processBuildResult(result, startTime);

      return buildResult;
    } catch (error) {
      throw new Error(`ESBuild 构建失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 启动开发服务器
   */
  async serve(): Promise<void> {
    if (!this.config.serve) {
      throw new Error('开发服务器未启用');
    }

    try {
      const buildOptions = this.createBuildOptions();

      // 创建构建上下文
      this.context = await esbuild.context(buildOptions);

      // 启动开发服务器
      const serveResult = await this.context.serve(this.config.serveOptions || {
        port: 3000,
        host: 'localhost'
      });

      console.log(`ESBuild 开发服务器已启动: http://${serveResult.host}:${serveResult.port}`);
    } catch (error) {
      throw new Error(`启动开发服务器失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 启动监听模式
   */
  async watch(callback?: (result: esbuild.BuildResult) => void): Promise<void> {
    if (!this.config.watch) {
      throw new Error('监听模式未启用');
    }

    try {
      const buildOptions = this.createBuildOptions();

      // 创建构建上下文
      this.context = await esbuild.context(buildOptions);

      // 启动监听
      await this.context.watch();

      console.log('ESBuild 监听模式已启动');

      // 如果提供了回调，执行初始构建
      if (callback) {
        const result = await esbuild.build(buildOptions);
        callback(result);
      }
    } catch (error) {
      throw new Error(`启动监听模式失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 停止构建器
   */
  async stop(): Promise<void> {
    if (this.context) {
      await this.context.dispose();
      this.context = null;
      console.log('ESBuild 构建器已停止');
    }
  }

  /**
   * 创建 ESBuild 配置
   */
  private createBuildOptions(): esbuild.BuildOptions {
    const isDev = this.config.mode === 'development';

    const baseOptions: esbuild.BuildOptions = {
      entryPoints: [this.config.entry || 'src/index.ts'],
      outdir: this.config.outdir,
      outfile: this.config.outfile,
      bundle: true,
      format: this.config.format,
      target: this.config.target,
      platform: this.config.platform,
      sourcemap: this.config.sourcemap,
      minify: this.config.minify && !isDev,
      splitting: this.config.splitting && this.config.format === 'esm',
      external: this.config.external || [],
      define: {
        'process.env.NODE_ENV': JSON.stringify(this.config.mode || 'production'),
        '__MICRO_CORE_DEV__': String(isDev),
        '__MICRO_CORE_PROD__': String(!isDev)
      },
      loader: {
        '.png': 'file',
        '.jpg': 'file',
        '.jpeg': 'file',
        '.gif': 'file',
        '.svg': 'file',
        '.woff': 'file',
        '.woff2': 'file',
        '.eot': 'file',
        '.ttf': 'file'
      },
      resolveExtensions: ['.tsx', '.ts', '.jsx', '.js', '.json'],
      metafile: true, // 生成元数据用于分析
      write: true,
      logLevel: 'info'
    };

    // 合并用户自定义配置
    if (this.config.esbuildConfig) {
      return { ...baseOptions, ...this.config.esbuildConfig };
    }

    return baseOptions;
  }

  /**
   * 处理构建结果
   */
  private processBuildResult(result: esbuild.BuildResult, startTime: number): BuildResult {
    const endTime = Date.now();
    const buildTime = endTime - startTime;

    // 处理输出文件
    const assets: any[] = [];
    let totalSize = 0;

    if (result.metafile) {
      Object.entries(result.metafile.outputs).forEach(([path, output]) => {
        const size = output.bytes || 0;
        totalSize += size;

        assets.push({
          name: path.replace(/^dist\//, ''),
          size: formatBytes(size),
          type: this.getAssetType(path)
        });
      });
    }

    // 处理错误和警告
    const errors = result.errors.map(error => ({
      message: error.text,
      location: error.location ? `${error.location.file}:${error.location.line}:${error.location.column}` : undefined
    }));

    const warnings = result.warnings.map(warning => warning.text);

    return {
      success: errors.length === 0,
      buildTime: formatTime(buildTime),
      outputSize: formatBytes(totalSize),
      assets,
      warnings,
      errors,
      metadata: {
        esbuild: {
          version: this.getESBuildVersion(),
          mode: this.config.mode,
          format: this.config.format,
          platform: this.config.platform,
          target: this.config.target
        }
      }
    };
  }

  /**
   * 获取资源类型
   */
  private getAssetType(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'js':
      case 'mjs':
        return 'javascript';
      case 'css':
        return 'stylesheet';
      case 'html':
        return 'html';
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return 'image';
      case 'woff':
      case 'woff2':
      case 'eot':
      case 'ttf':
        return 'font';
      default:
        return 'other';
    }
  }

  /**
   * 获取 ESBuild 版本
   */
  private getESBuildVersion(): string {
    try {
      return require('esbuild/package.json').version;
    } catch {
      return 'unknown';
    }
  }

  /**
   * 获取构建器信息
   */
  getInfo(): any {
    return {
      name: 'ESBuildBuilder',
      version: '1.0.0',
      type: 'esbuild',
      supportedFormats: ['iife', 'cjs', 'esm'],
      features: [
        'Ultra Fast Building',
        'TypeScript Support',
        'JSX Support',
        'Tree Shaking',
        'Code Splitting',
        'Source Maps',
        'Watch Mode',
        'Dev Server'
      ]
    };
  }
}

/**
 * 创建 ESBuild 构建器实例
 */
export function createESBuildBuilder(config: ESBuildBuilderConfig): ESBuildBuilder {
  return new ESBuildBuilder(config);
}

/**
 * ESBuild 构建器工厂
 */
export const ESBuildBuilderFactory = {
  type: 'esbuild',
  create: createESBuildBuilder,
  metadata: {
    name: 'ESBuild Builder',
    version: '1.0.0',
    description: 'ESBuild 构建器，提供极速构建体验',
    author: 'Echo <<EMAIL>>'
  }
};

export default ESBuildBuilder;