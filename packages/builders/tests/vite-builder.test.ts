/**
 * Vite 构建器测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { ViteBuilder } from '../builder-vite/src';
import { BuilderOptions } from '../shared/types';
import * as testUtils from './test-utils';

// 模拟 vite 依赖
vi.mock('vite', () => ({
    build: vi.fn().mockResolvedValue(undefined),
    createServer: vi.fn().mockResolvedValue({
        listen: vi.fn().mockResolvedValue(undefined),
        close: vi.fn().mockResolvedValue(undefined)
    })
}));

import * as vite from 'vite';

describe('ViteBuilder', () => {
    let builder: ViteBuilder;
    let mockOptions: BuilderOptions;

    beforeEach(() => {
        // 重置所有模拟
        vi.resetAllMocks();

        // 创建模拟选项
        mockOptions = {
            entry: './src/index.js',
            output: {
                path: './dist',
                filename: 'bundle.js'
            },
            mode: 'development',
            devServer: {
                port: 3000
            }
        };

        // 创建构建器实例
        builder = new ViteBuilder();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('构建配置', () => {
        it('应该能够创建有效的 Vite 配置', () => {
            const config = builder.createConfig(mockOptions);
            expect(config).toBeDefined();
            expect(config.root).toBeDefined();
            expect(config.build).toBeDefined();
            expect(config.server).toBeDefined();
        });

        it('应该能够处理自定义配置', () => {
            const customOptions = {
                ...mockOptions,
                customConfig: {
                    resolve: {
                        alias: {
                            '@': './src'
                        }
                    }
                }
            };

            const config = builder.createConfig(customOptions);
            expect(config.resolve).toBeDefined();
            expect(config.resolve.alias).toBeDefined();
        });

        it('应该能够处理插件配置', () => {
            const pluginOptions = {
                ...mockOptions,
                plugins: [
                    { name: 'vite-plugin-react', options: {} }
                ]
            };

            const config = builder.createConfig(pluginOptions);
            expect(config.plugins).toBeDefined();
            expect(config.plugins.length).toBeGreaterThan(0);
        });
    });

    describe('构建过程', () => {
        it('应该能够执行构建', async () => {
            await builder.build(mockOptions);
            expect(vite.build).toHaveBeenCalled();
        });

        it('应该在构建失败时抛出错误', async () => {
            (vite.build as any).mockRejectedValue(new Error('构建失败'));
            await expect(builder.build(mockOptions)).rejects.toThrow('构建失败');
        });

        it('应该能够处理构建警告', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });

            (vite.build as any).mockImplementation(() => {
                console.warn('构建警告');
                return Promise.resolve();
            });

            await builder.build(mockOptions);
            expect(consoleSpy).toHaveBeenCalled();
        });
    });

    describe('开发服务器', () => {
        it('应该能够启动开发服务器', async () => {
            await builder.serve(mockOptions);
            expect(vite.createServer).toHaveBeenCalled();
        });

        it('应该能够停止开发服务器', async () => {
            const mockServer = {
                close: vi.fn().mockResolvedValue(undefined)
            };
            (builder as any).server = mockServer;

            await builder.stop();
            expect(mockServer.close).toHaveBeenCalled();
        });

        it('应该在开发服务器启动失败时抛出错误', async () => {
            (vite.createServer as any).mockRejectedValue(new Error('服务器启动失败'));
            await expect(builder.serve(mockOptions)).rejects.toThrow('服务器启动失败');
        });
    });

    describe('构建验证', () => {
        it('应该能够验证构建产物', async () => {
            const validateSpy = vi.spyOn(testUtils, 'validateBuildOutput').mockResolvedValue(true);

            await builder.build(mockOptions);
            expect(validateSpy).toHaveBeenCalled();
        });

        it('应该能够检测构建产物完整性', async () => {
            const checkIntegritySpy = vi.spyOn(testUtils, 'checkBuildIntegrity').mockResolvedValue(true);

            await builder.build(mockOptions);
            expect(checkIntegritySpy).toHaveBeenCalled();
        });
    });

    describe('错误处理', () => {
        it('应该能够处理配置错误', () => {
            const invalidOptions = {
                ...mockOptions,
                entry: undefined
            } as any;

            expect(() => builder.createConfig(invalidOptions)).toThrow();
        });

        it('应该能够处理构建过程错误', async () => {
            (vite.build as any).mockRejectedValue(new Error('构建过程错误'));
            await expect(builder.build(mockOptions)).rejects.toThrow('构建过程错误');
        });

        it('应该能够处理开发服务器错误', async () => {
            (vite.createServer as any).mockRejectedValue(new Error('服务器错误'));
            await expect(builder.serve(mockOptions)).rejects.toThrow('服务器错误');
        });
    });

    describe('HMR 支持', () => {
        it('应该能够配置热模块替换', () => {
            const hmrOptions = {
                ...mockOptions,
                devServer: {
                    ...mockOptions.devServer,
                    hmr: true
                }
            };

            const config = builder.createConfig(hmrOptions);
            expect(config.server.hmr).toBeTruthy();
        });

        it('应该能够处理 HMR 连接错误', async () => {
            const mockServer = {
                listen: vi.fn().mockImplementation(() => {
                    throw new Error('HMR 连接错误');
                }),
                close: vi.fn().mockResolvedValue(undefined)
            };
            (vite.createServer as any).mockResolvedValue(mockServer);

            await expect(builder.serve(mockOptions)).rejects.toThrow('HMR 连接错误');
        });
    });

    describe('构建优化', () => {
        it('应该能够配置构建优化选项', () => {
            const optimizeOptions = {
                ...mockOptions,
                optimize: {
                    minify: true,
                    target: 'es2015'
                }
            };

            const config = builder.createConfig(optimizeOptions);
            expect(config.build.minify).toBe(true);
            expect(config.build.target).toBe('es2015');
        });

        it('应该能够配置依赖预构建', () => {
            const optimizeOptions = {
                ...mockOptions,
                optimize: {
                    deps: {
                        force: true
                    }
                }
            };

            const config = builder.createConfig(optimizeOptions);
            expect(config.optimizeDeps).toBeDefined();
            expect(config.optimizeDeps.force).toBe(true);
        });
    });
});