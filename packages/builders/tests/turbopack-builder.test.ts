/**
 * Turbopack 构建器测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { TurbopackBuilder } from '../builder-turbopack/src';
import { BuilderOptions } from '../shared/types';
import * as testUtils from './test-utils';

// 模拟 @vercel/turbopack 依赖
vi.mock('@vercel/turbopack', () => ({
    build: vi.fn().mockResolvedValue({
        errors: [],
        warnings: []
    }),
    createServer: vi.fn().mockResolvedValue({
        start: vi.fn().mockResolvedValue(undefined),
        stop: vi.fn().mockResolvedValue(undefined)
    })
}));

import * as turbopack from '@vercel/turbopack';

describe('TurbopackBuilder', () => {
    let builder: TurbopackBuilder;
    let mockOptions: BuilderOptions;

    beforeEach(() => {
        // 重置所有模拟
        vi.resetAllMocks();

        // 创建模拟选项
        mockOptions = {
            entry: './src/index.js',
            output: {
                path: './dist',
                filename: 'bundle.js'
            },
            mode: 'development',
            devServer: {
                port: 3000
            }
        };

        // 创建构建器实例
        builder = new TurbopackBuilder();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('构建配置', () => {
        it('应该能够创建有效的 Turbopack 配置', () => {
            const config = builder.createConfig(mockOptions);
            expect(config).toBeDefined();
            expect(config.entry).toBe(mockOptions.entry);
            expect(config.outputPath).toBe(mockOptions.output.path);
            expect(config.mode).toBe(mockOptions.mode);
        });

        it('应该能够处理自定义配置', () => {
            const customOptions = {
                ...mockOptions,
                customConfig: {
                    resolve: {
                        alias: {
                            '@': './src'
                        }
                    }
                }
            };

            const config = builder.createConfig(customOptions);
            expect(config.resolve).toBeDefined();
            expect(config.resolve.alias).toBeDefined();
        });

        it('应该能够处理插件配置', () => {
            const pluginOptions = {
                ...mockOptions,
                plugins: [
                    { name: 'turbopack-plugin-html', options: {} }
                ]
            };

            const config = builder.createConfig(pluginOptions);
            expect(config.plugins).toBeDefined();
            expect(config.plugins.length).toBeGreaterThan(0);
        });
    });

    describe('构建过程', () => {
        it('应该能够执行构建', async () => {
            await builder.build(mockOptions);
            expect(turbopack.build).toHaveBeenCalled();
        });

        it('应该在构建失败时抛出错误', async () => {
            (turbopack.build as any).mockResolvedValue({
                errors: ['构建错误'],
                warnings: []
            });
            await expect(builder.build(mockOptions)).rejects.toThrow();
        });

        it('应该能够处理构建警告', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });

            (turbopack.build as any).mockResolvedValue({
                errors: [],
                warnings: ['构建警告']
            });

            await builder.build(mockOptions);
            expect(consoleSpy).toHaveBeenCalled();
        });
    });

    describe('开发服务器', () => {
        it('应该能够启动开发服务器', async () => {
            await builder.serve(mockOptions);
            expect(turbopack.createServer).toHaveBeenCalled();
        });

        it('应该能够停止开发服务器', async () => {
            const mockServer = {
                stop: vi.fn().mockResolvedValue(undefined)
            };
            (builder as any).server = mockServer;

            await builder.stop();
            expect(mockServer.stop).toHaveBeenCalled();
        });

        it('应该在开发服务器启动失败时抛出错误', async () => {
            (turbopack.createServer as any).mockRejectedValue(new Error('服务器启动失败'));
            await expect(builder.serve(mockOptions)).rejects.toThrow('服务器启动失败');
        });
    });

    describe('构建验证', () => {
        it('应该能够验证构建产物', async () => {
            const validateSpy = vi.spyOn(testUtils, 'validateBuildOutput').mockResolvedValue(true);

            await builder.build(mockOptions);
            expect(validateSpy).toHaveBeenCalled();
        });

        it('应该能够检测构建产物完整性', async () => {
            const checkIntegritySpy = vi.spyOn(testUtils, 'checkBuildIntegrity').mockResolvedValue(true);

            await builder.build(mockOptions);
            expect(checkIntegritySpy).toHaveBeenCalled();
        });
    });

    describe('错误处理', () => {
        it('应该能够处理配置错误', () => {
            const invalidOptions = {
                ...mockOptions,
                entry: undefined
            } as any;

            expect(() => builder.createConfig(invalidOptions)).toThrow();
        });

        it('应该能够处理构建过程错误', async () => {
            (turbopack.build as any).mockRejectedValue(new Error('构建过程错误'));
            await expect(builder.build(mockOptions)).rejects.toThrow('构建过程错误');
        });

        it('应该能够处理开发服务器错误', async () => {
            (turbopack.createServer as any).mockRejectedValue(new Error('服务器错误'));
            await expect(builder.serve(mockOptions)).rejects.toThrow('服务器错误');
        });
    });

    describe('Rust 引擎优化', () => {
        it('应该能够配置 Rust 引擎优化选项', () => {
            const rustOptions = {
                ...mockOptions,
                rustEngine: {
                    threads: 4,
                    memoryLimit: 1024
                }
            };

            const config = builder.createConfig(rustOptions);
            expect(config.rustEngine).toBeDefined();
            expect(config.rustEngine.threads).toBe(4);
            expect(config.rustEngine.memoryLimit).toBe(1024);
        });
    });

    describe('增量构建', () => {
        it('应该能够配置增量构建选项', () => {
            const incrementalOptions = {
                ...mockOptions,
                incremental: true
            };

            const config = builder.createConfig(incrementalOptions);
            expect(config.incremental).toBe(true);
        });

        it('应该能够处理增量构建缓存', async () => {
            const cacheOptions = {
                ...mockOptions,
                incremental: true,
                cache: {
                    directory: '.turbo-cache'
                }
            };

            const config = builder.createConfig(cacheOptions);
            expect(config.cache).toBeDefined();
            expect(config.cache.directory).toBe('.turbo-cache');
        });
    });

    describe('Next.js 集成', () => {
        it('应该能够配置 Next.js 集成选项', () => {
            const nextjsOptions = {
                ...mockOptions,
                nextjs: {
                    enabled: true,
                    pagesDir: './pages'
                }
            };

            const config = builder.createConfig(nextjsOptions);
            expect(config.nextjs).toBeDefined();
            expect(config.nextjs.enabled).toBe(true);
            expect(config.nextjs.pagesDir).toBe('./pages');
        });
    });
});