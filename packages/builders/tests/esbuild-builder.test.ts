/**
 * ESBuild 构建器测试
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { ESBuildBuilder } from '../builder-esbuild/src';
import { BuilderOptions } from '../shared/types';
import * as testUtils from './test-utils';

// 模拟 esbuild 依赖
vi.mock('esbuild', () => ({
    build: vi.fn().mockResolvedValue({
        errors: [],
        warnings: []
    }),
    serve: vi.fn().mockResolvedValue({
        stop: vi.fn().mockResolvedValue(undefined)
    }),
    context: vi.fn().mockResolvedValue({
        watch: vi.fn().mockResolvedValue(undefined),
        dispose: vi.fn().mockResolvedValue(undefined),
        serve: vi.fn().mockResolvedValue({
            stop: vi.fn().mockResolvedValue(undefined)
        })
    })
}));

import * as esbuild from 'esbuild';

describe('ESBuildBuilder', () => {
    let builder: ESBuildBuilder;
    let mockOptions: BuilderOptions;

    beforeEach(() => {
        // 重置所有模拟
        vi.resetAllMocks();

        // 创建模拟选项
        mockOptions = {
            entry: './src/index.js',
            output: {
                path: './dist',
                filename: 'bundle.js'
            },
            mode: 'development'
        };

        // 创建构建器实例
        builder = new ESBuildBuilder();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('构建配置', () => {
        it('应该能够创建有效的 ESBuild 配置', () => {
            const config = builder.createConfig(mockOptions);
            expect(config).toBeDefined();
            expect(config.entryPoints).toContain(mockOptions.entry);
            expect(config.outfile).toContain(mockOptions.output.filename);
        });

        it('应该能够处理自定义配置', () => {
            const customOptions = {
                ...mockOptions,
                customConfig: {
                    target: ['es2020', 'chrome80']
                }
            };

            const config = builder.createConfig(customOptions);
            expect(config.target).toEqual(['es2020', 'chrome80']);
        });

        it('应该能够处理插件配置', () => {
            const pluginOptions = {
                ...mockOptions,
                plugins: [
                    { name: 'esbuild-plugin-html', options: {} }
                ]
            };

            const config = builder.createConfig(pluginOptions);
            expect(config.plugins).toBeDefined();
            expect(config.plugins.length).toBeGreaterThan(0);
        });
    });

    describe('构建过程', () => {
        it('应该能够执行构建', async () => {
            await builder.build(mockOptions);
            expect(esbuild.build).toHaveBeenCalled();
        });

        it('应该在构建失败时抛出错误', async () => {
            (esbuild.build as any).mockResolvedValue({
                errors: [{ text: '构建错误' }],
                warnings: []
            });
            await expect(builder.build(mockOptions)).rejects.toThrow();
        });

        it('应该能够处理构建警告', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });

            (esbuild.build as any).mockResolvedValue({
                errors: [],
                warnings: [{ text: '构建警告' }]
            });

            await builder.build(mockOptions);
            expect(consoleSpy).toHaveBeenCalled();
        });
    });

    describe('开发服务器', () => {
        it('应该能够启动开发服务器', async () => {
            await builder.serve(mockOptions);
            expect(esbuild.context).toHaveBeenCalled();
        });

        it('应该能够停止开发服务器', async () => {
            const mockServer = {
                stop: vi.fn().mockResolvedValue(undefined)
            };
            const mockContext = {
                dispose: vi.fn().mockResolvedValue(undefined),
                serve: vi.fn().mockResolvedValue(mockServer)
            };
            (esbuild.context as any).mockResolvedValue(mockContext);

            await builder.serve(mockOptions);
            await builder.stop();

            expect(mockServer.stop).toHaveBeenCalled();
            expect(mockContext.dispose).toHaveBeenCalled();
        });

        it('应该在开发服务器启动失败时抛出错误', async () => {
            (esbuild.context as any).mockRejectedValue(new Error('服务器启动失败'));
            await expect(builder.serve(mockOptions)).rejects.toThrow('服务器启动失败');
        });
    });

    describe('构建验证', () => {
        it('应该能够验证构建产物', async () => {
            const validateSpy = vi.spyOn(testUtils, 'validateBuildOutput').mockResolvedValue(true);

            await builder.build(mockOptions);
            expect(validateSpy).toHaveBeenCalled();
        });

        it('应该能够检测构建产物完整性', async () => {
            const checkIntegritySpy = vi.spyOn(testUtils, 'checkBuildIntegrity').mockResolvedValue(true);

            await builder.build(mockOptions);
            expect(checkIntegritySpy).toHaveBeenCalled();
        });
    });

    describe('错误处理', () => {
        it('应该能够处理配置错误', () => {
            const invalidOptions = {
                ...mockOptions,
                entry: undefined
            } as any;

            expect(() => builder.createConfig(invalidOptions)).toThrow();
        });

        it('应该能够处理构建过程错误', async () => {
            (esbuild.build as any).mockRejectedValue(new Error('构建过程错误'));
            await expect(builder.build(mockOptions)).rejects.toThrow('构建过程错误');
        });

        it('应该能够处理开发服务器错误', async () => {
            (esbuild.context as any).mockRejectedValue(new Error('服务器错误'));
            await expect(builder.serve(mockOptions)).rejects.toThrow('服务器错误');
        });
    });

    describe('构建优化', () => {
        it('应该能够配置构建优化选项', () => {
            const optimizeOptions = {
                ...mockOptions,
                optimize: {
                    minify: true,
                    treeShaking: true
                }
            };

            const config = builder.createConfig(optimizeOptions);
            expect(config.minify).toBe(true);
            expect(config.treeShaking).toBe(true);
        });

        it('应该能够配置代码分割', () => {
            const optimizeOptions = {
                ...mockOptions,
                output: {
                    ...mockOptions.output,
                    dir: './dist'
                },
                optimize: {
                    splitting: true
                }
            };

            const config = builder.createConfig(optimizeOptions);
            expect(config.outdir).toBe('./dist');
            expect(config.splitting).toBe(true);
        });
    });

    describe('构建性能', () => {
        it('应该能够配置构建性能选项', () => {
            const performanceOptions = {
                ...mockOptions,
                performance: {
                    concurrent: true,
                    metafile: true
                }
            };

            const config = builder.createConfig(performanceOptions);
            expect(config.metafile).toBe(true);
        });

        it('应该能够生成构建元数据', async () => {
            const metaOptions = {
                ...mockOptions,
                performance: {
                    metafile: true
                }
            };

            const mockMetaResult = {
                errors: [],
                warnings: [],
                metafile: {
                    inputs: {},
                    outputs: {}
                }
            };
            (esbuild.build as any).mockResolvedValue(mockMetaResult);

            await builder.build(metaOptions);
            const config = builder.createConfig(metaOptions);
            expect(config.metafile).toBe(true);
        });
    });
});