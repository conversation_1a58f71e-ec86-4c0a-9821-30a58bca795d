/**
 * @fileoverview Rollup Builder Implementation
 * @description Rollup 构建器实现，提供 Rollup 构建支持
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { BaseBuilderConfig, BuildResult } from '@micro-core/shared/types';
import { formatBytes, formatTime } from '@micro-core/shared/utils';
import { OutputOptions, rollup, RollupOptions, watch } from 'rollup';

/**
 * Rollup 构建器配置
 */
export interface RollupBuilderConfig extends BaseBuilderConfig {
  /** Rollup 配置 */
  rollupConfig?: RollupOptions;
  /** 输出配置 */
  output?: OutputOptions | OutputOptions[];
  /** 是否启用监听模式 */
  watch?: boolean;
  /** 监听配置 */
  watchOptions?: any;
  /** 构建模式 */
  mode?: 'development' | 'production';
  /** 输出目录 */
  outputDir?: string;
  /** 输出格式 */
  format?: 'es' | 'cjs' | 'umd' | 'iife';
  /** 是否生成 source map */
  sourcemap?: boolean;
  /** 是否压缩代码 */
  minify?: boolean;
  /** 外部依赖 */
  external?: string[] | ((id: string) => boolean);
}

/**
 * Rollup 构建器类
 */
export class RollupBuilder {
  private config: RollupBuilderConfig;
  private watcher: any = null;

  constructor(config: RollupBuilderConfig) {
    this.config = {
      mode: 'production',
      outputDir: 'dist',
      format: 'es',
      sourcemap: false,
      minify: true,
      watch: false,
      ...config
    };
  }

  /**
   * 构建应用
   */
  async build(): Promise<BuildResult> {
    const startTime = Date.now();

    try {
      // 创建 Rollup 配置
      const rollupConfig = this.createRollupConfig();

      // 执行构建
      const bundle = await rollup(rollupConfig);

      // 生成输出
      const outputs = Array.isArray(this.config.output)
        ? this.config.output
        : [this.config.output || this.createOutputConfig()];

      const results = [];
      for (const output of outputs) {
        const result = await bundle.generate(output);
        results.push(result);
        await bundle.write(output);
      }

      // 关闭 bundle
      await bundle.close();

      // 处理构建结果
      const buildResult = this.processBuildResult(results, startTime);

      return buildResult;
    } catch (error) {
      throw new Error(`Rollup 构建失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 启动监听模式
   */
  async watch(callback?: (event: string, file?: string) => void): Promise<void> {
    if (!this.config.watch) {
      throw new Error('监听模式未启用');
    }

    try {
      const rollupConfig = this.createRollupConfig();
      const watchOptions = {
        ...rollupConfig,
        watch: this.config.watchOptions || {}
      };

      this.watcher = watch(watchOptions);

      this.watcher.on('event', (event: any) => {
        switch (event.code) {
          case 'START':
            console.log('Rollup 开始构建...');
            callback?.('start');
            break;
          case 'BUNDLE_START':
            console.log('开始打包...');
            callback?.('bundle-start');
            break;
          case 'BUNDLE_END':
            console.log('打包完成');
            callback?.('bundle-end');
            break;
          case 'END':
            console.log('构建完成');
            callback?.('end');
            break;
          case 'ERROR':
            console.error('构建错误:', event.error);
            callback?.('error', event.error?.message);
            break;
        }
      });

      console.log('Rollup 监听模式已启动');
    } catch (error) {
      throw new Error(`启动监听模式失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 停止构建器
   */
  async stop(): Promise<void> {
    if (this.watcher) {
      this.watcher.close();
      this.watcher = null;
      console.log('Rollup 监听器已关闭');
    }
  }

  /**
   * 创建 Rollup 配置
   */
  private createRollupConfig(): RollupOptions {
    const isDev = this.config.mode === 'development';

    const baseConfig: RollupOptions = {
      input: this.config.entry || 'src/index.ts',
      external: this.config.external || this.createExternalConfig(),
      plugins: this.createPlugins(),
      output: this.config.output || this.createOutputConfig()
    };

    // 合并用户自定义配置
    if (this.config.rollupConfig) {
      return { ...baseConfig, ...this.config.rollupConfig };
    }

    return baseConfig;
  }

  /**
   * 创建输出配置
   */
  private createOutputConfig(): OutputOptions {
    const isDev = this.config.mode === 'development';

    return {
      dir: this.config.outputDir,
      format: this.config.format,
      sourcemap: this.config.sourcemap,
      name: this.config.name,
      exports: 'auto',
      chunkFileNames: isDev ? '[name].js' : '[name]-[hash].js',
      entryFileNames: isDev ? '[name].js' : '[name]-[hash].js',
      assetFileNames: isDev ? '[name][extname]' : '[name]-[hash][extname]'
    };
  }

  /**
   * 创建外部依赖配置
   */
  private createExternalConfig(): (string | RegExp)[] {
    return [
      // Node.js 内置模块
      /^node:/,
      // 常见的外部依赖
      'react',
      'react-dom',
      'vue',
      'angular',
      'lodash',
      'moment'
    ];
  }

  /**
   * 创建插件配置
   */
  private createPlugins(): any[] {
    const plugins: any[] = [];
    const isDev = this.config.mode === 'development';

    // Node resolve 插件
    const nodeResolve = require('@rollup/plugin-node-resolve');
    plugins.push(nodeResolve({
      preferBuiltins: false,
      browser: true
    }));

    // CommonJS 插件
    const commonjs = require('@rollup/plugin-commonjs');
    plugins.push(commonjs());

    // TypeScript 插件
    const typescript = require('@rollup/plugin-typescript');
    plugins.push(typescript({
      tsconfig: './tsconfig.json',
      sourceMap: this.config.sourcemap
    }));

    // JSON 插件
    const json = require('@rollup/plugin-json');
    plugins.push(json());

    // 替换插件
    const replace = require('@rollup/plugin-replace');
    plugins.push(replace({
      'process.env.NODE_ENV': JSON.stringify(this.config.mode || 'production'),
      preventAssignment: true
    }));

    // 生产环境插件
    if (!isDev && this.config.minify) {
      const terser = require('@rollup/plugin-terser');
      plugins.push(terser({
        compress: {
          drop_console: true,
          drop_debugger: true
        }
      }));
    }

    return plugins;
  }

  /**
   * 处理构建结果
   */
  private processBuildResult(results: any[], startTime: number): BuildResult {
    const endTime = Date.now();
    const buildTime = endTime - startTime;

    // 计算总大小
    let totalSize = 0;
    const assets: any[] = [];

    results.forEach(result => {
      if (result.output) {
        result.output.forEach((chunk: any) => {
          const size = chunk.code ? chunk.code.length : 0;
          totalSize += size;

          assets.push({
            name: chunk.fileName || chunk.name || 'unknown',
            size: formatBytes(size),
            type: this.getAssetType(chunk.fileName || chunk.name || '')
          });
        });
      }
    });

    return {
      success: true,
      buildTime: formatTime(buildTime),
      outputSize: formatBytes(totalSize),
      assets,
      warnings: [],
      errors: [],
      metadata: {
        rollup: {
          version: this.getRollupVersion(),
          mode: this.config.mode,
          format: this.config.format,
          chunks: assets.length
        }
      }
    };
  }

  /**
   * 获取资源类型
   */
  private getAssetType(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'js':
      case 'mjs':
        return 'javascript';
      case 'css':
        return 'stylesheet';
      case 'html':
        return 'html';
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return 'image';
      default:
        return 'other';
    }
  }

  /**
   * 获取 Rollup 版本
   */
  private getRollupVersion(): string {
    try {
      return require('rollup/package.json').version;
    } catch {
      return 'unknown';
    }
  }

  /**
   * 获取构建器信息
   */
  getInfo(): any {
    return {
      name: 'RollupBuilder',
      version: '1.0.0',
      type: 'rollup',
      supportedFormats: ['es', 'cjs', 'umd', 'iife'],
      features: [
        'Tree Shaking',
        'Code Splitting',
        'TypeScript Support',
        'Source Maps',
        'Watch Mode',
        'Plugin System'
      ]
    };
  }
}

/**
 * 创建 Rollup 构建器实例
 */
export function createRollupBuilder(config: RollupBuilderConfig): RollupBuilder {
  return new RollupBuilder(config);
}

/**
 * Rollup 构建器工厂
 */
export const RollupBuilderFactory = {
  type: 'rollup',
  create: createRollupBuilder,
  metadata: {
    name: 'Rollup Builder',
    version: '1.0.0',
    description: 'Rollup 构建器，专注于库和组件构建',
    author: 'Echo <<EMAIL>>'
  }
};

export default RollupBuilder;