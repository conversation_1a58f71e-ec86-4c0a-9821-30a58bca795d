/**
 * @fileoverview 资源管理器
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { EventEmitter } from 'events';
import { ERROR_CODES, MicroCoreError } from '../errors';
import type { LoadOptions, ResourceInfo } from '../types';
import { createLogger, isValidUrl } from '../utils';

/**
 * 扩展的加载选项
 */
interface ExtendedLoadOptions extends LoadOptions {
    /** 是否强制重新加载 */
    forceReload?: boolean;
    /** 是否为预加载 */
    preload?: boolean;
}

/**
 * 扩展的资源信息
 */
interface ExtendedResourceInfo extends ResourceInfo {
    /** 关联的应用名称 */
    app?: string;
    /** 脚本列表 */
    scripts?: string[];
    /** 样式列表 */
    styles?: string[];
    /** HTML内容 */
    html?: string;
}

/**
 * 资源错误类
 */
class ResourceError extends MicroCoreError {
    constructor(
        message: string,
        public url: string,
        code: string = ERROR_CODES.RESOURCE_LOAD_FAILED,
        context?: Record<string, any>
    ) {
        super(code, message, { url, ...context });
        this.name = 'ResourceError';
    }
}

/**
 * 资源管理器
 * 负责管理微前端应用的资源加载、缓存和预加载
 */
export class ResourceManager extends EventEmitter {
    private readonly logger = createLogger('ResourceManager');
    private readonly cache = new Map<string, ResourceInfo>();
    private readonly loadingPromises = new Map<string, Promise<ResourceInfo>>();
    private started = false;

    constructor() {
        super();
    }

    /**
     * 启动资源管理器
     */
    async start(): Promise<void> {
        if (this.started) {
            this.logger.warn('资源管理器已经启动');
            return;
        }

        this.logger.info('启动资源管理器...');
        this.started = true;
        this.emit('started');
        this.logger.info('资源管理器启动成功');
    }

    /**
     * 停止资源管理器
     */
    async stop(): Promise<void> {
        if (!this.started) {
            this.logger.warn('资源管理器未启动');
            return;
        }

        this.logger.info('停止资源管理器...');
        this.started = false;
        this.cache.clear();
        this.loadingPromises.clear();
        this.emit('stopped');
        this.logger.info('资源管理器停止成功');
    }

    /**
     * 加载应用资源
     * @param entry 应用入口
     * @param options 加载选项
     */
    async loadAppResources(entry: string, options: ExtendedLoadOptions = {}): Promise<ExtendedResourceInfo> {
        const cacheKey = this.getCacheKey(entry, options);

        // 检查缓存
        if (this.cache.has(cacheKey) && !options.forceReload) {
            this.logger.debug(`从缓存加载资源: ${entry}`);
            return this.cache.get(cacheKey) as ExtendedResourceInfo;
        }

        // 检查是否正在加载
        if (this.loadingPromises.has(cacheKey)) {
            this.logger.debug(`等待资源加载完成: ${entry}`);
            return this.loadingPromises.get(cacheKey) as Promise<ExtendedResourceInfo>;
        }

        // 开始加载资源
        const loadingPromise = this.doLoadAppResources(entry, options);
        this.loadingPromises.set(cacheKey, loadingPromise);

        try {
            const resourceInfo = await loadingPromise;

            // 缓存资源信息
            this.cacheResource(cacheKey, resourceInfo);

            this.logger.info(`资源加载成功: ${entry}`);
            return resourceInfo;
        } catch (error) {
            this.logger.error(`资源加载失败: ${entry}`, error);
            throw new ResourceError(
                `应用资源加载失败: ${(error as Error).message}`,
                entry,
                ERROR_CODES.RESOURCE_LOAD_FAILED
            );
        } finally {
            this.loadingPromises.delete(cacheKey);
        }
    }

    /**
     * 实际执行应用资源加载
     */
    private async doLoadAppResources(entry: string, options: ExtendedLoadOptions): Promise<ExtendedResourceInfo> {
        const startTime = Date.now();
        const resourceInfo: ExtendedResourceInfo = {
            url: entry,
            type: RESOURCE_TYPES.HTML,
            loaded: false,
            loadTime: startTime,
            scripts: [],
            styles: []
        };

        try {
            // 加载入口HTML
            const htmlResource = await this.loadResource(entry, options);
            resourceInfo.html = htmlResource.content;
            resourceInfo.loaded = true;
            resourceInfo.loadTime = Date.now() - startTime;

            // 解析HTML中的资源引用
            if (htmlResource.content) {
                const { scripts, styles } = this.parseResourcesFromHTML(htmlResource.content, entry);
                resourceInfo.scripts = scripts;
                resourceInfo.styles = styles;
            }

            this.emit('resourceLoaded', resourceInfo);
            return resourceInfo;
        } catch (error) {
            resourceInfo.error = error as Error;
            resourceInfo.loadTime = Date.now() - startTime;
            throw new ResourceError(
                `解析应用资源失败: ${(error as Error).message}`,
                entry,
                ERROR_CODES.RESOURCE_LOAD_FAILED,
                { originalError: error }
            );
        }
    }

    /**
     * 从HTML中解析资源引用
     */
    private parseResourcesFromHTML(html: string, baseUrl: string): { scripts: string[]; styles: string[] } {
        const scripts: string[] = [];
        const styles: string[] = [];

        // 简单的正则解析（生产环境建议使用专业的HTML解析器）
        const scriptRegex = /<script[^>]*src=["']([^"']+)["'][^>]*>/gi;
        const linkRegex = /<link[^>]*href=["']([^"']+)["'][^>]*rel=["']stylesheet["'][^>]*>/gi;

        let match;
        while ((match = scriptRegex.exec(html)) !== null) {
            const src = this.resolveUrl(match[1], baseUrl);
            scripts.push(src);
        }

        while ((match = linkRegex.exec(html)) !== null) {
            const href = this.resolveUrl(match[1], baseUrl);
            styles.push(href);
        }

        return { scripts, styles };
    }

    /**
     * 解析相对URL
     */
    private resolveUrl(url: string, baseUrl: string): string {
        if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('//')) {
            return url;
        }

        const base = new URL(baseUrl);
        return new URL(url, base.origin + base.pathname).href;
    }

    /**
     * 加载资源
     * @param url 资源URL
     * @param options 加载选项
     */
    async loadResource(url: string, options: LoadOptions = {}): Promise<ResourceInfo> {
        if (!isValidUrl(url)) {
            throw new ResourceError(`无效的资源URL: ${url}`, url, ERROR_CODES.INVALID_ARGUMENT);
        }

        // 检查缓存
        if (options.cache !== false) {
            const cached = this.cache.get(url);
            if (cached && cached.loaded) {
                this.logger.debug(`从缓存加载资源: ${url}`);
                return cached;
            }
        }

        // 检查是否正在加载
        const loadingPromise = this.loadingPromises.get(url);
        if (loadingPromise) {
            this.logger.debug(`等待资源加载完成: ${url}`);
            return loadingPromise;
        }

        // 开始加载
        const promise = this.doLoadResource(url, options);
        this.loadingPromises.set(url, promise);

        try {
            const result = await promise;
            this.cache.set(url, result);
            return result;
        } finally {
            this.loadingPromises.delete(url);
        }
    }

    /**
     * 实际执行资源加载
     */
    private async doLoadResource(url: string, options: LoadOptions): Promise<ResourceInfo> {
        const startTime = Date.now();
        const resourceInfo: ResourceInfo = {
            url,
            type: this.getResourceType(url),
            loaded: false,
            loadTime: startTime
        };

        try {
            const response = await fetch(url, {
                method: 'GET',
                cache: options.cache === false ? 'no-cache' : 'default'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            resourceInfo.content = await response.text();
            resourceInfo.loaded = true;
            resourceInfo.loadTime = Date.now() - startTime;

            this.logger.debug(`资源加载成功: ${url}`);
            this.emit('resourceLoaded', resourceInfo);
            return resourceInfo;
        } catch (loadError) {
            resourceInfo.error = loadError as Error;
            resourceInfo.loadTime = Date.now() - startTime;

            this.logger.error(`资源加载失败: ${url}`, loadError);
            this.emit('loadError', resourceInfo);

            throw new ResourceError(
                `资源加载失败: ${(loadError as Error).message}`,
                url,
                ERROR_CODES.RESOURCE_LOAD_FAILED,
                { originalError: loadError }
            );
        }
    }

    /**
     * 获取资源类型
     */
    private getResourceType(url: string): string {
        const extension = url.split('.').pop()?.toLowerCase();
        switch (extension) {
            case 'js':
                return RESOURCE_TYPES.SCRIPT;
            case 'css':
                return RESOURCE_TYPES.STYLE;
            case 'html':
            case 'htm':
                return RESOURCE_TYPES.HTML;
            default:
                return RESOURCE_TYPES.HTML;
        }
    }

    /**
     * 获取缓存键
     */
    private getCacheKey(url: string, options: ExtendedLoadOptions): string {
        const optionsKey = JSON.stringify({
            cache: options.cache,
            forceReload: options.forceReload
        });
        return `${url}#${optionsKey}`;
    }

    /**
     * 缓存资源
     */
    private cacheResource(key: string, resource: ResourceInfo): void {
        this.cache.set(key, resource);
    }

    /**
     * 清理缓存
     */
    clearCache(url?: string): void {
        if (url) {
            this.cache.delete(url);
            this.logger.debug(`清理缓存: ${url}`);
        } else {
            this.cache.clear();
            this.logger.info('清理所有缓存');
        }
    }

    /**
     * 获取资源信息
     */
    getResource(url: string): ResourceInfo | null {
        return this.cache.get(url) || null;
    }
}

export default ResourceManager;
