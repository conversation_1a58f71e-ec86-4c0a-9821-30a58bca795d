/**
 * 应用加载器 - 处理资源加载和解析
 */

import { LoadedApp, MicroAppConfig, ResourceInfo } from '../types';
import { logger } from '../utils';

export interface LoadOptions {
  timeout?: number;
  cache?: boolean;
  credentials?: RequestCredentials;
}

export class AppLoader {
  private cache = new Map<string, LoadedApp>();
  private loadingPromises = new Map<string, Promise<LoadedApp>>();

  /**
   * 加载应用
   */
  async loadApp(config: MicroAppConfig, options: LoadOptions = {}): Promise<LoadedApp> {
    const { name, entry } = config;
    const cacheKey = `${name}:${entry}`;

    // 检查缓存
    if (options.cache !== false && this.cache.has(cacheKey)) {
      logger.debug(`从缓存加载应用: ${name}`);
      return this.cache.get(cacheKey)!;
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(cacheKey)) {
      logger.debug(`等待应用加载完成: ${name}`);
      return this.loadingPromises.get(cacheKey)!;
    }

    // 开始加载
    const loadPromise = this.doLoadApp(config, options);
    this.loadingPromises.set(cacheKey, loadPromise);

    try {
      const loadedApp = await loadPromise;

      // 缓存结果
      if (options.cache !== false) {
        this.cache.set(cacheKey, loadedApp);
      }

      return loadedApp;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  /**
   * 执行应用加载
   */
  private async doLoadApp(config: MicroAppConfig, options: LoadOptions): Promise<LoadedApp> {
    const { name, entry } = config;
    logger.info(`开始加载应用: ${name}, 入口: ${entry}`);

    try {
      let resources: ResourceInfo[];

      if (typeof entry === 'string') {
        if (entry.endsWith('.html')) {
          // HTML 入口
          resources = await this.loadHtmlEntry(entry, options);
        } else {
          // JS 入口
          resources = await this.loadJsEntry(entry, options);
        }
      } else {
        // 多入口配置 - 转换 AppEntry 到 Record<string, string>
        const entryRecord: Record<string, string> = {};
        if (entry.scripts) {
          entry.scripts.forEach((script, index) => {
            entryRecord[`script_${index}`] = script;
          });
        }
        if (entry.styles) {
          entry.styles.forEach((style, index) => {
            entryRecord[`style_${index}`] = style;
          });
        }
        if (entry.html) {
          entryRecord['html'] = entry.html;
        }
        resources = await this.loadMultipleEntries(entryRecord, options);
      }

      // 提取生命周期函数
      const lifecycles = await this.extractLifecycles(resources, config);

      const loadedApp: LoadedApp = {
        name,
        entry: config.entry,
        container: config.container,
        props: config.props || {},
        ...(config.sandbox && { sandbox: config.sandbox }),
        ...(config.loader && { loader: config.loader }),
        ...(lifecycles && { lifecycle: lifecycles }),
        status: 'NOT_BOOTSTRAPPED',
        loadTime: Date.now(),
        mountTime: 0,
        unmountTime: 0,
        error: null,
        instance: null,
        sandboxInstance: null,
        resources,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        loaded: true,
        mounted: false
      };

      logger.info(`应用加载完成: ${name}`);
      return loadedApp;

    } catch (error) {
      logger.error(`应用加载失败: ${name}`, error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to load app ${name}: ${errorMessage}`);
    }
  }

  /**
   * 加载 HTML 入口
   */
  private async loadHtmlEntry(url: string, _options: LoadOptions): Promise<ResourceInfo[]> {
    const response = await this.fetchWithTimeout(url, _options);
    const html = await response.text();

    return this.parseHtmlResources(html, url);
  }

  /**
   * 加载 JS 入口
   */
  private async loadJsEntry(url: string, _options: LoadOptions): Promise<ResourceInfo[]> {
    return [{
      type: 'script',
      url,
      content: null,
      inline: false,
      status: 'loaded'
    }];
  }

  /**
   * 加载多个入口
   */
  private async loadMultipleEntries(entries: Record<string, string>, _options: LoadOptions): Promise<ResourceInfo[]> {
    const resources: ResourceInfo[] = [];

    for (const [type, url] of Object.entries(entries)) {
      if (type === 'js' || type === 'script') {
        resources.push({
          type: 'script',
          url,
          content: null,
          inline: false,
          status: 'loaded'
        });
      } else if (type === 'css' || type === 'style') {
        resources.push({
          type: 'style',
          url,
          content: null,
          inline: false,
          status: 'loaded'
        });
      }
    }

    return resources;
  }

  /**
   * 解析 HTML 资源
   */
  private parseHtmlResources(html: string, baseUrl: string): ResourceInfo[] {
    const resources: ResourceInfo[] = [];
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // 解析脚本
    const scripts = doc.querySelectorAll('script');
    scripts.forEach(script => {
      if (script.src) {
        resources.push({
          type: 'script',
          url: this.resolveUrl(script.src, baseUrl),
          content: null,
          inline: false,
          status: 'loaded'
        });
      } else if (script.textContent) {
        resources.push({
          type: 'script',
          url: null,
          content: script.textContent,
          inline: true,
          status: 'loaded'
        });
      }
    });

    // 解析样式
    const links = doc.querySelectorAll('link[rel="stylesheet"]');
    links.forEach(link => {
      const href = link.getAttribute('href');
      if (href) {
        resources.push({
          type: 'style',
          url: this.resolveUrl(href, baseUrl),
          content: null,
          inline: false,
          status: 'loaded'
        });
      }
    });

    const styles = doc.querySelectorAll('style');
    styles.forEach(style => {
      if (style.textContent) {
        resources.push({
          type: 'style',
          url: null,
          content: style.textContent,
          inline: true,
          status: 'loaded'
        });
      }
    });

    return resources;
  }

  /**
   * 提取生命周期函数
   */
  private async extractLifecycles(resources: ResourceInfo[], config: MicroAppConfig): Promise<any> {
    // 执行脚本并提取生命周期函数
    const scriptResources = resources.filter(r => r.type === 'script');

    for (const resource of scriptResources) {
      try {
        let scriptContent: string;

        if (resource.inline) {
          scriptContent = resource.content!;
        } else {
          const response = await fetch(resource.url!);
          scriptContent = await response.text();
        }

        // 在沙箱环境中执行脚本
        const lifecycles = this.executeScriptInSandbox(scriptContent, config);
        if (lifecycles) {
          return lifecycles;
        }
      } catch (error) {
        logger.warn(`执行脚本失败: ${resource.url || 'inline'}`, error);
      }
    }

    return {};
  }

  /**
   * 在沙箱中执行脚本
   */
  private executeScriptInSandbox(script: string, _config: MicroAppConfig): any {
    // 创建沙箱环境
    const sandbox = {
      window: {},
      document: {},
      console,
      setTimeout,
      setInterval,
      clearTimeout,
      clearInterval
    };

    try {
      // 使用 Function 构造器在沙箱中执行脚本
      const func = new Function(
        'window',
        'document',
        'console',
        'setTimeout',
        'setInterval',
        'clearTimeout',
        'clearInterval',
        `
        ${script}
        
        // 尝试提取生命周期函数
        if (typeof bootstrap === 'function' || typeof mount === 'function' || typeof unmount === 'function') {
          return {
            bootstrap: typeof bootstrap === 'function' ? bootstrap : undefined,
            mount: typeof mount === 'function' ? mount : undefined,
            unmount: typeof unmount === 'function' ? unmount : undefined,
            update: typeof update === 'function' ? update : undefined
          };
        }
        
        return null;
        `
      );

      return func.call(
        sandbox,
        sandbox.window,
        sandbox.document,
        sandbox.console,
        sandbox.setTimeout,
        sandbox.setInterval,
        sandbox.clearTimeout,
        sandbox.clearInterval
      );
    } catch (error) {
      logger.warn('沙箱执行脚本失败', error);
      return null;
    }
  }

  /**
   * 带超时的 fetch
   */
  private async fetchWithTimeout(url: string, options: LoadOptions): Promise<Response> {
    const timeout = options.timeout || 10000;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        credentials: options.credentials || 'same-origin',
        signal: controller.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 解析相对 URL
   */
  private resolveUrl(url: string, baseUrl: string): string {
    if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('//')) {
      return url;
    }

    const base = new URL(baseUrl);
    return new URL(url, base.origin + base.pathname).href;
  }

  /**
   * 清除缓存
   */
  clearCache(name?: string): void {
    if (name) {
      // 清除特定应用的缓存
      for (const key of this.cache.keys()) {
        if (key.startsWith(`${name}:`)) {
          this.cache.delete(key);
        }
      }
    } else {
      // 清除所有缓存
      this.cache.clear();
    }
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}