/**
 * @fileoverview 运行时系统导出
 * @description 导出微前端框架的核心运行时组件
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

// 核心运行时组件
export { AppLoader } from './app-loader';
export { AppRegistry } from './app-registry';
export { <PERSON>rrorHandler } from './error-handler';
export { MicroCoreKernel } from './kernel';
export { LifecycleManager } from './lifecycle-manager';
export { PluginSystem } from './plugin-system';
export { ResourceManager } from './resource-manager';

// 类型定义
export type { MicroCoreOptions as KernelOptions } from '../types';
