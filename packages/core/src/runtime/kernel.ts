/**
 * @fileoverview 微前端内核 - 核心调度器，负责应用注册、生命周期管理、插件系统等
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 * @performance Optimized for <15KB bundle size with lazy loading and memory efficiency
 */

import { EventBus } from '../communication/event-bus';
import { APP_STATUS } from '../constants';
import { ERROR_CODES, MicroCoreError } from '../errors';
import type { AppConfig, AppInstance, MicroCoreOptions, Plugin } from '../types';
import { createLogger } from '../utils';
import { AppRegistry } from './app-registry';
import { LifecycleManager } from './lifecycle-manager';

/**
 * 微前端内核
 * 整个微前端系统的核心，负责协调各个管理器和插件的工作
 * @performance 优化内存使用和启动性能
 */
export class MicroCoreKernel {
    private config: MicroCoreOptions;
    private appRegistry: AppRegistry;
    private lifecycleManager: LifecycleManager;
    private eventBus: EventBus;
    private plugins = new Map<string, Plugin>();
    private logger = createLogger('[MicroCoreKernel]');
    private isStarted = false;
    private isDestroyed = false;
    // Performance monitoring temporarily disabled until shared package is properly linked
    private memoryLeakDetector: WeakSet<object> = new WeakSet();
    private cleanupTasks: Array<() => void> = [];

    // 性能优化：延迟初始化重型组件
    private _sandboxManager?: import('../sandbox/sandbox-manager').SandboxManager;
    private _resourceManager?: import('./resource-manager').ResourceManager;

    constructor(config: MicroCoreOptions = {}) {
        this.config = {
            development: false,
            logLevel: 'INFO',
            defaultSandbox: 'proxy',
            ...config
        };

        // 初始化核心组件
        this.eventBus = new EventBus();
        this.appRegistry = new AppRegistry();
        this.lifecycleManager = new LifecycleManager(this.eventBus);

        this.logger.info('微前端内核初始化完成');
    }

    /**
     * 注册微前端应用
     */
    registerApplication(config: AppConfig): void {
        this.checkDestroyed();
        this.logger.debug('注册应用:', config.name);

        try {
            if (!config) {
                throw new MicroCoreError(ERROR_CODES.INVALID_ARGUMENT, '应用配置无效');
            }
            this.appRegistry.register(config);
        } catch (error) {
            this.logger.error('应用注册失败:', error);
            throw error;
        }
    }

    /**
     * 注销微前端应用
     */
    unregisterApplication(name: string): void {
        this.checkDestroyed();
        this.logger.debug('注销应用:', name);

        try {
            this.appRegistry.unregister(name);
        } catch (error) {
            this.logger.error('应用注销失败:', error);
            throw error;
        }
    }

    /**
     * 获取应用信息
     */
    getApplication(name: string): AppInstance | null {
        this.checkDestroyed();
        return this.appRegistry.get(name);
    }

    /**
     * 获取所有应用信息
     */
    getApplications(): AppInstance[] {
        this.checkDestroyed();
        return this.appRegistry.getAll();
    }

    /**
     * 启动内核
     */
    async start(): Promise<void> {
        this.checkDestroyed();

        if (this.isStarted) {
            this.logger.warn('内核已启动，忽略重复启动请求');
            return;
        }

        this.logger.info('启动微前端内核...');

        try {
            // 发送启动事件
            this.eventBus.emit('kernel:starting');

            // 启动路由监听
            this.startRouteListener();

            this.isStarted = true;

            // 发送启动完成事件
            this.eventBus.emit('kernel:started');

            this.logger.info('微前端内核启动完成');
        } catch (error) {
            this.logger.error('内核启动失败:', error);
            throw new MicroCoreError(
                ERROR_CODES.OPERATION_FAILED,
                '内核启动失败',
                {},
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 停止内核
     */
    async stop(): Promise<void> {
        this.checkDestroyed();

        if (!this.isStarted) {
            this.logger.warn('内核未启动，忽略停止请求');
            return;
        }

        this.logger.info('停止微前端内核...');

        try {
            // 发送停止事件
            this.eventBus.emit('kernel:stopping');

            // 卸载所有已挂载的应用
            const mountedApps = this.appRegistry.getByStatus(APP_STATUS.MOUNTED);
            for (const app of mountedApps) {
                await this.unmountApplication(app.name);
            }

            // 停止路由监听
            this.stopRouteListener();

            this.isStarted = false;

            // 发送停止完成事件
            this.eventBus.emit('kernel:stopped');

            this.logger.info('微前端内核停止完成');
        } catch (error) {
            this.logger.error('内核停止失败:', error);
            throw new MicroCoreError(
                ERROR_CODES.OPERATION_FAILED,
                '内核停止失败',
                {},
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 销毁内核
     */
    async destroy(): Promise<void> {
        if (this.isDestroyed) {
            this.logger.warn('内核已销毁，忽略重复销毁请求');
            return;
        }

        this.logger.info('销毁微前端内核...');

        try {
            // 先停止内核
            if (this.isStarted) {
                await this.stop();
            }

            // 发送销毁事件
            this.eventBus.emit('kernel:destroying');

            // 清理所有应用
            this.appRegistry.clear();

            // 清理所有插件
            this.plugins.clear();

            // 清理生命周期钩子
            this.lifecycleManager.clearHooks();

            // 清理事件监听器
            this.eventBus.clear();

            // 发送销毁完成事件
            this.eventBus.emit('kernel:destroyed');

            this.logger.info('微前端内核销毁完成');
        } catch (error) {
            this.logger.error('内核销毁失败:', error);
            // 不抛出异常，让销毁过程继续
        } finally {
            // 确保无论如何都标记为已销毁
            this.isDestroyed = true;
        }
    }

    /**
     * 加载应用
     */
    async loadApplication(name: string): Promise<void> {
        this.checkDestroyed();
        this.logger.debug('加载应用:', name);

        const app = this.appRegistry.get(name);
        if (!app) {
            throw new MicroCoreError(
                ERROR_CODES.APPLICATION_NOT_FOUND,
                `应用 ${name} 未找到`
            );
        }

        if (app.status !== APP_STATUS.NOT_LOADED) {
            this.logger.warn(`应用 ${name} 已加载，当前状态: ${app.status}`);
            return;
        }

        try {
            // 更新状态
            this.appRegistry.updateStatus(name, APP_STATUS.LOADING_SOURCE_CODE);

            // TODO: 实现资源加载逻辑
            // 这里应该加载应用的 JS/CSS 资源

            // 更新状态
            this.appRegistry.updateStatus(name, APP_STATUS.NOT_BOOTSTRAPPED);

            this.logger.info(`应用 ${name} 加载完成`);
        } catch (error) {
            this.appRegistry.setError(name, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    /**
     * 挂载应用
     */
    async mountApplication(name: string): Promise<void> {
        this.checkDestroyed();
        this.logger.debug('挂载应用:', name);

        const app = this.appRegistry.get(name);
        if (!app) {
            throw new MicroCoreError(
                ERROR_CODES.APPLICATION_NOT_FOUND,
                `应用 ${name} 未找到`
            );
        }

        try {
            // 如果应用未加载，先加载
            if (app.status === APP_STATUS.NOT_LOADED) {
                await this.loadApplication(name);
            }

            // 如果应用未引导，先引导
            if (app.status === APP_STATUS.NOT_BOOTSTRAPPED) {
                await this.lifecycleManager.bootstrap(app);
            }

            // 挂载应用
            if (app.status === APP_STATUS.NOT_MOUNTED) {
                await this.lifecycleManager.mount(app);
            }

            this.logger.info(`应用 ${name} 挂载完成`);
        } catch (error) {
            this.appRegistry.setError(name, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    /**
     * 卸载应用
     */
    async unmountApplication(name: string): Promise<void> {
        this.checkDestroyed();
        this.logger.debug('卸载应用:', name);

        const app = this.appRegistry.get(name);
        if (!app) {
            throw new MicroCoreError(
                ERROR_CODES.APPLICATION_NOT_FOUND,
                `应用 ${name} 未找到`
            );
        }

        try {
            if (app.status === APP_STATUS.MOUNTED) {
                await this.lifecycleManager.unmount(app);
            }

            this.logger.info(`应用 ${name} 卸载完成`);
        } catch (error) {
            this.appRegistry.setError(name, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    /**
     * 使用插件
     */
    use(plugin: Plugin, options?: any): void {
        this.checkDestroyed();
        this.logger.debug('安装插件:', plugin.name);

        if (this.plugins.has(plugin.name)) {
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_ALREADY_EXISTS,
                `插件 ${plugin.name} 已存在`
            );
        }

        try {
            if (!plugin || !plugin.name) {
                throw new MicroCoreError(ERROR_CODES.INVALID_ARGUMENT, '插件无效');
            }
            // 安装插件
            if (plugin.install) {
                plugin.install(this, options);
            }

            this.plugins.set(plugin.name, plugin);

            // 发送插件安装事件
            this.eventBus.emit('plugin:installed', { plugin, options });

            this.logger.info(`插件 ${plugin.name} 安装完成`);
        } catch (error) {
            this.logger.error(`插件 ${plugin.name} 安装失败:`, error);
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_INSTALL_FAILED,
                `插件 ${plugin.name} 安装失败`,
                { pluginName: plugin.name },
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 获取插件
     */
    getPlugin(name: string): Plugin | null {
        this.checkDestroyed();
        return this.plugins.get(name) || null;
    }

    /**
     * 获取所有已安装的插件名称列表
     */
    getPlugins(): string[] {
        this.checkDestroyed();
        return Array.from(this.plugins.keys());
    }

    /**
     * 检查插件是否已安装
     */
    hasPlugin(name: string): boolean {
        this.checkDestroyed();
        return this.plugins.has(name);
    }

    /**
     * 获取事件总线
     */
    getEventBus(): EventBus {
        this.checkDestroyed();
        return this.eventBus;
    }

    /**
     * 获取应用注册中心
     */
    getAppRegistry(): AppRegistry {
        this.checkDestroyed();
        return this.appRegistry;
    }

    /**
     * 获取生命周期管理器
     */
    getLifecycleManager(): LifecycleManager {
        this.checkDestroyed();
        return this.lifecycleManager;
    }

    /**
     * 检查内核是否已启动
     */
    isKernelStarted(): boolean {
        return this.isStarted;
    }

    /**
     * 检查内核是否已销毁
     */
    isKernelDestroyed(): boolean {
        return this.isDestroyed;
    }

    /**
     * 启动路由监听
     */
    private startRouteListener(): void {
        // TODO: 实现路由监听逻辑
        this.logger.debug('启动路由监听');
    }

    /**
     * 停止路由监听
     */
    private stopRouteListener(): void {
        // TODO: 实现停止路由监听逻辑
        this.logger.debug('停止路由监听');
    }

    /**
     * 检查内核是否已销毁
     */
    private checkDestroyed(): void {
        if (this.isDestroyed) {
            throw new MicroCoreError(
                ERROR_CODES.OPERATION_FAILED,
                '内核已销毁，无法执行操作'
            );
        }
    }
}