/**
 * @fileoverview 生命周期管理器 - 统一的应用生命周期管理
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { createLogger } from '@micro-core/shared/utils';
import { EventBus } from '../communication/event-bus';
import { APP_STATUS } from '../constants';
import { ERROR_CODES, MicroCoreError } from '../errors';
import type { AppInstance, LifecycleHooks } from '../types';

/**
 * 生命周期操作配置
 */
export interface LifecycleOperationConfig {
    /** 超时时间（毫秒） */
    timeout?: number;
    /** 重试次数 */
    retries?: number;
    /** 重试延迟（毫秒） */
    retryDelay?: number;
    /** 是否启用错误恢复 */
    enableRecovery?: boolean;
    /** 恢复策略 */
    recoveryStrategy?: 'skip' | 'fallback' | 'retry';
}

/**
 * 生命周期操作结果
 */
export interface LifecycleOperationResult {
    /** 是否成功 */
    success: boolean;
    /** 执行时间（毫秒） */
    duration: number;
    /** 重试次数 */
    retryCount: number;
    /** 错误信息 */
    error?: Error;
    /** 是否通过恢复成功 */
    recoveredFromError?: boolean;
}

/**
 * 生命周期状态
 */
export interface LifecycleState {
    /** 当前操作 */
    currentOperation?: string | undefined;
    /** 操作开始时间 */
    operationStartTime?: number | undefined;
    /** 历史操作记录 */
    operationHistory: LifecycleOperationResult[];
    /** 错误恢复次数 */
    recoveryCount: number;
    /** 最后一次错误 */
    lastError?: Error;
}

/**
 * 生命周期管理器
 * 负责管理微前端应用的完整生命周期
 */
export class LifecycleManager {
    private eventBus: EventBus;
    private logger = createLogger('LifecycleManager');
    private hooks: Partial<Record<keyof LifecycleHooks, Function[]>> = {};
    private lifecycleStates = new Map<string, LifecycleState>();
    private defaultConfig: LifecycleOperationConfig = {
        timeout: 30000,      // 30秒超时
        retries: 3,          // 重试3次
        retryDelay: 1000,    // 1秒重试延迟
        enableRecovery: true,
        recoveryStrategy: 'retry'
    };

    constructor(eventBus: EventBus, config?: Partial<LifecycleOperationConfig>) {
        this.eventBus = eventBus;
        if (config) {
            this.defaultConfig = { ...this.defaultConfig, ...config };
        }
    }

    /**
     * 注册生命周期钩子
     */
    registerHook(hookName: keyof LifecycleHooks, hook: Function): void {
        if (!this.hooks[hookName]) {
            this.hooks[hookName] = [];
        }
        this.hooks[hookName]!.push(hook);
    }

    /**
     * 执行生命周期钩子
     */
    private async executeHooks(hookName: keyof LifecycleHooks, app: AppInstance): Promise<void> {
        const hooks = this.hooks[hookName];
        if (!hooks || hooks.length === 0) {
            return;
        }

        for (const hook of hooks) {
            try {
                await hook(app);
            } catch (error) {
                this.logger.error(`执行 ${hookName} 钩子失败:`, error);
                throw new MicroCoreError(
                    ERROR_CODES.LIFECYCLE_ERROR,
                    `执行 ${hookName} 钩子失败: ${error instanceof Error ? error.message : String(error)}`,
                    { appName: app.name, hookName }
                );
            }
        }
    }

    /**
     * 获取应用的生命周期状态
     */
    private getLifecycleState(appName: string): LifecycleState {
        if (!this.lifecycleStates.has(appName)) {
            this.lifecycleStates.set(appName, {
                operationHistory: [],
                recoveryCount: 0
            });
        }
        return this.lifecycleStates.get(appName)!;
    }

    /**
     * 执行带有超时、重试和错误恢复的生命周期操作
     */
    private async executeWithRecovery<T>(
        operationName: string,
        app: AppInstance,
        operation: () => Promise<T>,
        config?: Partial<LifecycleOperationConfig>
    ): Promise<LifecycleOperationResult> {
        const finalConfig = { ...this.defaultConfig, ...config };
        const state = this.getLifecycleState(app.name);
        const startTime = Date.now();

        state.currentOperation = operationName;
        state.operationStartTime = startTime;

        let lastError: Error | undefined;
        let retryCount = 0;
        let recoveredFromError = false;

        // 重试逻辑
        for (let attempt = 0; attempt <= finalConfig.retries!; attempt++) {
            try {
                // 超时处理
                const timeoutPromise = new Promise<never>((_, reject) => {
                    setTimeout(() => {
                        reject(new Error(`生命周期操作 ${operationName} 超时 (${finalConfig.timeout}ms)`));
                    }, finalConfig.timeout);
                });

                // 执行操作
                await Promise.race([operation(), timeoutPromise]);

                // 成功执行
                const duration = Date.now() - startTime;
                const result: LifecycleOperationResult = {
                    success: true,
                    duration,
                    retryCount,
                    recoveredFromError
                };

                state.operationHistory.push(result);
                state.currentOperation = undefined;
                state.operationStartTime = undefined;

                this.logger.debug(`生命周期操作 ${operationName} 成功完成，耗时: ${duration}ms，重试次数: ${retryCount}`);
                return result;

            } catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                retryCount = attempt;

                this.logger.warn(`生命周期操作 ${operationName} 失败 (尝试 ${attempt + 1}/${finalConfig.retries! + 1}):`, lastError);

                // 如果不是最后一次尝试，等待重试延迟
                if (attempt < finalConfig.retries!) {
                    await new Promise(resolve => setTimeout(resolve, finalConfig.retryDelay));
                }
            }
        }

        // 所有重试都失败了，尝试错误恢复
        if (finalConfig.enableRecovery && lastError) {
            try {
                const recovered = await this.attemptRecovery(operationName, app, lastError, finalConfig);
                if (recovered) {
                    recoveredFromError = true;
                    state.recoveryCount++;

                    const duration = Date.now() - startTime;
                    const result: LifecycleOperationResult = {
                        success: true,
                        duration,
                        retryCount,
                        recoveredFromError: true
                    };

                    state.operationHistory.push(result);
                    state.currentOperation = undefined;
                    state.operationStartTime = undefined;

                    this.logger.info(`生命周期操作 ${operationName} 通过错误恢复成功完成`);
                    return result;
                }
            } catch (recoveryError) {
                this.logger.error(`生命周期操作 ${operationName} 错误恢复失败:`, recoveryError);
            }
        }

        // 记录失败结果
        const duration = Date.now() - startTime;
        const result: LifecycleOperationResult = {
            success: false,
            duration,
            retryCount,
            error: lastError || new Error('Unknown error')
        };

        if (state && state.operationHistory) {
            state.operationHistory.push(result);
            state.currentOperation = undefined;
            state.operationStartTime = undefined;
            state.lastError = lastError;
        }

        return result;
    }

    /**
     * 尝试错误恢复
     */
    private async attemptRecovery(
        operationName: string,
        app: AppInstance,
        error: Error,
        config: LifecycleOperationConfig
    ): Promise<boolean> {
        this.logger.info(`尝试对生命周期操作 ${operationName} 进行错误恢复，策略: ${config.recoveryStrategy}`);

        switch (config.recoveryStrategy) {
            case 'skip':
                // 跳过当前操作，标记为成功
                this.logger.warn(`跳过失败的生命周期操作: ${operationName}`);
                return true;

            case 'fallback':
                // 使用降级策略
                return await this.executeFallbackStrategy(operationName, app, error);

            case 'retry':
                // 额外的重试（已经在主逻辑中处理）
                return false;

            default:
                return false;
        }
    }

    /**
     * 执行降级策略
     */
    private async executeFallbackStrategy(operationName: string, app: AppInstance, error: Error): Promise<boolean> {
        this.logger.info(`执行生命周期操作 ${operationName} 的降级策略`);

        try {
            switch (operationName) {
                case 'bootstrap':
                    // 引导失败时的降级：设置最小化状态
                    app.status = APP_STATUS.NOT_MOUNTED;
                    return true;

                case 'mount':
                    // 挂载失败时的降级：尝试简化挂载
                    if (app.lifecycle?.mount) {
                        await app.lifecycle.mount({ name: app.name });
                        app.status = APP_STATUS.MOUNTED;
                        return true;
                    }
                    return false;

                case 'unmount':
                    // 卸载失败时的降级：强制清理状态
                    app.status = APP_STATUS.NOT_MOUNTED;
                    return true;

                default:
                    return false;
            }
        } catch (fallbackError) {
            this.logger.error(`降级策略执行失败:`, fallbackError);
            return false;
        }
    }

    /**
     * 引导应用（增强版本，支持错误恢复、重试和超时）
     */
    async bootstrap(app: AppInstance, config?: Partial<LifecycleOperationConfig>): Promise<LifecycleOperationResult> {
        this.logger.debug(`引导应用: ${app.name}`);

        const result = await this.executeWithRecovery('bootstrap', app, async () => {
            // 执行前置钩子
            await this.executeHooks('beforeBootstrap', app);

            // 执行应用的 bootstrap 生命周期
            if (app.lifecycle?.bootstrap) {
                await app.lifecycle.bootstrap(app.props || {});
            }

            // 更新状态
            app.status = APP_STATUS.NOT_MOUNTED;

            // 执行后置钩子
            await this.executeHooks('afterBootstrap', app);

            // 发送事件
            this.eventBus.emit('app:bootstrapped', app);

            this.logger.info(`应用 ${app.name} 引导完成`);
        }, config);

        if (!result.success) {
            app.status = APP_STATUS.SKIP_BECAUSE_BROKEN;

            this.logger.error(`应用 ${app.name} 引导失败:`, result.error);
            this.eventBus.emit('app:bootstrap-failed', { app, error: result.error });

            throw new MicroCoreError(
                ERROR_CODES.LIFECYCLE_ERROR,
                `应用 ${app.name} 引导失败`,
                { appName: app.name },
                result.error
            );
        }

        return result;
    }

    /**
     * 挂载应用（增强版本，支持错误恢复、重试和超时）
     */
    async mount(app: AppInstance, config?: Partial<LifecycleOperationConfig>): Promise<LifecycleOperationResult> {
        this.logger.debug(`挂载应用: ${app.name}`);

        const result = await this.executeWithRecovery('mount', app, async () => {
            // 执行前置钩子
            await this.executeHooks('beforeMount', app);

            // 更新状态
            app.status = APP_STATUS.MOUNTING;

            // 执行应用的 mount 生命周期
            if (app.lifecycle?.mount) {
                await app.lifecycle.mount({
                    ...app.props,
                    container: app.container,
                    name: app.name
                });
            }

            // 更新状态和时间戳
            app.status = APP_STATUS.MOUNTED;
            app.mountTime = Date.now();

            // 执行后置钩子
            await this.executeHooks('afterMount', app);

            // 发送事件
            this.eventBus.emit('app:mounted', app);

            this.logger.info(`应用 ${app.name} 挂载完成`);
        }, config);

        if (!result.success) {
            app.status = APP_STATUS.SKIP_BECAUSE_BROKEN;

            this.logger.error(`应用 ${app.name} 挂载失败:`, result.error);
            this.eventBus.emit('app:mount-failed', { app, error: result.error });

            throw new MicroCoreError(
                ERROR_CODES.LIFECYCLE_ERROR,
                `应用 ${app.name} 挂载失败`,
                { appName: app.name },
                result.error
            );
        }

        return result;
    }

    /**
     * 卸载应用
     */
    async unmount(app: AppInstance): Promise<void> {
        this.logger.debug(`卸载应用: ${app.name}`);

        try {
            // 执行前置钩子
            await this.executeHooks('beforeUnmount', app);

            // 更新状态
            app.status = APP_STATUS.UNMOUNTING;

            // 执行应用的 unmount 生命周期
            if (app.lifecycle?.unmount) {
                await app.lifecycle.unmount({
                    ...app.props,
                    container: app.container,
                    name: app.name
                });
            }

            // 更新状态和时间戳
            app.status = APP_STATUS.NOT_MOUNTED;


            // 执行后置钩子
            await this.executeHooks('afterUnmount', app);

            // 发送事件
            this.eventBus.emit('app:unmounted', app);

            this.logger.info(`应用 ${app.name} 卸载完成`);
        } catch (error) {
            app.error = error instanceof Error ? error : new Error(String(error));

            this.logger.error(`应用 ${app.name} 卸载失败:`, error);
            this.eventBus.emit('app:unmount-failed', { app, error });

            throw new MicroCoreError(
                ERROR_CODES.LIFECYCLE_ERROR,
                `应用 ${app.name} 卸载失败`,
                { appName: app.name },
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 更新应用
     */
    async update(app: AppInstance, props: Record<string, any>): Promise<void> {
        this.logger.debug(`更新应用: ${app.name}`);

        try {
            // 执行前置钩子
            await this.executeHooks('beforeUpdate', app);

            // 更新状态
            app.status = APP_STATUS.UPDATING;

            // 执行应用的 update 生命周期
            if (app.lifecycle?.update) {
                await app.lifecycle.update({
                    ...app.props,
                    ...props,
                    container: app.container,
                    name: app.name
                });
            }

            // 更新配置
            app.props = { ...app.props, ...props };

            // 恢复状态
            app.status = APP_STATUS.MOUNTED;

            // 执行后置钩子
            await this.executeHooks('afterUpdate', app);

            // 发送事件
            this.eventBus.emit('app:updated', app);

            this.logger.info(`应用 ${app.name} 更新完成`);
        } catch (error) {
            app.error = error instanceof Error ? error : new Error(String(error));

            this.logger.error(`应用 ${app.name} 更新失败:`, error);
            this.eventBus.emit('app:update-failed', { app, error });

            throw new MicroCoreError(
                ERROR_CODES.LIFECYCLE_ERROR,
                `应用 ${app.name} 更新失败`,
                { appName: app.name },
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 卸载应用资源
     */
    async unload(app: AppInstance): Promise<void> {
        this.logger.debug(`卸载应用资源: ${app.name}`);

        try {
            // 执行前置钩子
            await this.executeHooks('beforeUnload', app);

            // 更新状态
            app.status = APP_STATUS.UNLOADING;

            // 执行应用的 unload 生命周期
            if (app.lifecycle?.unload) {
                await app.lifecycle.unload({
                    ...app.props,
                    container: app.container,
                    name: app.name
                });
            }

            // 清理应用实例
            app.container = null;
            app.sandbox = null;
            app.lifecycle = null;
            app.status = APP_STATUS.NOT_LOADED;

            // 执行后置钩子
            await this.executeHooks('afterUnload', app);

            // 发送事件
            this.eventBus.emit('app:unloaded', app);

            this.logger.info(`应用 ${app.name} 资源卸载完成`);
        } catch (error) {
            app.error = error instanceof Error ? error : new Error(String(error));

            this.logger.error(`应用 ${app.name} 资源卸载失败:`, error);
            this.eventBus.emit('app:unload-failed', { app, error });

            throw new MicroCoreError(
                ERROR_CODES.LIFECYCLE_ERROR,
                `应用 ${app.name} 资源卸载失败`,
                { appName: app.name },
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 获取应用当前生命周期状态
     */
    getLifecycleState(app: AppInstance): {
        status: string;
        canBootstrap: boolean;
        canMount: boolean;
        canUnmount: boolean;
        canUpdate: boolean;
        canUnload: boolean;
    } {
        const status = app.status;

        return {
            status,
            canBootstrap: status === APP_STATUS.NOT_BOOTSTRAPPED,
            canMount: status === APP_STATUS.NOT_MOUNTED,
            canUnmount: status === APP_STATUS.MOUNTED,
            canUpdate: status === APP_STATUS.MOUNTED,
            canUnload: [APP_STATUS.NOT_MOUNTED, APP_STATUS.SKIP_BECAUSE_BROKEN].includes(status)
        };
    }

    /**
     * 清理所有钩子
     */
    clearHooks(): void {
        this.hooks = {};
        this.logger.debug('已清理所有生命周期钩子');
    }
}