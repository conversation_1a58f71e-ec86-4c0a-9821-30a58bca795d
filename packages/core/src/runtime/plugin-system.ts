/**
 * @fileoverview 插件系统
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { ERROR_CODES, MicroCoreError } from '../errors';
import type { HookCallback, Plugin } from '../types';
import { createLogger } from '../utils';

const logger = createLogger('[PluginSystem]');

/**
 * 插件系统
 * 负责管理插件的注册、卸载和钩子执行
 */
export class PluginSystem {
    private plugins = new Map<string, Plugin>();
    private hooks = new Map<string, HookCallback[]>();

    constructor() {
        // 构造函数
    }

    /**
     * 安装插件
     * @param plugin 插件实例
     * @param options 插件选项
     */
    install(plugin: Plugin, options?: any): void {
        if (this.plugins.has(plugin.name)) {
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_ALREADY_EXISTS,
                `插件 ${plugin.name} 已存在`
            );
        }

        // 如果插件有安装方法，先调用安装方法
        if (typeof plugin.install === 'function') {
            try {
                (plugin.install as any)(this, options);
            } catch (error) {
                // 如果安装失败，不要将插件添加到系统中
                logger.error(`插件 ${plugin.name} 安装失败:`, error);
                throw error;
            }
        }

        // 安装成功后才添加到插件列表
        this.plugins.set(plugin.name, plugin);
        logger.info(`插件 ${plugin.name} 安装成功`);
    }

    /**
     * 卸载插件
     * @param name 插件名称
     */
    uninstall(name: string): void {
        const plugin = this.plugins.get(name);
        if (!plugin) {
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_NOT_FOUND,
                `插件 ${name} 不存在`
            );
        }

        // 如果插件有卸载方法，则调用
        if (typeof plugin.uninstall === 'function') {
            (plugin.uninstall as any)(this);
        }

        this.plugins.delete(name);
        logger.info(`插件 ${name} 卸载成功`);
    }

    /**
     * 获取插件
     * @param name 插件名称
     * @returns 插件实例
     */
    get(name: string): Plugin | null {
        return this.plugins.get(name) || null;
    }

    /**
     * 获取所有插件
     * @returns 所有插件
     */
    getAll(): Plugin[] {
        return Array.from(this.plugins.values());
    }

    /**
     * 检查插件是否存在
     * @param name 插件名称
     * @returns 是否存在
     */
    has(name: string): boolean {
        return this.plugins.has(name);
    }

    /**
     * 注册钩子
     * @param hookName 钩子名称
     * @param callback 回调函数
     */
    registerHook(hookName: string, callback: HookCallback): void {
        if (!this.hooks.has(hookName)) {
            this.hooks.set(hookName, []);
        }

        this.hooks.get(hookName)!.push(callback);
    }

    /**
     * 执行钩子
     * @param hookName 钩子名称
     * @param args 参数
     */
    async executeHook(hookName: string, ...args: any[]): Promise<void> {
        const callbacks = this.hooks.get(hookName);
        if (!callbacks || callbacks.length === 0) {
            return;
        }

        for (const callback of callbacks) {
            try {
                await callback(...args);
            } catch (error) {
                logger.error(`执行钩子 ${hookName} 时发生错误:`, error);
                throw error;
            }
        }
    }

    /**
     * 移除钩子
     * @param hookName 钩子名称
     * @param callback 回调函数
     */
    removeHook(hookName: string, callback: HookCallback): void {
        const callbacks = this.hooks.get(hookName);
        if (!callbacks) {
            return;
        }

        const index = callbacks.indexOf(callback);
        if (index > -1) {
            callbacks.splice(index, 1);
        }
    }

    /**
     * 清除所有钩子
     * @param hookName 钩子名称
     */
    clearHooks(hookName: string): void {
        this.hooks.delete(hookName);
    }

    /**
     * 获取钩子数量
     * @param hookName 钩子名称
     * @returns 钩子数量
     */
    getHookCount(hookName: string): number {
        const callbacks = this.hooks.get(hookName);
        return callbacks ? callbacks.length : 0;
    }

    /**
     * 获取所有钩子名称
     * @returns 钩子名称列表
     */
    getHookNames(): string[] {
        return Array.from(this.hooks.keys());
    }

    /**
     * 批量注册钩子
     * @param hooks 钩子映射
     */
    registerHooks(hooks: Record<string, HookCallback>): void {
        Object.entries(hooks).forEach(([hookName, callback]) => {
            this.registerHook(hookName, callback);
        });
    }

    /**
     * 获取插件统计信息
     * @returns 统计信息
     */
    getStats(): {
        pluginCount: number;
        hookCount: number;
        hooksPerPlugin: Record<string, number>;
    } {
        const hooksPerPlugin: Record<string, number> = {};

        this.plugins.forEach((_, name) => {
            hooksPerPlugin[name] = 0;
        });

        let totalHooks = 0;
        this.hooks.forEach(callbacks => {
            totalHooks += callbacks.length;
        });

        return {
            pluginCount: this.plugins.size,
            hookCount: totalHooks,
            hooksPerPlugin
        };
    }

    /**
     * 清空所有插件和钩子
     */
    clear(): void {
        // 先卸载所有插件
        const pluginNames = Array.from(this.plugins.keys());
        pluginNames.forEach(name => {
            try {
                this.uninstall(name);
            } catch (error) {
                logger.error(`卸载插件 ${name} 时发生错误:`, error);
            }
        });

        // 清空钩子
        this.hooks.clear();
    }

    /**
     * 销毁插件系统
     * @description 清空所有插件和钩子，与 clear() 方法功能相同
     */
    destroy(): void {
        this.clear();
    }
}
