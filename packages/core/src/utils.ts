/**
 * @fileoverview 核心工具函数 - 重构后版本
 * @description 使用 shared 包的实现，保持向后兼容
 * <AUTHOR> <<EMAIL>>
 * @version 2.1.0
 */

// 从 shared 包导入实现
import {
    createLogger as sharedCreateLogger,
    formatError as sharedFormatError,
    generateId as sharedGenerateId,
    isArray as sharedIsArray,
    isBoolean as sharedIsBoolean,
    isEmpty as sharedIsEmpty,
    isFunction as sharedIsFunction,
    isNumber as sharedIsNumber,
    isObject as sharedIsObject,
    isPromise as sharedIsPromise,
    isString as sharedIsString,
    isValidUrl as sharedIsValidUrl
} from '@micro-core/shared/utils';

// 添加废弃警告（仅开发环境）
if (process.env && process.env['NODE_ENV'] === 'development') {
    console.warn(
        '[DEPRECATED] 从 @micro-core/core 导入工具函数已废弃。' +
        '请改为从 @micro-core/shared/utils 导入。'
    );
}

/**
 * 生成微前端应用唯一ID
 * @description 基于时间戳和随机字符串生成唯一标识符，用于微前端应用标识
 * @param prefix ID前缀，默认为'micro-app'
 * @returns 生成的唯一ID
 * @example
 * ```typescript
 * const id = generateId('my-app'); // 'my-app_1640995200000_abc123'
 * ```
 */
export const generateId = sharedGenerateId;

/**
 * 验证URL是否有效
 * @description 检查URL格式是否正确，支持http、https、file等协议
 * @param url 待验证的URL字符串
 * @returns 是否为有效URL
 * @example
 * ```typescript
 * isValidUrl('https://example.com'); // true
 * isValidUrl('invalid-url'); // false
 * ```
 */
export const isValidUrl = sharedIsValidUrl;

/**
 * 格式化错误信息
 * @description 将各种类型的错误转换为可读的字符串格式
 * @param error 错误对象、字符串或其他类型
 * @returns 格式化后的错误信息
 * @example
 * ```typescript
 * formatError(new Error('Something went wrong')); // 'Error: Something went wrong\n...'
 * formatError('Simple error'); // 'Simple error'
 * formatError({ code: 500 }); // '{\n  "code": 500\n}'
 * ```
 */
export function formatError(error: unknown, context?: any): string {
    return sharedFormatError(error, context);
}

/**
 * 日志记录器接口
 */
export interface Logger {
    debug(message: string, ...args: unknown[]): void;
    info(message: string, ...args: unknown[]): void;
    warn(message: string, ...args: unknown[]): void;
    error(message: string, ...args: unknown[]): void;
    log(message: string, ...args: unknown[]): void;
}

/**
 * 创建日志记录器
 * @description 创建带有命名空间的日志记录器，支持不同级别的日志输出
 * @param namespace 日志命名空间，用于标识日志来源
 * @returns 日志记录器实例
 * @example
 * ```typescript
 * const logger = createLogger('MyModule');
 * logger.info('Application started'); // [2023-12-01T10:00:00.000Z] [MyModule] [INFO] Application started
 * logger.error('Something went wrong', { code: 500 });
 * ```
 */
export const createLogger = sharedCreateLogger;

/**
 * 默认日志记录器实例
 * @description 使用'MicroCore'命名空间的默认日志记录器
 */
export const logger = createLogger('MicroCore') as ReturnType<typeof createLogger>;

// 类型检查工具函数 - 使用 shared 包实现，保持向后兼容

/**
 * 检查值是否为对象
 * @param value 待检查的值
 * @returns 是否为对象
 */
export const isObject = sharedIsObject;

/**
 * 检查值是否为函数
 * @param value 待检查的值
 * @returns 是否为函数
 */
export const isFunction = sharedIsFunction;

/**
 * 检查值是否为字符串
 * @param value 待检查的值
 * @returns 是否为字符串
 */
export const isString = sharedIsString;

/**
 * 检查值是否为数字
 * @param value 待检查的值
 * @returns 是否为数字
 */
export const isNumber = sharedIsNumber;

/**
 * 检查值是否为布尔值
 * @param value 待检查的值
 * @returns 是否为布尔值
 */
export const isBoolean = sharedIsBoolean;

/**
 * 检查值是否为数组
 * @param value 待检查的值
 * @returns 是否为数组
 */
export const isArray = sharedIsArray;

/**
 * 检查值是否为Promise
 * @param value 待检查的值
 * @returns 是否为Promise
 */
export const isPromise = sharedIsPromise;

/**
 * 检查值是否为空
 * @param value 待检查的值
 * @returns 是否为空
 */
export const isEmpty = sharedIsEmpty;
