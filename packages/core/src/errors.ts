/**
 * @fileoverview 错误处理类和错误码定义
 * @description 提供微前端框架的统一错误处理机制，包括错误码定义和自定义错误类
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

/**
 * 错误码常量定义
 * @description 定义微前端框架中所有可能出现的错误码，用于错误分类和处理
 */
export const ERROR_CODES = {
    // 通用错误
    UNKNOWN_ERROR: 'UNKNOWN_ERROR',
    INVALID_ARGUMENT: 'INVALID_ARGUMENT',
    OPERATION_FAILED: 'OPERATION_FAILED',

    // 应用相关错误
    APPLICATION_ERROR: 'APPLICATION_ERROR',
    APPLICATION_NOT_FOUND: 'APPLICATION_NOT_FOUND',
    APPLICATION_ALREADY_EXISTS: 'APPLICATION_ALREADY_EXISTS',
    APPLICATION_STILL_MOUNTED: 'APPLICATION_STILL_MOUNTED',
    APPLICATION_LOAD_FAILED: 'APPLICATION_LOAD_FAILED',
    APPLICATION_MOUNT_FAILED: 'APPLICATION_MOUNT_FAILED',
    APPLICATION_UNMOUNT_FAILED: 'APPLICATION_UNMOUNT_FAILED',

    // 生命周期错误
    LIFECYCLE_ERROR: 'LIFECYCLE_ERROR',
    LIFECYCLE_BOOTSTRAP_FAILED: 'LIFECYCLE_BOOTSTRAP_FAILED',
    LIFECYCLE_MOUNT_FAILED: 'LIFECYCLE_MOUNT_FAILED',
    LIFECYCLE_UNMOUNT_FAILED: 'LIFECYCLE_UNMOUNT_FAILED',
    LIFECYCLE_UPDATE_FAILED: 'LIFECYCLE_UPDATE_FAILED',
    LIFECYCLE_TIMEOUT: 'LIFECYCLE_TIMEOUT',

    // 插件相关错误
    PLUGIN_ERROR: 'PLUGIN_ERROR',
    PLUGIN_NOT_FOUND: 'PLUGIN_NOT_FOUND',
    PLUGIN_ALREADY_EXISTS: 'PLUGIN_ALREADY_EXISTS',
    PLUGIN_INSTALL_FAILED: 'PLUGIN_INSTALL_FAILED',
    PLUGIN_UNINSTALL_FAILED: 'PLUGIN_UNINSTALL_FAILED',

    // 沙箱相关错误
    SANDBOX_ERROR: 'SANDBOX_ERROR',
    SANDBOX_CREATE_FAILED: 'SANDBOX_CREATE_FAILED',
    SANDBOX_DESTROY_FAILED: 'SANDBOX_DESTROY_FAILED',
    SANDBOX_EXECUTION_FAILED: 'SANDBOX_EXECUTION_FAILED',
    SANDBOX_SCRIPT_EXECUTION_FAILED: 'SANDBOX_SCRIPT_EXECUTION_FAILED',

    // 路由相关错误
    ROUTER_ERROR: 'ROUTER_ERROR',
    ROUTER_NAVIGATION_FAILED: 'ROUTER_NAVIGATION_FAILED',
    ROUTER_GUARD_REJECTED: 'ROUTER_GUARD_REJECTED',

    // 通信相关错误
    COMMUNICATION_ERROR: 'COMMUNICATION_ERROR',
    EVENT_HANDLER_ERROR: 'EVENT_HANDLER_ERROR',
    STATE_UPDATE_FAILED: 'STATE_UPDATE_FAILED',

    // 资源相关错误
    RESOURCE_ERROR: 'RESOURCE_ERROR',
    RESOURCE_LOAD_FAILED: 'RESOURCE_LOAD_FAILED',
    RESOURCE_PARSE_FAILED: 'RESOURCE_PARSE_FAILED',
    RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND'
} as const;

/**
 * 错误码类型定义
 * @description 从ERROR_CODES常量中提取的联合类型
 */
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];

/**
 * 向后兼容的错误码常量对象
 * @deprecated 请使用ERROR_CODES常量，此导出仅为向后兼容保留
 */
export const ErrorCode = ERROR_CODES;

/**
 * Micro-Core 自定义错误类
 * @description 微前端框架的统一错误类，提供结构化的错误信息和上下文
 * @example
 * ```typescript
 * throw new MicroCoreError(
 *   ERROR_CODES.APPLICATION_LOAD_FAILED,
 *   'Failed to load application',
 *   { appName: 'my-app', url: 'http://example.com' }
 * );
 * ```
 */
export enum ErrorCodes {
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  APP_LOAD_ERROR = 'APP_LOAD_ERROR',
  APP_MOUNT_ERROR = 'APP_MOUNT_ERROR',
  APP_UNMOUNT_ERROR = 'APP_UNMOUNT_ERROR',
  SANDBOX_ERROR = 'SANDBOX_ERROR',
  COMMUNICATION_ERROR = 'COMMUNICATION_ERROR',
  ROUTER_ERROR = 'ROUTER_ERROR',
  PLUGIN_ERROR = 'PLUGIN_ERROR'
}

export class MicroCoreError extends Error {
    /** 错误码 */
    public readonly code: ErrorCode;
    /** 错误发生时间戳 */
    public readonly timestamp: number;
    /** 错误上下文信息 */
    public readonly context?: Record<string, unknown>;

    /**
     * 构造函数
     * @param code 错误码
     * @param message 错误消息
     * @param context 错误上下文信息
     * @param cause 原始错误对象
     */
    constructor(
        code: ErrorCode,
        message: string,
        context?: Record<string, unknown>,
        cause?: Error
    ) {
        super(message);

        this.name = 'MicroCoreError';
        this.code = code;
        this.timestamp = Date.now();
        this.context = context || {};

        // 设置错误原因（如果支持）
        if (cause && 'cause' in Error.prototype) {
            (this as Error & { cause?: Error }).cause = cause;
        }

        // 确保堆栈跟踪正确
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, MicroCoreError);
        }
    }

    /**
     * 转换为JSON格式
     * @description 将错误对象序列化为JSON格式，便于日志记录和传输
     * @returns 错误对象的JSON表示
     */
    toJSON(): Record<string, unknown> {
        return {
            name: this.name,
            code: this.code,
            message: this.message,
            timestamp: this.timestamp,
            context: this.context || {},
            stack: this.stack || ''
        };
    }

    /**
     * 创建应用错误
     * @description 快速创建应用相关错误的静态方法
     * @param message 错误消息
     * @param context 错误上下文
     * @returns MicroCoreError实例
     */
    static createApplicationError(message: string, context?: Record<string, unknown>): MicroCoreError {
        return new MicroCoreError(ERROR_CODES.APPLICATION_ERROR, message, context);
    }

    /**
     * 创建生命周期错误
     * @description 快速创建生命周期相关错误的静态方法
     * @param message 错误消息
     * @param context 错误上下文
     * @returns MicroCoreError实例
     */
    static createLifecycleError(message: string, context?: Record<string, unknown>): MicroCoreError {
        return new MicroCoreError(ERROR_CODES.LIFECYCLE_ERROR, message, context);
    }

    /**
     * 创建插件错误
     * @description 快速创建插件相关错误的静态方法
     * @param message 错误消息
     * @param context 错误上下文
     * @returns MicroCoreError实例
     */
    static createPluginError(message: string, context?: Record<string, unknown>): MicroCoreError {
        return new MicroCoreError(ERROR_CODES.PLUGIN_ERROR, message, context);
    }

    /**
     * 创建沙箱错误
     * @description 快速创建沙箱相关错误的静态方法
     * @param message 错误消息
     * @param context 错误上下文
     * @returns MicroCoreError实例
     */
    static createSandboxError(message: string, context?: Record<string, unknown>): MicroCoreError {
        return new MicroCoreError(ERROR_CODES.SANDBOX_ERROR, message, context);
    }

    /**
     * 创建路由错误
     * @description 快速创建路由相关错误的静态方法
     * @param message 错误消息
     * @param context 错误上下文
     * @returns MicroCoreError实例
     */
    static createRouterError(message: string, context?: Record<string, unknown>): MicroCoreError {
        return new MicroCoreError(ERROR_CODES.ROUTER_ERROR, message, context);
    }

    /**
     * 创建通信错误
     */
    static createCommunicationError(message: string, context?: Record<string, any>): MicroCoreError {
        return new MicroCoreError(ERROR_CODES.COMMUNICATION_ERROR, message, context);
    }

    /**
     * 创建资源错误
     */
    static createResourceError(message: string, context?: Record<string, any>): MicroCoreError {
        return new MicroCoreError(ERROR_CODES.RESOURCE_ERROR, message, context);
    }
}

/**
 * 错误处理器接口
 */
export interface ErrorHandler {
    (error: MicroCoreError, context?: Record<string, any>): void;
}

/**
 * 默认错误处理器
 */
export const defaultErrorHandler: ErrorHandler = (error: MicroCoreError, context?: Record<string, any>) => {
    console.error('[MicroCore Error]', {
        code: error.code,
        message: error.message,
        timestamp: new Date(error.timestamp).toISOString(),
        context: { ...error.context, ...context },
        stack: error.stack || ''
    });
};

/**
 * 错误处理工具函数
 */
export class ErrorUtils {
    /**
     * 包装异步函数，自动捕获错误
     */
    static async wrapAsync<T>(
        fn: () => Promise<T>,
        errorCode: ErrorCode,
        errorMessage: string,
        context?: Record<string, any>
    ): Promise<T> {
        try {
            return await fn();
        } catch (error) {
            throw new MicroCoreError(
                errorCode,
                errorMessage,
                context,
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 包装同步函数，自动捕获错误
     */
    static wrapSync<T>(
        fn: () => T,
        errorCode: ErrorCode,
        errorMessage: string,
        context?: Record<string, any>
    ): T {
        try {
            return fn();
        } catch (error) {
            throw new MicroCoreError(
                errorCode,
                errorMessage,
                context,
                error instanceof Error ? error : new Error(String(error))
            );
        }
    }

    /**
     * 检查是否为 MicroCore 错误
     */
    static isMicroCoreError(error: any): error is MicroCoreError {
        return error instanceof MicroCoreError;
    }

    /**
     * 格式化错误信息
     */
    static formatError(error: Error): string {
        if (ErrorUtils.isMicroCoreError(error)) {
            return `[${error.code}] ${error.message}`;
        }
        return error.message;
    }
}

/**
 * 沙箱错误类
 */
export class SandboxError extends MicroCoreError {
    constructor(
        sandboxName: string,
        message: string,
        code: ErrorCode = ERROR_CODES.SANDBOX_ERROR,
        cause?: Error
    ) {
        super(code, `[${sandboxName}] ${message}`, { sandboxName }, cause);
        this.name = 'SandboxError';
    }
}