/**
 * @fileoverview 基础沙箱实现
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { ERROR_CODES, MicroCoreError } from '../errors';
import type { MicroApp } from '../types';
import { createLogger } from '../utils';

/**
 * 沙箱选项接口
 */
export interface SandboxOptions {
    /** 沙箱名称 */
    name: string;
    /** 是否启用严格模式 */
    strict?: boolean;
    /** 是否启用样式隔离 */
    styleIsolation?: boolean;
    /** 自定义全局变量 */
    globals?: Record<string, any>;
    /** 沙箱类型 */
    type?: 'proxy' | 'iframe' | 'webcomponent' | 'namespace';
}

/**
 * 沙箱上下文接口
 */
export interface SandboxContext {
    /** 沙箱名称 */
    name: string;
    /** 沙箱状态 */
    active: boolean;
    /** 全局对象代理 */
    window: Window & typeof globalThis;
    /** 文档对象 */
    document: Document;
    /** 沙箱创建时间 */
    createdAt: number;
    /** 沙箱激活时间 */
    activatedAt?: number;
}

/**
 * 基础沙箱抽象类
 * 定义沙箱的基本接口和通用功能
 */
export abstract class BaseSandbox {
    protected logger = createLogger('BaseSandbox');
    protected options: Required<SandboxOptions>;
    protected context: SandboxContext | null = null;
    protected isActive = false;

    constructor(options: SandboxOptions) {
        this.options = {
            strict: false,
            styleIsolation: true,
            globals: {},
            type: 'proxy',
            ...options
        };
    }

    /**
     * 激活沙箱
     * @param app 微应用配置
     * @returns Promise<SandboxContext>
     */
    async activate(app: MicroApp): Promise<SandboxContext> {
        if (this.isActive) {
            this.logger.warn(`沙箱 "${this.options.name}" 已经激活`);
            return this.context!;
        }

        try {
            this.logger.info(`激活沙箱: ${this.options.name}`);

            // 创建沙箱上下文
            this.context = await this.createSandboxContext(app);
            this.isActive = true;

            // 执行激活后的钩子
            await this.onActivated(app);

            this.logger.info(`沙箱激活成功: ${this.options.name}`);
            return this.context;
        } catch (error) {
            this.logger.error(`沙箱激活失败: ${this.options.name}`, error);
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_SCRIPT_EXECUTION_FAILED,
                `沙箱代码执行失败: ${(error as Error).message}`,
                { sandboxName: this.options.name }
            );
        }
    }

    /**
     * 停用沙箱
     * @returns Promise<void>
     */
    async deactivate(): Promise<void> {
        if (!this.isActive) {
            this.logger.warn(`沙箱 "${this.options.name}" 未激活`);
            return;
        }

        try {
            this.logger.info(`停用沙箱: ${this.options.name}`);

            // 执行停用前的钩子
            await this.onDeactivating();

            // 清理沙箱上下文
            await this.destroySandboxContext();

            this.context = null;
            this.isActive = false;

            this.logger.info(`沙箱停用成功: ${this.options.name}`);
        } catch (error) {
            this.logger.error(`沙箱停用失败: ${this.options.name}`, error);
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_DESTROY_FAILED,
                `沙箱 "${this.options.name}" 停用失败`,
                { sandboxName: this.options.name }
            );
        }
    }

    /**
     * 执行代码
     * @param code 要执行的代码
     * @param filename 文件名（可选）
     * @returns 执行结果
     */
    async execScript(code: string, filename?: string): Promise<any> {
        if (!this.isActive || !this.context) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_EXECUTION_FAILED,
                `沙箱 "${this.options.name}" 未激活，无法执行代码`,
                { sandboxName: this.options.name }
            );
        }

        try {
            this.logger.debug(`执行代码: ${filename || 'inline'}`);
            return await this.doExecScript(code, filename);
        } catch (error) {
            this.logger.error(`代码执行失败: ${filename || 'inline'}`, error);
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_SCRIPT_EXECUTION_FAILED,
                `沙箱代码执行失败: ${(error as Error).message}`,
                { sandboxName: this.options.name }
            );
        }
    }

    /**
     * 获取沙箱上下文
     * @returns SandboxContext | null
     */
    getContext(): SandboxContext | null {
        return this.context;
    }

    /**
     * 获取沙箱选项
     * @returns SandboxOptions
     */
    getOptions(): SandboxOptions {
        return { ...this.options };
    }

    /**
     * 检查沙箱是否激活
     * @returns boolean
     */
    isActivated(): boolean {
        return this.isActive;
    }

    /**
     * 设置全局变量
     * @param key 变量名
     * @param value 变量值
     */
    setGlobal(key: string, value: any): void {
        if (!this.context) {
            throw new MicroCoreError(
                `沙箱 "${this.options.name}" 未激活，无法设置全局变量`,
                ErrorCodes.SANDBOX_CREATE_FAILED,
                { sandboxName: this.options.name }
            );
        }

        (this.context.window as any)[key] = value;
        this.logger.debug(`设置全局变量: ${key}`);
    }

    /**
     * 获取全局变量
     * @param key 变量名
     * @returns 变量值
     */
    getGlobal(key: string): any {
        if (!this.context) {
            throw new MicroCoreError(
                `沙箱 "${this.options.name}" 未激活，无法获取全局变量`,
                ErrorCodes.SANDBOX_CREATE_FAILED,
                { sandboxName: this.options.name }
            );
        }

        return (this.context.window as any)[key];
    }

    /**
     * 创建沙箱上下文（抽象方法）
     * @param app 微应用配置
     * @returns Promise<SandboxContext>
     */
    protected abstract createSandboxContext(app: MicroApp): Promise<SandboxContext>;

    /**
     * 销毁沙箱上下文（抽象方法）
     * @returns Promise<void>
     */
    protected abstract destroySandboxContext(): Promise<void>;

    /**
     * 执行代码的具体实现（抽象方法）
     * @param code 要执行的代码
     * @param filename 文件名
     * @returns Promise<any>
     */
    protected abstract doExecScript(code: string, filename?: string): Promise<any>;

    /**
     * 沙箱激活后的钩子
     * @param app 微应用配置
     * @returns Promise<void>
     */
    protected async onActivated(app: MicroApp): Promise<void> {
        // 子类可以重写此方法
    }

    /**
     * 沙箱停用前的钩子
     * @returns Promise<void>
     */
    protected async onDeactivating(): Promise<void> {
        // 子类可以重写此方法
    }
}

export default BaseSandbox;