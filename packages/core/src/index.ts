/**
 * @fileoverview Micro-Core 主入口文件
 * @description 导出所有核心功能模块，提供统一的API接口
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

// 核心运行时组件
export { AppLoader } from './runtime/app-loader';
export { AppRegistry } from './runtime/app-registry';
export { ErrorHandler } from './runtime/error-handler';
export { MicroCoreKernel } from './runtime/kernel';
export { LifecycleManager } from './runtime/lifecycle-manager';
export { PluginSystem } from './runtime/plugin-system';
export { ResourceManager } from './runtime/resource-manager';

// 通信模块
export type { CommunicationMessage } from './communication';
export { EventBus } from './communication/event-bus';

// 沙箱模块
export { SandboxType } from './constants';
export { BaseSandbox } from './sandbox/base-sandbox';
export type { SandboxContext, SandboxOptions } from './sandbox/base-sandbox';

// 路由模块
export type { RouteConfig, RouteMatch } from './router';

// 类型定义
export * from './types';

// 错误处理
export { ERROR_CODES, MicroCoreError } from './errors';

// 工具函数
export { createLogger, formatError, generateId, isValidUrl } from './utils';

// 导入依赖（避免重复导入）
import { EventBus } from './communication/event-bus';
import { MicroCoreKernel } from './runtime/kernel';
import type { MicroAppConfig, MicroCoreOptions, Plugin } from './types';

/**
 * Micro-Core 主类
 * @description 微前端框架的主要入口类，提供应用注册、生命周期管理、插件系统等核心功能
 * @example
 * ```typescript
 * const microCore = new MicroCore({
 *   container: '#app',
 *   mode: 'development'
 * });
 *
 * microCore.registerApplication({
 *   name: 'app1',
 *   entry: 'http://localhost:3001',
 *   container: '#container',
 *   activeWhen: '/app1'
 * });
 *
 * await microCore.start();
 * ```
 */
export class MicroCore {
    /** 微前端内核实例 */
    private readonly kernel: MicroCoreKernel;
    /** 事件总线实例 */
    private readonly eventBus: EventBus;

    /**
     * 构造函数
     * @param options 内核配置选项
     */
    constructor(options: MicroCoreOptions = {}) {
        this.kernel = new MicroCoreKernel(options);
        this.eventBus = new EventBus();
    }

    /**
     * 注册微前端应用
     * @param config 应用配置
     */
    registerApplication(config: MicroAppConfig): void {
        this.kernel.registerApplication(config);
    }

    /**
     * 注销微前端应用
     * @param name 应用名称
     */
    unregisterApplication(name: string): void {
        this.kernel.unregisterApplication(name);
    }

    /**
     * 启动微前端框架
     */
    async start(): Promise<void> {
        await this.kernel.start();
    }

    /**
     * 停止微前端框架
     */
    async stop(): Promise<void> {
        await this.kernel.stop();
    }

    /**
     * 获取内核实例
     * @returns 内核实例
     */
    getKernel(): MicroCoreKernel {
        return this.kernel;
    }

    /**
     * 获取事件总线实例
     * @returns 事件总线实例
     */
    getEventBus(): EventBus {
        return this.eventBus;
    }

    /**
     * 使用插件
     * @param plugin 插件实例或插件构造函数
     * @param options 插件配置选项
     */
    use(plugin: Plugin, options?: Record<string, any>): void {
        this.kernel.use(plugin, options);
    }

    /**
     * 获取已安装的插件列表
     * @returns 插件名称列表
     */
    getPlugins(): string[] {
        return this.kernel.getPlugins();
    }

    /**
     * 检查是否已安装指定插件
     * @param name 插件名称
     * @returns 是否已安装
     */
    hasPlugin(name: string): boolean {
        return this.kernel.hasPlugin(name);
    }
}

export default MicroCore;