/**
 * @fileoverview Core包测试环境设置
 * <AUTHOR> <<EMAIL>>
 */

import { vi } from 'vitest';

// Mock DOM APIs
Object.defineProperty(window, 'fetch', {
    value: vi.fn(),
    writable: true,
});

// Mock console methods to reduce noise in tests
global.console = {
    ...console,
    warn: vi.fn(),
    error: vi.fn(),
};

// Setup DOM
document.body.innerHTML = '<div id="container"></div>';

// 模拟微前端全局对象
Object.defineProperty(window, '__MICRO_CORE__', {
    value: {
        version: '1.0.0',
        apps: new Map(),
        plugins: new Map(),
        eventBus: {
            on: vi.fn(),
            off: vi.fn(),
            emit: vi.fn(),
            once: vi.fn()
        },
        registerApp: vi.fn(),
        unregisterApp: vi.fn(),
        startApp: vi.fn(),
        stopApp: vi.fn(),
        getApp: vi.fn(),
        getApps: vi.fn(),
        setState: vi.fn(),
        getState: vi.fn(),
        removeState: vi.fn()
    },
    writable: true
});

// 模拟微前端标识
Object.defineProperty(window, '__POWERED_BY_MICRO_CORE__', {
    value: true,
    writable: true
});

// 模拟性能 API
Object.defineProperty(window, 'performance', {
    value: {
        now: vi.fn(() => Date.now()),
        mark: vi.fn(),
        measure: vi.fn(),
        getEntriesByName: vi.fn(() => []),
        getEntriesByType: vi.fn(() => []),
        clearMarks: vi.fn(),
        clearMeasures: vi.fn()
    },
    writable: true
});

// 模拟 URL API
global.URL = class URL {
    constructor(public href: string, base?: string) {
        if (base) {
            this.href = new URL(href, base).href;
        }
    }

    get origin() { return 'http://localhost:3000'; }
    get protocol() { return 'http:'; }
    get host() { return 'localhost:3000'; }
    get hostname() { return 'localhost'; }
    get port() { return '3000'; }
    get pathname() { return '/'; }
    get search() { return ''; }
    get hash() { return ''; }

    toString() { return this.href; }
    toJSON() { return this.href; }
} as any;

// 模拟 URLSearchParams API
global.URLSearchParams = class URLSearchParams {
    private params = new Map<string, string>();

    constructor(init?: string | URLSearchParams | Record<string, string>) {
        if (typeof init === 'string') {
            init.replace(/^\?/, '').split('&').forEach(pair => {
                const [key, value] = pair.split('=');
                if (key) this.params.set(decodeURIComponent(key), decodeURIComponent(value || ''));
            });
        }
    }

    get(name: string) { return this.params.get(name); }
    set(name: string, value: string) { this.params.set(name, value); }
    has(name: string) { return this.params.has(name); }
    delete(name: string) { this.params.delete(name); }
    toString() {
        return Array.from(this.params.entries())
            .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
            .join('&');
    }
} as any;