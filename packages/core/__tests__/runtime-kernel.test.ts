/**
 * @fileoverview MicroCoreKernel 运行时内核单元测试
 * @description 测试微前端内核的完整功能，确保100%代码覆盖率
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { APP_STATUS } from '../src/constants';
import { MicroCoreError } from '../src/errors';
import { MicroCoreKernel } from '../src/runtime/kernel';
import type { AppConfig, AppInstance, MicroCoreOptions } from '../src/types';

// Mock 依赖模块
vi.mock('../src/communication/event-bus');
vi.mock('../src/runtime/app-registry');
vi.mock('../src/runtime/lifecycle-manager');
vi.mock('../src/utils');

describe('MicroCoreKernel 运行时内核', () => {
    let kernel: MicroCoreKernel;
    let mockEventBus: any;
    let mockAppRegistry: any;
    let mockLifecycleManager: any;
    let mockLogger: any;

    beforeEach(() => {
        // 创建模拟对象
        mockEventBus = {
            emit: vi.fn(),
            on: vi.fn(),
            off: vi.fn(),
            clear: vi.fn()
        };

        mockAppRegistry = {
            register: vi.fn(),
            unregister: vi.fn(),
            get: vi.fn(),
            getAll: vi.fn(),
            getByStatus: vi.fn(),
            updateStatus: vi.fn(),
            setError: vi.fn(),
            clear: vi.fn()
        };

        mockLifecycleManager = {
            bootstrap: vi.fn(),
            mount: vi.fn(),
            unmount: vi.fn(),
            clearHooks: vi.fn()
        };

        mockLogger = {
            debug: vi.fn(),
            info: vi.fn(),
            warn: vi.fn(),
            error: vi.fn()
        };

        // Mock 构造函数
        const { EventBus } = require('../src/communication/event-bus');
        const { AppRegistry } = require('../src/runtime/app-registry');
        const { LifecycleManager } = require('../src/runtime/lifecycle-manager');
        const { createLogger } = require('../src/utils');

        EventBus.mockImplementation(() => mockEventBus);
        AppRegistry.mockImplementation(() => mockAppRegistry);
        LifecycleManager.mockImplementation(() => mockLifecycleManager);
        createLogger.mockReturnValue(mockLogger);

        kernel = new MicroCoreKernel();
    });

    afterEach(() => {
        vi.clearAllMocks();
        vi.resetModules();
    });

    describe('构造函数', () => {
        it('应该使用默认配置创建内核实例', () => {
            const newKernel = new MicroCoreKernel();
            expect(newKernel).toBeInstanceOf(MicroCoreKernel);
            expect(mockLogger.info).toHaveBeenCalledWith('微前端内核初始化完成');
        });

        it('应该使用自定义配置创建内核实例', () => {
            const config: MicroCoreOptions = {
                development: true,
                logLevel: 'DEBUG',
                defaultSandbox: 'iframe'
            };

            const newKernel = new MicroCoreKernel(config);
            expect(newKernel).toBeInstanceOf(MicroCoreKernel);
        });

        it('应该合并默认配置和自定义配置', () => {
            const config: MicroCoreOptions = {
                development: true
            };

            const newKernel = new MicroCoreKernel(config);
            expect(newKernel).toBeInstanceOf(MicroCoreKernel);
        });

        it('应该处理空配置对象', () => {
            const newKernel = new MicroCoreKernel({});
            expect(newKernel).toBeInstanceOf(MicroCoreKernel);
        });
    });

    describe('应用注册管理', () => {
        describe('registerApplication', () => {
            it('应该成功注册应用', () => {
                const appConfig: AppConfig = {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                };

                kernel.registerApplication(appConfig);

                expect(mockLogger.debug).toHaveBeenCalledWith('注册应用:', 'test-app');
                expect(mockAppRegistry.register).toHaveBeenCalledWith(appConfig);
            });

            it('应该处理无效的应用配置', () => {
                expect(() => {
                    kernel.registerApplication(null as any);
                }).toThrow(MicroCoreError);

                expect(mockLogger.error).toHaveBeenCalledWith('应用注册失败:', expect.any(Error));
            });

            it('应该处理注册失败的情况', () => {
                const error = new Error('注册失败');
                mockAppRegistry.register.mockImplementation(() => {
                    throw error;
                });

                const appConfig: AppConfig = {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                };

                expect(() => {
                    kernel.registerApplication(appConfig);
                }).toThrow(error);

                expect(mockLogger.error).toHaveBeenCalledWith('应用注册失败:', error);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                const appConfig: AppConfig = {
                    name: 'test-app',
                    entry: 'http://localhost:3000',
                    container: '#app',
                    activeWhen: '/test'
                };

                expect(() => {
                    kernel.registerApplication(appConfig);
                }).toThrow(MicroCoreError);
            });
        });

        describe('unregisterApplication', () => {
            it('应该成功注销应用', () => {
                kernel.unregisterApplication('test-app');

                expect(mockLogger.debug).toHaveBeenCalledWith('注销应用:', 'test-app');
                expect(mockAppRegistry.unregister).toHaveBeenCalledWith('test-app');
            });

            it('应该处理注销失败的情况', () => {
                const error = new Error('注销失败');
                mockAppRegistry.unregister.mockImplementation(() => {
                    throw error;
                });

                expect(() => {
                    kernel.unregisterApplication('test-app');
                }).toThrow(error);

                expect(mockLogger.error).toHaveBeenCalledWith('应用注销失败:', error);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                expect(() => {
                    kernel.unregisterApplication('test-app');
                }).toThrow(MicroCoreError);
            });
        });

        describe('getApplication', () => {
            it('应该返回指定的应用实例', () => {
                const mockApp: any = {
                    name: 'test-app',
                    status: APP_STATUS.NOT_LOADED,
                    config: {
                        name: 'test-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                };

                mockAppRegistry.get.mockReturnValue(mockApp);

                const result = kernel.getApplication('test-app');

                expect(result).toBe(mockApp);
                expect(mockAppRegistry.get).toHaveBeenCalledWith('test-app');
            });

            it('应该在应用不存在时返回null', () => {
                mockAppRegistry.get.mockReturnValue(null);

                const result = kernel.getApplication('non-existent-app');

                expect(result).toBeNull();
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                expect(() => {
                    kernel.getApplication('test-app');
                }).toThrow(MicroCoreError);
            });
        });

        describe('getApplications', () => {
            it('应该返回所有应用实例', () => {
                const mockApps: AppInstance[] = [
                    {
                        name: 'app1',
                        status: APP_STATUS.NOT_LOADED,
                        config: {
                            name: 'app1',
                            entry: 'http://localhost:3001',
                            container: '#app1',
                            activeWhen: '/app1'
                        }
                    },
                    {
                        name: 'app2',
                        status: APP_STATUS.MOUNTED,
                        config: {
                            name: 'app2',
                            entry: 'http://localhost:3002',
                            container: '#app2',
                            activeWhen: '/app2'
                        }
                    }
                ];

                mockAppRegistry.getAll.mockReturnValue(mockApps);

                const result = kernel.getApplications();

                expect(result).toBe(mockApps);
                expect(mockAppRegistry.getAll).toHaveBeenCalled();
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                expect(() => {
                    kernel.getApplications();
                }).toThrow(MicroCoreError);
            });
        });
    });

    describe('内核生命周期管理', () => {
        describe('start', () => {
            it('应该成功启动内核', async () => {
                await kernel.start();

                expect(mockLogger.info).toHaveBeenCalledWith('启动微前端内核...');
                expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:starting');
                expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:started');
                expect(mockLogger.info).toHaveBeenCalledWith('微前端内核启动完成');
                expect(kernel.isKernelStarted()).toBe(true);
            });

            it('应该忽略重复启动请求', async () => {
                await kernel.start();
                mockLogger.warn.mockClear();

                await kernel.start();

                expect(mockLogger.warn).toHaveBeenCalledWith('内核已启动，忽略重复启动请求');
            });

            it('应该处理启动失败的情况', async () => {
                const error = new Error('启动失败');
                mockEventBus.emit.mockImplementation((event: string) => {
                    if (event === 'kernel:starting') {
                        throw error;
                    }
                });

                await expect(kernel.start()).rejects.toThrow(MicroCoreError);
                expect(mockLogger.error).toHaveBeenCalledWith('内核启动失败:', error);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                await expect(kernel.start()).rejects.toThrow(MicroCoreError);
            });
        });

        describe('stop', () => {
            it('应该成功停止内核', async () => {
                // 先启动内核
                await kernel.start();

                // 模拟已挂载的应用
                const mountedApps: AppInstance[] = [
                    {
                        name: 'mounted-app',
                        status: APP_STATUS.MOUNTED,
                        config: {
                            name: 'mounted-app',
                            entry: 'http://localhost:3000',
                            container: '#app',
                            activeWhen: '/test'
                        }
                    }
                ];
                mockAppRegistry.getByStatus.mockReturnValue(mountedApps);

                await kernel.stop();

                expect(mockLogger.info).toHaveBeenCalledWith('停止微前端内核...');
                expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:stopping');
                expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:stopped');
                expect(mockLogger.info).toHaveBeenCalledWith('微前端内核停止完成');
                expect(kernel.isKernelStarted()).toBe(false);
            });

            it('应该忽略未启动时的停止请求', async () => {
                await kernel.stop();

                expect(mockLogger.warn).toHaveBeenCalledWith('内核未启动，忽略停止请求');
            });

            it('应该处理停止失败的情况', async () => {
                await kernel.start();

                const error = new Error('停止失败');
                mockEventBus.emit.mockImplementation((event: string) => {
                    if (event === 'kernel:stopping') {
                        throw error;
                    }
                });

                await expect(kernel.stop()).rejects.toThrow(MicroCoreError);
                expect(mockLogger.error).toHaveBeenCalledWith('内核停止失败:', error);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                await expect(kernel.stop()).rejects.toThrow(MicroCoreError);
            });
        });

        describe('destroy', () => {
            it('应该成功销毁内核', async () => {
                await kernel.start();
                await kernel.destroy();

                expect(mockLogger.info).toHaveBeenCalledWith('销毁微前端内核...');
                expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:destroying');
                expect(mockAppRegistry.clear).toHaveBeenCalled();
                expect(mockLifecycleManager.clearHooks).toHaveBeenCalled();
                expect(mockEventBus.clear).toHaveBeenCalled();
                expect(mockEventBus.emit).toHaveBeenCalledWith('kernel:destroyed');
                expect(mockLogger.info).toHaveBeenCalledWith('微前端内核销毁完成');
                expect(kernel.isKernelDestroyed()).toBe(true);
            });

            it('应该忽略重复销毁请求', async () => {
                await kernel.destroy();
                mockLogger.warn.mockClear();

                await kernel.destroy();

                expect(mockLogger.warn).toHaveBeenCalledWith('内核已销毁，忽略重复销毁请求');
            });

            it('应该处理销毁失败的情况', async () => {
                const error = new Error('销毁失败');
                mockAppRegistry.clear.mockImplementation(() => {
                    throw error;
                });

                await kernel.destroy();

                expect(mockLogger.error).toHaveBeenCalledWith('内核销毁失败:', error);
                expect(kernel.isKernelDestroyed()).toBe(true); // 即使失败也应该标记为已销毁
            });

            it('应该在销毁前先停止内核', async () => {
                await kernel.start();
                expect(kernel.isKernelStarted()).toBe(true);

                await kernel.destroy();

                expect(kernel.isKernelStarted()).toBe(false);
                expect(kernel.isKernelDestroyed()).toBe(true);
            });
        });
    });

    describe('应用生命周期管理', () => {
        describe('loadApplication', () => {
            it('应该成功加载应用', async () => {
                const mockApp: AppInstance = {
                    name: 'test-app',
                    status: APP_STATUS.NOT_LOADED,
                    config: {
                        name: 'test-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                };

                mockAppRegistry.get.mockReturnValue(mockApp);

                await kernel.loadApplication('test-app');

                expect(mockLogger.debug).toHaveBeenCalledWith('加载应用:', 'test-app');
                expect(mockAppRegistry.updateStatus).toHaveBeenCalledWith('test-app', APP_STATUS.LOADING_SOURCE_CODE);
                expect(mockAppRegistry.updateStatus).toHaveBeenCalledWith('test-app', APP_STATUS.NOT_BOOTSTRAPPED);
                expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 加载完成');
            });

            it('应该在应用不存在时抛出错误', async () => {
                mockAppRegistry.get.mockReturnValue(null);

                await expect(kernel.loadApplication('non-existent-app')).rejects.toThrow(MicroCoreError);
            });

            it('应该忽略已加载的应用', async () => {
                const mockApp: AppInstance = {
                    name: 'test-app',
                    status: APP_STATUS.NOT_BOOTSTRAPPED,
                    config: {
                        name: 'test-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                };

                mockAppRegistry.get.mockReturnValue(mockApp);

                await kernel.loadApplication('test-app');

                expect(mockLogger.warn).toHaveBeenCalledWith('应用 test-app 已加载，当前状态: NOT_BOOTSTRAPPED');
            });

            it('应该处理加载失败的情况', async () => {
                const mockApp: AppInstance = {
                    name: 'test-app',
                    status: APP_STATUS.NOT_LOADED,
                    config: {
                        name: 'test-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                };

                mockAppRegistry.get.mockReturnValue(mockApp);

                const error = new Error('加载失败');
                mockAppRegistry.updateStatus.mockImplementation(() => {
                    throw error;
                });

                await expect(kernel.loadApplication('test-app')).rejects.toThrow(error);
                expect(mockAppRegistry.setError).toHaveBeenCalledWith('test-app', error);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                await expect(kernel.loadApplication('test-app')).rejects.toThrow(MicroCoreError);
            });
        });

        describe('mountApplication', () => {
            it('应该成功挂载应用', async () => {
                const mockApp: AppInstance = {
                    name: 'test-app',
                    status: APP_STATUS.NOT_MOUNTED,
                    config: {
                        name: 'test-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                };

                mockAppRegistry.get.mockReturnValue(mockApp);

                await kernel.mountApplication('test-app');

                expect(mockLogger.debug).toHaveBeenCalledWith('挂载应用:', 'test-app');
                expect(mockLifecycleManager.mount).toHaveBeenCalledWith(mockApp);
                expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 挂载完成');
            });

            it('应该在应用不存在时抛出错误', async () => {
                mockAppRegistry.get.mockReturnValue(null);

                await expect(kernel.mountApplication('non-existent-app')).rejects.toThrow(MicroCoreError);
            });

            it('应该先加载未加载的应用', async () => {
                const mockApp: AppInstance = {
                    name: 'test-app',
                    status: APP_STATUS.NOT_LOADED,
                    config: {
                        name: 'test-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                };

                mockAppRegistry.get.mockReturnValue(mockApp);

                await kernel.mountApplication('test-app');

                expect(mockAppRegistry.updateStatus).toHaveBeenCalledWith('test-app', APP_STATUS.LOADING_SOURCE_CODE);
            });

            it('应该先引导未引导的应用', async () => {
                const mockApp: AppInstance = {
                    name: 'test-app',
                    status: APP_STATUS.NOT_BOOTSTRAPPED,
                    config: {
                        name: 'test-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                };

                mockAppRegistry.get.mockReturnValue(mockApp);

                await kernel.mountApplication('test-app');

                expect(mockLifecycleManager.bootstrap).toHaveBeenCalledWith(mockApp);
            });

            it('应该处理挂载失败的情况', async () => {
                const mockApp: AppInstance = {
                    name: 'test-app',
                    status: APP_STATUS.NOT_MOUNTED,
                    config: {
                        name: 'test-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                };

                mockAppRegistry.get.mockReturnValue(mockApp);

                const error = new Error('挂载失败');
                mockLifecycleManager.mount.mockRejectedValue(error);

                await expect(kernel.mountApplication('test-app')).rejects.toThrow(error);
                expect(mockAppRegistry.setError).toHaveBeenCalledWith('test-app', error);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                await expect(kernel.mountApplication('test-app')).rejects.toThrow(MicroCoreError);
            });
        });

        describe('unmountApplication', () => {
            it('应该成功卸载应用', async () => {
                const mockApp: AppInstance = {
                    name: 'test-app',
                    status: APP_STATUS.MOUNTED,
                    config: {
                        name: 'test-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                };

                mockAppRegistry.get.mockReturnValue(mockApp);

                await kernel.unmountApplication('test-app');

                expect(mockLogger.debug).toHaveBeenCalledWith('卸载应用:', 'test-app');
                expect(mockLifecycleManager.unmount).toHaveBeenCalledWith(mockApp);
                expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 卸载完成');
            });

            it('应该在应用不存在时抛出错误', async () => {
                mockAppRegistry.get.mockReturnValue(null);

                await expect(kernel.unmountApplication('non-existent-app')).rejects.toThrow(MicroCoreError);
            });

            it('应该忽略未挂载的应用', async () => {
                const mockApp: AppInstance = {
                    name: 'test-app',
                    status: APP_STATUS.NOT_MOUNTED,
                    config: {
                        name: 'test-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                };

                mockAppRegistry.get.mockReturnValue(mockApp);

                await kernel.unmountApplication('test-app');

                expect(mockLifecycleManager.unmount).not.toHaveBeenCalled();
                expect(mockLogger.info).toHaveBeenCalledWith('应用 test-app 卸载完成');
            });

            it('应该处理卸载失败的情况', async () => {
                const mockApp: AppInstance = {
                    name: 'test-app',
                    status: APP_STATUS.MOUNTED,
                    config: {
                        name: 'test-app',
                        entry: 'http://localhost:3000',
                        container: '#app',
                        activeWhen: '/test'
                    }
                };

                mockAppRegistry.get.mockReturnValue(mockApp);

                const error = new Error('卸载失败');
                mockLifecycleManager.unmount.mockRejectedValue(error);

                await expect(kernel.unmountApplication('test-app')).rejects.toThrow(error);
                expect(mockAppRegistry.setError).toHaveBeenCalledWith('test-app', error);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                await expect(kernel.unmountApplication('test-app')).rejects.toThrow(MicroCoreError);
            });
        });
    });

    describe('插件系统', () => {
        describe('use', () => {
            it('应该成功安装插件', () => {
                const mockPlugin: any = {
                    name: 'test-plugin',
                    version: '1.0.0',
                    install: vi.fn()
                };

                kernel.use(mockPlugin, { option: 'value' });

                expect(mockLogger.debug).toHaveBeenCalledWith('安装插件:', 'test-plugin');
                expect(mockPlugin.install).toHaveBeenCalledWith(kernel, { option: 'value' });
                expect(mockEventBus.emit).toHaveBeenCalledWith('plugin:installed', {
                    plugin: mockPlugin,
                    options: { option: 'value' }
                });
                expect(mockLogger.info).toHaveBeenCalledWith('插件 test-plugin 安装完成');
            });

            it('应该处理无install方法的插件', () => {
                const mockPlugin: any = {
                    name: 'simple-plugin',
                    version: '1.0.0'
                };

                kernel.use(mockPlugin);

                expect(mockLogger.info).toHaveBeenCalledWith('插件 simple-plugin 安装完成');
            });

            it('应该在插件已存在时抛出错误', () => {
                const mockPlugin: any = {
                    name: 'test-plugin',
                    version: '1.0.0',
                    install: vi.fn()
                };

                kernel.use(mockPlugin);

                expect(() => {
                    kernel.use(mockPlugin);
                }).toThrow(MicroCoreError);
            });

            it('应该处理无效插件', () => {
                expect(() => {
                    kernel.use(null as any);
                }).toThrow(MicroCoreError);

                expect(() => {
                    kernel.use({ name: '' } as any);
                }).toThrow(MicroCoreError);
            });

            it('应该处理插件安装失败', () => {
                const error = new Error('安装失败');
                const mockPlugin: any = {
                    name: 'failing-plugin',
                    version: '1.0.0',
                    install: vi.fn().mockImplementation(() => {
                        throw error;
                    })
                };

                expect(() => {
                    kernel.use(mockPlugin);
                }).toThrow(MicroCoreError);

                expect(mockLogger.error).toHaveBeenCalledWith('插件 failing-plugin 安装失败:', error);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                const mockPlugin: any = {
                    name: 'test-plugin',
                    version: '1.0.0'
                };

                expect(() => {
                    kernel.use(mockPlugin);
                }).toThrow(MicroCoreError);
            });
        });

        describe('getPlugin', () => {
            it('应该返回已安装的插件', () => {
                const mockPlugin: any = {
                    name: 'test-plugin',
                    version: '1.0.0',
                    install: vi.fn()
                };

                kernel.use(mockPlugin);

                const result = kernel.getPlugin('test-plugin');
                expect(result).toBe(mockPlugin);
            });

            it('应该在插件不存在时返回null', () => {
                const result = kernel.getPlugin('non-existent-plugin');
                expect(result).toBeNull();
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                expect(() => {
                    kernel.getPlugin('test-plugin');
                }).toThrow(MicroCoreError);
            });
        });

        describe('getPlugins', () => {
            it('应该返回所有已安装的插件名称', () => {
                const plugin1: any = { name: 'plugin1', version: '1.0.0' };
                const plugin2: any = { name: 'plugin2', version: '1.0.0' };

                kernel.use(plugin1);
                kernel.use(plugin2);

                const result = kernel.getPlugins();
                expect(result).toContain('plugin1');
                expect(result).toContain('plugin2');
                expect(result.length).toBe(2);
            });

            it('应该在没有插件时返回空数组', () => {
                const result = kernel.getPlugins();
                expect(result).toEqual([]);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                expect(() => {
                    kernel.getPlugins();
                }).toThrow(MicroCoreError);
            });
        });

        describe('hasPlugin', () => {
            it('应该正确检查插件是否存在', () => {
                const mockPlugin: any = {
                    name: 'test-plugin',
                    version: '1.0.0'
                };

                expect(kernel.hasPlugin('test-plugin')).toBe(false);

                kernel.use(mockPlugin);

                expect(kernel.hasPlugin('test-plugin')).toBe(true);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                expect(() => {
                    kernel.hasPlugin('test-plugin');
                }).toThrow(MicroCoreError);
            });
        });
    });

    describe('获取器方法', () => {
        describe('getEventBus', () => {
            it('应该返回事件总线实例', () => {
                const result = kernel.getEventBus();
                expect(result).toBe(mockEventBus);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                expect(() => {
                    kernel.getEventBus();
                }).toThrow(MicroCoreError);
            });
        });

        describe('getAppRegistry', () => {
            it('应该返回应用注册中心实例', () => {
                const result = kernel.getAppRegistry();
                expect(result).toBe(mockAppRegistry);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                expect(() => {
                    kernel.getAppRegistry();
                }).toThrow(MicroCoreError);
            });
        });

        describe('getLifecycleManager', () => {
            it('应该返回生命周期管理器实例', () => {
                const result = kernel.getLifecycleManager();
                expect(result).toBe(mockLifecycleManager);
            });

            it('应该在内核销毁后抛出错误', async () => {
                await kernel.destroy();

                expect(() => {
                    kernel.getLifecycleManager();
                }).toThrow(MicroCoreError);
            });
        });
    });

    describe('状态检查方法', () => {
        describe('isKernelStarted', () => {
            it('应该正确返回内核启动状态', async () => {
                expect(kernel.isKernelStarted()).toBe(false);

                await kernel.start();
                expect(kernel.isKernelStarted()).toBe(true);

                await kernel.stop();
                expect(kernel.isKernelStarted()).toBe(false);
            });
        });

        describe('isKernelDestroyed', () => {
            it('应该正确返回内核销毁状态', async () => {
                expect(kernel.isKernelDestroyed()).toBe(false);

                await kernel.destroy();
                expect(kernel.isKernelDestroyed()).toBe(true);
            });
        });
    });

    describe('私有方法测试', () => {
        it('应该正确处理路由监听', async () => {
            await kernel.start();
            expect(mockLogger.debug).toHaveBeenCalledWith('启动路由监听');

            await kernel.stop();
            expect(mockLogger.debug).toHaveBeenCalledWith('停止路由监听');
        });
    });

    describe('边界情况和错误处理', () => {
        it('应该处理字符串类型的错误', async () => {
            const mockApp: any = {
                name: 'test-app',
                status: APP_STATUS.NOT_LOADED
            };

            mockAppRegistry.get.mockReturnValue(mockApp);
            mockAppRegistry.updateStatus.mockImplementation(() => {
                throw 'string error';
            });

            await expect(kernel.loadApplication('test-app')).rejects.toThrow();
            expect(mockAppRegistry.setError).toHaveBeenCalledWith('test-app', expect.any(Error));
        });

        it('应该处理插件安装时的字符串错误', () => {
            const mockPlugin: any = {
                name: 'failing-plugin',
                version: '1.0.0',
                install: vi.fn().mockImplementation(() => {
                    throw 'string error';
                })
            };

            expect(() => {
                kernel.use(mockPlugin);
            }).toThrow(MicroCoreError);
        });

        it('应该处理内核启动时的字符串错误', async () => {
            mockEventBus.emit.mockImplementation((event: string) => {
                if (event === 'kernel:starting') {
                    throw 'string error';
                }
            });

            await expect(kernel.start()).rejects.toThrow(MicroCoreError);
        });

        it('应该处理内核停止时的字符串错误', async () => {
            await kernel.start();

            mockEventBus.emit.mockImplementation((event: string) => {
                if (event === 'kernel:stopping') {
                    throw 'string error';
                }
            });

            await expect(kernel.stop()).rejects.toThrow(MicroCoreError);
        });
    });

    describe('性能测试', () => {
        it('应该高效处理大量应用注册', () => {
            const start = performance.now();

            for (let i = 0; i < 1000; i++) {
                const appConfig: any = {
                    name: `app-${i}`,
                    entry: `http://localhost:${3000 + i}`,
                    container: `#app-${i}`,
                    activeWhen: `/app-${i}`
                };
                kernel.registerApplication(appConfig);
            }

            const end = performance.now();
            expect(end - start).toBeLessThan(1000); // 应该在1秒内完成
            expect(mockAppRegistry.register).toHaveBeenCalledTimes(1000);
        });

        it('应该高效处理大量插件安装', () => {
            const start = performance.now();

            for (let i = 0; i < 100; i++) {
                const plugin: any = {
                    name: `plugin-${i}`,
                    version: '1.0.0'
                };
                kernel.use(plugin);
            }

            const end = performance.now();
            expect(end - start).toBeLessThan(500); // 应该在500ms内完成
            expect(kernel.getPlugins().length).toBe(100);
        });
    });

    describe('内存泄漏防护', () => {
        it('应该在销毁后清理所有引用', async () => {
            // 注册一些应用和插件
            const appConfig: any = {
                name: 'test-app',
                entry: 'http://localhost:3000',
                container: '#app',
                activeWhen: '/test'
            };
            kernel.registerApplication(appConfig);

            const plugin: any = {
                name: 'test-plugin',
                version: '1.0.0'
            };
            kernel.use(plugin);

            await kernel.start();
            await kernel.destroy();

            // 验证清理工作
            expect(mockAppRegistry.clear).toHaveBeenCalled();
            expect(mockLifecycleManager.clearHooks).toHaveBeenCalled();
            expect(mockEventBus.clear).toHaveBeenCalled();
            expect(kernel.isKernelDestroyed()).toBe(true);
        });

        it('应该正确处理重复操作', async () => {
            // 重复启动
            await kernel.start();
            await kernel.start();
            expect(mockLogger.warn).toHaveBeenCalledWith('内核已启动，忽略重复启动请求');

            // 重复停止
            await kernel.stop();
            await kernel.stop();
            expect(mockLogger.warn).toHaveBeenCalledWith('内核未启动，忽略停止请求');

            // 重复销毁
            await kernel.destroy();
            await kernel.destroy();
            expect(mockLogger.warn).toHaveBeenCalledWith('内核已销毁，忽略重复销毁请求');
        });
    });
});
