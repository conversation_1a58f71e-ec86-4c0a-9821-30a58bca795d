/**
 * 插件系统测试用例
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { PluginSystem } from '../src/runtime/plugin-system';
import type { Plugin } from '../src/types';

describe('PluginSystem', () => {
    let pluginSystem: PluginSystem;

    beforeEach(() => {
        pluginSystem = new PluginSystem();
    });

    afterEach(() => {
        pluginSystem.destroy();
    });

    describe('插件安装', () => {
        it('应该能够安装插件', () => {
            const mockPlugin: Plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            };

            pluginSystem.install(mockPlugin, {});
            expect(mockPlugin.install).toHaveBeenCalled();
            expect(pluginSystem.has('test-plugin')).toBe(true);
        });

        it('应该能够卸载插件', () => {
            const mockPlugin: Plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            };

            pluginSystem.install(mockPlugin, {});
            pluginSystem.uninstall('test-plugin');

            expect(mockPlugin.uninstall).toHaveBeenCalled();
            expect(pluginSystem.has('test-plugin')).toBe(false);
        });

        it('应该防止重复安装同名插件', () => {
            const mockPlugin1: Plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            };

            const mockPlugin2: Plugin = {
                name: 'test-plugin',
                version: '2.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            };

            pluginSystem.install(mockPlugin1, {});
            expect(() => pluginSystem.install(mockPlugin2, {})).toThrow();
        });
    });

    describe('插件查询', () => {
        it('应该能够检查插件是否存在', () => {
            const mockPlugin: Plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            };

            expect(pluginSystem.has('test-plugin')).toBe(false);
            pluginSystem.install(mockPlugin, {});
            expect(pluginSystem.has('test-plugin')).toBe(true);
        });

        it('应该能够获取插件实例', () => {
            const mockPlugin: Plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            };

            pluginSystem.install(mockPlugin, {});
            const plugin = pluginSystem.get('test-plugin');
            expect(plugin).toBe(mockPlugin);
        });

        it('应该能够获取所有插件', () => {
            const mockPlugin1: Plugin = {
                name: 'test-plugin-1',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            };

            const mockPlugin2: Plugin = {
                name: 'test-plugin-2',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            };

            pluginSystem.install(mockPlugin1, {});
            pluginSystem.install(mockPlugin2, {});

            const plugins = pluginSystem.getAll();
            expect(plugins).toHaveLength(2);
            expect(plugins.map(p => p.name)).toContain('test-plugin-1');
            expect(plugins.map(p => p.name)).toContain('test-plugin-2');
        });
    });

    describe('插件生命周期', () => {
        it('应该按正确顺序调用插件生命周期', () => {
            const callOrder: string[] = [];

            const mockPlugin: Plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(() => callOrder.push('install')),
                uninstall: vi.fn(() => callOrder.push('uninstall'))
            };

            pluginSystem.install(mockPlugin, {});
            pluginSystem.uninstall('test-plugin');

            expect(callOrder).toEqual(['install', 'uninstall']);
        });

        it('应该处理插件安装错误', () => {
            const mockPlugin: Plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(() => {
                    throw new Error('安装失败');
                }),
                uninstall: vi.fn()
            };

            expect(() => pluginSystem.install(mockPlugin, {})).toThrow('安装失败');
            expect(pluginSystem.has('test-plugin')).toBe(false);
        });

        it('应该处理插件卸载错误', () => {
            const mockPlugin: Plugin = {
                name: 'test-plugin',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn(() => {
                    throw new Error('卸载失败');
                })
            };

            pluginSystem.install(mockPlugin, {});
            expect(() => pluginSystem.uninstall('test-plugin')).toThrow('卸载失败');
            expect(pluginSystem.has('test-plugin')).toBe(true);
        });
    });

    describe('插件销毁', () => {
        it('应该能够销毁所有插件', () => {
            const mockPlugin1: Plugin = {
                name: 'test-plugin-1',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            };

            const mockPlugin2: Plugin = {
                name: 'test-plugin-2',
                version: '1.0.0',
                install: vi.fn(),
                uninstall: vi.fn()
            };

            pluginSystem.install(mockPlugin1, {});
            pluginSystem.install(mockPlugin2, {});
            pluginSystem.destroy();

            expect(mockPlugin1.uninstall).toHaveBeenCalled();
            expect(mockPlugin2.uninstall).toHaveBeenCalled();
            expect(pluginSystem.getAll()).toHaveLength(0);
        });
    });
});