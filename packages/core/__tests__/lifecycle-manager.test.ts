/**
 * 生命周期管理器测试用例
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { EventBus } from '../src/communication/event-bus';
import { LifecycleManager } from '../src/runtime/lifecycle-manager';

// 模拟依赖
vi.mock('../src/communication/event-bus');

describe('LifecycleManager', () => {
    let lifecycleManager: LifecycleManager;
    let mockEventBus: EventBus;

    beforeEach(() => {
        // 创建模拟对象
        mockEventBus = {
            on: vi.fn(),
            off: vi.fn(),
            emit: vi.fn(),
            once: vi.fn()
        } as unknown as EventBus;

        // 重置模拟
        vi.resetAllMocks();

        // 创建生命周期管理器实例
        lifecycleManager = new LifecycleManager({
            eventBus: mockEventBus
        });
    });

    describe('生命周期钩子注册', () => {
        it('应该能够注册生命周期钩子', () => {
            const handler = vi.fn();
            lifecycleManager.register('init', handler);

            expect(lifecycleManager.hasHook('init')).toBe(true);
        });

        it('应该能够注册多个相同类型的钩子', () => {
            const handler1 = vi.fn();
            const handler2 = vi.fn();

            lifecycleManager.register('init', handler1);
            lifecycleManager.register('init', handler2);

            const hooks = lifecycleManager.getHooks('init');
            expect(hooks).toHaveLength(2);
            expect(hooks).toContain(handler1);
            expect(hooks).toContain(handler2);
        });

        it('应该能够注册带优先级的钩子', () => {
            const handler1 = vi.fn();
            const handler2 = vi.fn();
            const handler3 = vi.fn();

            lifecycleManager.register('init', handler2, { priority: 2 });
            lifecycleManager.register('init', handler3, { priority: 3 });
            lifecycleManager.register('init', handler1, { priority: 1 });

            const hooks = lifecycleManager.getHooks('init');
            expect(hooks[0]).toBe(handler3); // 优先级最高的应该在最前面
            expect(hooks[1]).toBe(handler2);
            expect(hooks[2]).toBe(handler1);
        });
    });

    describe('生命周期钩子取消注册', () => {
        it('应该能够取消注册特定钩子', () => {
            const handler1 = vi.fn();
            const handler2 = vi.fn();

            lifecycleManager.register('init', handler1);
            lifecycleManager.register('init', handler2);
            lifecycleManager.unregister('init', handler1);

            const hooks = lifecycleManager.getHooks('init');
            expect(hooks).toHaveLength(1);
            expect(hooks).not.toContain(handler1);
            expect(hooks).toContain(handler2);
        });

        it('应该能够取消注册所有特定类型的钩子', () => {
            const handler1 = vi.fn();
            const handler2 = vi.fn();

            lifecycleManager.register('init', handler1);
            lifecycleManager.register('init', handler2);
            lifecycleManager.unregisterAll('init');

            expect(lifecycleManager.hasHook('init')).toBe(false);
            expect(lifecycleManager.getHooks('init')).toHaveLength(0);
        });
    });

    describe('生命周期钩子触发', () => {
        it('应该能够触发生命周期钩子', async () => {
            const handler = vi.fn();
            lifecycleManager.register('init', handler);

            await lifecycleManager.trigger('init', { data: 'test' });

            expect(handler).toHaveBeenCalledWith({ data: 'test' });
        });

        it('应该按照优先级顺序触发钩子', async () => {
            const result: number[] = [];
            const handler1 = vi.fn(() => result.push(1));
            const handler2 = vi.fn(() => result.push(2));
            const handler3 = vi.fn(() => result.push(3));

            lifecycleManager.register('init', handler2, { priority: 2 });
            lifecycleManager.register('init', handler3, { priority: 3 });
            lifecycleManager.register('init', handler1, { priority: 1 });

            await lifecycleManager.trigger('init');

            expect(result).toEqual([3, 2, 1]);
        });

        it('应该在触发钩子时发出事件', async () => {
            const handler = vi.fn();
            lifecycleManager.register('init', handler);

            const data = { data: 'test' };
            await lifecycleManager.trigger('init', data);

            expect(mockEventBus.emit).toHaveBeenCalledWith('lifecycle:before-init', data);
            expect(mockEventBus.emit).toHaveBeenCalledWith('lifecycle:after-init', data);
        });

        it('应该支持异步钩子', async () => {
            const result: string[] = [];
            const asyncHandler = vi.fn(async () => {
                await new Promise(resolve => setTimeout(resolve, 10));
                result.push('async');
            });
            const syncHandler = vi.fn(() => {
                result.push('sync');
            });

            lifecycleManager.register('init', asyncHandler);
            lifecycleManager.register('init', syncHandler);

            await lifecycleManager.trigger('init');

            expect(asyncHandler).toHaveBeenCalledTimes(1);
            expect(syncHandler).toHaveBeenCalledTimes(1);
            expect(result).toContain('async');
            expect(result).toContain('sync');
        });
    });

    describe('生命周期钩子错误处理', () => {
        it('应该捕获钩子执行过程中的错误', async () => {
            const errorHandler = vi.fn();
            const handler = vi.fn(() => {
                throw new Error('测试错误');
            });

            lifecycleManager.onError(errorHandler);
            lifecycleManager.register('init', handler);

            await lifecycleManager.trigger('init');

            expect(errorHandler).toHaveBeenCalledTimes(1);
            expect(errorHandler.mock.calls[0][0]).toBeInstanceOf(Error);
            expect(errorHandler.mock.calls[0][0].message).toBe('测试错误');
        });

        it('应该在没有错误处理函数时不抛出错误', async () => {
            const handler = vi.fn(() => {
                throw new Error('测试错误');
            });

            lifecycleManager.register('init', handler);

            await expect(lifecycleManager.trigger('init')).resolves.not.toThrow();
        });
    });

    describe('生命周期阶段管理', () => {
        it('应该能够设置和获取当前阶段', () => {
            lifecycleManager.setStage('initialized');
            expect(lifecycleManager.getStage()).toBe('initialized');
        });

        it('应该在设置阶段时发出事件', () => {
            lifecycleManager.setStage('initialized');
            expect(mockEventBus.emit).toHaveBeenCalledWith('lifecycle:stage-changed', {
                stage: 'initialized',
                previousStage: undefined
            });
        });

        it('应该能够检查是否处于特定阶段', () => {
            lifecycleManager.setStage('initialized');
            expect(lifecycleManager.isStage('initialized')).toBe(true);
            expect(lifecycleManager.isStage('destroyed')).toBe(false);
        });
    });

    describe('生命周期阶段转换', () => {
        it('应该能够执行阶段转换', async () => {
            const initHandler = vi.fn();
            const startHandler = vi.fn();

            lifecycleManager.register('init', initHandler);
            lifecycleManager.register('start', startHandler);

            await lifecycleManager.transition('init', 'initialized');
            await lifecycleManager.transition('start', 'started');

            expect(initHandler).toHaveBeenCalledTimes(1);
            expect(startHandler).toHaveBeenCalledTimes(1);
            expect(lifecycleManager.getStage()).toBe('started');
        });

        it('应该在阶段转换时传递数据', async () => {
            const initHandler = vi.fn();
            const data = { config: { theme: 'dark' } };

            lifecycleManager.register('init', initHandler);
            await lifecycleManager.transition('init', 'initialized', data);

            expect(initHandler).toHaveBeenCalledWith(data);
        });

        it('应该在阶段转换前后发出事件', async () => {
            await lifecycleManager.transition('init', 'initialized');

            expect(mockEventBus.emit).toHaveBeenCalledWith('lifecycle:before-transition', {
                hook: 'init',
                targetStage: 'initialized',
                currentStage: undefined
            });

            expect(mockEventBus.emit).toHaveBeenCalledWith('lifecycle:after-transition', {
                hook: 'init',
                stage: 'initialized',
                previousStage: undefined
            });
        });
    });

    describe('生命周期销毁', () => {
        it('应该能够销毁生命周期管理器', () => {
            const handler = vi.fn();
            lifecycleManager.register('init', handler);
            lifecycleManager.destroy();

            expect(lifecycleManager.hasHook('init')).toBe(false);
        });

        it('应该在销毁时发出事件', () => {
            lifecycleManager.destroy();
            expect(mockEventBus.emit).toHaveBeenCalledWith('lifecycle:destroyed');
        });
    });
});