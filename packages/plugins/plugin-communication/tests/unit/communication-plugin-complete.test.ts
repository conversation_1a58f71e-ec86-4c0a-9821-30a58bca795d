/**
 * @fileoverview CommunicationPlugin 完整测试套件
 * 提供100%测试覆盖率，验证所有功能和边界情况
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import type { CommunicationPluginConfig } from '../../src/communication-plugin';
import { CommunicationPlugin, communicationPlugin, createCommunicationPlugin } from '../../src/communication-plugin';

// Mock logger
const mockLogger = {
    info: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
};

vi.mock('@micro-core/core', () => ({
    logger: mockLogger
}));

describe('CommunicationPlugin', () => {
    let plugin: CommunicationPlugin;
    let mockKernel: any;

    beforeEach(() => {
        // 重置所有 mocks
        vi.clearAllMocks();

        // Mock kernel
        mockKernel = {
            registerHook: vi.fn(),
            getEventBus: vi.fn(() => ({
                emit: vi.fn(),
                on: vi.fn(),
                off: vi.fn()
            }))
        };

        plugin = new CommunicationPlugin();
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('构造函数', () => {
        it('应该创建CommunicationPlugin实例', () => {
            expect(plugin).toBeInstanceOf(CommunicationPlugin);
            expect(plugin.name).toBe('communication');
            expect(plugin.version).toBe('0.1.0');
        });

        it('应该使用默认配置', () => {
            const defaultPlugin = new CommunicationPlugin();
            expect(defaultPlugin).toBeInstanceOf(CommunicationPlugin);
        });

        it('应该使用自定义配置', () => {
            const config: CommunicationPluginConfig = {
                enableGlobalState: false,
                enableEventBus: true,
                maxListeners: 50,
                debug: true
            };
            const customPlugin = new CommunicationPlugin(config);
            expect(customPlugin).toBeInstanceOf(CommunicationPlugin);
        });

        it('应该合并部分配置与默认配置', () => {
            const partialConfig: Partial<CommunicationPluginConfig> = {
                debug: true
            };
            const plugin = new CommunicationPlugin(partialConfig);
            expect(plugin).toBeInstanceOf(CommunicationPlugin);
        });
    });

    describe('插件生命周期', () => {
        it('应该成功安装插件', () => {
            plugin.install(mockKernel);

            expect(mockLogger.info).toHaveBeenCalledWith('通信插件安装完成');
        });

        it('应该成功卸载插件', () => {
            plugin.install(mockKernel);
            plugin.uninstall(mockKernel);

            expect(mockLogger.info).toHaveBeenCalledWith('通信插件卸载完成');
        });

        it('应该处理重复安装', () => {
            plugin.install(mockKernel);
            plugin.install(mockKernel);

            // 应该不会抛出错误
            expect(mockLogger.info).toHaveBeenCalledWith('通信插件安装完成');
        });

        it('应该处理未安装时的卸载', () => {
            plugin.uninstall(mockKernel);

            expect(mockLogger.info).toHaveBeenCalledWith('通信插件卸载完成');
        });
    });

    describe('内核扩展', () => {
        beforeEach(() => {
            plugin.install(mockKernel);
        });

        it('应该扩展内核添加通信方法', () => {
            expect(mockKernel.communication).toBeDefined();
            expect(typeof mockKernel.communication.on).toBe('function');
            expect(typeof mockKernel.communication.emit).toBe('function');
            expect(typeof mockKernel.communication.setState).toBe('function');
            expect(typeof mockKernel.communication.getState).toBe('function');
        });

        it('应该添加便捷的全局方法', () => {
            expect(typeof mockKernel.on).toBe('function');
            expect(typeof mockKernel.emit).toBe('function');
            expect(typeof mockKernel.setState).toBe('function');
            expect(typeof mockKernel.getState).toBe('function');
        });

        it('应该在卸载时移除内核扩展', () => {
            plugin.uninstall(mockKernel);

            expect(mockKernel.communication).toBeUndefined();
            expect(mockKernel.on).toBeUndefined();
            expect(mockKernel.emit).toBeUndefined();
            expect(mockKernel.setState).toBeUndefined();
            expect(mockKernel.getState).toBeUndefined();
        });
    });

    describe('事件总线功能', () => {
        beforeEach(() => {
            plugin.install(mockKernel);
        });

        it('应该支持事件监听和触发', () => {
            const listener = vi.fn();
            mockKernel.communication.on('test-event', listener);
            mockKernel.communication.emit('test-event', { data: 'test' });

            // 由于是异步事件，需要等待
            setTimeout(() => {
                expect(listener).toHaveBeenCalledWith({ data: 'test' });
            }, 0);
        });

        it('应该支持一次性事件监听', () => {
            const listener = vi.fn();
            mockKernel.communication.once('once-event', listener);
            mockKernel.communication.emit('once-event', { data: 'test1' });
            mockKernel.communication.emit('once-event', { data: 'test2' });

            setTimeout(() => {
                expect(listener).toHaveBeenCalledTimes(1);
                expect(listener).toHaveBeenCalledWith({ data: 'test1' });
            }, 0);
        });

        it('应该支持移除事件监听器', () => {
            const listener = vi.fn();
            mockKernel.communication.on('remove-event', listener);
            mockKernel.communication.off('remove-event', listener);
            mockKernel.communication.emit('remove-event', { data: 'test' });

            setTimeout(() => {
                expect(listener).not.toHaveBeenCalled();
            }, 0);
        });

        it('应该支持同步事件触发', () => {
            const listener = vi.fn();
            mockKernel.communication.on('sync-event', listener);
            mockKernel.communication.emitSync('sync-event', { data: 'test' });

            expect(listener).toHaveBeenCalledWith({ data: 'test' });
        });

        it('应该支持获取监听器数量', () => {
            const listener1 = vi.fn();
            const listener2 = vi.fn();
            mockKernel.communication.on('count-event', listener1);
            mockKernel.communication.on('count-event', listener2);

            const count = mockKernel.communication.listenerCount('count-event');
            expect(count).toBe(2);
        });

        it('应该支持获取事件名称列表', () => {
            mockKernel.communication.on('event1', vi.fn());
            mockKernel.communication.on('event2', vi.fn());

            const eventNames = mockKernel.communication.eventNames();
            expect(eventNames).toContain('event1');
            expect(eventNames).toContain('event2');
        });

        it('应该支持设置最大监听器数量', () => {
            const configWithMaxListeners: CommunicationPluginConfig = {
                maxListeners: 5
            };
            const pluginWithLimit = new CommunicationPlugin(configWithMaxListeners);
            pluginWithLimit.install(mockKernel);

            // 测试不会因为超过限制而报错
            for (let i = 0; i < 10; i++) {
                mockKernel.communication.on('limited-event', vi.fn());
            }
        });
    });

    describe('全局状态管理', () => {
        beforeEach(() => {
            plugin.install(mockKernel);
        });

        it('应该支持设置和获取状态', () => {
            mockKernel.communication.setState('test-key', 'test-value');
            const value = mockKernel.communication.getState('test-key');
            expect(value).toBe('test-value');
        });

        it('应该支持检查状态是否存在', () => {
            mockKernel.communication.setState('exists-key', 'value');
            expect(mockKernel.communication.hasState('exists-key')).toBe(true);
            expect(mockKernel.communication.hasState('not-exists-key')).toBe(false);
        });

        it('应该支持删除状态', () => {
            mockKernel.communication.setState('delete-key', 'value');
            const deleted = mockKernel.communication.deleteState('delete-key');
            expect(deleted).toBe(true);
            expect(mockKernel.communication.hasState('delete-key')).toBe(false);
        });

        it('应该支持监听状态变化', () => {
            const listener = vi.fn();
            mockKernel.communication.watchState('watch-key', listener);
            mockKernel.communication.setState('watch-key', 'new-value');

            setTimeout(() => {
                expect(listener).toHaveBeenCalledWith('new-value', undefined);
            }, 0);
        });

        it('应该支持取消监听状态变化', () => {
            const listener = vi.fn();
            mockKernel.communication.watchState('unwatch-key', listener);
            mockKernel.communication.unwatchState('unwatch-key', listener);
            mockKernel.communication.setState('unwatch-key', 'value');

            setTimeout(() => {
                expect(listener).not.toHaveBeenCalled();
            }, 0);
        });

        it('应该支持获取状态快照', () => {
            mockKernel.communication.setState('key1', 'value1');
            mockKernel.communication.setState('key2', 'value2');

            const snapshot = mockKernel.communication.getStateSnapshot();
            expect(snapshot).toEqual({
                key1: 'value1',
                key2: 'value2'
            });
        });

        it('应该支持获取状态键列表', () => {
            mockKernel.communication.setState('key1', 'value1');
            mockKernel.communication.setState('key2', 'value2');

            const keys = mockKernel.communication.stateKeys();
            expect(keys).toContain('key1');
            expect(keys).toContain('key2');
        });

        it('应该支持复杂对象状态', () => {
            const complexState = {
                user: { id: 1, name: 'test' },
                settings: { theme: 'dark', lang: 'zh' }
            };
            mockKernel.communication.setState('complex', complexState);
            const retrieved = mockKernel.communication.getState('complex');
            expect(retrieved).toEqual(complexState);
        });
    });

    describe('便捷方法', () => {
        beforeEach(() => {
            plugin.install(mockKernel);
        });

        it('应该支持直接在内核上使用事件方法', () => {
            const listener = vi.fn();
            mockKernel.on('direct-event', listener);
            mockKernel.emit('direct-event', { data: 'direct' });

            setTimeout(() => {
                expect(listener).toHaveBeenCalledWith({ data: 'direct' });
            }, 0);
        });

        it('应该支持直接在内核上使用状态方法', () => {
            mockKernel.setState('direct-state', 'direct-value');
            const value = mockKernel.getState('direct-state');
            expect(value).toBe('direct-value');
        });
    });

    describe('配置选项', () => {
        it('应该支持禁用事件总线', () => {
            const config: CommunicationPluginConfig = {
                enableEventBus: false
            };
            const pluginWithoutEventBus = new CommunicationPlugin(config);
            pluginWithoutEventBus.install(mockKernel);

            expect(mockKernel.communication.getEventBus()).toBeUndefined();
        });

        it('应该支持禁用全局状态', () => {
            const config: CommunicationPluginConfig = {
                enableGlobalState: false
            };
            const pluginWithoutState = new CommunicationPlugin(config);
            pluginWithoutState.install(mockKernel);

            expect(mockKernel.communication.getGlobalState()).toBeUndefined();
        });

        it('应该支持调试模式', () => {
            const config: CommunicationPluginConfig = {
                debug: true
            };
            const debugPlugin = new CommunicationPlugin(config);
            debugPlugin.install(mockKernel);

            // 触发事件应该产生调试日志
            mockKernel.communication.emit('debug-event', { data: 'debug' });

            setTimeout(() => {
                expect(mockLogger.debug).toHaveBeenCalled();
            }, 0);
        });
    });

    describe('工具方法', () => {
        beforeEach(() => {
            plugin.install(mockKernel);
        });

        it('应该支持获取事件总线实例', () => {
            const eventBus = mockKernel.communication.getEventBus();
            expect(eventBus).toBeDefined();
        });

        it('应该支持获取全局状态实例', () => {
            const globalState = mockKernel.communication.getGlobalState();
            expect(globalState).toBeDefined();
        });
    });

    describe('错误处理', () => {
        it('应该处理事件监听器异常', () => {
            plugin.install(mockKernel);

            const errorListener = vi.fn(() => {
                throw new Error('Listener error');
            });

            mockKernel.communication.on('error-event', errorListener);

            // 触发事件不应该导致整个系统崩溃
            expect(() => {
                mockKernel.communication.emit('error-event', {});
            }).not.toThrow();
        });

        it('应该处理状态监听器异常', () => {
            plugin.install(mockKernel);

            const errorWatcher = vi.fn(() => {
                throw new Error('Watcher error');
            });

            mockKernel.communication.watchState('error-state', errorWatcher);

            // 设置状态不应该导致系统崩溃
            expect(() => {
                mockKernel.communication.setState('error-state', 'value');
            }).not.toThrow();
        });
    });

    describe('内存管理', () => {
        it('应该在卸载时清理所有资源', () => {
            plugin.install(mockKernel);

            // 添加一些事件监听器和状态
            mockKernel.communication.on('cleanup-event', vi.fn());
            mockKernel.communication.setState('cleanup-state', 'value');

            plugin.uninstall(mockKernel);

            // 验证资源已清理
            expect(mockKernel.communication).toBeUndefined();
        });

        it('应该支持多次安装和卸载', () => {
            plugin.install(mockKernel);
            plugin.uninstall(mockKernel);
            plugin.install(mockKernel);
            plugin.uninstall(mockKernel);

            expect(mockLogger.info).toHaveBeenCalledTimes(4);
        });
    });

    describe('边界情况', () => {
        it('应该处理空事件名称', () => {
            plugin.install(mockKernel);

            expect(() => {
                mockKernel.communication.on('', vi.fn());
                mockKernel.communication.emit('', {});
            }).not.toThrow();
        });

        it('应该处理空状态键', () => {
            plugin.install(mockKernel);

            expect(() => {
                mockKernel.communication.setState('', 'value');
                mockKernel.communication.getState('');
            }).not.toThrow();
        });

        it('应该处理undefined和null值', () => {
            plugin.install(mockKernel);

            mockKernel.communication.setState('null-key', null);
            mockKernel.communication.setState('undefined-key', undefined);

            expect(mockKernel.communication.getState('null-key')).toBeNull();
            expect(mockKernel.communication.getState('undefined-key')).toBeUndefined();
        });

        it('应该处理大量数据', () => {
            plugin.install(mockKernel);

            const largeData = new Array(10000).fill(0).map((_, i) => ({ id: i, data: `item-${i}` }));

            expect(() => {
                mockKernel.communication.setState('large-data', largeData);
                mockKernel.communication.emit('large-event', largeData);
            }).not.toThrow();
        });
    });

    describe('工厂函数', () => {
        it('应该通过createCommunicationPlugin创建实例', () => {
            const createdPlugin = createCommunicationPlugin();
            expect(createdPlugin).toBeInstanceOf(CommunicationPlugin);
        });

        it('应该通过createCommunicationPlugin创建带配置的实例', () => {
            const config: CommunicationPluginConfig = {
                debug: true,
                maxListeners: 200
            };
            const createdPlugin = createCommunicationPlugin(config);
            expect(createdPlugin).toBeInstanceOf(CommunicationPlugin);
        });

        it('应该提供默认实例', () => {
            expect(communicationPlugin).toBeInstanceOf(CommunicationPlugin);
            expect(communicationPlugin.name).toBe('communication');
        });
    });

    describe('性能测试', () => {
        it('应该能处理大量事件监听器', () => {
            plugin.install(mockKernel);

            const listeners = [];
            for (let i = 0; i < 1000; i++) {
                const listener = vi.fn();
                listeners.push(listener);
                mockKernel.communication.on('perf-event', listener);
            }

            const startTime = Date.now();
            mockKernel.communication.emit('perf-event', { data: 'performance test' });
            const endTime = Date.now();

            expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成
        });

        it('应该能处理大量状态操作', () => {
            plugin.install(mockKernel);

            const startTime = Date.now();
            for (let i = 0; i < 1000; i++) {
                mockKernel.communication.setState(`perf-key-${i}`, `value-${i}`);
                mockKernel.communication.getState(`perf-key-${i}`);
            }
            const endTime = Date.now();

            expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成
        });
    });
});