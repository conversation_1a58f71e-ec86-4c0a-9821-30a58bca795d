/**
 * Metrics 插件测试
 */

// 模拟 EventBus 类型
interface EventBus {
    on: (event: string, handler: Function) => void;
    off: (event: string, handler: Function) => void;
    emit: (event: string, ...args: any[]) => void;
    once: (event: string, handler: Function) => void;
}
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { MetricsPlugin } from '../plugin-metrics/src';

// 模拟依赖
const MockEventBus = vi.fn().mockImplementation(() => ({
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    once: vi.fn()
}));

describe('MetricsPlugin', () => {
    let metricsPlugin: MetricsPlugin;
    let mockEventBus: EventBus;
    let mockConfig: any;
    let originalPerformance: any;

    beforeEach(() => {
        // 保存原始性能 API
        originalPerformance = global.performance;

        // 模拟性能 API
        global.performance = {
            now: vi.fn().mockReturnValue(100),
            mark: vi.fn(),
            measure: vi.fn(),
            getEntriesByType: vi.fn().mockReturnValue([]),
            getEntriesByName: vi.fn().mockReturnValue([]),
            clearMarks: vi.fn(),
            clearMeasures: vi.fn()
        } as any;

        // 重置所有模拟
        vi.resetAllMocks();

        // 创建模拟事件总线
        mockEventBus = MockEventBus();

        // 创建模拟配置
        mockConfig = {
            enabled: true,
            sampleRate: 1.0, // 100% 采样率
            reportingEndpoint: 'https://api.example.com/metrics',
            reportingInterval: 60000, // 60秒
            customMetrics: [],
            autoTrackPageViews: true,
            autoTrackErrors: true,
            autoTrackPerformance: true,
            autoTrackNavigation: true,
            dimensions: {
                appVersion: '1.0.0',
                environment: 'test'
            }
        };

        // 创建插件实例
        metricsPlugin = new MetricsPlugin();

        // 模拟 fetch API
        global.fetch = vi.fn().mockResolvedValue({
            ok: true,
            json: () => Promise.resolve({ success: true })
        });
    });

    afterEach(() => {
        // 恢复原始性能 API
        global.performance = originalPerformance;

        vi.clearAllMocks();
    });

    describe('插件初始化', () => {
        it('应该能够正确初始化插件', async () => {
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);
            expect(mockEventBus.on).toHaveBeenCalled();
        });

        it('应该在没有配置时使用默认配置', async () => {
            await metricsPlugin.install({ eventBus: mockEventBus }, {});
            expect(mockEventBus.on).toHaveBeenCalled();
        });

        it('应该在禁用时不设置事件监听器', async () => {
            mockConfig.enabled = false;
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);
            expect(mockEventBus.on).not.toHaveBeenCalled();
        });

        it('应该在初始化时设置事件监听器', async () => {
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);
            expect(mockEventBus.on).toHaveBeenCalledWith('metrics:track', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('metrics:timing', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('metrics:count', expect.any(Function));
            expect(mockEventBus.on).toHaveBeenCalledWith('metrics:gauge', expect.any(Function));
        });
    });

    describe('指标跟踪', () => {
        it('应该能够跟踪自定义事件', async () => {
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟跟踪事件处理函数
            const trackHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'metrics:track'
            )[1];

            // 模拟事件数据
            const eventData = {
                name: 'button_click',
                properties: {
                    buttonId: 'submit',
                    pageId: 'checkout'
                }
            };

            // 调用跟踪处理函数
            trackHandler(eventData);

            // 验证事件跟踪
            expect(mockEventBus.emit).toHaveBeenCalledWith(
                'metrics:tracked',
                expect.objectContaining({
                    name: eventData.name,
                    properties: eventData.properties,
                    timestamp: expect.any(Number)
                })
            );
        });

        it('应该能够跟踪计时指标', async () => {
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟计时事件处理函数
            const timingHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'metrics:timing'
            )[1];

            // 模拟计时数据
            const timingData = {
                name: 'page_load',
                duration: 1500, // 1.5秒
                category: 'performance'
            };

            // 调用计时处理函数
            timingHandler(timingData);

            // 验证计时跟踪
            expect(mockEventBus.emit).toHaveBeenCalledWith(
                'metrics:timed',
                expect.objectContaining({
                    name: timingData.name,
                    duration: timingData.duration,
                    category: timingData.category,
                    timestamp: expect.any(Number)
                })
            );
        });

        it('应该能够跟踪计数指标', async () => {
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟计数事件处理函数
            const countHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'metrics:count'
            )[1];

            // 模拟计数数据
            const countData = {
                name: 'api_errors',
                value: 1,
                tags: ['api', 'error']
            };

            // 调用计数处理函数
            countHandler(countData);

            // 验证计数跟踪
            expect(mockEventBus.emit).toHaveBeenCalledWith(
                'metrics:counted',
                expect.objectContaining({
                    name: countData.name,
                    value: countData.value,
                    tags: countData.tags,
                    timestamp: expect.any(Number)
                })
            );
        });

        it('应该能够跟踪仪表盘指标', async () => {
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟仪表盘事件处理函数
            const gaugeHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'metrics:gauge'
            )[1];

            // 模拟仪表盘数据
            const gaugeData = {
                name: 'memory_usage',
                value: 256,
                unit: 'MB'
            };

            // 调用仪表盘处理函数
            gaugeHandler(gaugeData);

            // 验证仪表盘跟踪
            expect(mockEventBus.emit).toHaveBeenCalledWith(
                'metrics:gauged',
                expect.objectContaining({
                    name: gaugeData.name,
                    value: gaugeData.value,
                    unit: gaugeData.unit,
                    timestamp: expect.any(Number)
                })
            );
        });
    });

    describe('自动跟踪', () => {
        it('应该能够自动跟踪页面浏览', async () => {
            // 启用页面浏览跟踪
            mockConfig.autoTrackPageViews = true;
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟路由变化事件
            mockEventBus.emit.mockClear();
            const routeChangeHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'router:change'
            )?.[1];

            if (routeChangeHandler) {
                // 模拟路由数据
                const routeData = {
                    from: '/home',
                    to: '/products',
                    params: { id: '123' }
                };

                // 调用路由变化处理函数
                routeChangeHandler(routeData);

                // 验证页面浏览跟踪
                expect(mockEventBus.emit).toHaveBeenCalledWith(
                    'metrics:track',
                    expect.objectContaining({
                        name: 'page_view',
                        properties: expect.objectContaining({
                            path: routeData.to,
                            params: routeData.params
                        })
                    })
                );
            }
        });

        it('应该能够自动跟踪错误', async () => {
            // 启用错误跟踪
            mockConfig.autoTrackErrors = true;
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟错误事件
            mockEventBus.emit.mockClear();
            const errorHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'error'
            )?.[1];

            if (errorHandler) {
                // 模拟错误数据
                const errorData = {
                    message: '测试错误',
                    stack: 'Error: 测试错误\n    at test.js:10:15',
                    source: 'app1'
                };

                // 调用错误处理函数
                errorHandler(errorData);

                // 验证错误跟踪
                expect(mockEventBus.emit).toHaveBeenCalledWith(
                    'metrics:track',
                    expect.objectContaining({
                        name: 'error',
                        properties: expect.objectContaining({
                            message: errorData.message,
                            source: errorData.source
                        })
                    })
                );
            }
        });

        it('应该能够自动跟踪性能指标', async () => {
            // 启用性能跟踪
            mockConfig.autoTrackPerformance = true;
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟性能指标
            const performanceEntries = [
                {
                    name: 'first-paint',
                    startTime: 100,
                    duration: 0,
                    entryType: 'paint'
                },
                {
                    name: 'first-contentful-paint',
                    startTime: 120,
                    duration: 0,
                    entryType: 'paint'
                }
            ];

            // 模拟性能 API
            global.performance.getEntriesByType = vi.fn().mockReturnValue(performanceEntries);

            // 模拟 DOMContentLoaded 事件
            const domContentLoadedHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'app:mounted'
            )?.[1];

            if (domContentLoadedHandler) {
                mockEventBus.emit.mockClear();
                // 调用 DOMContentLoaded 处理函数
                domContentLoadedHandler({ appId: 'app1' });

                // 验证性能指标跟踪
                expect(mockEventBus.emit).toHaveBeenCalledWith(
                    'metrics:timing',
                    expect.objectContaining({
                        name: 'first-paint',
                        duration: 100,
                        category: 'performance'
                    })
                );

                expect(mockEventBus.emit).toHaveBeenCalledWith(
                    'metrics:timing',
                    expect.objectContaining({
                        name: 'first-contentful-paint',
                        duration: 120,
                        category: 'performance'
                    })
                );
            }
        });
    });

    describe('指标报告', () => {
        it('应该能够定期发送指标报告', async () => {
            // 设置较短的报告间隔以便测试
            mockConfig.reportingInterval = 100; // 100ms
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟跟踪事件处理函数
            const trackHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'metrics:track'
            )[1];

            // 跟踪一些事件
            trackHandler({ name: 'event1' });
            trackHandler({ name: 'event2' });

            // 等待报告间隔
            await new Promise(resolve => setTimeout(resolve, 150));

            // 验证指标报告发送
            expect(global.fetch).toHaveBeenCalledWith(
                mockConfig.reportingEndpoint,
                expect.objectContaining({
                    method: 'POST',
                    headers: expect.objectContaining({
                        'Content-Type': 'application/json'
                    }),
                    body: expect.any(String)
                })
            );

            // 验证报告内容
            const reportData = JSON.parse((global.fetch as any).mock.calls[0][1].body);
            expect(reportData).toEqual(
                expect.objectContaining({
                    dimensions: mockConfig.dimensions,
                    metrics: expect.arrayContaining([
                        expect.objectContaining({ name: 'event1' }),
                        expect.objectContaining({ name: 'event2' })
                    ])
                })
            );
        });

        it('应该在报告发送失败时处理错误', async () => {
            // 设置较短的报告间隔以便测试
            mockConfig.reportingInterval = 100; // 100ms
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟跟踪事件处理函数
            const trackHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'metrics:track'
            )[1];

            // 跟踪一些事件
            trackHandler({ name: 'event1' });

            // 模拟 fetch API 失败
            global.fetch = vi.fn().mockRejectedValue(new Error('网络错误'));

            // 模拟控制台错误方法以捕获内部错误
            const consoleErrorSpy = vi.spyOn(console, 'error');

            // 等待报告间隔
            await new Promise(resolve => setTimeout(resolve, 150));

            // 验证错误处理
            expect(consoleErrorSpy).toHaveBeenCalledWith(
                expect.stringContaining('指标报告发送失败'),
                expect.any(Error)
            );
        });
    });

    describe('采样率', () => {
        it('应该根据采样率过滤指标', async () => {
            // 设置低采样率
            mockConfig.sampleRate = 0;
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟跟踪事件处理函数
            const trackHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'metrics:track'
            )[1];

            // 跟踪事件
            trackHandler({ name: 'event1' });

            // 验证事件未被跟踪（因为采样率为0）
            expect(mockEventBus.emit).not.toHaveBeenCalledWith(
                'metrics:tracked',
                expect.anything()
            );
        });
    });

    describe('自定义指标', () => {
        it('应该能够注册和跟踪自定义指标', async () => {
            // 添加自定义指标
            mockConfig.customMetrics = [
                {
                    name: 'memory_usage',
                    type: 'gauge',
                    collectFn: () => ({ value: 256, unit: 'MB' })
                }
            ];

            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 等待自定义指标收集（通常在初始化和定期收集时）
            await new Promise(resolve => setTimeout(resolve, 50));

            // 验证自定义指标跟踪
            expect(mockEventBus.emit).toHaveBeenCalledWith(
                'metrics:gauge',
                expect.objectContaining({
                    name: 'memory_usage',
                    value: 256,
                    unit: 'MB'
                })
            );
        });
    });

    describe('插件卸载', () => {
        it('应该能够正确卸载插件', async () => {
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);
            await metricsPlugin.uninstall({ eventBus: mockEventBus });

            expect(mockEventBus.off).toHaveBeenCalled();
        });

        it('应该在卸载时移除事件监听器', async () => {
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);
            await metricsPlugin.uninstall({ eventBus: mockEventBus });

            expect(mockEventBus.off).toHaveBeenCalledWith('metrics:track', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('metrics:timing', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('metrics:count', expect.any(Function));
            expect(mockEventBus.off).toHaveBeenCalledWith('metrics:gauge', expect.any(Function));
        });

        it('应该在卸载时发送最终的指标报告', async () => {
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);

            // 模拟跟踪事件处理函数
            const trackHandler = (mockEventBus.on as any).mock.calls.find(
                call => call[0] === 'metrics:track'
            )[1];

            // 跟踪一些事件
            trackHandler({ name: 'final_event' });

            // 重置 fetch 调用
            (global.fetch as any).mockClear();

            // 卸载插件
            await metricsPlugin.uninstall({ eventBus: mockEventBus });

            // 验证最终报告发送
            expect(global.fetch).toHaveBeenCalledWith(
                mockConfig.reportingEndpoint,
                expect.any(Object)
            );
        });

        it('应该在卸载时清除定时器', async () => {
            const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
            await metricsPlugin.install({ eventBus: mockEventBus }, mockConfig);
            await metricsPlugin.uninstall({ eventBus: mockEventBus });

            expect(clearIntervalSpy).toHaveBeenCalled();
        });
    });
});