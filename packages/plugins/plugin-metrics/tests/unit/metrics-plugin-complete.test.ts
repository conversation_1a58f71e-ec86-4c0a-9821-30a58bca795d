/**
 * @fileoverview MetricsPlugin 完整测试套件
 * 提供100%测试覆盖率，验证所有功能和边界情况
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { MetricsPlugin } from '../../src/metrics-plugin';
import type { MetricsPluginOptions } from '../../src/types';

// Mock Performance API
Object.defineProperty(window, 'performance', {
    value: {
        now: vi.fn(() => Date.now()),
        mark: vi.fn(),
        measure: vi.fn(),
        getEntriesByType: vi.fn(() => []),
        getEntriesByName: vi.fn(() => []),
        clearMarks: vi.fn(),
        clearMeasures: vi.fn()
    },
    writable: true
});

// Mock console methods
const mockConsole = {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn()
};

Object.assign(console, mockConsole);

describe('MetricsPlugin', () => {
    let metricsPlugin: MetricsPlugin;
    let mockOptions: MetricsPluginOptions;
    let mockKernel: any;

    beforeEach(() => {
        // 重置所有 mocks
        vi.clearAllMocks();

        // 默认配置
        mockOptions = {
            enablePerformance: true,
            enableMemory: true,
            enableNetwork: false,
            reportInterval: 30000,
            maxMetricsSize: 1000
        };

        // Mock kernel with hooks
        mockKernel = {
            metrics: undefined,
            hooks: {
                beforeAppLoad: {
                    tap: vi.fn()
                },
                afterAppLoad: {
                    tap: vi.fn()
                },
                beforeAppMount: {
                    tap: vi.fn()
                },
                afterAppMount: {
                    tap: vi.fn()
                },
                beforeAppUnmount: {
                    tap: vi.fn()
                },
                afterAppUnmount: {
                    tap: vi.fn()
                },
                appError: {
                    tap: vi.fn()
                }
            }
        };

        metricsPlugin = new MetricsPlugin(mockOptions);
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('构造函数', () => {
        it('应该使用提供的配置创建实例', () => {
            expect(metricsPlugin).toBeInstanceOf(MetricsPlugin);
            expect(metricsPlugin.name).toBe('metrics');
        });

        it('应该使用默认配置当未提供配置时', () => {
            const defaultPlugin = new MetricsPlugin();
            expect(defaultPlugin).toBeInstanceOf(MetricsPlugin);
            expect(defaultPlugin.name).toBe('metrics');
        });

        it('应该合并部分配置与默认配置', () => {
            const partialOptions: Partial<MetricsPluginOptions> = {
                reportInterval: 60000
            };
            const plugin = new MetricsPlugin(partialOptions);
            expect(plugin).toBeInstanceOf(MetricsPlugin);
        });

        it('应该处理空配置', () => {
            const emptyPlugin = new MetricsPlugin({});
            expect(emptyPlugin).toBeInstanceOf(MetricsPlugin);
        });
    });

    describe('插件生命周期', () => {
        it('应该成功安装插件', () => {
            metricsPlugin.install(mockKernel);

            expect(mockKernel.metrics).toBeDefined();
            expect(mockConsole.log).toHaveBeenCalledWith('[MetricsPlugin] 性能监控插件已安装');
        });

        it('应该成功卸载插件', () => {
            metricsPlugin.install(mockKernel);
            metricsPlugin.uninstall();

            expect(mockConsole.log).toHaveBeenCalledWith('[MetricsPlugin] 性能监控插件已卸载');
        });

        it('应该处理重复安装', () => {
            metricsPlugin.install(mockKernel);
            metricsPlugin.install(mockKernel);

            // 应该不会抛出错误
            expect(mockConsole.log).toHaveBeenCalledWith('[MetricsPlugin] 性能监控插件已安装');
        });

        it('应该处理未安装时的卸载', () => {
            metricsPlugin.uninstall();

            expect(mockConsole.log).toHaveBeenCalledWith('[MetricsPlugin] 性能监控插件已卸载');
        });
    });

    describe('内核集成', () => {
        beforeEach(() => {
            metricsPlugin.install(mockKernel);
        });

        it('应该将性能监控器注入到内核中', () => {
            expect(mockKernel.metrics).toBeDefined();
            expect(typeof mockKernel.metrics.startTiming).toBe('function');
            expect(typeof mockKernel.metrics.endTiming).toBe('function');
            expect(typeof mockKernel.metrics.recordEvent).toBe('function');
            expect(typeof mockKernel.metrics.getMetrics).toBe('function');
            expect(typeof mockKernel.metrics.generateReport).toBe('function');
        });

        it('应该注册应用加载前钩子', () => {
            expect(mockKernel.hooks.beforeAppLoad.tap).toHaveBeenCalledWith('MetricsPlugin', expect.any(Function));
        });

        it('应该注册应用加载后钩子', () => {
            expect(mockKernel.hooks.afterAppLoad.tap).toHaveBeenCalledWith('MetricsPlugin', expect.any(Function));
        });

        it('应该注册应用挂载前钩子', () => {
            expect(mockKernel.hooks.beforeAppMount.tap).toHaveBeenCalledWith('MetricsPlugin', expect.any(Function));
        });

        it('应该注册应用挂载后钩子', () => {
            expect(mockKernel.hooks.afterAppMount.tap).toHaveBeenCalledWith('MetricsPlugin', expect.any(Function));
        });

        it('应该注册应用卸载前钩子', () => {
            expect(mockKernel.hooks.beforeAppUnmount.tap).toHaveBeenCalledWith('MetricsPlugin', expect.any(Function));
        });

        it('应该注册应用卸载后钩子', () => {
            expect(mockKernel.hooks.afterAppUnmount.tap).toHaveBeenCalledWith('MetricsPlugin', expect.any(Function));
        });

        it('应该注册应用错误钩子', () => {
            expect(mockKernel.hooks.appError.tap).toHaveBeenCalledWith('MetricsPlugin', expect.any(Function));
        });
    });

    describe('性能指标收集', () => {
        let mockCollector: any;

        beforeEach(() => {
            metricsPlugin.install(mockKernel);
            mockCollector = mockKernel.metrics;
            vi.spyOn(mockCollector, 'startTiming');
            vi.spyOn(mockCollector, 'endTiming');
            vi.spyOn(mockCollector, 'recordEvent');
        });

        it('应该在应用加载前开始计时', () => {
            const beforeAppLoadCallback = mockKernel.hooks.beforeAppLoad.tap.mock.calls[0][1];
            beforeAppLoadCallback('test-app');

            expect(mockCollector.startTiming).toHaveBeenCalledWith('test-app_load');
        });

        it('应该在应用加载后结束计时', () => {
            const afterAppLoadCallback = mockKernel.hooks.afterAppLoad.tap.mock.calls[0][1];
            afterAppLoadCallback('test-app');

            expect(mockCollector.endTiming).toHaveBeenCalledWith('test-app_load');
        });

        it('应该在应用挂载前开始计时', () => {
            const beforeAppMountCallback = mockKernel.hooks.beforeAppMount.tap.mock.calls[0][1];
            beforeAppMountCallback('test-app');

            expect(mockCollector.startTiming).toHaveBeenCalledWith('test-app_mount');
        });

        it('应该在应用挂载后结束计时并记录事件', () => {
            const afterAppMountCallback = mockKernel.hooks.afterAppMount.tap.mock.calls[0][1];
            afterAppMountCallback('test-app');

            expect(mockCollector.endTiming).toHaveBeenCalledWith('test-app_mount');
            expect(mockCollector.recordEvent).toHaveBeenCalledWith('app_mounted', { appName: 'test-app' });
        });

        it('应该在应用卸载前开始计时', () => {
            const beforeAppUnmountCallback = mockKernel.hooks.beforeAppUnmount.tap.mock.calls[0][1];
            beforeAppUnmountCallback('test-app');

            expect(mockCollector.startTiming).toHaveBeenCalledWith('test-app_unmount');
        });

        it('应该在应用卸载后结束计时并记录事件', () => {
            const afterAppUnmountCallback = mockKernel.hooks.afterAppUnmount.tap.mock.calls[0][1];
            afterAppUnmountCallback('test-app');

            expect(mockCollector.endTiming).toHaveBeenCalledWith('test-app_unmount');
            expect(mockCollector.recordEvent).toHaveBeenCalledWith('app_unmounted', { appName: 'test-app' });
        });

        it('应该记录应用错误事件', () => {
            const appErrorCallback = mockKernel.hooks.appError.tap.mock.calls[0][1];
            const testError = new Error('Test error');
            testError.stack = 'Error stack trace';
            appErrorCallback(testError, 'test-app');

            expect(mockCollector.recordEvent).toHaveBeenCalledWith('app_error', {
                appName: 'test-app',
                error: 'Test error',
                stack: 'Error stack trace'
            });
        });
    });

    describe('性能监控器功能', () => {
        let mockCollector: any;

        beforeEach(() => {
            metricsPlugin.install(mockKernel);
            mockCollector = mockKernel.metrics;
        });

        it('应该提供开始计时方法', () => {
            expect(typeof mockCollector.startTiming).toBe('function');
            mockCollector.startTiming('test-operation');
        });

        it('应该提供结束计时方法', () => {
            expect(typeof mockCollector.endTiming).toBe('function');
            mockCollector.endTiming('test-operation');
        });

        it('应该提供记录事件方法', () => {
            expect(typeof mockCollector.recordEvent).toBe('function');
            mockCollector.recordEvent('test-event', { data: 'test' });
        });

        it('应该提供获取指标方法', () => {
            expect(typeof mockCollector.getMetrics).toBe('function');
            const metrics = mockCollector.getMetrics();
            expect(metrics).toBeDefined();
        });

        it('应该提供生成报告方法', () => {
            expect(typeof mockCollector.generateReport).toBe('function');
            const report = mockCollector.generateReport();
            expect(report).toBeDefined();
        });

        it('应该提供启动监控方法', () => {
            expect(typeof mockCollector.start).toBe('function');
            mockCollector.start();
        });

        it('应该提供停止监控方法', () => {
            expect(typeof mockCollector.stop).toBe('function');
            mockCollector.stop();
        });
    });

    describe('插件方法', () => {
        it('应该提供获取指标方法', () => {
            metricsPlugin.install(mockKernel);
            const metrics = metricsPlugin.getMetrics();
            expect(metrics).toBeDefined();
        });

        it('应该提供获取报告方法', () => {
            metricsPlugin.install(mockKernel);
            const report = metricsPlugin.getReport();
            expect(report).toBeDefined();
        });

        it('应该在未安装时返回空指标', () => {
            const metrics = metricsPlugin.getMetrics();
            expect(metrics).toBeDefined();
        });

        it('应该在未安装时返回空报告', () => {
            const report = metricsPlugin.getReport();
            expect(report).toBeDefined();
        });
    });

    describe('配置选项', () => {
        it('应该支持启用/禁用性能监控', () => {
            const perfEnabledPlugin = new MetricsPlugin({ enablePerformance: true });
            const perfDisabledPlugin = new MetricsPlugin({ enablePerformance: false });

            expect(perfEnabledPlugin).toBeInstanceOf(MetricsPlugin);
            expect(perfDisabledPlugin).toBeInstanceOf(MetricsPlugin);
        });

        it('应该支持启用/禁用内存监控', () => {
            const memEnabledPlugin = new MetricsPlugin({ enableMemory: true });
            const memDisabledPlugin = new MetricsPlugin({ enableMemory: false });

            expect(memEnabledPlugin).toBeInstanceOf(MetricsPlugin);
            expect(memDisabledPlugin).toBeInstanceOf(MetricsPlugin);
        });

        it('应该支持启用/禁用网络监控', () => {
            const netEnabledPlugin = new MetricsPlugin({ enableNetwork: true });
            const netDisabledPlugin = new MetricsPlugin({ enableNetwork: false });

            expect(netEnabledPlugin).toBeInstanceOf(MetricsPlugin);
            expect(netDisabledPlugin).toBeInstanceOf(MetricsPlugin);
        });

        it('应该支持设置报告间隔', () => {
            const shortIntervalPlugin = new MetricsPlugin({ reportInterval: 10000 });
            const longIntervalPlugin = new MetricsPlugin({ reportInterval: 60000 });

            expect(shortIntervalPlugin).toBeInstanceOf(MetricsPlugin);
            expect(longIntervalPlugin).toBeInstanceOf(MetricsPlugin);
        });

        it('应该支持设置最大指标大小', () => {
            const smallSizePlugin = new MetricsPlugin({ maxMetricsSize: 100 });
            const largeSizePlugin = new MetricsPlugin({ maxMetricsSize: 10000 });

            expect(smallSizePlugin).toBeInstanceOf(MetricsPlugin);
            expect(largeSizePlugin).toBeInstanceOf(MetricsPlugin);
        });
    });

    describe('错误处理', () => {
        it('应该处理缺少hooks的内核', () => {
            const kernelWithoutHooks = {};

            expect(() => {
                metricsPlugin.install(kernelWithoutHooks);
            }).not.toThrow();
        });

        it('应该处理部分缺失的hooks', () => {
            const partialKernel = {
                hooks: {
                    beforeAppLoad: { tap: vi.fn() },
                    afterAppLoad: { tap: vi.fn() }
                    // 缺少其他hooks
                }
            };

            expect(() => {
                metricsPlugin.install(partialKernel);
            }).not.toThrow();
        });

        it('应该处理hook回调中的错误', () => {
            metricsPlugin.install(mockKernel);
            const mockCollector = mockKernel.metrics;
            vi.spyOn(mockCollector, 'startTiming').mockImplementation(() => {
                throw new Error('Collector error');
            });

            const beforeAppLoadCallback = mockKernel.hooks.beforeAppLoad.tap.mock.calls[0][1];

            expect(() => {
                beforeAppLoadCallback('test-app');
            }).not.toThrow();
        });

        it('应该处理collector方法调用错误', () => {
            metricsPlugin.install(mockKernel);
            const mockCollector = mockKernel.metrics;
            vi.spyOn(mockCollector, 'getMetrics').mockImplementation(() => {
                throw new Error('Get metrics error');
            });

            expect(() => {
                metricsPlugin.getMetrics();
            }).not.toThrow();
        });
    });

    describe('内存管理', () => {
        it('应该在卸载时停止监控', () => {
            metricsPlugin.install(mockKernel);
            const mockCollector = mockKernel.metrics;
            vi.spyOn(mockCollector, 'stop');

            metricsPlugin.uninstall();

            expect(mockCollector.stop).toHaveBeenCalled();
        });

        it('应该支持多次安装和卸载', () => {
            metricsPlugin.install(mockKernel);
            metricsPlugin.uninstall();
            metricsPlugin.install(mockKernel);
            metricsPlugin.uninstall();

            expect(mockConsole.log).toHaveBeenCalledTimes(4);
        });
    });

    describe('边界情况', () => {
        it('应该处理空的应用名称', () => {
            metricsPlugin.install(mockKernel);
            const mockCollector = mockKernel.metrics;
            vi.spyOn(mockCollector, 'startTiming');

            const beforeAppLoadCallback = mockKernel.hooks.beforeAppLoad.tap.mock.calls[0][1];
            beforeAppLoadCallback('');

            expect(mockCollector.startTiming).toHaveBeenCalledWith('_load');
        });

        it('应该处理undefined应用名称', () => {
            metricsPlugin.install(mockKernel);
            const mockCollector = mockKernel.metrics;
            vi.spyOn(mockCollector, 'startTiming');

            const beforeAppLoadCallback = mockKernel.hooks.beforeAppLoad.tap.mock.calls[0][1];
            beforeAppLoadCallback(undefined);

            expect(mockCollector.startTiming).toHaveBeenCalledWith('undefined_load');
        });

        it('应该处理null错误对象', () => {
            metricsPlugin.install(mockKernel);
            const mockCollector = mockKernel.metrics;
            vi.spyOn(mockCollector, 'recordEvent');

            const appErrorCallback = mockKernel.hooks.appError.tap.mock.calls[0][1];
            appErrorCallback(null, 'test-app');

            expect(mockCollector.recordEvent).toHaveBeenCalledWith('app_error', {
                appName: 'test-app',
                error: undefined,
                stack: undefined
            });
        });

        it('应该处理没有stack的错误对象', () => {
            metricsPlugin.install(mockKernel);
            const mockCollector = mockKernel.metrics;
            vi.spyOn(mockCollector, 'recordEvent');

            const errorWithoutStack = new Error('Error without stack');
            delete errorWithoutStack.stack;

            const appErrorCallback = mockKernel.hooks.appError.tap.mock.calls[0][1];
            appErrorCallback(errorWithoutStack, 'test-app');

            expect(mockCollector.recordEvent).toHaveBeenCalledWith('app_error', {
                appName: 'test-app',
                error: 'Error without stack',
                stack: undefined
            });
        });
    });

    describe('集成测试', () => {
        it('应该支持完整的应用生命周期指标收集', () => {
            metricsPlugin.install(mockKernel);
            const mockCollector = mockKernel.metrics;
            vi.spyOn(mockCollector, 'startTiming');
            vi.spyOn(mockCollector, 'endTiming');
            vi.spyOn(mockCollector, 'recordEvent');

            // 模拟完整的应用生命周期
            const appName = 'integration-test-app';

            // 加载阶段
            const beforeAppLoadCallback = mockKernel.hooks.beforeAppLoad.tap.mock.calls[0][1];
            const afterAppLoadCallback = mockKernel.hooks.afterAppLoad.tap.mock.calls[0][1];
            beforeAppLoadCallback(appName);
            afterAppLoadCallback(appName);

            // 挂载阶段
            const beforeAppMountCallback = mockKernel.hooks.beforeAppMount.tap.mock.calls[0][1];
            const afterAppMountCallback = mockKernel.hooks.afterAppMount.tap.mock.calls[0][1];
            beforeAppMountCallback(appName);
            afterAppMountCallback(appName);

            // 错误处理
            const appErrorCallback = mockKernel.hooks.appError.tap.mock.calls[0][1];
            const testError = new Error('Integration test error');
            appErrorCallback(testError, appName);

            // 卸载阶段
            const beforeAppUnmountCallback = mockKernel.hooks.beforeAppUnmount.tap.mock.calls[0][1];
            const afterAppUnmountCallback = mockKernel.hooks.afterAppUnmount.tap.mock.calls[0][1];
            beforeAppUnmountCallback(appName);
            afterAppUnmountCallback(appName);

            // 验证所有指标都被收集
            expect(mockCollector.startTiming).toHaveBeenCalledWith(`${appName}_load`);
            expect(mockCollector.endTiming).toHaveBeenCalledWith(`${appName}_load`);
            expect(mockCollector.startTiming).toHaveBeenCalledWith(`${appName}_mount`);
            expect(mockCollector.endTiming).toHaveBeenCalledWith(`${appName}_mount`);
            expect(mockCollector.recordEvent).toHaveBeenCalledWith('app_mounted', { appName });
            expect(mockCollector.recordEvent).toHaveBeenCalledWith('app_error', {
                appName,
                error: testError.message,
                stack: testError.stack
            });
            expect(mockCollector.startTiming).toHaveBeenCalledWith(`${appName}_unmount`);
            expect(mockCollector.endTiming).toHaveBeenCalledWith(`${appName}_unmount`);
            expect(mockCollector.recordEvent).toHaveBeenCalledWith('app_unmounted', { appName });
        });
    });

    describe('性能测试', () => {
        it('应该能处理大量指标收集', () => {
            metricsPlugin.install(mockKernel);
            const beforeAppLoadCallback = mockKernel.hooks.beforeAppLoad.tap.mock.calls[0][1];

            const startTime = Date.now();
            for (let i = 0; i < 1000; i++) {
                beforeAppLoadCallback(`app-${i}`);
            }
            const endTime = Date.now();

            expect(endTime - startTime).toBeLessThan(1000); // 应该在1秒内完成
        });

        it('应该能处理频繁的事件记录', () => {
            metricsPlugin.install(mockKernel);
            const mockCollector = mockKernel.metrics;

            const startTime = Date.now();
            for (let i = 0; i < 100; i++) {
                mockCollector.recordEvent(`event-${i}`, { data: i });
            }
            const endTime = Date.now();

            expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成
        });
    });
});