/**
 * @fileoverview 错误边界组件
 * @description 提供React和Vue的错误边界组件，用于捕获和处理组件错误
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { ErrorLevel, ErrorType, globalErrorHandler, MicroCoreError } from './handler';

/**
 * 错误边界配置
 */
export interface ErrorBoundaryConfig {
    /** 是否显示错误详情 */
    showErrorDetails?: boolean;
    /** 自定义错误显示组件 */
    fallbackComponent?: any;
    /** 错误回调函数 */
    onError?: (error: Error, errorInfo?: any) => void;
    /** 是否自动重试 */
    enableRetry?: boolean;
    /** 重试次数限制 */
    maxRetries?: number;
    /** 重试间隔（毫秒） */
    retryDelay?: number;
}

/**
 * 错误边界状态
 */
export interface ErrorBoundaryState {
    /** 是否有错误 */
    hasError: boolean;
    /** 错误对象 */
    error?: Error;
    /** 错误信息 */
    errorInfo?: any;
    /** 重试次数 */
    retryCount: number;
    /** 错误ID */
    errorId?: string;
}

/**
 * React 错误边界基类
 */
export class ReactErrorBoundary {
    private config: ErrorBoundaryConfig;
    private state: ErrorBoundaryState;

    constructor(config: ErrorBoundaryConfig = {}) {
        this.config = {
            showErrorDetails: process.env.NODE_ENV === 'development',
            enableRetry: true,
            maxRetries: 3,
            retryDelay: 1000,
            ...config
        };

        this.state = {
            hasError: false,
            retryCount: 0
        };
    }

    /**
     * 捕获错误
     */
    static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
        return {
            hasError: true,
            error
        };
    }

    /**
     * 组件捕获错误时调用
     */
    componentDidCatch(error: Error, errorInfo: any): void {
        const microError = new MicroCoreError(error.message, {
            type: ErrorType.SYSTEM,
            level: ErrorLevel.ERROR,
            context: {
                appName: 'react-app',
                extra: {
                    componentStack: errorInfo.componentStack,
                    errorBoundary: 'ReactErrorBoundary'
                }
            },
            cause: error
        });

        // 更新状态
        this.state = {
            ...this.state,
            hasError: true,
            error,
            errorInfo,
            errorId: microError.id
        };

        // 报告错误
        globalErrorHandler.handle(microError);

        // 调用用户回调
        if (this.config.onError) {
            this.config.onError(error, errorInfo);
        }
    }

    /**
     * 重试操作
     */
    retry(): void {
        if (this.state.retryCount >= (this.config.maxRetries || 3)) {
            console.warn('已达到最大重试次数');
            return;
        }

        setTimeout(() => {
            this.state = {
                hasError: false,
                retryCount: this.state.retryCount + 1,
                error: undefined,
                errorInfo: undefined,
                errorId: undefined
            };
        }, this.config.retryDelay || 1000);
    }

    /**
     * 重置错误状态
     */
    reset(): void {
        this.state = {
            hasError: false,
            retryCount: 0,
            error: undefined,
            errorInfo: undefined,
            errorId: undefined
        };
    }

    /**
     * 渲染错误UI
     */
    renderError(): any {
        if (this.config.fallbackComponent) {
            return this.config.fallbackComponent({
                error: this.state.error,
                errorInfo: this.state.errorInfo,
                retry: () => this.retry(),
                reset: () => this.reset()
            });
        }

        return this.createDefaultErrorUI();
    }

    /**
     * 创建默认错误UI
     */
    private createDefaultErrorUI(): string {
        const { error, errorInfo, errorId } = this.state;
        const showDetails = this.config.showErrorDetails;
        const canRetry = this.config.enableRetry &&
            this.state.retryCount < (this.config.maxRetries || 3);

        return `
            <div style="
                padding: 20px;
                border: 1px solid #ff6b6b;
                border-radius: 8px;
                background-color: #ffe0e0;
                color: #d63031;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 20px;
            ">
                <h3 style="margin: 0 0 16px 0; font-size: 18px; display: flex; align-items: center;">
                    <span style="margin-right: 8px;">⚠️</span>
                    组件渲染出错
                </h3>
                
                <p style="margin: 0 0 16px 0; font-size: 14px;">
                    抱歉，此组件遇到了一个错误。${canRetry ? '您可以尝试重新加载。' : ''}
                </p>

                ${showDetails ? `
                    <details style="margin-bottom: 16px;">
                        <summary style="cursor: pointer; font-weight: bold; margin-bottom: 8px;">
                            错误详情
                        </summary>
                        <div style="
                            background-color: #f8f8f8;
                            padding: 12px;
                            border-radius: 4px;
                            font-family: 'Courier New', monospace;
                            font-size: 12px;
                            white-space: pre-wrap;
                            overflow-x: auto;
                        ">
                            <strong>错误消息:</strong> ${error?.message || '未知错误'}
                            ${errorId ? `\n<strong>错误ID:</strong> ${errorId}` : ''}
                            ${error?.stack ? `\n<strong>错误堆栈:</strong>\n${error.stack}` : ''}
                            ${errorInfo?.componentStack ? `\n<strong>组件堆栈:</strong>${errorInfo.componentStack}` : ''}
                        </div>
                    </details>
                ` : ''}

                <div style="display: flex; gap: 12px;">
                    ${canRetry ? `
                        <button 
                            onclick="this.closest('[data-error-boundary]').dispatchEvent(new CustomEvent('retry'))"
                            style="
                                background-color: #0066cc;
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 4px;
                                cursor: pointer;
                                font-size: 14px;
                            "
                        >
                            重试 (${this.state.retryCount}/${this.config.maxRetries})
                        </button>
                    ` : ''}
                    
                    <button 
                        onclick="this.closest('[data-error-boundary]').dispatchEvent(new CustomEvent('reset'))"
                        style="
                            background-color: #6c757d;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 14px;
                        "
                    >
                        重置
                    </button>
                </div>
            </div>
        `;
    }
}

/**
 * Vue 错误边界组件
 */
export class VueErrorBoundary {
    private config: ErrorBoundaryConfig;
    private state: ErrorBoundaryState;

    constructor(config: ErrorBoundaryConfig = {}) {
        this.config = {
            showErrorDetails: process.env.NODE_ENV === 'development',
            enableRetry: true,
            maxRetries: 3,
            retryDelay: 1000,
            ...config
        };

        this.state = {
            hasError: false,
            retryCount: 0
        };
    }

    /**
     * Vue 2 错误处理器
     */
    createVue2ErrorHandler() {
        return {
            data: () => ({
                hasError: false,
                error: null,
                errorInfo: null,
                retryCount: 0,
                errorId: null
            }),

            errorCaptured: (error: Error, instance: any, info: string) => {
                const microError = new MicroCoreError(error.message, {
                    type: ErrorType.SYSTEM,
                    level: ErrorLevel.ERROR,
                    context: {
                        appName: 'vue2-app',
                        extra: {
                            componentName: instance?.$options.name || 'Unknown',
                            errorInfo: info,
                            errorBoundary: 'VueErrorBoundary'
                        }
                    },
                    cause: error
                });

                this.state = {
                    hasError: true,
                    error,
                    errorInfo: { info, instance },
                    retryCount: this.state.retryCount,
                    errorId: microError.id
                };

                // 报告错误
                globalErrorHandler.handle(microError);

                // 调用用户回调
                if (this.config.onError) {
                    this.config.onError(error, { info, instance });
                }

                return false; // 阻止错误继续传播
            },

            methods: {
                retry: () => this.retry(),
                reset: () => this.reset()
            },

            render: (h: any) => {
                if (this.state.hasError) {
                    return this.renderVueError(h);
                }
                return this.$slots.default;
            }
        };
    }

    /**
     * Vue 3 错误处理器
     */
    createVue3ErrorHandler() {
        return {
            setup: () => {
                const state = {
                    hasError: false,
                    error: null,
                    errorInfo: null,
                    retryCount: 0,
                    errorId: null
                };

                return {
                    ...state,
                    retry: () => this.retry(),
                    reset: () => this.reset()
                };
            },

            errorCaptured: (error: Error, instance: any, info: string) => {
                const microError = new MicroCoreError(error.message, {
                    type: ErrorType.SYSTEM,
                    level: ErrorLevel.ERROR,
                    context: {
                        appName: 'vue3-app',
                        extra: {
                            componentName: instance?.type?.name || 'Unknown',
                            errorInfo: info,
                            errorBoundary: 'VueErrorBoundary'
                        }
                    },
                    cause: error
                });

                this.state = {
                    hasError: true,
                    error,
                    errorInfo: { info, instance },
                    retryCount: this.state.retryCount,
                    errorId: microError.id
                };

                // 报告错误
                globalErrorHandler.handle(microError);

                // 调用用户回调
                if (this.config.onError) {
                    this.config.onError(error, { info, instance });
                }

                return false;
            },

            render: () => {
                if (this.state.hasError) {
                    return this.renderVueError();
                }
                return this.$slots.default?.();
            }
        };
    }

    /**
     * 重试操作
     */
    retry(): void {
        if (this.state.retryCount >= (this.config.maxRetries || 3)) {
            console.warn('已达到最大重试次数');
            return;
        }

        setTimeout(() => {
            this.state = {
                hasError: false,
                retryCount: this.state.retryCount + 1,
                error: undefined,
                errorInfo: undefined,
                errorId: undefined
            };
        }, this.config.retryDelay || 1000);
    }

    /**
     * 重置错误状态
     */
    reset(): void {
        this.state = {
            hasError: false,
            retryCount: 0,
            error: undefined,
            errorInfo: undefined,
            errorId: undefined
        };
    }

    /**
     * 渲染Vue错误UI
     */
    private renderVueError(h?: any): any {
        if (this.config.fallbackComponent) {
            return this.config.fallbackComponent({
                error: this.state.error,
                errorInfo: this.state.errorInfo,
                retry: () => this.retry(),
                reset: () => this.reset()
            });
        }

        const errorUI = this.createDefaultErrorUI();

        if (h) {
            // Vue 2
            return h('div', {
                domProps: { innerHTML: errorUI },
                attrs: { 'data-error-boundary': 'vue2' }
            });
        } else {
            // Vue 3
            return {
                template: `<div data-error-boundary="vue3" v-html="errorUI"></div>`,
                data: () => ({ errorUI })
            };
        }
    }

    /**
     * 创建默认错误UI
     */
    private createDefaultErrorUI(): string {
        const { error, errorInfo, errorId } = this.state;
        const showDetails = this.config.showErrorDetails;
        const canRetry = this.config.enableRetry &&
            this.state.retryCount < (this.config.maxRetries || 3);

        return `
            <div style="
                padding: 20px;
                border: 1px solid #ff6b6b;
                border-radius: 8px;
                background-color: #ffe0e0;
                color: #d63031;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 20px;
            ">
                <h3 style="margin: 0 0 16px 0; font-size: 18px; display: flex; align-items: center;">
                    <span style="margin-right: 8px;">⚠️</span>
                    Vue组件渲染出错
                </h3>
                
                <p style="margin: 0 0 16px 0; font-size: 14px;">
                    抱歉，此Vue组件遇到了一个错误。${canRetry ? '您可以尝试重新加载。' : ''}
                </p>

                ${showDetails ? `
                    <details style="margin-bottom: 16px;">
                        <summary style="cursor: pointer; font-weight: bold; margin-bottom: 8px;">
                            错误详情
                        </summary>
                        <div style="
                            background-color: #f8f8f8;
                            padding: 12px;
                            border-radius: 4px;
                            font-family: 'Courier New', monospace;
                            font-size: 12px;
                            white-space: pre-wrap;
                            overflow-x: auto;
                        ">
                            <strong>错误消息:</strong> ${error?.message || '未知错误'}
                            ${errorId ? `\n<strong>错误ID:</strong> ${errorId}` : ''}
                            ${error?.stack ? `\n<strong>错误堆栈:</strong>\n${error.stack}` : ''}
                            ${errorInfo?.info ? `\n<strong>Vue错误信息:</strong> ${errorInfo.info}` : ''}
                        </div>
                    </details>
                ` : ''}

                <div style="display: flex; gap: 12px;">
                    ${canRetry ? `
                        <button 
                            onclick="this.closest('[data-error-boundary]').dispatchEvent(new CustomEvent('retry'))"
                            style="
                                background-color: #0066cc;
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 4px;
                                cursor: pointer;
                                font-size: 14px;
                            "
                        >
                            重试 (${this.state.retryCount}/${this.config.maxRetries})
                        </button>
                    ` : ''}
                    
                    <button 
                        onclick="this.closest('[data-error-boundary]').dispatchEvent(new CustomEvent('reset'))"
                        style="
                            background-color: #6c757d;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 14px;
                        "
                    >
                        重置
                    </button>
                </div>
            </div>
        `;
    }
}

/**
 * 通用错误边界工厂
 */
export class ErrorBoundaryFactory {
    /**
     * 创建React错误边界
     */
    static createReactErrorBoundary(config?: ErrorBoundaryConfig): ReactErrorBoundary {
        return new ReactErrorBoundary(config);
    }

    /**
     * 创建Vue错误边界
     */
    static createVueErrorBoundary(config?: ErrorBoundaryConfig): VueErrorBoundary {
        return new VueErrorBoundary(config);
    }

    /**
     * 创建通用错误边界
     */
    static createErrorBoundary(framework: 'react' | 'vue2' | 'vue3', config?: ErrorBoundaryConfig): any {
        switch (framework) {
            case 'react':
                return this.createReactErrorBoundary(config);
            case 'vue2':
            case 'vue3':
                return this.createVueErrorBoundary(config);
            default:
                throw new Error(`不支持的框架: ${framework}`);
        }
    }
}

/**
 * 全局错误边界配置
 */
export function setupGlobalErrorBoundary(config: ErrorBoundaryConfig & {
    frameworks?: ('react' | 'vue2' | 'vue3')[];
} = {}): void {
    const frameworks = config.frameworks || ['react', 'vue2', 'vue3'];

    frameworks.forEach(framework => {
        try {
            const boundary = ErrorBoundaryFactory.createErrorBoundary(framework, config);

            // 这里可以根据需要将错误边界注册到全局
            console.log(`${framework} 错误边界已设置`);
        } catch (error) {
            console.warn(`设置 ${framework} 错误边界失败:`, error);
        }
    });
}

/**
 * 错误边界装饰器（用于类组件）
 */
export function withErrorBoundary<T extends any>(
    Component: T,
    config?: ErrorBoundaryConfig
): T {
    // 这里可以实现装饰器逻辑
    // 由于不同框架的实现方式不同，这里只是一个占位符
    return Component;
}

/**
 * 错误边界高阶组件
 */
export function createErrorBoundaryHOC(config?: ErrorBoundaryConfig) {
    return function <T extends any>(Component: T): T {
        return withErrorBoundary(Component, config);
    };
}