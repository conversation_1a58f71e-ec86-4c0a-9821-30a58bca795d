/**
 * @fileoverview 统一错误处理系统
 * @description 提供统一的错误处理、收集和报告机制
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 错误级别
 */
export enum ErrorLevel {
    DEBUG = 'debug',
    INFO = 'info',
    WARN = 'warn',
    ERROR = 'error',
    FATAL = 'fatal'
}

/**
 * 错误类型
 */
export enum ErrorType {
    SYSTEM = 'system',
    NETWORK = 'network',
    BUSINESS = 'business',
    VALIDATION = 'validation',
    PERMISSION = 'permission',
    TIMEOUT = 'timeout',
    UNKNOWN = 'unknown'
}

/**
 * 错误上下文
 */
export interface ErrorContext {
    /** 应用名称 */
    appName?: string;
    /** 用户ID */
    userId?: string;
    /** 会话ID */
    sessionId?: string;
    /** 请求ID */
    requestId?: string;
    /** 用户代理 */
    userAgent?: string;
    /** 页面URL */
    url?: string;
    /** 时间戳 */
    timestamp?: number;
    /** 额外数据 */
    extra?: Record<string, any>;
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
    /** 错误ID */
    id: string;
    /** 错误消息 */
    message: string;
    /** 错误级别 */
    level: ErrorLevel;
    /** 错误类型 */
    type: ErrorType;
    /** 错误代码 */
    code?: string;
    /** 错误堆栈 */
    stack?: string;
    /** 错误上下文 */
    context: ErrorContext;
    /** 创建时间 */
    createdAt: number;
}

/**
 * 错误处理器接口
 */
export interface ErrorHandler {
    /** 处理错误 */
    handle(error: Error | ErrorInfo): void;
    /** 设置上下文 */
    setContext(context: Partial<ErrorContext>): void;
    /** 获取上下文 */
    getContext(): ErrorContext;
}

/**
 * 错误报告器接口
 */
export interface ErrorReporter {
    /** 报告错误 */
    report(errorInfo: ErrorInfo): Promise<void>;
    /** 批量报告错误 */
    reportBatch(errorInfos: ErrorInfo[]): Promise<void>;
}

/**
 * 微核心错误类
 */
export class MicroCoreError extends Error {
    public readonly id: string;
    public readonly level: ErrorLevel;
    public readonly type: ErrorType;
    public readonly code?: string;
    public readonly context: ErrorContext;
    public readonly createdAt: number;

    constructor(
        message: string,
        options: {
            level?: ErrorLevel;
            type?: ErrorType;
            code?: string;
            context?: Partial<ErrorContext>;
            cause?: Error;
        } = {}
    ) {
        super(message);

        this.name = 'MicroCoreError';
        this.id = this.generateId();
        this.level = options.level || ErrorLevel.ERROR;
        this.type = options.type || ErrorType.UNKNOWN;
        this.code = options.code;
        this.context = {
            timestamp: Date.now(),
            url: typeof window !== 'undefined' ? window.location.href : undefined,
            userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
            ...options.context
        };
        this.createdAt = Date.now();

        // 设置错误原因
        if (options.cause) {
            this.cause = options.cause;
        }

        // 确保堆栈信息正确
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, MicroCoreError);
        }
    }

    /**
     * 生成错误ID
     */
    private generateId(): string {
        return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 转换为错误信息对象
     */
    toErrorInfo(): ErrorInfo {
        return {
            id: this.id,
            message: this.message,
            level: this.level,
            type: this.type,
            code: this.code,
            stack: this.stack,
            context: this.context,
            createdAt: this.createdAt
        };
    }

    /**
     * 序列化为JSON
     */
    toJSON(): any {
        return {
            id: this.id,
            name: this.name,
            message: this.message,
            level: this.level,
            type: this.type,
            code: this.code,
            stack: this.stack,
            context: this.context,
            createdAt: this.createdAt
        };
    }
}

/**
 * 微核心错误处理器
 */
export class MicroCoreErrorHandler implements ErrorHandler {
    private context: ErrorContext = {};
    private reporters: ErrorReporter[] = [];
    private errorQueue: ErrorInfo[] = [];
    private maxQueueSize = 100;
    private flushInterval = 5000; // 5秒
    private flushTimer: NodeJS.Timeout | null = null;

    constructor(options: {
        maxQueueSize?: number;
        flushInterval?: number;
        reporters?: ErrorReporter[];
    } = {}) {
        this.maxQueueSize = options.maxQueueSize || 100;
        this.flushInterval = options.flushInterval || 5000;
        this.reporters = options.reporters || [];

        // 启动定时刷新
        this.startFlushTimer();

        // 监听全局错误
        this.setupGlobalErrorHandlers();
    }

    /**
     * 处理错误
     */
    handle(error: Error | ErrorInfo): void {
        let errorInfo: ErrorInfo;

        if (error instanceof MicroCoreError) {
            errorInfo = error.toErrorInfo();
        } else if (this.isErrorInfo(error)) {
            errorInfo = error;
        } else {
            errorInfo = this.createErrorInfo(error);
        }

        // 合并上下文
        errorInfo.context = { ...this.context, ...errorInfo.context };

        // 添加到队列
        this.addToQueue(errorInfo);

        // 控制台输出
        this.logError(errorInfo);
    }

    /**
     * 设置上下文
     */
    setContext(context: Partial<ErrorContext>): void {
        this.context = { ...this.context, ...context };
    }

    /**
     * 获取上下文
     */
    getContext(): ErrorContext {
        return { ...this.context };
    }

    /**
     * 添加错误报告器
     */
    addReporter(reporter: ErrorReporter): void {
        this.reporters.push(reporter);
    }

    /**
     * 移除错误报告器
     */
    removeReporter(reporter: ErrorReporter): void {
        const index = this.reporters.indexOf(reporter);
        if (index > -1) {
            this.reporters.splice(index, 1);
        }
    }

    /**
     * 立即刷新错误队列
     */
    async flush(): Promise<void> {
        if (this.errorQueue.length === 0) {
            return;
        }

        const errors = [...this.errorQueue];
        this.errorQueue = [];

        // 并行报告给所有报告器
        const promises = this.reporters.map(reporter =>
            reporter.reportBatch(errors).catch(err => {
                console.error('错误报告失败:', err);
            })
        );

        await Promise.allSettled(promises);
    }

    /**
     * 销毁错误处理器
     */
    destroy(): void {
        if (this.flushTimer) {
            clearInterval(this.flushTimer);
            this.flushTimer = null;
        }

        // 最后一次刷新
        this.flush().catch(err => {
            console.error('最终刷新失败:', err);
        });

        // 移除全局错误监听器
        this.removeGlobalErrorHandlers();
    }

    /**
     * 检查是否为错误信息对象
     */
    private isErrorInfo(obj: any): obj is ErrorInfo {
        return obj && typeof obj === 'object' &&
            'id' in obj && 'message' in obj && 'level' in obj && 'type' in obj;
    }

    /**
     * 创建错误信息对象
     */
    private createErrorInfo(error: Error): ErrorInfo {
        return {
            id: this.generateId(),
            message: error.message,
            level: ErrorLevel.ERROR,
            type: ErrorType.UNKNOWN,
            stack: error.stack,
            context: {
                timestamp: Date.now(),
                url: typeof window !== 'undefined' ? window.location.href : undefined,
                userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined
            },
            createdAt: Date.now()
        };
    }

    /**
     * 生成错误ID
     */
    private generateId(): string {
        return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 添加到错误队列
     */
    private addToQueue(errorInfo: ErrorInfo): void {
        this.errorQueue.push(errorInfo);

        // 如果队列满了，移除最旧的错误
        if (this.errorQueue.length > this.maxQueueSize) {
            this.errorQueue.shift();
        }
    }

    /**
     * 启动定时刷新
     */
    private startFlushTimer(): void {
        this.flushTimer = setInterval(() => {
            this.flush().catch(err => {
                console.error('定时刷新失败:', err);
            });
        }, this.flushInterval);
    }

    /**
     * 控制台输出错误
     */
    private logError(errorInfo: ErrorInfo): void {
        const logMethod = this.getLogMethod(errorInfo.level);
        const prefix = `[${errorInfo.level.toUpperCase()}] [${errorInfo.type}]`;

        logMethod(`${prefix} ${errorInfo.message}`, {
            id: errorInfo.id,
            code: errorInfo.code,
            context: errorInfo.context,
            stack: errorInfo.stack
        });
    }

    /**
     * 获取日志方法
     */
    private getLogMethod(level: ErrorLevel): (...args: any[]) => void {
        switch (level) {
            case ErrorLevel.DEBUG:
                return console.debug;
            case ErrorLevel.INFO:
                return console.info;
            case ErrorLevel.WARN:
                return console.warn;
            case ErrorLevel.ERROR:
            case ErrorLevel.FATAL:
                return console.error;
            default:
                return console.log;
        }
    }

    /**
     * 设置全局错误处理器
     */
    private setupGlobalErrorHandlers(): void {
        if (typeof window !== 'undefined') {
            // 处理未捕获的错误
            window.addEventListener('error', (event) => {
                this.handle(new MicroCoreError(event.message, {
                    type: ErrorType.SYSTEM,
                    context: {
                        url: event.filename,
                        extra: {
                            line: event.lineno,
                            column: event.colno
                        }
                    }
                }));
            });

            // 处理未捕获的Promise拒绝
            window.addEventListener('unhandledrejection', (event) => {
                this.handle(new MicroCoreError(
                    event.reason?.message || '未处理的Promise拒绝',
                    {
                        type: ErrorType.SYSTEM,
                        context: {
                            extra: {
                                reason: event.reason
                            }
                        }
                    }
                ));
            });
        }

        if (typeof process !== 'undefined') {
            // Node.js 环境下的未捕获异常
            process.on('uncaughtException', (error) => {
                this.handle(new MicroCoreError(error.message, {
                    type: ErrorType.SYSTEM,
                    level: ErrorLevel.FATAL,
                    context: {
                        extra: {
                            stack: error.stack
                        }
                    }
                }));
            });

            // Node.js 环境下的未处理Promise拒绝
            process.on('unhandledRejection', (reason) => {
                this.handle(new MicroCoreError(
                    reason instanceof Error ? reason.message : String(reason),
                    {
                        type: ErrorType.SYSTEM,
                        level: ErrorLevel.ERROR,
                        context: {
                            extra: {
                                reason
                            }
                        }
                    }
                ));
            });
        }
    }

    /**
     * 移除全局错误处理器
     */
    private removeGlobalErrorHandlers(): void {
        // 在实际应用中，这里应该移除之前添加的事件监听器
        // 但由于我们没有保存监听器引用，这里只是占位
    }
}

/**
 * 控制台错误报告器
 */
export class ConsoleErrorReporter implements ErrorReporter {
    async report(errorInfo: ErrorInfo): Promise<void> {
        console.group(`🚨 错误报告 [${errorInfo.level}]`);
        console.error('消息:', errorInfo.message);
        console.error('类型:', errorInfo.type);
        console.error('代码:', errorInfo.code);
        console.error('上下文:', errorInfo.context);
        if (errorInfo.stack) {
            console.error('堆栈:', errorInfo.stack);
        }
        console.groupEnd();
    }

    async reportBatch(errorInfos: ErrorInfo[]): Promise<void> {
        console.group(`🚨 批量错误报告 (${errorInfos.length} 个错误)`);
        for (const errorInfo of errorInfos) {
            await this.report(errorInfo);
        }
        console.groupEnd();
    }
}

/**
 * HTTP错误报告器
 */
export class HttpErrorReporter implements ErrorReporter {
    private endpoint: string;
    private headers: Record<string, string>;

    constructor(endpoint: string, headers: Record<string, string> = {}) {
        this.endpoint = endpoint;
        this.headers = {
            'Content-Type': 'application/json',
            ...headers
        };
    }

    async report(errorInfo: ErrorInfo): Promise<void> {
        try {
            const response = await fetch(this.endpoint, {
                method: 'POST',
                headers: this.headers,
                body: JSON.stringify(errorInfo)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('HTTP错误报告失败:', error);
            throw error;
        }
    }

    async reportBatch(errorInfos: ErrorInfo[]): Promise<void> {
        try {
            const response = await fetch(`${this.endpoint}/batch`, {
                method: 'POST',
                headers: this.headers,
                body: JSON.stringify({ errors: errorInfos })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('HTTP批量错误报告失败:', error);
            throw error;
        }
    }
}

/**
 * 创建默认错误处理器
 */
export function createDefaultErrorHandler(options: {
    enableConsoleReporter?: boolean;
    httpEndpoint?: string;
    httpHeaders?: Record<string, string>;
    maxQueueSize?: number;
    flushInterval?: number;
} = {}): MicroCoreErrorHandler {
    const reporters: ErrorReporter[] = [];

    // 添加控制台报告器
    if (options.enableConsoleReporter !== false) {
        reporters.push(new ConsoleErrorReporter());
    }

    // 添加HTTP报告器
    if (options.httpEndpoint) {
        reporters.push(new HttpErrorReporter(options.httpEndpoint, options.httpHeaders));
    }

    return new MicroCoreErrorHandler({
        maxQueueSize: options.maxQueueSize,
        flushInterval: options.flushInterval,
        reporters
    });
}

// 导出全局错误处理器实例
export const globalErrorHandler = createDefaultErrorHandler();

/**
 * 便捷的错误处理函数
 */
export function handleError(error: Error | string, options: {
    level?: ErrorLevel;
    type?: ErrorType;
    code?: string;
    context?: Partial<ErrorContext>;
} = {}): void {
    const errorObj = typeof error === 'string'
        ? new MicroCoreError(error, options)
        : error instanceof MicroCoreError
            ? error
            : new MicroCoreError(error.message, { ...options, cause: error });

    globalErrorHandler.handle(errorObj);
}

/**
 * 创建特定类型的错误
 */
export const createError = {
    system: (message: string, context?: Partial<ErrorContext>) =>
        new MicroCoreError(message, { type: ErrorType.SYSTEM, context }),

    network: (message: string, context?: Partial<ErrorContext>) =>
        new MicroCoreError(message, { type: ErrorType.NETWORK, context }),

    business: (message: string, context?: Partial<ErrorContext>) =>
        new MicroCoreError(message, { type: ErrorType.BUSINESS, context }),

    validation: (message: string, context?: Partial<ErrorContext>) =>
        new MicroCoreError(message, { type: ErrorType.VALIDATION, context }),

    permission: (message: string, context?: Partial<ErrorContext>) =>
        new MicroCoreError(message, { type: ErrorType.PERMISSION, context }),

    timeout: (message: string, context?: Partial<ErrorContext>) =>
        new MicroCoreError(message, { type: ErrorType.TIMEOUT, context })
};