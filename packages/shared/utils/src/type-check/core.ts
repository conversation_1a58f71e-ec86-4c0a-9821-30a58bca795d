/**
 * 核心类型检查工具函数
 * 从 @micro-core/core 迁移而来
 */

/**
 * 检查值是否为对象
 */
export function isObject(value: unknown): value is Record<string, unknown> {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
}

/**
 * 检查值是否为函数
 */
export function isFunction(value: unknown): value is Function {
    return typeof value === 'function';
}

/**
 * 检查值是否为字符串
 */
export function isString(value: unknown): value is string {
    return typeof value === 'string';
}

/**
 * 检查值是否为数字
 */
export function isNumber(value: unknown): value is number {
    return typeof value === 'number' && !isNaN(value);
}

/**
 * 检查值是否为布尔值
 */
export function isBoolean(value: unknown): value is boolean {
    return typeof value === 'boolean';
}

/**
 * 检查值是否为数组
 */
export function isArray(value: unknown): value is unknown[] {
    return Array.isArray(value);
}

/**
 * 检查值是否为Promise
 */
export function isPromise(value: unknown): value is Promise<unknown> {
    return value !== null && typeof value === 'object' && 'then' in value && typeof (value as any).then === 'function';
}

/**
 * 检查值是否为空
 */
export function isEmpty(value: unknown): boolean {
    if (value == null) return true;
    if (isArray(value) || isString(value)) return value.length === 0;
    if (isObject(value)) return Object.keys(value).length === 0;
    return false;
}