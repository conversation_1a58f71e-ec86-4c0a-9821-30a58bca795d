/**
 * @fileoverview 框架检测器
 * @description 自动检测当前页面使用的前端框架，支持多种主流框架识别
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 支持的框架类型
 */
export type FrameworkType = 'react' | 'vue' | 'angular' | 'svelte' | 'solid' | 'jquery' | 'vanilla' | 'unknown';

/**
 * 框架检测结果
 */
export interface FrameworkDetectionResult {
    framework: FrameworkType;
    version?: string;
    confidence: number; // 0-1之间的置信度
    evidence: string[]; // 检测依据
}

/**
 * 框架检测器
 * 提供自动框架检测和版本识别功能
 */
export class FrameworkDetector {
    private detectionCache: FrameworkDetectionResult | null = null;
    private cacheExpiry = 60000; // 缓存1分钟
    private cacheTime = 0;

    /**
     * 检测当前页面使用的框架
     */
    detect(): FrameworkDetectionResult {
        // 检查缓存
        if (this.detectionCache && (Date.now() - this.cacheTime) < this.cacheExpiry) {
            return this.detectionCache;
        }

        const results: FrameworkDetectionResult[] = [
            this.detectReact(),
            this.detectVue(),
            this.detectAngular(),
            this.detectSvelte(),
            this.detectSolid(),
            this.detectJQuery(),
            this.detectVanilla()
        ];

        // 选择置信度最高的结果
        const bestResult = results.reduce((best, current) => 
            current.confidence > best.confidence ? current : best
        );

        // 缓存结果
        this.detectionCache = bestResult;
        this.cacheTime = Date.now();

        return bestResult;
    }

    /**
     * 检测 React
     */
    private detectReact(): FrameworkDetectionResult {
        const evidence: string[] = [];
        let confidence = 0;
        let version: string | undefined;

        // 检查全局 React 对象
        if (typeof window !== 'undefined') {
            const win = window as any;
            
            if (win.React) {
                evidence.push('Global React object found');
                confidence += 0.4;
                version = win.React.version;
            }

            // 检查 ReactDOM
            if (win.ReactDOM) {
                evidence.push('ReactDOM found');
                confidence += 0.3;
            }

            // 检查 React DevTools
            if (win.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
                evidence.push('React DevTools detected');
                confidence += 0.2;
            }

            // 检查 React Fiber
            const reactFiberNodes = document.querySelectorAll('[data-reactroot], [data-reactid]');
            if (reactFiberNodes.length > 0) {
                evidence.push(`React DOM nodes found (${reactFiberNodes.length})`);
                confidence += 0.3;
            }

            // 检查 React 组件实例
            const reactInstances = document.querySelectorAll('*');
            let reactNodeCount = 0;
            reactInstances.forEach(node => {
                const keys = Object.keys(node);
                if (keys.some(key => key.startsWith('__reactInternalInstance') || key.startsWith('_reactInternalFiber'))) {
                    reactNodeCount++;
                }
            });

            if (reactNodeCount > 0) {
                evidence.push(`React internal instances found (${reactNodeCount})`);
                confidence += Math.min(0.4, reactNodeCount * 0.1);
            }
        }

        return {
            framework: 'react',
            version,
            confidence: Math.min(1, confidence),
            evidence
        };
    }

    /**
     * 检测 Vue
     */
    private detectVue(): FrameworkDetectionResult {
        const evidence: string[] = [];
        let confidence = 0;
        let version: string | undefined;

        if (typeof window !== 'undefined') {
            const win = window as any;

            // Vue 2.x
            if (win.Vue) {
                evidence.push('Global Vue object found');
                confidence += 0.4;
                version = win.Vue.version;
            }

            // Vue 3.x
            if (win.Vue?.version || win.__VUE__) {
                evidence.push('Vue 3.x detected');
                confidence += 0.4;
                version = win.Vue?.version;
            }

            // Vue DevTools
            if (win.__VUE_DEVTOOLS_GLOBAL_HOOK__) {
                evidence.push('Vue DevTools detected');
                confidence += 0.2;
            }

            // Vue 指令
            const vueDirectives = document.querySelectorAll('[v-if], [v-for], [v-model], [v-show], [v-bind], [v-on]');
            if (vueDirectives.length > 0) {
                evidence.push(`Vue directives found (${vueDirectives.length})`);
                confidence += 0.3;
            }

            // Vue 实例
            const vueInstances = document.querySelectorAll('*');
            let vueNodeCount = 0;
            vueInstances.forEach(node => {
                if ((node as any).__vue__ || (node as any).__vueParentComponent) {
                    vueNodeCount++;
                }
            });

            if (vueNodeCount > 0) {
                evidence.push(`Vue instances found (${vueNodeCount})`);
                confidence += Math.min(0.4, vueNodeCount * 0.1);
            }
        }

        return {
            framework: 'vue',
            version,
            confidence: Math.min(1, confidence),
            evidence
        };
    }

    /**
     * 检测 Angular
     */
    private detectAngular(): FrameworkDetectionResult {
        const evidence: string[] = [];
        let confidence = 0;
        let version: string | undefined;

        if (typeof window !== 'undefined') {
            const win = window as any;

            // Angular 全局对象
            if (win.ng || win.angular) {
                evidence.push('Angular global object found');
                confidence += 0.4;
                version = win.ng?.version?.full || win.angular?.version?.full;
            }

            // Angular 元素
            const ngElements = document.querySelectorAll('[ng-app], [ng-controller], [ng-if], [ng-for], [ng-model]');
            if (ngElements.length > 0) {
                evidence.push(`Angular directives found (${ngElements.length})`);
                confidence += 0.3;
            }

            // Angular 组件
            const angularComponents = document.querySelectorAll('*');
            let angularNodeCount = 0;
            angularComponents.forEach(node => {
                const keys = Object.keys(node);
                if (keys.some(key => key.startsWith('__ngContext') || key.startsWith('ng-'))) {
                    angularNodeCount++;
                }
            });

            if (angularNodeCount > 0) {
                evidence.push(`Angular components found (${angularNodeCount})`);
                confidence += Math.min(0.4, angularNodeCount * 0.1);
            }

            // Zone.js (Angular 依赖)
            if (win.Zone) {
                evidence.push('Zone.js detected');
                confidence += 0.2;
            }
        }

        return {
            framework: 'angular',
            version,
            confidence: Math.min(1, confidence),
            evidence
        };
    }

    /**
     * 检测 Svelte
     */
    private detectSvelte(): FrameworkDetectionResult {
        const evidence: string[] = [];
        let confidence = 0;
        let version: string | undefined;

        if (typeof window !== 'undefined') {
            const win = window as any;

            // Svelte 全局对象
            if (win.Svelte) {
                evidence.push('Svelte global object found');
                confidence += 0.4;
            }

            // Svelte 组件标识
            const svelteElements = document.querySelectorAll('*');
            let svelteNodeCount = 0;
            svelteElements.forEach(node => {
                const keys = Object.keys(node);
                if (keys.some(key => key.startsWith('__svelte') || key.includes('svelte'))) {
                    svelteNodeCount++;
                }
            });

            if (svelteNodeCount > 0) {
                evidence.push(`Svelte components found (${svelteNodeCount})`);
                confidence += Math.min(0.5, svelteNodeCount * 0.1);
            }

            // 检查 CSS 类名模式
            const svelteClasses = document.querySelectorAll('[class*="svelte-"]');
            if (svelteClasses.length > 0) {
                evidence.push(`Svelte CSS classes found (${svelteClasses.length})`);
                confidence += 0.3;
            }
        }

        return {
            framework: 'svelte',
            version,
            confidence: Math.min(1, confidence),
            evidence
        };
    }

    /**
     * 检测 Solid.js
     */
    private detectSolid(): FrameworkDetectionResult {
        const evidence: string[] = [];
        let confidence = 0;
        let version: string | undefined;

        if (typeof window !== 'undefined') {
            const win = window as any;

            // Solid 全局对象
            if (win.Solid || win.SolidJS) {
                evidence.push('Solid.js global object found');
                confidence += 0.4;
            }

            // Solid 特征检测
            const solidElements = document.querySelectorAll('*');
            let solidNodeCount = 0;
            solidElements.forEach(node => {
                const keys = Object.keys(node);
                if (keys.some(key => key.includes('solid') || key.startsWith('_$'))) {
                    solidNodeCount++;
                }
            });

            if (solidNodeCount > 0) {
                evidence.push(`Solid.js components found (${solidNodeCount})`);
                confidence += Math.min(0.5, solidNodeCount * 0.1);
            }
        }

        return {
            framework: 'solid',
            version,
            confidence: Math.min(1, confidence),
            evidence
        };
    }

    /**
     * 检测 jQuery
     */
    private detectJQuery(): FrameworkDetectionResult {
        const evidence: string[] = [];
        let confidence = 0;
        let version: string | undefined;

        if (typeof window !== 'undefined') {
            const win = window as any;

            // jQuery 全局对象
            if (win.jQuery || win.$) {
                evidence.push('jQuery global object found');
                confidence += 0.5;
                version = win.jQuery?.fn?.jquery || win.$?.fn?.jquery;
            }

            // jQuery 数据属性
            const jqueryElements = document.querySelectorAll('[data-jquery]');
            if (jqueryElements.length > 0) {
                evidence.push(`jQuery data attributes found (${jqueryElements.length})`);
                confidence += 0.2;
            }
        }

        return {
            framework: 'jquery',
            version,
            confidence: Math.min(1, confidence),
            evidence
        };
    }

    /**
     * 检测原生 JavaScript
     */
    private detectVanilla(): FrameworkDetectionResult {
        const evidence: string[] = [];
        let confidence = 0.1; // 默认最低置信度

        // 如果没有检测到其他框架，则认为是原生 JavaScript
        const otherFrameworks = [
            this.detectReact(),
            this.detectVue(),
            this.detectAngular(),
            this.detectSvelte(),
            this.detectSolid(),
            this.detectJQuery()
        ];

        const hasOtherFramework = otherFrameworks.some(result => result.confidence > 0.3);

        if (!hasOtherFramework) {
            evidence.push('No major framework detected');
            confidence = 0.6;
        }

        return {
            framework: 'vanilla',
            confidence,
            evidence
        };
    }

    /**
     * 清除缓存
     */
    clearCache(): void {
        this.detectionCache = null;
        this.cacheTime = 0;
    }

    /**
     * 获取所有框架的检测结果
     */
    detectAll(): FrameworkDetectionResult[] {
        return [
            this.detectReact(),
            this.detectVue(),
            this.detectAngular(),
            this.detectSvelte(),
            this.detectSolid(),
            this.detectJQuery(),
            this.detectVanilla()
        ].sort((a, b) => b.confidence - a.confidence);
    }

    /**
     * 检测是否为特定框架
     */
    isFramework(framework: FrameworkType, minConfidence: number = 0.5): boolean {
        const result = this.detect();
        return result.framework === framework && result.confidence >= minConfidence;
    }
}

/**
 * 全局框架检测器实例
 */
export const globalFrameworkDetector = new FrameworkDetector();

/**
 * 便捷的框架检测函数
 */
export const detectFramework = (): FrameworkDetectionResult => {
    return globalFrameworkDetector.detect();
};

/**
 * 检测是否为特定框架
 */
export const isFramework = (framework: FrameworkType, minConfidence: number = 0.5): boolean => {
    return globalFrameworkDetector.isFramework(framework, minConfidence);
};

export default FrameworkDetector;
