/**
 * URL工具函数
 * 从 @micro-core/core 迁移而来
 */

/**
 * 验证URL是否有效
 * @description 检查URL格式是否正确，支持http、https、file等协议
 * @param url 待验证的URL字符串
 * @returns 是否为有效URL
 * @example
 * ```typescript
 * isValidUrl('https://example.com'); // true
 * isValidUrl('invalid-url'); // false
 * ```
 */
export function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}