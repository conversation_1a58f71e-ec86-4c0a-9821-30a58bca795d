/**
 * @fileoverview 从 @micro-core/core 迁移的 URL 工具函数
 * @description 提供与 core 包完全兼容的 URL 验证函数
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

/**
 * 检查是否为有效URL
 * @description 验证URL格式是否正确，与 @micro-core/core 包的实现完全兼容
 * @param url 待验证的URL字符串
 * @returns 是否为有效URL
 * @example
 * ```typescript
 * isValidUrl('https://example.com'); // true
 * isValidUrl('invalid-url'); // false
 * isValidUrl(''); // false
 * isValidUrl('/relative/path'); // false
 * ```
 */
export function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * 核心 URL 工具集合
 * @description 提供所有从 core 包迁移的 URL 工具函数
 */
export const coreUrlUtils = {
    isValidUrl
} as const;

/**
 * URL 工具函数名称列表
 * @description 用于验证和测试的函数名称列表
 */
export const URL_FUNCTIONS = [
    'isValidUrl'
] as const;

/**
 * 验证所有 URL 工具函数是否可用
 * @description 用于测试和验证的辅助函数
 * @returns 验证结果
 */
export function validateUrlFunctions(): {
    available: string[];
    missing: string[];
    allAvailable: boolean;
} {
    const available: string[] = [];
    const missing: string[] = [];

    URL_FUNCTIONS.forEach(funcName => {
        if (typeof (coreUrlUtils as any)[funcName] === 'function') {
            available.push(funcName);
        } else {
            missing.push(funcName);
        }
    });

    return {
        available,
        missing,
        allAvailable: missing.length === 0
    };
}