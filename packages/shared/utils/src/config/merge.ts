/**
 * 通用配置合并工具
 * 从适配器包中提取的通用配置合并逻辑
 */

/**
 * 深度合并两个对象
 */
export function deepMerge<T extends Record<string, any>>(target: T, source: Partial<T>): T {
    const result = { ...target };

    for (const key in source) {
        if (source.hasOwnProperty(key)) {
            const sourceValue = source[key];
            const targetValue = result[key];

            if (isObject(sourceValue) && isObject(targetValue)) {
                result[key] = deepMerge(targetValue, sourceValue);
            } else if (sourceValue !== undefined) {
                result[key] = sourceValue;
            }
        }
    }

    return result;
}

/**
 * 合并配置对象
 */
export function mergeConfigs<T extends Record<string, any>>(
    base: T,
    override: Partial<T>
): T {
    return deepMerge(base, override);
}

/**
 * 检查值是否为对象
 */
function isObject(value: unknown): value is Record<string, any> {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
}

/**
 * 合并数组配置
 */
export function mergeArrayConfigs<T>(base: T[], override: T[]): T[] {
    return [...base, ...override];
}

/**
 * 合并适配器配置的通用函数
 */
export function mergeAdapterConfig<T extends Record<string, any>>(
    base: T,
    override: Partial<T>,
    options: {
        mergeArrays?: boolean;
        preserveUndefined?: boolean;
    } = {}
): T {
    const { mergeArrays = false, preserveUndefined = false } = options;
    const result = { ...base };

    for (const key in override) {
        if (override.hasOwnProperty(key)) {
            const sourceValue = override[key];
            const targetValue = result[key];

            if (sourceValue === undefined && !preserveUndefined) {
                continue;
            }

            if (Array.isArray(sourceValue) && Array.isArray(targetValue) && mergeArrays) {
                result[key] = mergeArrayConfigs(targetValue, sourceValue);
            } else if (isObject(sourceValue) && isObject(targetValue)) {
                result[key] = deepMerge(targetValue, sourceValue);
            } else {
                result[key] = sourceValue;
            }
        }
    }

    return result;
}