/**
 * 配置管理工具函数
 * 从各个适配器和构建器中提取的通用配置功能
 */

/**
 * 深度合并配置对象
 * @description 递归合并两个配置对象，支持嵌套对象的合并
 * @param base 基础配置对象
 * @param override 覆盖配置对象
 * @returns 合并后的配置对象
 * @example
 * ```typescript
 * const base = { a: 1, b: { c: 2 } };
 * const override = { b: { d: 3 }, e: 4 };
 * mergeConfigs(base, override); // { a: 1, b: { c: 2, d: 3 }, e: 4 }
 * ```
 */
export function mergeConfigs<T extends Record<string, any>>(
    base: T,
    override: Partial<T>
): T {
    const result = { ...base } as T;

    for (const key in override) {
        if (Object.prototype.hasOwnProperty.call(override, key)) {
            const overrideValue = override[key];
            const baseValue = result[key];

            if (
                typeof overrideValue === 'object' &&
                overrideValue !== null &&
                !Array.isArray(overrideValue) &&
                typeof baseValue === 'object' &&
                baseValue !== null &&
                !Array.isArray(baseValue)
            ) {
                (result as any)[key] = mergeConfigs(baseValue, overrideValue);
            } else if (overrideValue !== undefined) {
                (result as any)[key] = overrideValue;
            }
        }
    }

    return result;
}

/**
 * 验证配置对象
 * @description 根据提供的规则验证配置对象的有效性
 * @param config 待验证的配置对象
 * @param rules 验证规则
 * @returns 验证结果
 * @example
 * ```typescript
 * const config = { name: 'test', port: 3000 };
 * const rules = {
 *   name: { required: true, type: 'string' },
 *   port: { required: true, type: 'number', min: 1000 }
 * };
 * validateConfig(config, rules); // { valid: true, errors: [] }
 * ```
 */
export function validateConfig(
    config: Record<string, any>,
    rules: Record<string, ValidationRule>
): ValidationResult {
    const errors: string[] = [];

    for (const [key, rule] of Object.entries(rules)) {
        const value = config[key];

        // 检查必填项
        if (rule.required && (value === undefined || value === null)) {
            errors.push(`配置项 '${key}' 是必填的`);
            continue;
        }

        // 如果值不存在且不是必填项，跳过后续验证
        if (value === undefined || value === null) {
            continue;
        }

        // 检查类型
        if (rule.type && typeof value !== rule.type) {
            errors.push(`配置项 '${key}' 应该是 ${rule.type} 类型，实际是 ${typeof value}`);
            continue;
        }

        // 检查数值范围
        if (rule.type === 'number') {
            if (rule.min !== undefined && value < rule.min) {
                errors.push(`配置项 '${key}' 的值应该大于等于 ${rule.min}`);
            }
            if (rule.max !== undefined && value > rule.max) {
                errors.push(`配置项 '${key}' 的值应该小于等于 ${rule.max}`);
            }
        }

        // 检查字符串长度
        if (rule.type === 'string') {
            if (rule.minLength !== undefined && value.length < rule.minLength) {
                errors.push(`配置项 '${key}' 的长度应该大于等于 ${rule.minLength}`);
            }
            if (rule.maxLength !== undefined && value.length > rule.maxLength) {
                errors.push(`配置项 '${key}' 的长度应该小于等于 ${rule.maxLength}`);
            }
        }

        // 检查枚举值
        if (rule.enum && !rule.enum.includes(value)) {
            errors.push(`配置项 '${key}' 的值应该是 [${rule.enum.join(', ')}] 中的一个`);
        }

        // 自定义验证函数
        if (rule.validator) {
            const customResult = rule.validator(value);
            if (customResult !== true) {
                errors.push(typeof customResult === 'string' ? customResult : `配置项 '${key}' 验证失败`);
            }
        }
    }

    return {
        valid: errors.length === 0,
        errors
    };
}

/**
 * 验证规则接口
 */
export interface ValidationRule {
    required?: boolean;
    type?: 'string' | 'number' | 'boolean' | 'object' | 'function';
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
    enum?: any[];
    validator?: (value: any) => boolean | string;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
    valid: boolean;
    errors: string[];
}