/**
 * Shared Utils 主导出文件
 * 统一导出所有工具函数
 */

// 类型检查工具
export * from './type-check/core';

// URL工具
export * from './url';

// 日志工具
export * from './logger';

// 格式化工具
export * from './format';

// 配置管理工具
// 解决命名冲突问题，显式导出
import { mergeConfigs as configMergeConfigs, validateConfig as configValidateConfig } from './config';
export { configMergeConfigs as mergeConfigs, configValidateConfig as validateConfig };
// 导出其他非冲突的配置工具
export * from './config';

// DOM操作工具
export * from './dom';

// ID生成工具
export * from './id';

// 适配器通用工具
export * from './adapter';

// 显式导出 formatError 和 generateId 函数，确保它们可以被正确导入
export { formatError } from './format';
export { generateId } from './id';
