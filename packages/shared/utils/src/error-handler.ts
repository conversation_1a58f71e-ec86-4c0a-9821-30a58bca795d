/**
 * @fileoverview 统一错误处理工具
 * @description 提供全面的错误处理和恢复机制，支持策略模式和装饰器
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

// Import types from the local shared types
export enum ErrorType {
    INITIALIZATION = 'INITIALIZATION',
    CONFIGURATION = 'CONFIGURATION',
    NETWORK = 'NETWORK',
    COMPATIBILITY = 'COMPATIBILITY',
    SECURITY = 'SECURITY',
    PERFORMANCE = 'PERFORMANCE',
    APPLICATION = 'APPLICATION',
    PLUGIN = 'PLUGIN',
    SANDBOX = 'SANDBOX',
    RESOURCE = 'RESOURCE',
    UNKNOWN = 'UNKNOWN'
}

export enum ErrorSeverity {
    LOW = 'LOW',
    MEDIUM = 'MEDIUM',
    HIGH = 'HIGH',
    CRITICAL = 'CRITICAL'
}

export interface ErrorContext {
    component?: string;
    method?: string;
    userAgent?: string;
    timestamp?: number;
    data?: Record<string, any>;
}

export interface ErrorRecoveryStrategy {
    name: string;
    canRecover: (error: Error, context?: ErrorContext) => boolean;
    recover: (error: Error, context?: ErrorContext) => Promise<boolean>;
    priority: number;
}

export class MicroCoreError extends Error {
    code: string;
    details?: any;
    public readonly type: ErrorType;
    public readonly severity: ErrorSeverity;
    public readonly context: ErrorContext;
    public readonly timestamp: number;
    public readonly recoverable: boolean;

    constructor(
        message: string, 
        code?: string, 
        details?: any,
        type: ErrorType = ErrorType.UNKNOWN,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: ErrorContext = {},
        recoverable: boolean = false
    ) {
        super(message);
        this.name = 'MicroCoreError';
        this.code = code || 'UNKNOWN_ERROR';
        this.details = details;
        this.type = type;
        this.severity = severity;
        this.context = {
            timestamp: Date.now(),
            userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
            ...context
        };
        this.timestamp = Date.now();
        this.recoverable = recoverable;

        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, MicroCoreError);
        }
    }

    toJSON(): Record<string, any> {
        return {
            name: this.name,
            message: this.message,
            code: this.code,
            type: this.type,
            severity: this.severity,
            context: this.context,
            timestamp: this.timestamp,
            recoverable: this.recoverable,
            stack: this.stack,
            details: this.details
        };
    }
}

/**
 * 错误处理器类
 * 提供统一的错误处理、恢复和统计功能
 */
export class ErrorHandler {
    private strategies: ErrorRecoveryStrategy[] = [];
    private errorHistory: MicroCoreError[] = [];
    private maxHistorySize = 100;
    private onError?: (error: MicroCoreError) => void;
    private onRecovery?: (error: MicroCoreError, strategy: string) => void;

    constructor() {
        this.setupDefaultStrategies();
        this.setupGlobalErrorHandling();
    }

    /**
     * 设置错误回调
     */
    setErrorCallback(callback: (error: MicroCoreError) => void): void {
        this.onError = callback;
    }

    /**
     * 设置恢复回调
     */
    setRecoveryCallback(callback: (error: MicroCoreError, strategy: string) => void): void {
        this.onRecovery = callback;
    }

    /**
     * 注册恢复策略
     */
    registerStrategy(strategy: ErrorRecoveryStrategy): void {
        this.strategies.push(strategy);
        // 按优先级排序
        this.strategies.sort((a, b) => b.priority - a.priority);
    }

    /**
     * 处理错误
     */
    async handleError(error: Error | MicroCoreError, context?: ErrorContext): Promise<boolean> {
        let microError: MicroCoreError;
        
        if (error instanceof MicroCoreError) {
            microError = error;
        } else {
            microError = new MicroCoreError(
                error.message,
                'UNKNOWN_ERROR',
                { originalError: error },
                ErrorType.UNKNOWN,
                ErrorSeverity.MEDIUM,
                context
            );
        }

        // 记录错误
        this.recordError(microError);

        // 触发错误回调
        if (this.onError) {
            try {
                this.onError(microError);
            } catch (callbackError) {
                console.warn('Error callback failed:', callbackError);
            }
        }

        // 尝试恢复
        if (microError.recoverable) {
            return await this.attemptRecovery(microError);
        }

        return false;
    }

    /**
     * 尝试错误恢复
     */
    private async attemptRecovery(error: MicroCoreError): Promise<boolean> {
        for (const strategy of this.strategies) {
            try {
                if (strategy.canRecover(error, error.context)) {
                    const recovered = await strategy.recover(error, error.context);
                    if (recovered) {
                        // 触发恢复回调
                        if (this.onRecovery) {
                            this.onRecovery(error, strategy.name);
                        }
                        return true;
                    }
                }
            } catch (recoveryError) {
                console.warn(`Recovery strategy '${strategy.name}' failed:`, recoveryError);
            }
        }
        return false;
    }

    /**
     * 记录错误
     */
    private recordError(error: MicroCoreError): void {
        this.errorHistory.push(error);
        
        // 保持历史记录大小
        if (this.errorHistory.length > this.maxHistorySize) {
            this.errorHistory.shift();
        }
    }

    /**
     * 获取错误历史
     */
    getErrorHistory(): MicroCoreError[] {
        return [...this.errorHistory];
    }

    /**
     * 获取错误统计
     */
    getErrorStats(): Record<string, any> {
        const stats = {
            total: this.errorHistory.length,
            byType: {} as Record<string, number>,
            bySeverity: {} as Record<string, number>,
            byComponent: {} as Record<string, number>,
            recentErrors: this.errorHistory.slice(-10)
        };

        this.errorHistory.forEach(error => {
            // 按类型统计
            stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
            
            // 按严重程度统计
            stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
            
            // 按组件统计
            if (error.context.component) {
                stats.byComponent[error.context.component] = (stats.byComponent[error.context.component] || 0) + 1;
            }
        });

        return stats;
    }

    /**
     * 清理错误历史
     */
    clearHistory(): void {
        this.errorHistory = [];
    }

    /**
     * 设置默认恢复策略
     */
    private setupDefaultStrategies(): void {
        // 网络错误重试策略
        this.registerStrategy({
            name: 'NetworkRetry',
            priority: 100,
            canRecover: (error: Error) => {
                return error instanceof MicroCoreError && error.type === ErrorType.NETWORK;
            },
            recover: async (error: Error, context?: ErrorContext) => {
                // 简单的重试逻辑
                console.log('Attempting network retry for:', error.message);
                return Math.random() > 0.5; // 模拟50%成功率
            }
        });

        // 配置错误恢复策略
        this.registerStrategy({
            name: 'ConfigurationFallback',
            priority: 90,
            canRecover: (error: Error) => {
                return error instanceof MicroCoreError && error.type === ErrorType.CONFIGURATION;
            },
            recover: async (error: Error, context?: ErrorContext) => {
                console.log('Attempting configuration fallback for:', error.message);
                return true; // 假设总是可以回退到默认配置
            }
        });

        // 性能错误优化策略
        this.registerStrategy({
            name: 'PerformanceOptimization',
            priority: 80,
            canRecover: (error: Error) => {
                return error instanceof MicroCoreError && error.type === ErrorType.PERFORMANCE;
            },
            recover: async (error: Error, context?: ErrorContext) => {
                console.log('Attempting performance optimization for:', error.message);
                return true; // 假设总是可以优化
            }
        });
    }

    /**
     * 设置全局错误处理
     */
    private setupGlobalErrorHandling(): void {
        // 处理未捕获的 Promise 拒绝
        if (typeof window !== 'undefined') {
            window.addEventListener('unhandledrejection', (event) => {
                this.handleError(
                    new MicroCoreError(
                        event.reason?.message || 'Unhandled promise rejection',
                        'UNHANDLED_REJECTION',
                        { reason: event.reason },
                        ErrorType.UNKNOWN,
                        ErrorSeverity.HIGH
                    )
                );
            });

            // 处理全局错误
            window.addEventListener('error', (event) => {
                this.handleError(
                    new MicroCoreError(
                        event.message || 'Global error',
                        'GLOBAL_ERROR',
                        { 
                            filename: event.filename,
                            lineno: event.lineno,
                            colno: event.colno,
                            error: event.error
                        },
                        ErrorType.UNKNOWN,
                        ErrorSeverity.HIGH
                    )
                );
            });
        }
    }
}

/**
 * 错误处理装饰器
 */
export function handleErrors(
    errorType: ErrorType = ErrorType.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM
) {
    return function (
        target: any,
        propertyName: string,
        descriptor: PropertyDescriptor
    ): PropertyDescriptor {
        const originalMethod = descriptor.value;
        const errorHandler = new ErrorHandler();

        descriptor.value = function (...args: any[]) {
            try {
                const result = originalMethod.apply(this, args);
                
                if (result instanceof Promise) {
                    return result.catch((error) => {
                        const context: ErrorContext = {
                            component: target.constructor.name,
                            method: propertyName,
                            data: { args }
                        };
                        
                        errorHandler.handleError(
                            new MicroCoreError(
                                error.message || 'Method execution failed',
                                'METHOD_ERROR',
                                { originalError: error },
                                errorType,
                                severity,
                                context,
                                true
                            )
                        );
                        
                        throw error;
                    });
                }
                
                return result;
            } catch (error) {
                const context: ErrorContext = {
                    component: target.constructor.name,
                    method: propertyName,
                    data: { args }
                };
                
                errorHandler.handleError(
                    new MicroCoreError(
                        (error as Error).message || 'Method execution failed',
                        'METHOD_ERROR',
                        { originalError: error },
                        errorType,
                        severity,
                        context,
                        true
                    )
                );
                
                throw error;
            }
        };

        return descriptor;
    };
}

/**
 * 全局错误处理器实例
 */
export const globalErrorHandler = new ErrorHandler();

/**
 * 便捷的错误处理函数
 */
export const handleError = (error: Error | MicroCoreError, context?: ErrorContext) => {
    return globalErrorHandler.handleError(error, context);
};

export default ErrorHandler;
