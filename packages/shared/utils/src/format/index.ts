/**
 * 格式化工具函数
 * 从各个包中提取的通用格式化功能
 */

/**
 * 格式化字节大小
 * @description 将字节数转换为可读的文件大小格式
 * @param bytes 字节数
 * @param decimals 小数位数，默认为2
 * @returns 格式化后的文件大小字符串
 * @example
 * ```typescript
 * formatBytes(1024); // '1.00 KB'
 * formatBytes(1048576); // '1.00 MB'
 * formatBytes(1073741824, 1); // '1.0 GB'
 * ```
 */
export function formatBytes(bytes: number, decimals = 2): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * 格式化时间
 * @description 将毫秒数转换为可读的时间格式
 * @param ms 毫秒数
 * @returns 格式化后的时间字符串
 * @example
 * ```typescript
 * formatTime(1000); // '1.00s'
 * formatTime(500); // '500ms'
 * formatTime(65000); // '1m 5s'
 * ```
 */
export function formatTime(ms: number): string {
    if (ms < 1000) {
        return `${ms}ms`;
    }

    if (ms < 60000) {
        return `${(ms / 1000).toFixed(2)}s`;
    }

    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);

    if (seconds === 0) {
        return `${minutes}m`;
    }

    return `${minutes}m ${seconds}s`;
}

/**
 * 格式化错误信息
 * @description 将各种类型的错误转换为可读的字符串格式
 * @param error 错误对象、字符串或其他类型
 * @param context 错误上下文信息
 * @returns 格式化后的错误信息
 * @example
 * ```typescript
 * formatError(new Error('Something went wrong')); // 'Error: Something went wrong\n...'
 * formatError('Simple error'); // 'Simple error'
 * formatError({ code: 500 }); // '{\n  "code": 500\n}'
 * ```
 */
export function formatError(error: unknown, context?: any): string {
    let errorMessage = '';

    if (error instanceof Error) {
        // 包含完整的错误堆栈信息
        errorMessage = `${error.name}: ${error.message}${error.stack ? '\n' + error.stack : ''}`;
        
        // 添加额外的错误属性（如果存在）
        const extraProps = Object.entries(error).filter(([key]) => 
            !['name', 'message', 'stack'].includes(key));
        if (extraProps.length > 0) {
            errorMessage += '\nAdditional properties: ' + 
                JSON.stringify(Object.fromEntries(extraProps), null, 2);
        }
    } else if (typeof error === 'string') {
        errorMessage = error;
    } else if (typeof error === 'object' && error !== null) {
        try {
            errorMessage = JSON.stringify(error, null, 2);
        } catch (e) {
            errorMessage = `[Non-serializable object: ${Object.prototype.toString.call(error)}]`;
        }
    } else {
        errorMessage = String(error);
    }

    if (context) {
        try {
            errorMessage += `\nContext: ${JSON.stringify(context, null, 2)}`;
        } catch (e) {
            errorMessage += `\nContext: [Non-serializable context: ${Object.prototype.toString.call(context)}]`;
        }
    }

    return errorMessage;
}