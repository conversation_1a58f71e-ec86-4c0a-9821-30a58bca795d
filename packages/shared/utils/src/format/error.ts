/**
 * 通用错误格式化工具
 * 从适配器包中提取的通用错误处理逻辑
 */

/**
 * 格式化错误信息为统一格式
 */
export function formatError(error: unknown, context?: string): string {
    let message = '';

    if (error instanceof Error) {
        message = `${error.name}: ${error.message}`;
        if (error.stack) {
            message += `\n${error.stack}`;
        }
    } else if (typeof error === 'string') {
        message = error;
    } else if (typeof error === 'object' && error !== null) {
        try {
            message = JSON.stringify(error, null, 2);
        } catch {
            message = String(error);
        }
    } else {
        message = String(error);
    }

    if (context) {
        message = `[${context}] ${message}`;
    }

    return message;
}

/**
 * 格式化 React 特定错误
 */
export function formatReactError(error: Error, errorInfo?: any): string {
    let message = formatError(error, 'React');

    if (errorInfo) {
        if (errorInfo.componentStack) {
            message += `\nComponent Stack:${errorInfo.componentStack}`;
        }
        if (errorInfo.errorBoundary) {
            message += `\nError Boundary: ${errorInfo.errorBoundary}`;
        }
    }

    return message;
}

/**
 * 格式化适配器错误
 */
export function formatAdapterError(error: unknown, adapterName: string, operation?: string): string {
    const context = operation ? `${adapterName}:${operation}` : adapterName;
    return formatError(error, context);
}