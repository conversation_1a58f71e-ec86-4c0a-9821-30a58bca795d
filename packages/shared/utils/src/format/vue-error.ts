/**
 * Vue2 错误格式化工具
 * 专门处理 Vue2 相关的错误格式化
 */

import { formatAdapterError } from './error';

/**
 * Vue2 错误信息接口
 */
export interface Vue2ErrorInfo {
    componentName?: string;
    componentStack?: string;
    errorBoundary?: string;
    appName?: string;
    operation?: string;
    vueVersion?: string;
    [key: string]: any;
}

/**
 * 格式化 Vue2 错误
 * @param error 错误对象
 * @param errorInfo Vue2 错误信息
 * @returns 格式化后的错误字符串
 */
export function formatVue2Error(error: Error, errorInfo?: Vue2ErrorInfo): string {
    const context = {
        adapterName: 'Vue2Adapter',
        appName: errorInfo?.appName || 'unknown',
        operation: errorInfo?.operation || 'unknown'
    };

    let formatted = formatAdapterError(error, context);

    if (errorInfo?.componentName) {
        formatted += `\nVue Component: ${errorInfo.componentName}`;
    }

    if (errorInfo?.componentStack) {
        formatted += `\nComponent Stack: ${errorInfo.componentStack}`;
    }

    if (errorInfo?.vueVersion) {
        formatted += `\nVue Version: ${errorInfo.vueVersion}`;
    }

    if (errorInfo?.errorBoundary) {
        formatted += `\nError Boundary: ${errorInfo.errorBoundary}`;
    }

    return formatted;
}

/**
 * 创建 Vue2 错误信息对象
 * @param error 错误对象
 * @param componentName 组件名称
 * @param componentStack 组件堆栈
 * @returns Vue2 错误信息对象
 */
export function createVue2ErrorInfo(
    error: Error,
    componentName?: string,
    componentStack?: string
): Vue2ErrorInfo {
    return {
        componentName: componentName || '',
        componentStack: componentStack || '',
        errorBoundary: 'Vue2Adapter',
        timestamp: new Date().toISOString(),
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
        url: typeof window !== 'undefined' ? window.location.href : '',
        vueVersion: getVue2Version()
    };
}

/**
 * 获取 Vue2 版本
 * @returns Vue2 版本字符串
 */
function getVue2Version(): string {
    try {
        if (typeof window !== 'undefined' && (window as any).Vue) {
            return (window as any).Vue.version || '2.x';
        }

        if (typeof require !== 'undefined') {
            try {
                const Vue = require('vue');
                return Vue.version || '2.x';
            } catch {
                // Vue not available
            }
        }

        return '2.x';
    } catch {
        return '2.x';
    }
}