/**
 * @fileoverview 健康检查系统
 * @description 提供应用健康状态监控和检查功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 健康状态枚举
 */
export enum HealthStatus {
    /** 健康 */
    HEALTHY = 'healthy',
    /** 警告 */
    WARNING = 'warning',
    /** 不健康 */
    UNHEALTHY = 'unhealthy',
    /** 未知 */
    UNKNOWN = 'unknown'
}

/**
 * 健康检查结果
 */
export interface HealthCheckResult {
    /** 检查名称 */
    name: string;
    /** 健康状态 */
    status: HealthStatus;
    /** 检查消息 */
    message?: string;
    /** 检查时间 */
    timestamp: number;
    /** 响应时间（毫秒） */
    responseTime: number;
    /** 额外数据 */
    data?: Record<string, any>;
    /** 错误信息 */
    error?: Error;
}

/**
 * 健康检查器接口
 */
export interface HealthChecker {
    /** 检查器名称 */
    name: string;
    /** 执行健康检查 */
    check(): Promise<HealthCheckResult>;
    /** 是否启用 */
    enabled?: boolean;
    /** 检查间隔（毫秒） */
    interval?: number;
    /** 超时时间（毫秒） */
    timeout?: number;
}

/**
 * 系统健康状态
 */
export interface SystemHealth {
    /** 整体状态 */
    status: HealthStatus;
    /** 检查时间 */
    timestamp: number;
    /** 检查结果列表 */
    checks: HealthCheckResult[];
    /** 健康的检查数量 */
    healthyCount: number;
    /** 警告的检查数量 */
    warningCount: number;
    /** 不健康的检查数量 */
    unhealthyCount: number;
    /** 总检查数量 */
    totalCount: number;
    /** 系统运行时间 */
    uptime: number;
    /** 版本信息 */
    version?: string;
}

/**
 * 健康检查配置
 */
export interface HealthCheckConfig {
    /** 是否启用健康检查 */
    enabled?: boolean;
    /** 默认检查间隔（毫秒） */
    defaultInterval?: number;
    /** 默认超时时间（毫秒） */
    defaultTimeout?: number;
    /** 并行检查数量限制 */
    concurrencyLimit?: number;
    /** 是否在启动时立即检查 */
    checkOnStart?: boolean;
    /** 自定义检查器 */
    customCheckers?: HealthChecker[];
}

/**
 * 微核心健康检查器
 */
export class MicroCoreHealthChecker {
    private config: Required<HealthCheckConfig>;
    private checkers: Map<string, HealthChecker> = new Map();
    private results: Map<string, HealthCheckResult> = new Map();
    private timers: Map<string, NodeJS.Timeout> = new Map();
    private startTime: number;
    private running = false;

    constructor(config: HealthCheckConfig = {}) {
        this.config = {
            enabled: true,
            defaultInterval: 30000, // 30秒
            defaultTimeout: 5000,   // 5秒
            concurrencyLimit: 10,
            checkOnStart: true,
            customCheckers: [],
            ...config
        };

        this.startTime = Date.now();

        if (this.config.enabled) {
            this.initialize();
        }
    }

    /**
     * 初始化健康检查器
     */
    private initialize(): void {
        // 添加默认检查器
        this.setupDefaultCheckers();

        // 添加自定义检查器
        this.config.customCheckers.forEach(checker => {
            this.addChecker(checker);
        });

        // 启动时立即检查
        if (this.config.checkOnStart) {
            this.checkAll().catch(error => {
                console.error('启动时健康检查失败:', error);
            });
        }
    }

    /**
     * 添加健康检查器
     */
    addChecker(checker: HealthChecker): void {
        this.checkers.set(checker.name, checker);

        // 如果正在运行，启动定时检查
        if (this.running) {
            this.startPeriodicCheck(checker);
        }
    }

    /**
     * 移除健康检查器
     */
    removeChecker(name: string): void {
        this.checkers.delete(name);
        this.results.delete(name);

        // 清除定时器
        const timer = this.timers.get(name);
        if (timer) {
            clearInterval(timer);
            this.timers.delete(name);
        }
    }

    /**
     * 获取健康检查器
     */
    getChecker(name: string): HealthChecker | undefined {
        return this.checkers.get(name);
    }

    /**
     * 获取所有检查器
     */
    getCheckers(): HealthChecker[] {
        return Array.from(this.checkers.values());
    }

    /**
     * 执行单个健康检查
     */
    async check(name: string): Promise<HealthCheckResult> {
        const checker = this.checkers.get(name);
        if (!checker) {
            throw new Error(`健康检查器 '${name}' 不存在`);
        }

        return this.executeCheck(checker);
    }

    /**
     * 执行所有健康检查
     */
    async checkAll(): Promise<HealthCheckResult[]> {
        const checkers = Array.from(this.checkers.values())
            .filter(checker => checker.enabled !== false);

        // 限制并发数量
        const results: HealthCheckResult[] = [];
        const limit = this.config.concurrencyLimit;

        for (let i = 0; i < checkers.length; i += limit) {
            const batch = checkers.slice(i, i + limit);
            const batchResults = await Promise.allSettled(
                batch.map(checker => this.executeCheck(checker))
            );

            batchResults.forEach(result => {
                if (result.status === 'fulfilled') {
                    results.push(result.value);
                } else {
                    console.error('健康检查执行失败:', result.reason);
                }
            });
        }

        return results;
    }

    /**
     * 获取系统健康状态
     */
    async getSystemHealth(): Promise<SystemHealth> {
        const checks = await this.checkAll();

        // 更新结果缓存
        checks.forEach(result => {
            this.results.set(result.name, result);
        });

        // 统计状态
        const healthyCount = checks.filter(c => c.status === HealthStatus.HEALTHY).length;
        const warningCount = checks.filter(c => c.status === HealthStatus.WARNING).length;
        const unhealthyCount = checks.filter(c => c.status === HealthStatus.UNHEALTHY).length;

        // 确定整体状态
        let overallStatus = HealthStatus.HEALTHY;
        if (unhealthyCount > 0) {
            overallStatus = HealthStatus.UNHEALTHY;
        } else if (warningCount > 0) {
            overallStatus = HealthStatus.WARNING;
        }

        return {
            status: overallStatus,
            timestamp: Date.now(),
            checks,
            healthyCount,
            warningCount,
            unhealthyCount,
            totalCount: checks.length,
            uptime: Date.now() - this.startTime,
            version: this.getVersion()
        };
    }

    /**
     * 获取缓存的检查结果
     */
    getCachedResults(): HealthCheckResult[] {
        return Array.from(this.results.values());
    }

    /**
     * 获取特定检查的缓存结果
     */
    getCachedResult(name: string): HealthCheckResult | undefined {
        return this.results.get(name);
    }

    /**
     * 启动定期健康检查
     */
    start(): void {
        if (this.running) {
            return;
        }

        this.running = true;

        // 为每个检查器启动定时检查
        this.checkers.forEach(checker => {
            this.startPeriodicCheck(checker);
        });

        console.log('健康检查器已启动');
    }

    /**
     * 停止定期健康检查
     */
    stop(): void {
        if (!this.running) {
            return;
        }

        this.running = false;

        // 清除所有定时器
        this.timers.forEach(timer => {
            clearInterval(timer);
        });
        this.timers.clear();

        console.log('健康检查器已停止');
    }

    /**
     * 执行健康检查
     */
    private async executeCheck(checker: HealthChecker): Promise<HealthCheckResult> {
        const startTime = Date.now();
        const timeout = checker.timeout || this.config.defaultTimeout;

        try {
            // 使用超时控制
            const result = await Promise.race([
                checker.check(),
                this.createTimeoutPromise(timeout, checker.name)
            ]);

            const responseTime = Date.now() - startTime;

            return {
                ...result,
                responseTime,
                timestamp: Date.now()
            };
        } catch (error) {
            const responseTime = Date.now() - startTime;

            return {
                name: checker.name,
                status: HealthStatus.UNHEALTHY,
                message: error instanceof Error ? error.message : String(error),
                timestamp: Date.now(),
                responseTime,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }

    /**
     * 创建超时Promise
     */
    private createTimeoutPromise(timeout: number, checkerName: string): Promise<never> {
        return new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`健康检查 '${checkerName}' 超时 (${timeout}ms)`));
            }, timeout);
        });
    }

    /**
     * 启动定期检查
     */
    private startPeriodicCheck(checker: HealthChecker): void {
        if (checker.enabled === false) {
            return;
        }

        const interval = checker.interval || this.config.defaultInterval;

        const timer = setInterval(async () => {
            try {
                const result = await this.executeCheck(checker);
                this.results.set(checker.name, result);
            } catch (error) {
                console.error(`定期健康检查 '${checker.name}' 失败:`, error);
            }
        }, interval);

        this.timers.set(checker.name, timer);
    }

    /**
     * 设置默认检查器
     */
    private setupDefaultCheckers(): void {
        // 内存使用检查器
        this.addChecker(new MemoryHealthChecker());

        // 应用状态检查器
        this.addChecker(new ApplicationHealthChecker(this.startTime));

        // 如果在浏览器环境，添加浏览器相关检查器
        if (typeof window !== 'undefined') {
            this.addChecker(new BrowserHealthChecker());
        }
    }

    /**
     * 获取版本信息
     */
    private getVersion(): string {
        try {
            return process.env.npm_package_version || '1.0.0';
        } catch {
            return '1.0.0';
        }
    }
}

/**
 * 内存健康检查器
 */
export class MemoryHealthChecker implements HealthChecker {
    name = 'memory';
    interval = 30000; // 30秒
    timeout = 5000;   // 5秒

    async check(): Promise<HealthCheckResult> {
        const startTime = Date.now();

        try {
            let memoryInfo: any = {};

            // 浏览器环境
            if (typeof window !== 'undefined' && 'memory' in performance) {
                const memory = (performance as any).memory;
                memoryInfo = {
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit,
                    usagePercent: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
                };
            }
            // Node.js 环境
            else if (typeof process !== 'undefined' && process.memoryUsage) {
                const memory = process.memoryUsage();
                memoryInfo = {
                    rss: memory.rss,
                    heapTotal: memory.heapTotal,
                    heapUsed: memory.heapUsed,
                    external: memory.external,
                    usagePercent: (memory.heapUsed / memory.heapTotal) * 100
                };
            }

            // 判断健康状态
            let status = HealthStatus.HEALTHY;
            let message = '内存使用正常';

            if (memoryInfo.usagePercent > 90) {
                status = HealthStatus.UNHEALTHY;
                message = `内存使用率过高: ${memoryInfo.usagePercent.toFixed(1)}%`;
            } else if (memoryInfo.usagePercent > 80) {
                status = HealthStatus.WARNING;
                message = `内存使用率较高: ${memoryInfo.usagePercent.toFixed(1)}%`;
            }

            return {
                name: this.name,
                status,
                message,
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                data: memoryInfo
            };
        } catch (error) {
            return {
                name: this.name,
                status: HealthStatus.UNHEALTHY,
                message: '内存检查失败',
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }
}

/**
 * 应用健康检查器
 */
export class ApplicationHealthChecker implements HealthChecker {
    name = 'application';
    interval = 60000; // 60秒
    timeout = 3000;   // 3秒

    constructor(private startTime: number) { }

    async check(): Promise<HealthCheckResult> {
        const startTime = Date.now();

        try {
            const uptime = Date.now() - this.startTime;
            const uptimeSeconds = Math.floor(uptime / 1000);
            const uptimeMinutes = Math.floor(uptimeSeconds / 60);
            const uptimeHours = Math.floor(uptimeMinutes / 60);

            const data = {
                uptime,
                uptimeFormatted: `${uptimeHours}h ${uptimeMinutes % 60}m ${uptimeSeconds % 60}s`,
                startTime: this.startTime,
                currentTime: Date.now(),
                environment: typeof window !== 'undefined' ? 'browser' : 'node'
            };

            return {
                name: this.name,
                status: HealthStatus.HEALTHY,
                message: `应用运行正常，已运行 ${data.uptimeFormatted}`,
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                data
            };
        } catch (error) {
            return {
                name: this.name,
                status: HealthStatus.UNHEALTHY,
                message: '应用状态检查失败',
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }
}

/**
 * 浏览器健康检查器
 */
export class BrowserHealthChecker implements HealthChecker {
    name = 'browser';
    interval = 60000; // 60秒
    timeout = 3000;   // 3秒

    async check(): Promise<HealthCheckResult> {
        const startTime = Date.now();

        try {
            if (typeof window === 'undefined') {
                return {
                    name: this.name,
                    status: HealthStatus.UNKNOWN,
                    message: '非浏览器环境',
                    timestamp: Date.now(),
                    responseTime: Date.now() - startTime
                };
            }

            const data: any = {
                userAgent: navigator.userAgent,
                language: navigator.language,
                onLine: navigator.onLine,
                cookieEnabled: navigator.cookieEnabled,
                platform: navigator.platform
            };

            // 检查连接状态
            if ('connection' in navigator) {
                const connection = (navigator as any).connection;
                data.connection = {
                    effectiveType: connection.effectiveType,
                    downlink: connection.downlink,
                    rtt: connection.rtt,
                    saveData: connection.saveData
                };
            }

            // 检查存储
            try {
                localStorage.setItem('health-check', 'test');
                localStorage.removeItem('health-check');
                data.localStorage = true;
            } catch {
                data.localStorage = false;
            }

            // 判断健康状态
            let status = HealthStatus.HEALTHY;
            let message = '浏览器环境正常';

            if (!navigator.onLine) {
                status = HealthStatus.WARNING;
                message = '网络连接异常';
            } else if (!data.localStorage) {
                status = HealthStatus.WARNING;
                message = 'localStorage 不可用';
            }

            return {
                name: this.name,
                status,
                message,
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                data
            };
        } catch (error) {
            return {
                name: this.name,
                status: HealthStatus.UNHEALTHY,
                message: '浏览器环境检查失败',
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }
}

/**
 * 网络健康检查器
 */
export class NetworkHealthChecker implements HealthChecker {
    name = 'network';
    interval = 30000; // 30秒
    timeout = 10000;  // 10秒

    constructor(private endpoints: string[] = ['https://www.google.com', 'https://www.baidu.com']) { }

    async check(): Promise<HealthCheckResult> {
        const startTime = Date.now();

        try {
            const results = await Promise.allSettled(
                this.endpoints.map(endpoint => this.checkEndpoint(endpoint))
            );

            const successful = results.filter(r => r.status === 'fulfilled').length;
            const total = results.length;
            const successRate = (successful / total) * 100;

            let status = HealthStatus.HEALTHY;
            let message = `网络连接正常 (${successful}/${total} 成功)`;

            if (successRate === 0) {
                status = HealthStatus.UNHEALTHY;
                message = '网络连接完全失败';
            } else if (successRate < 50) {
                status = HealthStatus.UNHEALTHY;
                message = `网络连接不稳定 (${successRate.toFixed(1)}% 成功率)`;
            } else if (successRate < 100) {
                status = HealthStatus.WARNING;
                message = `网络连接部分异常 (${successRate.toFixed(1)}% 成功率)`;
            }

            return {
                name: this.name,
                status,
                message,
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                data: {
                    successRate,
                    successful,
                    total,
                    results: results.map((r, i) => ({
                        endpoint: this.endpoints[i],
                        success: r.status === 'fulfilled',
                        error: r.status === 'rejected' ? r.reason?.message : undefined
                    }))
                }
            };
        } catch (error) {
            return {
                name: this.name,
                status: HealthStatus.UNHEALTHY,
                message: '网络检查失败',
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }

    private async checkEndpoint(endpoint: string): Promise<void> {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        try {
            const response = await fetch(endpoint, {
                method: 'HEAD',
                signal: controller.signal,
                mode: 'no-cors' // 避免CORS问题
            });
            clearTimeout(timeoutId);
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }
}

/**
 * 数据库健康检查器
 */
export class DatabaseHealthChecker implements HealthChecker {
    name = 'database';
    interval = 30000; // 30秒
    timeout = 5000;   // 5秒

    constructor(private checkFunction: () => Promise<boolean>) { }

    async check(): Promise<HealthCheckResult> {
        const startTime = Date.now();

        try {
            const isHealthy = await this.checkFunction();

            return {
                name: this.name,
                status: isHealthy ? HealthStatus.HEALTHY : HealthStatus.UNHEALTHY,
                message: isHealthy ? '数据库连接正常' : '数据库连接失败',
                timestamp: Date.now(),
                responseTime: Date.now() - startTime
            };
        } catch (error) {
            return {
                name: this.name,
                status: HealthStatus.UNHEALTHY,
                message: '数据库检查失败',
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }
}

/**
 * 自定义健康检查器
 */
export class CustomHealthChecker implements HealthChecker {
    constructor(
        public name: string,
        private checkFunction: () => Promise<Omit<HealthCheckResult, 'name' | 'timestamp' | 'responseTime'>>,
        public interval?: number,
        public timeout?: number,
        public enabled?: boolean
    ) { }

    async check(): Promise<HealthCheckResult> {
        const startTime = Date.now();

        try {
            const result = await this.checkFunction();

            return {
                ...result,
                name: this.name,
                timestamp: Date.now(),
                responseTime: Date.now() - startTime
            };
        } catch (error) {
            return {
                name: this.name,
                status: HealthStatus.UNHEALTHY,
                message: error instanceof Error ? error.message : String(error),
                timestamp: Date.now(),
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }
}

/**
 * 创建默认健康检查器
 */
export function createDefaultHealthChecker(config: HealthCheckConfig = {}): MicroCoreHealthChecker {
    return new MicroCoreHealthChecker(config);
}

/**
 * 创建内存健康检查器
 */
export function createMemoryHealthChecker(): MemoryHealthChecker {
    return new MemoryHealthChecker();
}

/**
 * 创建网络健康检查器
 */
export function createNetworkHealthChecker(endpoints?: string[]): NetworkHealthChecker {
    return new NetworkHealthChecker(endpoints);
}

/**
 * 创建数据库健康检查器
 */
export function createDatabaseHealthChecker(checkFunction: () => Promise<boolean>): DatabaseHealthChecker {
    return new DatabaseHealthChecker(checkFunction);
}

/**
 * 创建自定义健康检查器
 */
export function createCustomHealthChecker(
    name: string,
    checkFunction: () => Promise<Omit<HealthCheckResult, 'name' | 'timestamp' | 'responseTime'>>,
    options: {
        interval?: number;
        timeout?: number;
        enabled?: boolean;
    } = {}
): CustomHealthChecker {
    return new CustomHealthChecker(
        name,
        checkFunction,
        options.interval,
        options.timeout,
        options.enabled
    );
}

// 导出全局健康检查器实例
export const globalHealthChecker = createDefaultHealthChecker();

/**
 * 便捷的健康检查函数
 */
export async function checkHealth(): Promise<SystemHealth> {
    return globalHealthChecker.getSystemHealth();
}

/**
 * 健康检查装饰器
 */
export function healthCheck(checkerName?: string) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        const name = checkerName || `${target.constructor.name}.${propertyKey}`;

        // 创建自定义检查器
        const checker = createCustomHealthChecker(name, async () => {
            try {
                await originalMethod.apply(target);
                return {
                    status: HealthStatus.HEALTHY,
                    message: `${name} 执行成功`
                };
            } catch (error) {
                return {
                    status: HealthStatus.UNHEALTHY,
                    message: `${name} 执行失败: ${error instanceof Error ? error.message : String(error)}`,
                    error: error instanceof Error ? error : new Error(String(error))
                };
            }
        });

        // 添加到全局检查器
        globalHealthChecker.addChecker(checker);

        return descriptor;
    };
}

/**
 * 健康检查中间件（用于Express等）
 */
export function createHealthCheckMiddleware(path = '/health') {
    return async (req: any, res: any, next: any) => {
        if (req.path === path) {
            try {
                const health = await checkHealth();
                const statusCode = health.status === HealthStatus.HEALTHY ? 200 : 503;

                res.status(statusCode).json(health);
            } catch (error) {
                res.status(500).json({
                    status: HealthStatus.UNHEALTHY,
                    message: '健康检查失败',
                    error: error instanceof Error ? error.message : String(error),
                    timestamp: Date.now()
                });
            }
        } else {
            next();
        }
    };
}

/**
 * 健康状态格式化工具
 */
export const HealthFormatter = {
    /**
     * 格式化健康状态为字符串
     */
    formatStatus(status: HealthStatus): string {
        const statusMap = {
            [HealthStatus.HEALTHY]: '✅ 健康',
            [HealthStatus.WARNING]: '⚠️ 警告',
            [HealthStatus.UNHEALTHY]: '❌ 不健康',
            [HealthStatus.UNKNOWN]: '❓ 未知'
        };
        return statusMap[status] || '❓ 未知';
    },

    /**
     * 格式化系统健康状态
     */
    formatSystemHealth(health: SystemHealth): string {
        const lines = [
            `系统健康状态: ${this.formatStatus(health.status)}`,
            `检查时间: ${new Date(health.timestamp).toLocaleString()}`,
            `运行时间: ${Math.floor(health.uptime / 1000)}秒`,
            `检查结果: ${health.healthyCount}健康 / ${health.warningCount}警告 / ${health.unhealthyCount}不健康`,
            ''
        ];

        health.checks.forEach(check => {
            lines.push(`${this.formatStatus(check.status)} ${check.name}: ${check.message} (${check.responseTime}ms)`);
        });

        return lines.join('\n');
    },

    /**
     * 格式化为HTML
     */
    formatAsHTML(health: SystemHealth): string {
        const statusColor = {
            [HealthStatus.HEALTHY]: '#28a745',
            [HealthStatus.WARNING]: '#ffc107',
            [HealthStatus.UNHEALTHY]: '#dc3545',
            [HealthStatus.UNKNOWN]: '#6c757d'
        };

        return `
            <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
                <h1 style="color: ${statusColor[health.status]};">
                    ${this.formatStatus(health.status)} 系统健康状态
                </h1>
                
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <p><strong>检查时间:</strong> ${new Date(health.timestamp).toLocaleString()}</p>
                    <p><strong>运行时间:</strong> ${Math.floor(health.uptime / 1000)}秒</p>
                    <p><strong>检查统计:</strong> ${health.healthyCount}健康 / ${health.warningCount}警告 / ${health.unhealthyCount}不健康</p>
                </div>

                <h2>详细检查结果</h2>
                <div>
                    ${health.checks.map(check => `
                        <div style="
                            border-left: 4px solid ${statusColor[check.status]};
                            padding: 10px 15px;
                            margin: 10px 0;
                            background: #fff;
                            border-radius: 0 5px 5px 0;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        ">
                            <h3 style="margin: 0 0 5px 0; color: ${statusColor[check.status]};">
                                ${check.name}
                            </h3>
                            <p style="margin: 0; color: #666;">
                                ${check.message} <small>(响应时间: ${check.responseTime}ms)</small>
                            </p>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
};
