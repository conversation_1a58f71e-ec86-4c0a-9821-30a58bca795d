/**
 * @fileoverview 性能监控系统
 * @description 提供应用性能监控、指标收集和分析功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */


/**
 * 性能指标类型
 */
export enum MetricType {
    /** 计数器 */
    COUNTER = 'counter',
    /** 计量器 */
    GAUGE = 'gauge',
    /** 直方图 */
    HISTOGRAM = 'histogram',
    /** 计时器 */
    TIMER = 'timer'
}

/**
 * 性能指标
 */
export interface PerformanceMetric {
    /** 指标名称 */
    name: string;
    /** 指标类型 */
    type: MetricType;
    /** 指标值 */
    value: number;
    /** 指标单位 */
    unit?: string;
    /** 标签 */
    labels?: Record<string, string>;
    /** 时间戳 */
    timestamp: number;
    /** 描述 */
    description?: string;
}

/**
 * 性能监控配置
 */
export interface PerformanceMonitorConfig {
    /** 是否启用监控 */
    enabled?: boolean;
    /** 采样率 (0-1) */
    sampleRate?: number;
    /** 指标收集间隔（毫秒） */
    collectInterval?: number;
    /** 最大指标数量 */
    maxMetrics?: number;
    /** 是否自动收集浏览器性能指标 */
    collectBrowserMetrics?: boolean;
    /** 是否自动收集资源加载指标 */
    collectResourceMetrics?: boolean;
    /** 是否自动收集用户交互指标 */
    collectUserMetrics?: boolean;
    /** 自定义指标收集器 */
    customCollectors?: PerformanceCollector[];
}

/**
 * 性能收集器接口
 */
export interface PerformanceCollector {
    /** 收集器名称 */
    name: string;
    /** 收集指标 */
    collect(): PerformanceMetric[] | Promise<PerformanceMetric[]>;
    /** 是否启用 */
    enabled?: boolean;
}

/**
 * 性能报告器接口
 */
export interface PerformanceReporter {
    /** 报告指标 */
    report(metrics: PerformanceMetric[]): Promise<void>;
    /** 报告单个指标 */
    reportMetric(metric: PerformanceMetric): Promise<void>;
}

/**
 * 性能统计信息
 */
export interface PerformanceStats {
    /** 应用启动时间 */
    appStartTime: number;
    /** 页面加载时间 */
    pageLoadTime?: number;
    /** 首次内容绘制时间 */
    firstContentfulPaint?: number;
    /** 最大内容绘制时间 */
    largestContentfulPaint?: number;
    /** 首次输入延迟 */
    firstInputDelay?: number;
    /** 累积布局偏移 */
    cumulativeLayoutShift?: number;
    /** 内存使用情况 */
    memoryUsage?: {
        used: number;
        total: number;
        limit?: number;
    };
    /** 网络连接信息 */
    connection?: {
        effectiveType: string;
        downlink: number;
        rtt: number;
    };
}

/**
 * 微核心性能监控器
 */
export class MicroCorePerformanceMonitor {
    private config: Required<PerformanceMonitorConfig>;
    private metrics: PerformanceMetric[] = [];
    private collectors: PerformanceCollector[] = [];
    private reporters: PerformanceReporter[] = [];
    private collectTimer: NodeJS.Timeout | null = null;
    private startTime: number;
    private observer: PerformanceObserver | null = null;

    constructor(config: PerformanceMonitorConfig = {}) {
        this.config = {
            enabled: true,
            sampleRate: 1.0,
            collectInterval: 5000,
            maxMetrics: 1000,
            collectBrowserMetrics: true,
            collectResourceMetrics: true,
            collectUserMetrics: true,
            customCollectors: [],
            ...config
        };

        this.startTime = Date.now();

        if (this.config.enabled) {
            this.initialize();
        }
    }

    /**
     * 初始化监控器
     */
    private initialize(): void {
        // 设置默认收集器
        this.setupDefaultCollectors();

        // 添加自定义收集器
        this.collectors.push(...this.config.customCollectors);

        // 设置浏览器性能监控
        if (this.config.collectBrowserMetrics) {
            this.setupBrowserMetrics();
        }

        // 设置资源监控
        if (this.config.collectResourceMetrics) {
            this.setupResourceMetrics();
        }

        // 设置用户交互监控
        if (this.config.collectUserMetrics) {
            this.setupUserMetrics();
        }

        // 启动定时收集
        this.startCollection();
    }

    /**
     * 记录指标
     */
    recordMetric(
        name: string,
        value: number,
        type: MetricType = MetricType.GAUGE,
        options: {
            unit?: string;
            labels?: Record<string, string>;
            description?: string;
        } = {}
    ): void {
        if (!this.config.enabled || !this.shouldSample()) {
            return;
        }

        const metric: PerformanceMetric = {
            name,
            type,
            value,
            unit: options.unit,
            labels: options.labels,
            timestamp: Date.now(),
            description: options.description
        };

        this.addMetric(metric);
    }

    /**
     * 记录计时器
     */
    recordTimer(name: string, startTime: number, labels?: Record<string, string>): void {
        const duration = Date.now() - startTime;
        this.recordMetric(name, duration, MetricType.TIMER, {
            unit: 'ms',
            labels,
            description: `Timer for ${name}`
        });
    }

    /**
     * 记录计数器
     */
    recordCounter(name: string, increment: number = 1, labels?: Record<string, string>): void {
        this.recordMetric(name, increment, MetricType.COUNTER, {
            labels,
            description: `Counter for ${name}`
        });
    }

    /**
     * 创建计时器
     */
    createTimer(name: string, labels?: Record<string, string>): () => void {
        const startTime = Date.now();
        return () => {
            this.recordTimer(name, startTime, labels);
        };
    }

    /**
     * 获取性能统计信息
     */
    getStats(): PerformanceStats {
        const stats: PerformanceStats = {
            appStartTime: this.startTime
        };

        if (typeof window !== 'undefined' && window.performance) {
            const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
            if (navigation) {
                stats.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
            }

            // 获取 Web Vitals
            const paintEntries = performance.getEntriesByType('paint');
            const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
            if (fcpEntry) {
                stats.firstContentfulPaint = fcpEntry.startTime;
            }

            // 获取内存信息
            if ('memory' in performance) {
                const memory = (performance as any).memory;
                stats.memoryUsage = {
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit
                };
            }

            // 获取网络连接信息
            if ('connection' in navigator) {
                const connection = (navigator as any).connection;
                stats.connection = {
                    effectiveType: connection.effectiveType,
                    downlink: connection.downlink,
                    rtt: connection.rtt
                };
            }
        }

        return stats;
    }

    /**
     * 获取所有指标
     */
    getMetrics(): PerformanceMetric[] {
        return [...this.metrics];
    }

    /**
     * 清除指标
     */
    clearMetrics(): void {
        this.metrics = [];
    }

    /**
     * 添加收集器
     */
    addCollector(collector: PerformanceCollector): void {
        this.collectors.push(collector);
    }

    /**
     * 移除收集器
     */
    removeCollector(name: string): void {
        this.collectors = this.collectors.filter(c => c.name !== name);
    }

    /**
     * 添加报告器
     */
    addReporter(reporter: PerformanceReporter): void {
        this.reporters.push(reporter);
    }

    /**
     * 移除报告器
     */
    removeReporter(reporter: PerformanceReporter): void {
        const index = this.reporters.indexOf(reporter);
        if (index > -1) {
            this.reporters.splice(index, 1);
        }
    }

    /**
     * 启动监控
     */
    start(): void {
        if (!this.config.enabled) {
            this.config.enabled = true;
            this.initialize();
        }
    }

    /**
     * 停止监控
     */
    stop(): void {
        this.config.enabled = false;

        if (this.collectTimer) {
            clearInterval(this.collectTimer);
            this.collectTimer = null;
        }

        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }
    }

    /**
     * 立即收集并报告指标
     */
    async flush(): Promise<void> {
        await this.collectMetrics();
        await this.reportMetrics();
    }

    /**
     * 是否应该采样
     */
    private shouldSample(): boolean {
        return Math.random() < this.config.sampleRate;
    }

    /**
     * 添加指标
     */
    private addMetric(metric: PerformanceMetric): void {
        this.metrics.push(metric);

        // 如果超过最大数量，移除最旧的指标
        if (this.metrics.length > this.config.maxMetrics) {
            this.metrics.shift();
        }
    }

    /**
     * 设置默认收集器
     */
    private setupDefaultCollectors(): void {
        // 系统指标收集器
        this.collectors.push({
            name: 'system',
            collect: () => this.collectSystemMetrics()
        });

        // 应用指标收集器
        this.collectors.push({
            name: 'application',
            collect: () => this.collectApplicationMetrics()
        });
    }

    /**
     * 设置浏览器性能监控
     */
    private setupBrowserMetrics(): void {
        if (typeof window === 'undefined' || !window.performance) {
            return;
        }

        // 监听性能条目
        if ('PerformanceObserver' in window) {
            this.observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    this.processPerfEntry(entry);
                });
            });

            // 观察各种性能条目类型
            try {
                this.observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] });
            } catch (error) {
                console.warn('性能观察器设置失败:', error);
            }
        }
    }

    /**
     * 设置资源监控
     */
    private setupResourceMetrics(): void {
        if (typeof window === 'undefined' || !window.performance) {
            return;
        }

        // 监听资源加载
        const resourceObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach(entry => {
                if (entry.entryType === 'resource') {
                    this.processResourceEntry(entry as PerformanceResourceTiming);
                }
            });
        });

        try {
            resourceObserver.observe({ entryTypes: ['resource'] });
        } catch (error) {
            console.warn('资源监控设置失败:', error);
        }
    }

    /**
     * 设置用户交互监控
     */
    private setupUserMetrics(): void {
        if (typeof window === 'undefined') {
            return;
        }

        // 监听用户交互事件
        const events = ['click', 'scroll', 'keydown', 'touchstart'];
        events.forEach(eventType => {
            window.addEventListener(eventType, () => {
                this.recordCounter(`user.${eventType}`, 1, {
                    type: eventType,
                    timestamp: Date.now().toString()
                });
            }, { passive: true });
        });
    }

    /**
     * 启动定时收集
     */
    private startCollection(): void {
        this.collectTimer = setInterval(async () => {
            try {
                await this.collectMetrics();
                await this.reportMetrics();
            } catch (error) {
                console.error('指标收集失败:', error);
            }
        }, this.config.collectInterval);
    }

    /**
     * 收集指标
     */
    private async collectMetrics(): Promise<void> {
        const promises = this.collectors
            .filter(collector => collector.enabled !== false)
            .map(async collector => {
                try {
                    const metrics = await collector.collect();
                    metrics.forEach(metric => this.addMetric(metric));
                } catch (error) {
                    console.error(`收集器 ${collector.name} 执行失败:`, error);
                }
            });

        await Promise.allSettled(promises);
    }

    /**
     * 报告指标
     */
    private async reportMetrics(): Promise<void> {
        if (this.metrics.length === 0 || this.reporters.length === 0) {
            return;
        }

        const metricsToReport = [...this.metrics];
        this.clearMetrics();

        const promises = this.reporters.map(async reporter => {
            try {
                await reporter.report(metricsToReport);
            } catch (error) {
                console.error('指标报告失败:', error);
            }
        });

        await Promise.allSettled(promises);
    }

    /**
     * 收集系统指标
     */
    private collectSystemMetrics(): PerformanceMetric[] {
        const metrics: PerformanceMetric[] = [];
        const now = Date.now();

        // 内存使用情况
        if (typeof window !== 'undefined' && 'memory' in performance) {
            const memory = (performance as any).memory;
            metrics.push({
                name: 'system.memory.used',
                type: MetricType.GAUGE,
                value: memory.usedJSHeapSize,
                unit: 'bytes',
                timestamp: now
            });

            metrics.push({
                name: 'system.memory.total',
                type: MetricType.GAUGE,
                value: memory.totalJSHeapSize,
                unit: 'bytes',
                timestamp: now
            });

            metrics.push({
                name: 'system.memory.limit',
                type: MetricType.GAUGE,
                value: memory.jsHeapSizeLimit,
                unit: 'bytes',
                timestamp: now
            });
        }

        // 连接信息
        if (typeof navigator !== 'undefined' && 'connection' in navigator) {
            const connection = (navigator as any).connection;
            metrics.push({
                name: 'system.connection.downlink',
                type: MetricType.GAUGE,
                value: connection.downlink,
                unit: 'mbps',
                timestamp: now
            });

            metrics.push({
                name: 'system.connection.rtt',
                type: MetricType.GAUGE,
                value: connection.rtt,
                unit: 'ms',
                timestamp: now
            });
        }

        return metrics;
    }

    /**
     * 收集应用指标
     */
    private collectApplicationMetrics(): PerformanceMetric[] {
        const metrics: PerformanceMetric[] = [];
        const now = Date.now();

        // 应用运行时间
        metrics.push({
            name: 'app.uptime',
            type: MetricType.GAUGE,
            value: now - this.startTime,
            unit: 'ms',
            timestamp: now
        });

        // 指标数量
        metrics.push({
            name: 'app.metrics.count',
            type: MetricType.GAUGE,
            value: this.metrics.length,
            timestamp: now
        });

        return metrics;
    }

    /**
     * 处理性能条目
     */
    private processPerfEntry(entry: PerformanceEntry): void {
        const now = Date.now();

        switch (entry.entryType) {
            case 'navigation':
                const navEntry = entry as PerformanceNavigationTiming;
                this.recordMetric('page.load.time', navEntry.loadEventEnd - navEntry.fetchStart, MetricType.TIMER, {
                    unit: 'ms',
                    description: '页面加载时间'
                });
                break;

            case 'paint':
                if (entry.name === 'first-contentful-paint') {
                    this.recordMetric('page.fcp', entry.startTime, MetricType.TIMER, {
                        unit: 'ms',
                        description: '首次内容绘制时间'
                    });
                }
                break;

            case 'largest-contentful-paint':
                this.recordMetric('page.lcp', entry.startTime, MetricType.TIMER, {
                    unit: 'ms',
                    description: '最大内容绘制时间'
                });
                break;

            case 'first-input':
                const fidEntry = entry as PerformanceEventTiming;
                this.recordMetric('page.fid', fidEntry.processingStart - fidEntry.startTime, MetricType.TIMER, {
                    unit: 'ms',
                    description: '首次输入延迟'
                });
                break;

            case 'layout-shift':
                const clsEntry = entry as any;
                if (!clsEntry.hadRecentInput) {
                    this.recordMetric('page.cls', clsEntry.value, MetricType.GAUGE, {
                        description: '累积布局偏移'
                    });
                }
                break;
        }
    }

    /**
     * 处理资源条目
     */
    private processResourceEntry(entry: PerformanceResourceTiming): void {
        const resourceType = this.getResourceType(entry.name);
        const loadTime = entry.responseEnd - entry.startTime;

        this.recordMetric(`resource.${resourceType}.load.time`, loadTime, MetricType.TIMER, {
            unit: 'ms',
            labels: {
                url: entry.name,
                type: resourceType
            },
            description: `${resourceType} 资源加载时间`
        });

        this.recordCounter(`resource.${resourceType}.count`, 1, {
            type: resourceType
        });
    }

    /**
     * 获取资源类型
     */
    private getResourceType(url: string): string {
        const ext = url.split('.').pop()?.toLowerCase();

        switch (ext) {
            case 'js':
            case 'mjs':
                return 'script';
            case 'css':
                return 'stylesheet';
            case 'png':
            case 'jpg':
            case 'jpeg':
            case 'gif':
            case 'svg':
            case 'webp':
                return 'image';
            case 'woff':
            case 'woff2':
            case 'eot':
            case 'ttf':
                return 'font';
            case 'json':
                return 'xhr';
            default:
                return 'other';
        }
    }
}

/**
 * 控制台性能报告器
 */
export class ConsolePerformanceReporter implements PerformanceReporter {
    async report(metrics: PerformanceMetric[]): Promise<void> {
        if (metrics.length === 0) {
            return;
        }

        console.group(`📊 性能指标报告 (${metrics.length} 个指标)`);

        // 按类型分组
        const groupedMetrics = this.groupMetricsByType(metrics);

        Object.entries(groupedMetrics).forEach(([type, typeMetrics]) => {
            console.group(`${type.toUpperCase()} (${typeMetrics.length})`);
            typeMetrics.forEach(metric => {
                const value = metric.unit ? `${metric.value} ${metric.unit}` : metric.value;
                const labels = metric.labels ? ` [${Object.entries(metric.labels).map(([k, v]) => `${k}=${v}`).join(', ')}]` : '';
                console.log(`${metric.name}: ${value}${labels}`);
            });
            console.groupEnd();
        });

        console.groupEnd();
    }

    async reportMetric(metric: PerformanceMetric): Promise<void> {
        const value = metric.unit ? `${metric.value} ${metric.unit}` : metric.value;
        const labels = metric.labels ? ` [${Object.entries(metric.labels).map(([k, v]) => `${k}=${v}`).join(', ')}]` : '';
        console.log(`📊 ${metric.name}: ${value}${labels}`);
    }

    private groupMetricsByType(metrics: PerformanceMetric[]): Record<string, PerformanceMetric[]> {
        return metrics.reduce((groups, metric) => {
            const type = metric.type;
            if (!groups[type]) {
                groups[type] = [];
            }
            groups[type].push(metric);
            return groups;
        }, {} as Record<string, PerformanceMetric[]>);
    }
}

/**
 * HTTP性能报告器
 */
export class HttpPerformanceReporter implements PerformanceReporter {
    private endpoint: string;
    private headers: Record<string, string>;

    constructor(endpoint: string, headers: Record<string, string> = {}) {
        this.endpoint = endpoint;
        this.headers = {
            'Content-Type': 'application/json',
            ...headers
        };
    }

    async report(metrics: PerformanceMetric[]): Promise<void> {
        try {
            const response = await fetch(this.endpoint, {
                method: 'POST',
                headers: this.headers,
                body: JSON.stringify({ metrics })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('HTTP性能报告失败:', error);
            throw error;
        }
    }

    async reportMetric(metric: PerformanceMetric): Promise<void> {
        await this.report([metric]);
    }
}

/**
 * 创建默认性能监控器
 */
export function createDefaultPerformanceMonitor(options: PerformanceMonitorConfig & {
    enableConsoleReporter?: boolean;
    httpEndpoint?: string;
    httpHeaders?: Record<string, string>;
} = {}): MicroCorePerformanceMonitor {
    const monitor = new MicroCorePerformanceMonitor(options);

    // 添加控制台报告器
    if (options.enableConsoleReporter !== false) {
        monitor.addReporter(new ConsolePerformanceReporter());
    }

    // 添加HTTP报告器
    if (options.httpEndpoint) {
        monitor.addReporter(new HttpPerformanceReporter(options.httpEndpoint, options.httpHeaders));
    }

    return monitor;
}

// 导出全局性能监控器实例
export const globalPerformanceMonitor = createDefaultPerformanceMonitor();

/**
 * 便捷的性能记录函数
 */
export function recordPerformance(
    name: string,
    value: number,
    type: MetricType = MetricType.GAUGE,
    options?: {
        unit?: string;
        labels?: Record<string, string>;
        description?: string;
    }
): void {
    globalPerformanceMonitor.recordMetric(name, value, type, options);
}

/**
 * 创建性能计时器
 */
export function createPerformanceTimer(name: string, labels?: Record<string, string>): () => void {
    return globalPerformanceMonitor.createTimer(name, labels);
}

/**
 * 性能装饰器
 */
export function performanceMonitor(metricName?: string) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        const name = metricName || `${target.constructor.name}.${propertyKey}`;

        descriptor.value = function (...args: any[]) {
            const timer = createPerformanceTimer(name);

            try {
                const result = originalMethod.apply(this, args);

                if (result instanceof Promise) {
                    return result.finally(() => timer());
                } else {
                    timer();
                    return result;
                }
            } catch (error) {
                timer();
                throw error;
            }
        };

        return descriptor;
    };
}

/**
 * 异步函数性能监控
 */
export async function withPerformanceMonitoring<T>(
    name: string,
    fn: () => Promise<T>,
    labels?: Record<string, string>
): Promise<T> {
    const timer = createPerformanceTimer(name, labels);

    try {
        const result = await fn();
        timer();
        return result;
    } catch (error) {
        timer();
        throw error;
    }
}
