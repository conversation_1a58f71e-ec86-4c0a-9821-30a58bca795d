/**
 * @fileoverview 性能监控工具
 * @description 提供全面的性能监控和分析功能，支持计时、内存监控、FPS监控等
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
    /** 应用启动时间 */
    startupTime: number;
    /** 内存使用量 */
    memoryUsage: number;
    /** 应用加载时间 */
    loadTime: number;
    /** 错误计数 */
    errorCount: number;
    /** 成功操作计数 */
    successCount: number;
    /** 平均响应时间 */
    averageResponseTime: number;
}

/**
 * 性能监控配置
 */
export interface PerformanceConfig {
    /** 是否启用监控 */
    enabled?: boolean;
    /** 采样率 (0-1) */
    sampleRate?: number;
    /** 监控间隔 (ms) */
    interval?: number;
    /** 是否收集内存信息 */
    collectMemory?: boolean;
    /** 是否收集网络信息 */
    collectNetwork?: boolean;
}

/**
 * 性能监控器接口
 */
export interface PerformanceMonitor {
    start(label: string): void;
    end(label: string): number;
    measure(label: string, fn: () => any): any;
    getMetrics(): Record<string, number>;
    clear(): void;
}

/**
 * 创建性能监控器
 */
export function createPerformanceMonitor(namespace: string): PerformanceMonitor {
    const metrics: Record<string, number> = {};
    const startTimes: Record<string, number> = {};

    return {
        start(label: string): void {
            const fullLabel = `${namespace}:${label}`;
            startTimes[fullLabel] = performance.now();
        },

        end(label: string): number {
            const fullLabel = `${namespace}:${label}`;
            const startTime = startTimes[fullLabel];
            
            if (startTime === undefined) {
                console.warn(`Performance timer "${fullLabel}" was not started`);
                return 0;
            }

            const duration = performance.now() - startTime;
            metrics[fullLabel] = duration;
            delete startTimes[fullLabel];
            
            return duration;
        },

        measure<T>(label: string, fn: () => T): T {
            this.start(label);
            try {
                const result = fn();
                return result;
            } finally {
                this.end(label);
            }
        },

        getMetrics(): Record<string, number> {
            return { ...metrics };
        },

        clear(): void {
            Object.keys(metrics).forEach(key => delete metrics[key]);
            Object.keys(startTimes).forEach(key => delete startTimes[key]);
        }
    };
}

/**
 * 性能计时器
 */
export class PerformanceTimer {
    private startTime: number = 0;
    private endTime: number = 0;
    private label: string;

    constructor(label: string) {
        this.label = label;
    }

    start(): void {
        this.startTime = performance.now();
    }

    end(): number {
        this.endTime = performance.now();
        const duration = this.endTime - this.startTime;
        return duration;
    }

    getDuration(): number {
        if (this.endTime === 0) {
            return performance.now() - this.startTime;
        }
        return this.endTime - this.startTime;
    }

    getLabel(): string {
        return this.label;
    }
}

/**
 * 创建性能计时器
 */
export function createPerformanceTimer(label: string): PerformanceTimer {
    return new PerformanceTimer(label);
}

/**
 * 测量函数执行时间
 */
export function measureTime<T>(fn: () => T, label?: string): { result: T; duration: number } {
    const start = performance.now();
    const result = fn();
    const duration = performance.now() - start;
    
    if (label) {
        console.log(`${label}: ${duration.toFixed(2)}ms`);
    }
    
    return { result, duration };
}

/**
 * 测量异步函数执行时间
 */
export async function measureTimeAsync<T>(
    fn: () => Promise<T>, 
    label?: string
): Promise<{ result: T; duration: number }> {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    
    if (label) {
        console.log(`${label}: ${duration.toFixed(2)}ms`);
    }
    
    return { result, duration };
}

/**
 * 性能基准测试
 */
export function benchmark<T>(
    fn: () => T,
    iterations: number = 1000,
    label?: string
): {
    totalTime: number;
    averageTime: number;
    minTime: number;
    maxTime: number;
    iterations: number;
} {
    const times: number[] = [];
    
    for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        fn();
        const duration = performance.now() - start;
        times.push(duration);
    }
    
    const totalTime = times.reduce((sum, time) => sum + time, 0);
    const averageTime = totalTime / iterations;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    const result = {
        totalTime,
        averageTime,
        minTime,
        maxTime,
        iterations
    };
    
    if (label) {
        console.log(`Benchmark ${label}:`, result);
    }
    
    return result;
}

/**
 * 内存使用监控
 */
export function getMemoryUsage(): {
    used: number;
    total: number;
    percentage: number;
} | null {
    if (typeof window !== 'undefined' && 'memory' in performance) {
        const memory = (performance as any).memory;
        return {
            used: memory.usedJSHeapSize,
            total: memory.totalJSHeapSize,
            percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
        };
    }
    return null;
}

/**
 * FPS 监控器
 */
export class FPSMonitor {
    private frameCount: number = 0;
    private lastTime: number = 0;
    private fps: number = 0;
    private isRunning: boolean = false;
    private callback?: (fps: number) => void;

    constructor(callback?: (fps: number) => void) {
        this.callback = callback;
    }

    start(): void {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.lastTime = performance.now();
        this.frameCount = 0;
        this.tick();
    }

    stop(): void {
        this.isRunning = false;
    }

    getFPS(): number {
        return this.fps;
    }

    private tick = (): void => {
        if (!this.isRunning) return;

        this.frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - this.lastTime >= 1000) {
            this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
            this.frameCount = 0;
            this.lastTime = currentTime;
            
            if (this.callback) {
                this.callback(this.fps);
            }
        }
        
        requestAnimationFrame(this.tick);
    };
}

/**
 * 高级性能监控器
 * 提供完整的性能监控、分析和报告功能
 */
export class AdvancedPerformanceMonitor {
    private config: Required<PerformanceConfig>;
    private metrics: PerformanceMetrics;
    private startTime: number;
    private observers: PerformanceObserver[] = [];
    private timers: Map<string, number> = new Map();
    private responseTimeSamples: number[] = [];

    constructor(config: PerformanceConfig = {}) {
        this.config = {
            enabled: true,
            sampleRate: 1.0,
            interval: 5000,
            collectMemory: true,
            collectNetwork: true,
            ...config
        };

        this.startTime = performance.now();
        this.metrics = {
            startupTime: 0,
            memoryUsage: 0,
            loadTime: 0,
            errorCount: 0,
            successCount: 0,
            averageResponseTime: 0
        };

        if (this.config.enabled) {
            this.initialize();
        }
    }

    /**
     * 初始化性能监控
     */
    private initialize(): void {
        if (!this.shouldSample()) return;

        try {
            this.setupNavigationObserver();
            this.setupResourceObserver();
            this.setupMeasureObserver();
            
            // 定期收集指标
            setInterval(() => {
                this.collectMetrics();
            }, this.config.interval);
        } catch (error) {
            console.warn('Performance monitoring initialization failed:', error);
        }
    }

    /**
     * 设置导航性能观察器
     */
    private setupNavigationObserver(): void {
        if (typeof PerformanceObserver === 'undefined') return;

        const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry) => {
                if (entry.entryType === 'navigation') {
                    const navEntry = entry as PerformanceNavigationTiming;
                    this.metrics.loadTime = navEntry.loadEventEnd - navEntry.fetchStart;
                    this.metrics.startupTime = navEntry.domContentLoadedEventEnd - navEntry.fetchStart;
                }
            });
        });

        try {
            observer.observe({ entryTypes: ['navigation'] });
            this.observers.push(observer);
        } catch (error) {
            console.warn('Navigation observer setup failed:', error);
        }
    }

    /**
     * 设置资源性能观察器
     */
    private setupResourceObserver(): void {
        if (typeof PerformanceObserver === 'undefined') return;

        const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry) => {
                if (entry.entryType === 'resource') {
                    const duration = entry.duration;
                    this.updateAverageResponseTime(duration);
                }
            });
        });

        try {
            observer.observe({ entryTypes: ['resource'] });
            this.observers.push(observer);
        } catch (error) {
            console.warn('Resource observer setup failed:', error);
        }
    }

    /**
     * 设置测量性能观察器
     */
    private setupMeasureObserver(): void {
        if (typeof PerformanceObserver === 'undefined') return;

        const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry) => {
                if (entry.entryType === 'measure') {
                    this.updateAverageResponseTime(entry.duration);
                }
            });
        });

        try {
            observer.observe({ entryTypes: ['measure'] });
            this.observers.push(observer);
        } catch (error) {
            console.warn('Measure observer setup failed:', error);
        }
    }

    /**
     * 开始计时
     */
    startTimer(name: string): void {
        this.timers.set(name, performance.now());
    }

    /**
     * 结束计时
     */
    endTimer(name: string): number {
        const startTime = this.timers.get(name);
        if (!startTime) {
            console.warn(`Timer '${name}' not found`);
            return 0;
        }

        const duration = performance.now() - startTime;
        this.timers.delete(name);
        this.updateAverageResponseTime(duration);
        return duration;
    }

    /**
     * 记录成功操作
     */
    recordSuccess(): void {
        this.metrics.successCount++;
    }

    /**
     * 记录错误
     */
    recordError(): void {
        this.metrics.errorCount++;
    }

    /**
     * 收集当前指标
     */
    private collectMetrics(): void {
        if (this.config.collectMemory) {
            const memoryInfo = getMemoryUsage();
            if (memoryInfo) {
                this.metrics.memoryUsage = memoryInfo.used;
            }
        }
    }

    /**
     * 更新平均响应时间
     */
    private updateAverageResponseTime(duration: number): void {
        this.responseTimeSamples.push(duration);
        
        // 保持最近100个样本
        if (this.responseTimeSamples.length > 100) {
            this.responseTimeSamples.shift();
        }
        
        const sum = this.responseTimeSamples.reduce((a, b) => a + b, 0);
        this.metrics.averageResponseTime = sum / this.responseTimeSamples.length;
    }

    /**
     * 判断是否应该采样
     */
    private shouldSample(): boolean {
        return Math.random() < this.config.sampleRate;
    }

    /**
     * 获取当前指标
     */
    getMetrics(): PerformanceMetrics {
        return { ...this.metrics };
    }

    /**
     * 发送指标数据
     */
    sendMetrics(): void {
        if (!this.config.enabled) return;
        
        // 这里可以实现发送到监控服务的逻辑
        console.log('Performance Metrics:', this.getMetrics());
    }

    /**
     * 清理资源
     */
    destroy(): void {
        this.observers.forEach(observer => {
            try {
                observer.disconnect();
            } catch (error) {
                console.warn('Failed to disconnect observer:', error);
            }
        });
        this.observers = [];
        this.timers.clear();
        this.responseTimeSamples = [];
    }

    /**
     * 生成性能报告
     */
    generateReport(): string {
        const metrics = this.getMetrics();
        const uptime = performance.now() - this.startTime;
        
        return `
=== Performance Report ===
Uptime: ${uptime.toFixed(2)}ms
Startup Time: ${metrics.startupTime.toFixed(2)}ms
Load Time: ${metrics.loadTime.toFixed(2)}ms
Memory Usage: ${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB
Average Response Time: ${metrics.averageResponseTime.toFixed(2)}ms
Success Count: ${metrics.successCount}
Error Count: ${metrics.errorCount}
Error Rate: ${((metrics.errorCount / (metrics.successCount + metrics.errorCount)) * 100).toFixed(2)}%
========================
        `.trim();
    }
}

/**
 * 性能监控装饰器
 */
export function monitor(
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor
): PropertyDescriptor {
    const originalMethod = descriptor.value;
    const monitor = new AdvancedPerformanceMonitor();

    descriptor.value = function (...args: any[]) {
        const timerName = `${target.constructor.name}.${propertyName}`;
        monitor.startTimer(timerName);
        
        try {
            const result = originalMethod.apply(this, args);
            
            if (result instanceof Promise) {
                return result
                    .then((res) => {
                        monitor.endTimer(timerName);
                        monitor.recordSuccess();
                        return res;
                    })
                    .catch((error) => {
                        monitor.endTimer(timerName);
                        monitor.recordError();
                        throw error;
                    });
            } else {
                monitor.endTimer(timerName);
                monitor.recordSuccess();
                return result;
            }
        } catch (error) {
            monitor.endTimer(timerName);
            monitor.recordError();
            throw error;
        }
    };

    return descriptor;
}

/**
 * 全局性能监控实例
 */
export const globalPerformanceMonitor = new AdvancedPerformanceMonitor();

/**
 * 性能工具集合
 */
export const performanceUtils = {
    createMonitor: createPerformanceMonitor,
    createTimer: createPerformanceTimer,
    measureTime,
    measureTimeAsync,
    benchmark,
    getMemoryUsage,
    FPSMonitor,
    AdvancedPerformanceMonitor,
    monitor,
    globalMonitor: globalPerformanceMonitor
};
