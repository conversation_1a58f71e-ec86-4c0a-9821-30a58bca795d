/**
 * @fileoverview 缓存管理系统
 * @description 提供多种缓存策略的统一管理接口
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
    /** 最近最少使用 */
    LRU = 'lru',
    /** 先进先出 */
    FIFO = 'fifo',
    /** 最少使用频率 */
    LFU = 'lfu',
    /** 基于时间的过期 */
    TTL = 'ttl'
}

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
    /** 缓存键 */
    key: string;
    /** 缓存值 */
    value: T;
    /** 创建时间 */
    createdAt: number;
    /** 最后访问时间 */
    lastAccessedAt: number;
    /** 访问次数 */
    accessCount: number;
    /** 过期时间 */
    expiresAt?: number;
    /** 数据大小（字节） */
    size?: number;
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
    /** 缓存策略 */
    strategy?: CacheStrategy;
    /** 最大缓存项数量 */
    maxSize?: number;
    /** 最大内存使用（字节） */
    maxMemory?: number;
    /** 默认TTL（毫秒） */
    defaultTTL?: number;
    /** 是否启用统计 */
    enableStats?: boolean;
    /** 清理间隔（毫秒） */
    cleanupInterval?: number;
    /** 是否自动清理过期项 */
    autoCleanup?: boolean;
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
    /** 缓存命中次数 */
    hits: number;
    /** 缓存未命中次数 */
    misses: number;
    /** 缓存命中率 */
    hitRate: number;
    /** 当前缓存项数量 */
    size: number;
    /** 内存使用量 */
    memoryUsage: number;
    /** 过期项数量 */
    expiredCount: number;
    /** 驱逐项数量 */
    evictedCount: number;
}

/**
 * 缓存管理器接口
 */
export interface CacheManager<T = any> {
    /** 获取缓存 */
    get(key: string): T | undefined;
    /** 设置缓存 */
    set(key: string, value: T, ttl?: number): void;
    /** 删除缓存 */
    delete(key: string): boolean;
    /** 检查缓存是否存在 */
    has(key: string): boolean;
    /** 清空缓存 */
    clear(): void;
    /** 获取缓存大小 */
    size(): number;
    /** 获取所有键 */
    keys(): string[];
    /** 获取所有值 */
    values(): T[];
    /** 获取统计信息 */
    getStats(): CacheStats;
    /** 清理过期项 */
    cleanup(): void;
    /** 销毁缓存管理器 */
    destroy(): void;
}

/**
 * 基础缓存管理器
 */
export abstract class BaseCacheManager<T = any> implements CacheManager<T> {
    protected cache = new Map<string, CacheItem<T>>();
    protected config: Required<CacheConfig>;
    protected stats: CacheStats;
    private cleanupTimer: NodeJS.Timeout | null = null;

    constructor(config: CacheConfig = {}) {
        this.config = {
            strategy: CacheStrategy.LRU,
            maxSize: 1000,
            maxMemory: 50 * 1024 * 1024, // 50MB
            defaultTTL: 0, // 永不过期
            enableStats: true,
            cleanupInterval: 60000, // 1分钟
            autoCleanup: true,
            ...config
        };

        this.stats = {
            hits: 0,
            misses: 0,
            hitRate: 0,
            size: 0,
            memoryUsage: 0,
            expiredCount: 0,
            evictedCount: 0
        };

        if (this.config.autoCleanup) {
            this.startCleanupTimer();
        }
    }

    /**
     * 获取缓存
     */
    get(key: string): T | undefined {
        const item = this.cache.get(key);

        if (!item) {
            this.updateStats('miss');
            return undefined;
        }

        // 检查是否过期
        if (this.isExpired(item)) {
            this.cache.delete(key);
            this.updateStats('miss');
            this.stats.expiredCount++;
            return undefined;
        }

        // 更新访问信息
        this.updateAccessInfo(item);
        this.updateStats('hit');

        return item.value;
    }

    /**
     * 设置缓存
     */
    set(key: string, value: T, ttl?: number): void {
        const now = Date.now();
        const effectiveTTL = ttl ?? this.config.defaultTTL;

        const item: CacheItem<T> = {
            key,
            value,
            createdAt: now,
            lastAccessedAt: now,
            accessCount: 0,
            expiresAt: effectiveTTL > 0 ? now + effectiveTTL : undefined,
            size: this.calculateSize(value)
        };

        // 如果键已存在，先删除旧项
        if (this.cache.has(key)) {
            this.cache.delete(key);
        }

        // 检查是否需要驱逐
        this.evictIfNecessary();

        // 添加新项
        this.cache.set(key, item);
        this.updateCacheStats();
    }

    /**
     * 删除缓存
     */
    delete(key: string): boolean {
        const deleted = this.cache.delete(key);
        if (deleted) {
            this.updateCacheStats();
        }
        return deleted;
    }

    /**
     * 检查缓存是否存在
     */
    has(key: string): boolean {
        const item = this.cache.get(key);
        if (!item) {
            return false;
        }

        if (this.isExpired(item)) {
            this.cache.delete(key);
            this.stats.expiredCount++;
            return false;
        }

        return true;
    }

    /**
     * 清空缓存
     */
    clear(): void {
        this.cache.clear();
        this.updateCacheStats();
    }

    /**
     * 获取缓存大小
     */
    size(): number {
        return this.cache.size;
    }

    /**
     * 获取所有键
     */
    keys(): string[] {
        return Array.from(this.cache.keys());
    }

    /**
     * 获取所有值
     */
    values(): T[] {
        return Array.from(this.cache.values()).map(item => item.value);
    }

    /**
     * 获取统计信息
     */
    getStats(): CacheStats {
        return { ...this.stats };
    }

    /**
     * 清理过期项
     */
    cleanup(): void {
        const now = Date.now();
        let cleanedCount = 0;

        for (const [key, item] of this.cache.entries()) {
            if (this.isExpired(item)) {
                this.cache.delete(key);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            this.stats.expiredCount += cleanedCount;
            this.updateCacheStats();
        }
    }

    /**
     * 销毁缓存管理器
     */
    destroy(): void {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
        this.clear();
    }

    /**
     * 抽象方法：选择要驱逐的项
     */
    protected abstract selectItemToEvict(): string | null;

    /**
     * 检查项是否过期
     */
    protected isExpired(item: CacheItem<T>): boolean {
        return item.expiresAt !== undefined && Date.now() > item.expiresAt;
    }

    /**
     * 更新访问信息
     */
    protected updateAccessInfo(item: CacheItem<T>): void {
        item.lastAccessedAt = Date.now();
        item.accessCount++;
    }

    /**
     * 计算值的大小
     */
    protected calculateSize(value: T): number {
        try {
            return JSON.stringify(value).length * 2; // 粗略估算
        } catch {
            return 0;
        }
    }

    /**
     * 驱逐项（如果必要）
     */
    protected evictIfNecessary(): void {
        // 检查数量限制
        while (this.cache.size >= this.config.maxSize) {
            const keyToEvict = this.selectItemToEvict();
            if (keyToEvict) {
                this.cache.delete(keyToEvict);
                this.stats.evictedCount++;
            } else {
                break;
            }
        }

        // 检查内存限制
        while (this.getCurrentMemoryUsage() > this.config.maxMemory) {
            const keyToEvict = this.selectItemToEvict();
            if (keyToEvict) {
                this.cache.delete(keyToEvict);
                this.stats.evictedCount++;
            } else {
                break;
            }
        }
    }

    /**
     * 获取当前内存使用量
     */
    protected getCurrentMemoryUsage(): number {
        let totalSize = 0;
        for (const item of this.cache.values()) {
            totalSize += item.size || 0;
        }
        return totalSize;
    }

    /**
     * 更新统计信息
     */
    protected updateStats(type: 'hit' | 'miss'): void {
        if (!this.config.enableStats) {
            return;
        }

        if (type === 'hit') {
            this.stats.hits++;
        } else {
            this.stats.misses++;
        }

        const total = this.stats.hits + this.stats.misses;
        this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
    }

    /**
     * 更新缓存统计信息
     */
    protected updateCacheStats(): void {
        this.stats.size = this.cache.size;
        this.stats.memoryUsage = this.getCurrentMemoryUsage();
    }

    /**
     * 启动清理定时器
     */
    private startCleanupTimer(): void {
        this.cleanupTimer = setInterval(() => {
            this.cleanup();
        }, this.config.cleanupInterval);
    }
}

/**
 * LRU 缓存管理器
 */
export class LRUCacheManager<T = any> extends BaseCacheManager<T> {
    protected selectItemToEvict(): string | null {
        let oldestKey: string | null = null;
        let oldestTime = Infinity;

        for (const [key, item] of this.cache.entries()) {
            if (item.lastAccessedAt < oldestTime) {
                oldestTime = item.lastAccessedAt;
                oldestKey = key;
            }
        }

        return oldestKey;
    }
}

/**
 * FIFO 缓存管理器
 */
export class FIFOCacheManager<T = any> extends BaseCacheManager<T> {
    protected selectItemToEvict(): string | null {
        let oldestKey: string | null = null;
        let oldestTime = Infinity;

        for (const [key, item] of this.cache.entries()) {
            if (item.createdAt < oldestTime) {
                oldestTime = item.createdAt;
                oldestKey = key;
            }
        }

        return oldestKey;
    }
}

/**
 * LFU 缓存管理器
 */
export class LFUCacheManager<T = any> extends BaseCacheManager<T> {
    protected selectItemToEvict(): string | null {
        let leastUsedKey: string | null = null;
        let leastUsedCount = Infinity;

        for (const [key, item] of this.cache.entries()) {
            if (item.accessCount < leastUsedCount) {
                leastUsedCount = item.accessCount;
                leastUsedKey = key;
            }
        }

        return leastUsedKey;
    }
}

/**
 * TTL 缓存管理器
 */
export class TTLCacheManager<T = any> extends BaseCacheManager<T> {
    protected selectItemToEvict(): string | null {
        // TTL 策略优先清理过期项
        const now = Date.now();

        for (const [key, item] of this.cache.entries()) {
            if (this.isExpired(item)) {
                return key;
            }
        }

        // 如果没有过期项，选择最早过期的项
        let earliestExpiryKey: string | null = null;
        let earliestExpiryTime = Infinity;

        for (const [key, item] of this.cache.entries()) {
            const expiryTime = item.expiresAt || Infinity;
            if (expiryTime < earliestExpiryTime) {
                earliestExpiryTime = expiryTime;
                earliestExpiryKey = key;
            }
        }

        return earliestExpiryKey;
    }
}

/**
 * 缓存工厂
 */
export class CacheFactory {
    /**
     * 创建缓存管理器
     */
    static create<T = any>(strategy: CacheStrategy, config?: CacheConfig): CacheManager<T> {
        switch (strategy) {
            case CacheStrategy.LRU:
                return new LRUCacheManager<T>(config);
            case CacheStrategy.FIFO:
                return new FIFOCacheManager<T>(config);
            case CacheStrategy.LFU:
                return new LFUCacheManager<T>(config);
            case CacheStrategy.TTL:
                return new TTLCacheManager<T>(config);
            default:
                throw new Error(`不支持的缓存策略: ${strategy}`);
        }
    }

    /**
     * 创建 LRU 缓存
     */
    static createLRU<T = any>(config?: CacheConfig): LRUCacheManager<T> {
        return new LRUCacheManager<T>(config);
    }

    /**
     * 创建 FIFO 缓存
     */
    static createFIFO<T = any>(config?: CacheConfig): FIFOCacheManager<T> {
        return new FIFOCacheManager<T>(config);
    }

    /**
     * 创建 LFU 缓存
     */
    static createLFU<T = any>(config?: CacheConfig): LFUCacheManager<T> {
        return new LFUCacheManager<T>(config);
    }

    /**
     * 创建 TTL 缓存
     */
    static createTTL<T = any>(config?: CacheConfig): TTLCacheManager<T> {
        return new TTLCacheManager<T>(config);
    }
}

/**
 * 多级缓存管理器
 */
export class MultiLevelCacheManager<T = any> implements CacheManager<T> {
    private levels: CacheManager<T>[];
    private stats: CacheStats;

    constructor(levels: CacheManager<T>[]) {
        if (levels.length === 0) {
            throw new Error('至少需要一个缓存级别');
        }

        this.levels = levels;
        this.stats = {
            hits: 0,
            misses: 0,
            hitRate: 0,
            size: 0,
            memoryUsage: 0,
            expiredCount: 0,
            evictedCount: 0
        };
    }

    get(key: string): T | undefined {
        for (let i = 0; i < this.levels.length; i++) {
            const value = this.levels[i].get(key);
            if (value !== undefined) {
                // 将值提升到更高级别的缓存
                for (let j = 0; j < i; j++) {
                    this.levels[j].set(key, value);
                }
                this.stats.hits++;
                this.updateHitRate();
                return value;
            }
        }

        this.stats.misses++;
        this.updateHitRate();
        return undefined;
    }

    set(key: string, value: T, ttl?: number): void {
        // 设置到所有级别
        this.levels.forEach(level => {
            level.set(key, value, ttl);
        });
        this.updateStats();
    }

    delete(key: string): boolean {
        let deleted = false;
        this.levels.forEach(level => {
            if (level.delete(key)) {
                deleted = true;
            }
        });
        this.updateStats();
        return deleted;
    }

    has(key: string): boolean {
        return this.levels.some(level => level.has(key));
    }

    clear(): void {
        this.levels.forEach(level => level.clear());
        this.updateStats();
    }

    size(): number {
        return Math.max(...this.levels.map(level => level.size()));
    }

    keys(): string[] {
        const allKeys = new Set<string>();
        this.levels.forEach(level => {
            level.keys().forEach(key => allKeys.add(key));
        });
        return Array.from(allKeys);
    }

    values(): T[] {
        const allValues = new Map<string, T>();
        // 从最高级别开始，避免重复
        for (const level of this.levels) {
            level.keys().forEach(key => {
                if (!allValues.has(key)) {
                    const value = level.get(key);
                    if (value !== undefined) {
                        allValues.set(key, value);
                    }
                }
            });
        }
        return Array.from(allValues.values());
    }

    getStats(): CacheStats {
        return { ...this.stats };
    }

    cleanup(): void {
        this.levels.forEach(level => level.cleanup());
        this.updateStats();
    }

    destroy(): void {
        this.levels.forEach(level => level.destroy());
    }

    private updateHitRate(): void {
        const total = this.stats.hits + this.stats.misses;
        this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
    }

    private updateStats(): void {
        this.stats.size = this.size();
        // 聚合所有级别的统计信息
        let totalMemoryUsage = 0;
        let totalExpiredCount = 0;
        let totalEvictedCount = 0;

        this.levels.forEach(level => {
            const levelStats = level.getStats();
            totalMemoryUsage += levelStats.memoryUsage;
            totalExpiredCount += levelStats.expiredCount;
            totalEvictedCount += levelStats.evictedCount;
        });

        this.stats.memoryUsage = totalMemoryUsage;
        this.stats.expiredCount = totalExpiredCount;
        this.stats.evictedCount = totalEvictedCount;
    }
}

/**
 * 默认缓存实例
 */
export const defaultCache = CacheFactory.createLRU({
    maxSize: 1000,
    defaultTTL: 300000, // 5分钟
    enableStats: true
});

/**
 * 便捷的缓存函数
 */
export const cache = {
    get: <T>(key: string): T | undefined => defaultCache.get(key),
    set: <T>(key: string, value: T, ttl?: number): void => defaultCache.set(key, value, ttl),
    delete: (key: string): boolean => defaultCache.delete(key),
    has: (key: string): boolean => defaultCache.has(key),
    clear: (): void => defaultCache.clear(),
    stats: (): CacheStats => defaultCache.getStats()
};