/**
 * ID生成工具函数
 * 从 @micro-core/core 迁移而来
 */

/**
 * 生成微前端应用唯一ID
 * @description 基于时间戳和随机字符串生成唯一标识符，用于微前端应用标识
 * @param prefix ID前缀，默认为'micro-app'
 * @returns 生成的唯一ID
 * @example
 * ```typescript
 * const id = generateId('my-app'); // 'my-app_1640995200000_abc123'
 * ```
 */
export function generateId(prefix = 'micro-app'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}_${timestamp}_${random}`;
}