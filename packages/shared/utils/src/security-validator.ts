/**
 * @fileoverview 安全验证工具
 * @description 提供全面的安全验证和防护功能，包括XSS、CSRF、CSP等安全检查
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 安全配置接口
 */
export interface SecurityConfig {
    /** 是否启用 CSP 检查 */
    enableCSP?: boolean;
    /** 允许的域名列表 */
    allowedDomains?: string[];
    /** 是否启用 XSS 防护 */
    enableXSSProtection?: boolean;
    /** 是否启用 CSRF 防护 */
    enableCSRFProtection?: boolean;
    /** 最大请求大小 (字节) */
    maxRequestSize?: number;
    /** 请求频率限制 (每分钟) */
    rateLimit?: number;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
    /** 是否通过验证 */
    isValid: boolean;
    /** 错误信息 */
    errors: string[];
    /** 警告信息 */
    warnings: string[];
}

/**
 * 安全验证器
 * 提供全面的安全验证和防护功能
 */
export class SecurityValidator {
    private config: Required<SecurityConfig>;
    private requestCounts: Map<string, { count: number; timestamp: number }> = new Map();

    constructor(config: SecurityConfig = {}) {
        this.config = {
            enableCSP: true,
            allowedDomains: ['localhost', '127.0.0.1'],
            enableXSSProtection: true,
            enableCSRFProtection: true,
            maxRequestSize: 10 * 1024 * 1024, // 10MB
            rateLimit: 100, // 每分钟100次请求
            ...config
        };
    }

    /**
     * 验证 URL 安全性
     */
    validateURL(url: string): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        try {
            const urlObj = new URL(url);
            
            // 检查协议
            if (!['http:', 'https:', 'data:', 'blob:'].includes(urlObj.protocol)) {
                result.errors.push(`不安全的协议: ${urlObj.protocol}`);
                result.isValid = false;
            }

            // 检查域名
            if (urlObj.protocol === 'http:' || urlObj.protocol === 'https:') {
                const hostname = urlObj.hostname;
                const isAllowed = this.config.allowedDomains.some(domain => {
                    return hostname === domain || hostname.endsWith(`.${domain}`);
                });

                if (!isAllowed) {
                    result.errors.push(`域名不在允许列表中: ${hostname}`);
                    result.isValid = false;
                }
            }

            // 检查端口
            if (urlObj.port && !this.isPortAllowed(parseInt(urlObj.port))) {
                result.warnings.push(`使用了非标准端口: ${urlObj.port}`);
            }

            // 检查路径中的危险字符
            const dangerousPatterns = [
                /\.\./g, // 路径遍历
                /<script/gi, // 脚本注入
                /javascript:/gi, // JavaScript 协议
                /vbscript:/gi, // VBScript 协议
                /data:text\/html/gi // HTML 数据 URL
            ];

            dangerousPatterns.forEach(pattern => {
                if (pattern.test(url)) {
                    result.errors.push(`URL 包含危险模式: ${pattern.source}`);
                    result.isValid = false;
                }
            });

        } catch (error) {
            result.errors.push(`无效的 URL 格式: ${(error as Error).message}`);
            result.isValid = false;
        }

        return result;
    }

    /**
     * 验证 HTML 内容安全性
     */
    validateHTML(html: string): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        if (!this.config.enableXSSProtection) {
            return result;
        }

        // 检查脚本标签
        const scriptPattern = /<script[\s\S]*?>[\s\S]*?<\/script>/gi;
        const scriptMatches = html.match(scriptPattern);
        if (scriptMatches) {
            result.errors.push(`发现 ${scriptMatches.length} 个 script 标签`);
            result.isValid = false;
        }

        // 检查内联事件处理器
        const eventHandlers = [
            'onload', 'onclick', 'onmouseover', 'onfocus', 'onblur',
            'onchange', 'onsubmit', 'onkeydown', 'onkeyup', 'onkeypress'
        ];

        eventHandlers.forEach(handler => {
            const pattern = new RegExp(`${handler}\\s*=`, 'gi');
            if (pattern.test(html)) {
                result.errors.push(`发现内联事件处理器: ${handler}`);
                result.isValid = false;
            }
        });

        // 检查 JavaScript URL
        if (/javascript:/gi.test(html)) {
            result.errors.push('发现 JavaScript URL');
            result.isValid = false;
        }

        // 检查 iframe 标签
        const iframePattern = /<iframe[\s\S]*?>/gi;
        const iframeMatches = html.match(iframePattern);
        if (iframeMatches) {
            result.warnings.push(`发现 ${iframeMatches.length} 个 iframe 标签`);
        }

        // 检查 object 和 embed 标签
        const objectPattern = /<(object|embed)[\s\S]*?>/gi;
        const objectMatches = html.match(objectPattern);
        if (objectMatches) {
            result.warnings.push(`发现 ${objectMatches.length} 个 object/embed 标签`);
        }

        return result;
    }

    /**
     * 验证配置对象安全性
     */
    validateConfig(config: any): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        if (!config || typeof config !== 'object') {
            result.errors.push('配置必须是一个对象');
            result.isValid = false;
            return result;
        }

        // 检查函数
        this.checkForFunctions(config, result, 'root');

        // 检查敏感字段
        const sensitiveFields = [
            'password', 'token', 'secret', 'key', 'apiKey',
            'accessToken', 'refreshToken', 'privateKey'
        ];
        this.checkSensitiveFields(config, sensitiveFields, result, 'root');

        // 检查配置大小
        const configStr = JSON.stringify(config);
        if (configStr.length > this.config.maxRequestSize) {
            result.errors.push(`配置对象过大: ${configStr.length} 字节`);
            result.isValid = false;
        }

        return result;
    }

    /**
     * 验证请求频率
     */
    validateRateLimit(identifier: string): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        const now = Date.now();
        const windowMs = 60 * 1000; // 1分钟窗口

        // 清理过期记录
        this.cleanupExpiredRequests(now, windowMs);

        // 获取或创建请求记录
        let requestData = this.requestCounts.get(identifier);
        if (!requestData) {
            requestData = { count: 0, timestamp: now };
            this.requestCounts.set(identifier, requestData);
        }

        // 检查是否在同一时间窗口内
        if (now - requestData.timestamp < windowMs) {
            requestData.count++;
            if (requestData.count > this.config.rateLimit) {
                result.errors.push(`请求频率超限: ${requestData.count}/${this.config.rateLimit}`);
                result.isValid = false;
            }
        } else {
            // 重置计数器
            requestData.count = 1;
            requestData.timestamp = now;
        }

        return result;
    }

    /**
     * 验证 CSP 策略
     */
    validateCSP(): ValidationResult {
        const result: ValidationResult = {
            isValid: true,
            errors: [],
            warnings: []
        };

        if (!this.config.enableCSP) {
            return result;
        }

        // 检查页面的 CSP 设置
        const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
        const cspContent = metaCSP?.getAttribute('content');

        if (!cspContent) {
            result.warnings.push('未找到 CSP 策略');
            return result;
        }

        // 解析 CSP 策略
        const cspDirectives = this.parseCSP(cspContent);

        // 检查关键指令
        const criticalDirectives = ['default-src', 'script-src', 'style-src'];
        criticalDirectives.forEach(directive => {
            if (!cspDirectives[directive]) {
                result.warnings.push(`缺少关键 CSP 指令: ${directive}`);
            }
        });

        // 检查不安全的策略
        Object.entries(cspDirectives).forEach(([directive, sources]) => {
            if (sources.includes("'unsafe-inline'")) {
                result.warnings.push(`${directive} 允许内联代码，存在安全风险`);
            }
            if (sources.includes("'unsafe-eval'")) {
                result.warnings.push(`${directive} 允许 eval()，存在安全风险`);
            }
            if (sources.includes('*')) {
                result.warnings.push(`${directive} 允许所有来源，存在安全风险`);
            }
        });

        return result;
    }

    /**
     * 生成安全报告
     */
    generateSecurityReport(): string {
        const cspResult = this.validateCSP();
        const requestStats = Array.from(this.requestCounts.entries());
        
        return `
=== 安全验证报告 ===
CSP 状态: ${this.config.enableCSP ? '启用' : '禁用'}
XSS 防护: ${this.config.enableXSSProtection ? '启用' : '禁用'}
CSRF 防护: ${this.config.enableCSRFProtection ? '启用' : '禁用'}
允许域名: ${this.config.allowedDomains.join(', ')}
请求频率限制: ${this.config.rateLimit}/分钟
最大请求大小: ${(this.config.maxRequestSize / 1024 / 1024).toFixed(2)}MB

CSP 验证结果:
- 错误: ${cspResult.errors.length}
- 警告: ${cspResult.warnings.length}

当前请求统计:
${requestStats.map(([id, data]) => `- ${id}: ${data.count} 次`).join('\n')}
========================
        `.trim();
    }

    /**
     * 检查端口是否被允许
     */
    private isPortAllowed(port: number): boolean {
        // 标准 HTTP/HTTPS 端口
        const standardPorts = [80, 443, 8080, 8443, 3000, 4200, 5000];
        return standardPorts.includes(port);
    }

    /**
     * 递归检查对象中的函数
     */
    private checkForFunctions(obj: any, result: ValidationResult, path: string): void {
        if (typeof obj === 'function') {
            result.errors.push(`配置中包含函数: ${path}`);
            result.isValid = false;
            return;
        }

        if (typeof obj === 'object' && obj !== null) {
            Object.keys(obj).forEach(key => {
                this.checkForFunctions(obj[key], result, `${path}.${key}`);
            });
        }
    }

    /**
     * 检查敏感字段
     */
    private checkSensitiveFields(
        obj: any, 
        sensitiveFields: string[], 
        result: ValidationResult, 
        path: string
    ): void {
        if (typeof obj === 'object' && obj !== null) {
            Object.keys(obj).forEach(key => {
                const lowerKey = key.toLowerCase();
                if (sensitiveFields.some(field => lowerKey.includes(field))) {
                    result.warnings.push(`发现敏感字段: ${path}.${key}`);
                }
                this.checkSensitiveFields(obj[key], sensitiveFields, result, `${path}.${key}`);
            });
        }
    }

    /**
     * 检查字符串是否包含 JavaScript 代码
     */
    private containsJavaScript(str: string): boolean {
        const jsPatterns = [
            /javascript:/gi,
            /vbscript:/gi,
            /<script/gi,
            /eval\s*\(/gi,
            /setTimeout\s*\(/gi,
            /setInterval\s*\(/gi,
            /Function\s*\(/gi
        ];

        return jsPatterns.some(pattern => pattern.test(str));
    }

    /**
     * 解析 CSP 策略
     */
    private parseCSP(cspContent: string): Record<string, string[]> {
        const directives: Record<string, string[]> = {};
        
        cspContent.split(';').forEach(directive => {
            const trimmed = directive.trim();
            if (trimmed) {
                const [name, ...sources] = trimmed.split(/\s+/);
                if (name) {
                    directives[name] = sources;
                }
            }
        });

        return directives;
    }

    /**
     * 清理过期的请求记录
     */
    private cleanupExpiredRequests(now: number, windowMs: number): void {
        for (const [identifier, data] of this.requestCounts.entries()) {
            if (now - data.timestamp > windowMs) {
                this.requestCounts.delete(identifier);
            }
        }
    }

    /**
     * 清理所有记录
     */
    cleanup(): void {
        this.requestCounts.clear();
    }
}

/**
 * 安全验证装饰器
 */
export function secure(config?: SecurityConfig) {
    return function (
        target: any,
        propertyName: string,
        descriptor: PropertyDescriptor
    ): PropertyDescriptor {
        const originalMethod = descriptor.value;
        const validator = new SecurityValidator(config);

        descriptor.value = function (...args: any[]) {
            // 验证参数安全性
            args.forEach((arg, index) => {
                if (typeof arg === 'string') {
                    const urlResult = validator.validateURL(arg);
                    if (!urlResult.isValid) {
                        throw new Error(`参数 ${index} URL 验证失败: ${urlResult.errors.join(', ')}`);
                    }
                } else if (typeof arg === 'object' && arg !== null) {
                    const configResult = validator.validateConfig(arg);
                    if (!configResult.isValid) {
                        throw new Error(`参数 ${index} 配置验证失败: ${configResult.errors.join(', ')}`);
                    }
                }
            });

            return originalMethod.apply(this, args);
        };

        return descriptor;
    };
}

/**
 * 全局安全验证器实例
 */
export const globalSecurityValidator = new SecurityValidator();

/**
 * 便捷的验证函数
 */
export const validateURL = (url: string): ValidationResult => {
    return globalSecurityValidator.validateURL(url);
};

export const validateHTML = (html: string): ValidationResult => {
    return globalSecurityValidator.validateHTML(html);
};

export const validateConfig = (config: any): ValidationResult => {
    return globalSecurityValidator.validateConfig(config);
};

export default SecurityValidator;
