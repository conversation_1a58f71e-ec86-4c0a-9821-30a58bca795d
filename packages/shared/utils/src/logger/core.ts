/**
 * @fileoverview 从 @micro-core/core 迁移的日志工具函数
 * @description 提供与 core 包完全兼容的日志接口，同时利用 shared 包的增强功能
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */


/**
 * 核心日志记录器接口（与 core 包兼容）
 * @description 简化的日志接口，与 @micro-core/core 包的 Logger 接口完全兼容
 */
export interface CoreLogger {
    debug(message: string, ...args: unknown[]): void;
    info(message: string, ...args: unknown[]): void;
    warn(message: string, ...args: unknown[]): void;
    error(message: string, ...args: unknown[]): void;
    log(message: string, ...args: unknown[]): void;
}

/**
 * 日志记录器适配器
 * @description 将 shared 包的 Logger 适配为 core 包的简单接口
 */
class CoreLoggerAdapter implements CoreLogger {
    private namespace: string;

    constructor(namespace: string) {
        this.namespace = namespace;
    }

    private logWithLevel(level: string, message: string, ...args: unknown[]): void {
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${this.namespace}] [${level.toUpperCase()}]`;
        console.log(prefix, message, ...args);
    }

    debug(message: string, ...args: unknown[]): void {
        this.logWithLevel('DEBUG', message, ...args);
    }

    info(message: string, ...args: unknown[]): void {
        this.logWithLevel('INFO', message, ...args);
    }

    warn(message: string, ...args: unknown[]): void {
        this.logWithLevel('WARN', message, ...args);
    }

    error(message: string, ...args: unknown[]): void {
        this.logWithLevel('ERROR', message, ...args);
    }

    log(message: string, ...args: unknown[]): void {
        this.logWithLevel('INFO', message, ...args);
    }
}

/**
 * 创建日志记录器（与 core 包兼容）
 * @description 创建带有命名空间的日志记录器，与 @micro-core/core 包的实现完全兼容
 * @param namespace 日志命名空间，用于标识日志来源
 * @returns 日志记录器实例
 * @example
 * ```typescript
 * const logger = createLogger('MyModule');
 * logger.info('Application started'); // [2023-12-01T10:00:00.000Z] [MyModule] [INFO] Application started
 * logger.error('Something went wrong', { code: 500 });
 * ```
 */
export function createLogger(namespace: string): CoreLogger {
    return new CoreLoggerAdapter(namespace);
}

/**
 * 默认日志记录器实例（与 core 包兼容）
 * @description 使用'MicroCore'命名空间的默认日志记录器
 */
export const logger = createLogger('MicroCore');

/**
 * 核心日志工具集合
 * @description 提供所有从 core 包迁移的日志工具函数
 */
export const coreLoggerUtils = {
    createLogger,
    logger
} as const;

/**
 * 日志工具函数名称列表
 * @description 用于验证和测试的函数名称列表
 */
export const LOGGER_FUNCTIONS = [
    'createLogger'
] as const;

/**
 * 验证所有日志工具函数是否可用
 * @description 用于测试和验证的辅助函数
 * @returns 验证结果
 */
export function validateLoggerFunctions(): {
    available: string[];
    missing: string[];
    allAvailable: boolean;
} {
    const available: string[] = [];
    const missing: string[] = [];

    LOGGER_FUNCTIONS.forEach(funcName => {
        if (typeof (coreLoggerUtils as any)[funcName] === 'function') {
            available.push(funcName);
        } else {
            missing.push(funcName);
        }
    });

    return {
        available,
        missing,
        allAvailable: missing.length === 0
    };
}

/**
 * 创建简单的日志记录器（直接实现，用于性能敏感场景）
 * @description 提供与 core 包完全一致的简单实现
 * @param namespace 日志命名空间
 * @returns 简单的日志记录器
 */
export function createSimpleLogger(namespace: string): CoreLogger {
    const log = (level: string, message: string, ...args: unknown[]): void => {
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${namespace}] [${level.toUpperCase()}]`;
        console.log(prefix, message, ...args);
    };

    return {
        debug: (message: string, ...args: unknown[]) => log('DEBUG', message, ...args),
        info: (message: string, ...args: unknown[]) => log('INFO', message, ...args),
        warn: (message: string, ...args: unknown[]) => log('WARN', message, ...args),
        error: (message: string, ...args: unknown[]) => log('ERROR', message, ...args),
        log: (message: string, ...args: unknown[]) => log('INFO', message, ...args)
    };
}