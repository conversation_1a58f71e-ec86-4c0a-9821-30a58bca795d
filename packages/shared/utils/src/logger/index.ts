/**
 * 日志工具
 * 从 @micro-core/core 迁移而来
 */

/**
 * 日志记录器接口
 */
export interface Logger {
    debug(message: string, ...args: unknown[]): void;
    info(message: string, ...args: unknown[]): void;
    warn(message: string, ...args: unknown[]): void;
    error(message: string, ...args: unknown[]): void;
    log(message: string, ...args: unknown[]): void;
}

/**
 * 日志配置接口
 */
export interface LoggerConfig {
    namespace: string;
    level?: 'debug' | 'info' | 'warn' | 'error';
    enableTimestamp?: boolean;
    enableColors?: boolean;
}

/**
 * 创建日志记录器
 * @description 创建带有命名空间的日志记录器，支持不同级别的日志输出
 * @param namespace 日志命名空间，用于标识日志来源
 * @returns 日志记录器实例
 * @example
 * ```typescript
 * const logger = createLogger('MyModule');
 * logger.info('Application started'); // [2023-12-01T10:00:00.000Z] [MyModule] [INFO] Application started
 * logger.error('Something went wrong', { code: 500 });
 * ```
 */
export function createLogger(namespace: string): Logger {
    const log = (level: string, message: string, ...args: unknown[]): void => {
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${namespace}] [${level.toUpperCase()}]`;
        console.log(prefix, message, ...args);
    };

    return {
        debug: (message: string, ...args: unknown[]) => log('DEBUG', message, ...args),
        info: (message: string, ...args: unknown[]) => log('INFO', message, ...args),
        warn: (message: string, ...args: unknown[]) => log('WARN', message, ...args),
        error: (message: string, ...args: unknown[]) => log('ERROR', message, ...args),
        log: (message: string, ...args: unknown[]) => log('INFO', message, ...args)
    };
}