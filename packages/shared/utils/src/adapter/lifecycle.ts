/**
 * @fileoverview 适配器生命周期接口定义
 * @description 定义统一的适配器生命周期接口和管理器
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type { MicroAppInstance } from '@micro-core/shared/types';
import { formatError } from '../format';
import { createLogger, Logger } from '../logger';

/**
 * 生命周期阶段枚举
 */
export enum LifecyclePhase {
    BEFORE_LOAD = 'beforeLoad',
    AFTER_LOAD = 'afterLoad',
    BEFORE_BOOTSTRAP = 'beforeBootstrap',
    AFTER_BOOTSTRAP = 'afterBootstrap',
    BEFORE_MOUNT = 'beforeMount',
    AFTER_MOUNT = 'afterMount',
    BEFORE_UNMOUNT = 'beforeUnmount',
    AFTER_UNMOUNT = 'afterUnmount',
    BEFORE_UPDATE = 'beforeUpdate',
    AFTER_UPDATE = 'afterUpdate',
    ON_ERROR = 'onError'
}

/**
 * 生命周期钩子函数类型
 */
export type LifecycleHook<T = any> = (app: MicroAppInstance, data?: T) => Promise<void> | void;

/**
 * 错误处理钩子函数类型
 */
export type ErrorHook = (error: Error, app: MicroAppInstance, phase?: LifecyclePhase) => Promise<void> | void;

/**
 * 生命周期钩子集合接口
 */
export interface LifecycleHooks {
    /** 加载前钩子 */
    beforeLoad?: LifecycleHook;
    /** 加载后钩子 */
    afterLoad?: LifecycleHook;
    /** 启动前钩子 */
    beforeBootstrap?: LifecycleHook;
    /** 启动后钩子 */
    afterBootstrap?: LifecycleHook;
    /** 挂载前钩子 */
    beforeMount?: LifecycleHook;
    /** 挂载后钩子 */
    afterMount?: LifecycleHook;
    /** 卸载前钩子 */
    beforeUnmount?: LifecycleHook;
    /** 卸载后钩子 */
    afterUnmount?: LifecycleHook;
    /** 更新前钩子 */
    beforeUpdate?: LifecycleHook;
    /** 更新后钩子 */
    afterUpdate?: LifecycleHook;
    /** 错误处理钩子 */
    onError?: ErrorHook;
}

/**
 * 生命周期管理器配置
 */
export interface LifecycleManagerConfig {
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 钩子执行超时时间（毫秒） */
    timeout?: number;
    /** 是否在钩子失败时继续执行 */
    continueOnError?: boolean;
    /** 全局错误处理器 */
    globalErrorHandler?: ErrorHook;
}

/**
 * 生命周期执行结果
 */
export interface LifecycleExecutionResult {
    /** 是否成功 */
    success: boolean;
    /** 执行时间（毫秒） */
    duration: number;
    /** 错误信息（如果有） */
    error?: Error;
    /** 阶段名称 */
    phase: LifecyclePhase;
    /** 应用名称 */
    appName: string;
}

/**
 * 生命周期管理器
 * @description 管理适配器的生命周期钩子执行
 */
export class LifecycleManager {
    private readonly config: Required<LifecycleManagerConfig>;
    private readonly logger: Logger;
    private readonly globalHooks = new Map<LifecyclePhase, LifecycleHook[]>();
    private readonly appHooks = new Map<string, LifecycleHooks>();
    private readonly executionHistory = new Map<string, LifecycleExecutionResult[]>();

    constructor(config: LifecycleManagerConfig = {}) {
        this.config = {
            debug: false,
            timeout: 5000,
            continueOnError: true,
            globalErrorHandler: undefined,
            ...config
        };

        this.logger = createLogger('LifecycleManager');

        if (this.config.debug) {
            this.logger.debug('生命周期管理器初始化', { config: this.config });
        }
    }

    /**
     * 注册应用的生命周期钩子
     * @param appName 应用名称
     * @param hooks 生命周期钩子集合
     */
    registerAppHooks(appName: string, hooks: LifecycleHooks): void {
        this.appHooks.set(appName, hooks);
        this.logger.debug('注册应用生命周期钩子', { appName, hooks: Object.keys(hooks) });
    }

    /**
     * 注册全局生命周期钩子
     * @param phase 生命周期阶段
     * @param hook 钩子函数
     */
    registerGlobalHook(phase: LifecyclePhase, hook: LifecycleHook): void {
        if (!this.globalHooks.has(phase)) {
            this.globalHooks.set(phase, []);
        }
        this.globalHooks.get(phase)!.push(hook);
        this.logger.debug('注册全局生命周期钩子', { phase });
    }

    /**
     * 移除应用的生命周期钩子
     * @param appName 应用名称
     */
    unregisterAppHooks(appName: string): void {
        this.appHooks.delete(appName);
        this.executionHistory.delete(appName);
        this.logger.debug('移除应用生命周期钩子', { appName });
    }

    /**
     * 移除全局生命周期钩子
     * @param phase 生命周期阶段
     * @param hook 钩子函数（可选，不提供则移除该阶段的所有钩子）
     */
    unregisterGlobalHook(phase: LifecyclePhase, hook?: LifecycleHook): void {
        const hooks = this.globalHooks.get(phase);
        if (!hooks) return;

        if (hook) {
            const index = hooks.indexOf(hook);
            if (index > -1) {
                hooks.splice(index, 1);
            }
        } else {
            hooks.length = 0;
        }

        this.logger.debug('移除全局生命周期钩子', { phase });
    }

    /**
     * 执行生命周期钩子
     * @param phase 生命周期阶段
     * @param app 应用实例
     * @param data 传递给钩子的数据
     * @returns 执行结果
     */
    async executeHooks<T = any>(
        phase: LifecyclePhase,
        app: MicroAppInstance,
        data?: T
    ): Promise<LifecycleExecutionResult[]> {
        const startTime = Date.now();
        const results: LifecycleExecutionResult[] = [];

        this.logger.debug('开始执行生命周期钩子', { phase, appName: app.name });

        try {
            // 执行全局钩子
            const globalHooks = this.globalHooks.get(phase) || [];
            for (const hook of globalHooks) {
                const result = await this.executeHook(hook, phase, app, data);
                results.push(result);

                if (!result.success && !this.config.continueOnError) {
                    break;
                }
            }

            // 执行应用特定钩子
            const appHooks = this.appHooks.get(app.name);
            if (appHooks) {
                const hook = appHooks[phase as keyof LifecycleHooks];
                if (hook && phase !== LifecyclePhase.ON_ERROR) {
                    const result = await this.executeHook(hook as LifecycleHook, phase, app, data);
                    results.push(result);
                }
            }

            // 记录执行历史
            this.recordExecutionHistory(app.name, results);

            const totalDuration = Date.now() - startTime;
            this.logger.debug('生命周期钩子执行完成', {
                phase,
                appName: app.name,
                duration: totalDuration,
                results: results.length
            });

        } catch (error) {
            this.logger.error('生命周期钩子执行失败', {
                phase,
                appName: app.name,
                error: formatError(error)
            });

            // 触发错误处理钩子
            await this.handleError(error instanceof Error ? error : new Error(String(error)), app, phase);
        }

        return results;
    }

    /**
     * 执行单个钩子
     * @param hook 钩子函数
     * @param phase 生命周期阶段
     * @param app 应用实例
     * @param data 传递的数据
     * @returns 执行结果
     */
    private async executeHook<T = any>(
        hook: LifecycleHook<T>,
        phase: LifecyclePhase,
        app: MicroAppInstance,
        data?: T
    ): Promise<LifecycleExecutionResult> {
        const startTime = Date.now();

        try {
            // 使用超时控制
            await Promise.race([
                Promise.resolve(hook(app, data)),
                new Promise((_, reject) => {
                    setTimeout(() => {
                        reject(new Error(`生命周期钩子执行超时: ${phase}`));
                    }, this.config.timeout);
                })
            ]);

            const duration = Date.now() - startTime;
            return {
                success: true,
                duration,
                phase,
                appName: app.name
            };

        } catch (error) {
            const duration = Date.now() - startTime;
            const hookError = error instanceof Error ? error : new Error(String(error));

            this.logger.warn('生命周期钩子执行失败', {
                phase,
                appName: app.name,
                error: formatError(hookError),
                duration
            });

            return {
                success: false,
                duration,
                error: hookError,
                phase,
                appName: app.name
            };
        }
    }

    /**
     * 处理生命周期错误
     * @param error 错误对象
     * @param app 应用实例
     * @param phase 出错的生命周期阶段
     */
    private async handleError(error: Error, app: MicroAppInstance, phase?: LifecyclePhase): Promise<void> {
        try {
            // 执行全局错误处理器
            if (this.config.globalErrorHandler) {
                await this.config.globalErrorHandler(error, app, phase);
            }

            // 执行应用特定的错误处理钩子
            const appHooks = this.appHooks.get(app.name);
            if (appHooks?.onError) {
                await appHooks.onError(error, app, phase);
            }

        } catch (handlerError) {
            this.logger.error('错误处理器执行失败', {
                appName: app.name,
                originalError: formatError(error),
                handlerError: formatError(handlerError)
            });
        }
    }

    /**
     * 记录执行历史
     * @param appName 应用名称
     * @param results 执行结果
     */
    private recordExecutionHistory(appName: string, results: LifecycleExecutionResult[]): void {
        if (!this.executionHistory.has(appName)) {
            this.executionHistory.set(appName, []);
        }

        const history = this.executionHistory.get(appName)!;
        history.push(...results);

        // 限制历史记录数量，避免内存泄漏
        const maxHistorySize = 100;
        if (history.length > maxHistorySize) {
            history.splice(0, history.length - maxHistorySize);
        }
    }

    /**
     * 获取应用的执行历史
     * @param appName 应用名称
     * @returns 执行历史
     */
    getExecutionHistory(appName: string): LifecycleExecutionResult[] {
        return this.executionHistory.get(appName) || [];
    }

    /**
     * 获取所有应用的执行统计
     * @returns 执行统计信息
     */
    getExecutionStats(): Record<string, {
        totalExecutions: number;
        successfulExecutions: number;
        failedExecutions: number;
        averageDuration: number;
        phases: Record<LifecyclePhase, number>;
    }> {
        const stats: Record<string, any> = {};

        for (const [appName, history] of this.executionHistory.entries()) {
            const successful = history.filter(r => r.success).length;
            const failed = history.length - successful;
            const totalDuration = history.reduce((sum, r) => sum + r.duration, 0);
            const phases: Record<LifecyclePhase, number> = {} as any;

            // 统计各阶段执行次数
            for (const phase of Object.values(LifecyclePhase)) {
                phases[phase] = history.filter(r => r.phase === phase).length;
            }

            stats[appName] = {
                totalExecutions: history.length,
                successfulExecutions: successful,
                failedExecutions: failed,
                averageDuration: history.length > 0 ? totalDuration / history.length : 0,
                phases
            };
        }

        return stats;
    }

    /**
     * 清理资源
     */
    destroy(): void {
        this.globalHooks.clear();
        this.appHooks.clear();
        this.executionHistory.clear();
        this.logger.debug('生命周期管理器已销毁');
    }
}

/**
 * 创建生命周期管理器实例
 * @param config 配置选项
 * @returns 生命周期管理器实例
 */
export function createLifecycleManager(config?: LifecycleManagerConfig): LifecycleManager {
    return new LifecycleManager(config);
}

/**
 * 默认生命周期管理器实例
 */
export const defaultLifecycleManager = createLifecycleManager();

/**
 * 生命周期装饰器工厂
 * @description 创建生命周期装饰器，用于自动注册钩子
 */
export function createLifecycleDecorator(manager: LifecycleManager) {
    return function lifecycleHook(phase: LifecyclePhase) {
        return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
            const originalMethod = descriptor.value;

            descriptor.value = function (...args: any[]) {
                // 这里可以添加装饰器逻辑
                return originalMethod.apply(this, args);
            };

            return descriptor;
        };
    };
}

/**
 * 生命周期钩子组合器
 * @description 将多个钩子组合成一个钩子
 */
export function combineHooks(...hooks: LifecycleHook[]): LifecycleHook {
    return async (app: MicroAppInstance, data?: any) => {
        for (const hook of hooks) {
            await hook(app, data);
        }
    };
}

/**
 * 条件生命周期钩子
 * @description 根据条件决定是否执行钩子
 */
export function conditionalHook(
    condition: (app: MicroAppInstance) => boolean,
    hook: LifecycleHook
): LifecycleHook {
    return async (app: MicroAppInstance, data?: any) => {
        if (condition(app)) {
            await hook(app, data);
        }
    };
}

/**
 * 重试生命周期钩子
 * @description 为钩子添加重试机制
 */
export function retryHook(
    hook: LifecycleHook,
    maxRetries: number = 3,
    delay: number = 1000
): LifecycleHook {
    return async (app: MicroAppInstance, data?: any) => {
        let lastError: Error;

        for (let i = 0; i <= maxRetries; i++) {
            try {
                await hook(app, data);
                return;
            } catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));

                if (i < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        throw lastError!;
    };
}

/**
 * 缓存生命周期钩子结果
 * @description 缓存钩子执行结果，避免重复执行
 */
export function cacheHook(hook: LifecycleHook, ttl: number = 60000): LifecycleHook {
    const cache = new Map<string, { result: any; timestamp: number }>();

    return async (app: MicroAppInstance, data?: any) => {
        const cacheKey = `${app.name}:${JSON.stringify(data)}`;
        const cached = cache.get(cacheKey);

        if (cached && Date.now() - cached.timestamp < ttl) {
            return cached.result;
        }

        const result = await hook(app, data);
        cache.set(cacheKey, { result, timestamp: Date.now() });

        return result;
    };
}