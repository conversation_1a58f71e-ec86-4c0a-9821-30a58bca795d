/**
 * 适配器通用工具函数
 * 从各个适配器中提取的通用功能
 */

import { mergeConfigs } from '../config';
import { cleanupContainer, createContainer } from '../dom/index';
import { formatError } from '../format';

// 导出基础设施
export * from './base';
export * from './common';
export * from './factory';
export * from './lifecycle';
/**
 * 适配器通用工具函数
 * 从各个适配器中提取的通用功能
 */


/**
 * 基础适配器配置接口
 */
export interface BaseAdapterConfig {
    name: string;
    framework: string;
    entry?: string;
    container?: string | HTMLElement;
    props?: Record<string, any>;
    sandbox?: {
        strictStyleIsolation?: boolean;
        experimentalStyleIsolation?: boolean;
    };
    lifecycle?: {
        beforeLoad?: () => Promise<void> | void;
        beforeMount?: () => Promise<void> | void;
        afterMount?: () => Promise<void> | void;
        beforeUnmount?: () => Promise<void> | void;
        afterUnmount?: () => Promise<void> | void;
    };
}

/**
 * 格式化适配器错误
 * @description 统一格式化适配器相关的错误信息
 * @param error 错误对象
 * @param adapterName 适配器名称
 * @param operation 操作名称
 * @returns 格式化后的错误信息
 * @example
 * ```typescript
 * const errorMsg = formatAdapterError(new Error('Mount failed'), 'React', 'mount');
 * // '[React Adapter] Mount failed: Error details...'
 * ```
 */
export function formatAdapterError(error: unknown, adapterName: string, operation?: string): string {
    const prefix = `[${adapterName} Adapter]`;
    const operationText = operation ? ` ${operation}` : '';
    const errorText = formatError(error);

    return `${prefix}${operationText} 失败: ${errorText}`;
}

/**
 * 合并适配器配置
 * @description 深度合并适配器配置对象
 * @param base 基础配置
 * @param override 覆盖配置
 * @returns 合并后的配置
 * @example
 * ```typescript
 * const baseConfig = { name: 'app', props: { theme: 'light' } };
 * const overrideConfig = { props: { version: '1.0' } };
 * const merged = mergeAdapterConfigs(baseConfig, overrideConfig);
 * // { name: 'app', props: { theme: 'light', version: '1.0' } }
 * ```
 */
export function mergeAdapterConfigs<T extends BaseAdapterConfig>(
    base: T,
    override: Partial<T>
): T {
    return mergeConfigs(base, override);
}

/**
 * 创建适配器容器
 * @description 为适配器创建DOM容器
 * @param appName 应用名称
 * @param parentElement 父元素
 * @returns 创建的容器元素
 * @example
 * ```typescript
 * const container = createAdapterContainer('my-react-app');
 * ```
 */
export function createAdapterContainer(appName: string, parentElement?: HTMLElement): HTMLElement {
    return createContainer(appName, parentElement);
}

/**
 * 清理适配器容器
 * @description 清理适配器的DOM容器
 * @param container 容器元素
 * @example
 * ```typescript
 * cleanupAdapterContainer(container);
 * ```
 */
export function cleanupAdapterContainer(container: HTMLElement): void {
    cleanupContainer(container);
}

/**
 * 验证适配器配置
 * @description 验证适配器配置的有效性
 * @param config 适配器配置
 * @returns 验证结果
 * @example
 * ```typescript
 * const config = { name: 'my-app', framework: 'react' };
 * const result = validateAdapterConfig(config);
 * if (!result.valid) {
 *   console.error('配置无效:', result.errors);
 * }
 * ```
 */
export function validateAdapterConfig(config: BaseAdapterConfig): {
    valid: boolean;
    errors: string[];
} {
    const errors: string[] = [];

    // 检查必填字段
    if (!config.name || typeof config.name !== 'string') {
        errors.push('应用名称(name)是必填的，且必须是字符串');
    }

    if (!config.framework || typeof config.framework !== 'string') {
        errors.push('框架类型(framework)是必填的，且必须是字符串');
    }

    // 检查应用名称格式
    if (config.name && !/^[a-zA-Z][a-zA-Z0-9-_]*$/.test(config.name)) {
        errors.push('应用名称只能包含字母、数字、连字符和下划线，且必须以字母开头');
    }

    // 检查入口地址格式
    if (config.entry && typeof config.entry === 'string') {
        try {
            new URL(config.entry);
        } catch {
            errors.push('入口地址(entry)必须是有效的URL');
        }
    }

    // 检查容器配置
    if (config.container) {
        if (typeof config.container === 'string') {
            if (!document.querySelector(config.container)) {
                errors.push(`指定的容器选择器 "${config.container}" 未找到对应元素`);
            }
        } else if (!(config.container instanceof HTMLElement)) {
            errors.push('容器(container)必须是CSS选择器字符串或HTMLElement对象');
        }
    }

    return {
        valid: errors.length === 0,
        errors
    };
}

/**
 * 获取适配器运行时信息
 * @description 获取适配器的运行时状态信息
 * @param adapterName 适配器名称
 * @returns 运行时信息
 * @example
 * ```typescript
 * const info = getAdapterRuntimeInfo('React');
 * console.log('适配器版本:', info.version);
 * ```
 */
export function getAdapterRuntimeInfo(adapterName: string): {
    name: string;
    version: string;
    framework: string;
    timestamp: number;
    userAgent: string;
} {
    return {
        name: adapterName,
        version: '1.0.0', // 可以从package.json中读取
        framework: adapterName.toLowerCase(),
        timestamp: Date.now(),
        userAgent: navigator.userAgent
    };
}