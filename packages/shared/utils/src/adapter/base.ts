/**
 * @fileoverview 适配器基础设施 - 抽象基类和工厂接口
 * @description 提供所有适配器的通用基础类和工厂模式实现
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import type {
    MicroAppConfig,
    MicroAppInstance
} from '@micro-core/shared/types';
import { formatError } from '../format';
import { createLogger, Logger } from '../logger';

/**
 * 适配器状态枚举
 */
export enum AdapterStatus {
    IDLE = 'idle',
    LOADING = 'loading',
    LOADED = 'loaded',
    MOUNTING = 'mounting',
    MOUNTED = 'mounted',
    UNMOUNTING = 'unmounting',
    UNMOUNTED = 'unmounted',
    ERROR = 'error'
}

/**
 * 适配器配置接口
 */
export interface BaseAdapterConfig {
    /** 适配器名称 */
    name: string;
    /** 支持的框架类型 */
    framework: string;
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 超时配置 */
    timeout?: {
        load?: number;
        mount?: number;
        unmount?: number;
    };
    /** 重试配置 */
    retry?: {
        times?: number;
        delay?: number;
    };
    /** 沙箱配置 */
    sandbox?: {
        enabled?: boolean;
        strict?: boolean;
    };
}

/**
 * 适配器事件类型
 */
export interface AdapterEvents {
    'status-change': { from: AdapterStatus; to: AdapterStatus };
    'app-load': { app: MicroAppInstance };
    'app-mount': { app: MicroAppInstance };
    'app-unmount': { app: MicroAppInstance };
    'error': { error: Error; context?: any };
}

/**
 * 适配器接口
 */
export interface IAdapter {
    /** 适配器名称 */
    readonly name: string;
    /** 适配器状态 */
    readonly status: AdapterStatus;
    /** 支持的框架 */
    readonly framework: string;
    /** 当前管理的应用实例 */
    readonly apps: Map<string, MicroAppInstance>;

    /** 加载应用 */
    loadApp(config: MicroAppConfig): Promise<MicroAppInstance>;
    /** 挂载应用 */
    mountApp(app: MicroAppInstance): Promise<void>;
    /** 卸载应用 */
    unmountApp(app: MicroAppInstance): Promise<void>;
    /** 更新应用 */
    updateApp(app: MicroAppInstance, props?: Record<string, any>): Promise<void>;
    /** 销毁适配器 */
    destroy(): Promise<void>;

    /** 事件监听 */
    on<K extends keyof AdapterEvents>(event: K, listener: (data: AdapterEvents[K]) => void): void;
    /** 取消事件监听 */
    off<K extends keyof AdapterEvents>(event: K, listener?: (data: AdapterEvents[K]) => void): void;
    /** 触发事件 */
    emit<K extends keyof AdapterEvents>(event: K, data: AdapterEvents[K]): void;
}

/**
 * 适配器抽象基类
 */
export abstract class BaseAdapter implements IAdapter {
    protected readonly config: BaseAdapterConfig;
    protected readonly logger: Logger;
    protected _status: AdapterStatus = AdapterStatus.IDLE;
    protected readonly _apps = new Map<string, MicroAppInstance>();
    protected readonly _eventListeners = new Map<string, Function[]>();

    constructor(config: BaseAdapterConfig) {
        this.config = { ...config };
        this.logger = createLogger(`Adapter:${config.name}`);
        this.logger.debug('适配器初始化', { config });
    }

    // 只读属性
    get name(): string {
        return this.config.name;
    }

    get status(): AdapterStatus {
        return this._status;
    }

    get framework(): string {
        return this.config.framework;
    }

    get apps(): Map<string, MicroAppInstance> {
        return new Map(this._apps);
    }

    // 抽象方法 - 子类必须实现
    protected abstract doLoadApp(config: MicroAppConfig): Promise<MicroAppInstance>;
    protected abstract doMountApp(app: MicroAppInstance): Promise<void>;
    protected abstract doUnmountApp(app: MicroAppInstance): Promise<void>;
    protected abstract doUpdateApp(app: MicroAppInstance, props?: Record<string, any>): Promise<void>;

    // 公共接口实现
    async loadApp(config: MicroAppConfig): Promise<MicroAppInstance> {
        this.logger.info('开始加载应用', { appName: config.name });

        try {
            this.setStatus(AdapterStatus.LOADING);

            // 验证配置
            this.validateAppConfig(config);

            // 检查应用是否已存在
            if (this._apps.has(config.name)) {
                throw new Error(`应用 ${config.name} 已存在`);
            }

            // 执行加载逻辑
            const app = await this.executeWithTimeout(
                () => this.doLoadApp(config),
                this.config.timeout?.load || 30000,
                `加载应用 ${config.name} 超时`
            );

            // 注册应用
            this._apps.set(config.name, app);
            this.setStatus(AdapterStatus.LOADED);
            this.emit('app-load', { app });

            this.logger.info('应用加载成功', { appName: config.name });
            return app;

        } catch (error) {
            this.setStatus(AdapterStatus.ERROR);
            const formattedError = new Error(`加载应用失败: ${formatError(error)}`);
            this.emit('error', { error: formattedError, context: { config } });
            throw formattedError;
        }
    }

    async mountApp(app: MicroAppInstance): Promise<void> {
        this.logger.info('开始挂载应用', { appName: app.name });

        try {
            this.setStatus(AdapterStatus.MOUNTING);

            // 执行生命周期钩子
            await this.executeLifecycleHook(app.config.hooks?.beforeMount, app);

            // 执行挂载逻辑
            await this.executeWithTimeout(
                () => this.doMountApp(app),
                this.config.timeout?.mount || 10000,
                `挂载应用 ${app.name} 超时`
            );

            // 更新应用状态
            app.status = 'mounted' as any;
            app.updatedAt = new Date();

            this.setStatus(AdapterStatus.MOUNTED);
            this.emit('app-mount', { app });

            // 执行生命周期钩子
            await this.executeLifecycleHook(app.config.hooks?.afterMount, app);

            this.logger.info('应用挂载成功', { appName: app.name });

        } catch (error) {
            this.setStatus(AdapterStatus.ERROR);
            app.status = 'load_error' as any;
            app.error = error instanceof Error ? error : new Error(String(error));

            const formattedError = new Error(`挂载应用失败: ${formatError(error)}`);
            this.emit('error', { error: formattedError, context: { app } });
            throw formattedError;
        }
    }

    async unmountApp(app: MicroAppInstance): Promise<void> {
        this.logger.info('开始卸载应用', { appName: app.name });

        try {
            this.setStatus(AdapterStatus.UNMOUNTING);

            // 执行生命周期钩子
            await this.executeLifecycleHook(app.config.hooks?.beforeUnmount, app);

            // 执行卸载逻辑
            await this.executeWithTimeout(
                () => this.doUnmountApp(app),
                this.config.timeout?.unmount || 5000,
                `卸载应用 ${app.name} 超时`
            );

            // 更新应用状态
            app.status = 'not_loaded' as any;
            app.updatedAt = new Date();

            this.setStatus(AdapterStatus.UNMOUNTED);
            this.emit('app-unmount', { app });

            // 执行生命周期钩子
            await this.executeLifecycleHook(app.config.hooks?.afterUnmount, app);

            this.logger.info('应用卸载成功', { appName: app.name });

        } catch (error) {
            this.setStatus(AdapterStatus.ERROR);
            const formattedError = new Error(`卸载应用失败: ${formatError(error)}`);
            this.emit('error', { error: formattedError, context: { app } });
            throw formattedError;
        }
    }

    async updateApp(app: MicroAppInstance, props?: Record<string, any>): Promise<void> {
        this.logger.info('开始更新应用', { appName: app.name, props });

        try {
            // 执行生命周期钩子
            await this.executeLifecycleHook(app.config.hooks?.beforeUpdate, app);

            // 更新应用属性
            if (props) {
                app.props = { ...app.props, ...props };
            }

            // 执行更新逻辑
            await this.doUpdateApp(app, props);

            // 更新时间戳
            app.updatedAt = new Date();

            // 执行生命周期钩子
            await this.executeLifecycleHook(app.config.hooks?.afterUpdate, app);

            this.logger.info('应用更新成功', { appName: app.name });

        } catch (error) {
            const formattedError = new Error(`更新应用失败: ${formatError(error)}`);
            this.emit('error', { error: formattedError, context: { app, props } });
            throw formattedError;
        }
    }

    async destroy(): Promise<void> {
        this.logger.info('开始销毁适配器');

        try {
            // 卸载所有应用
            const unmountPromises = Array.from(this._apps.values()).map(app =>
                this.unmountApp(app).catch(error => {
                    this.logger.error('卸载应用失败', { appName: app.name, error });
                })
            );

            await Promise.all(unmountPromises);

            // 清理资源
            this._apps.clear();
            this._eventListeners.clear();
            this.setStatus(AdapterStatus.IDLE);

            this.logger.info('适配器销毁完成');

        } catch (error) {
            this.logger.error('销毁适配器失败', { error });
            throw error;
        }
    }

    // 事件系统
    on<K extends keyof AdapterEvents>(event: K, listener: (data: AdapterEvents[K]) => void): void {
        if (!this._eventListeners.has(event)) {
            this._eventListeners.set(event, []);
        }
        this._eventListeners.get(event)!.push(listener);
    }

    off<K extends keyof AdapterEvents>(event: K, listener?: (data: AdapterEvents[K]) => void): void {
        const listeners = this._eventListeners.get(event);
        if (!listeners) return;

        if (listener) {
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        } else {
            listeners.length = 0;
        }
    }

    emit<K extends keyof AdapterEvents>(event: K, data: AdapterEvents[K]): void {
        const listeners = this._eventListeners.get(event);
        if (listeners) {
            listeners.forEach(listener => {
                try {
                    listener(data);
                } catch (error) {
                    this.logger.error('事件监听器执行失败', { event, error });
                }
            });
        }
    }

    // 受保护的工具方法
    protected setStatus(status: AdapterStatus): void {
        const oldStatus = this._status;
        this._status = status;
        this.emit('status-change', { from: oldStatus, to: status });
        this.logger.debug('适配器状态变更', { from: oldStatus, to: status });
    }

    protected validateAppConfig(config: MicroAppConfig): void {
        if (!config.name) {
            throw new Error('应用名称不能为空');
        }

        if (!config.entry) {
            throw new Error('应用入口不能为空');
        }

        // 子类可以重写此方法添加特定验证
    }

    protected async executeLifecycleHook(
        hook: ((app: MicroAppInstance) => Promise<void> | void) | undefined,
        app: MicroAppInstance
    ): Promise<void> {
        if (hook) {
            try {
                await hook(app);
            } catch (error) {
                this.logger.warn('生命周期钩子执行失败', { appName: app.name, error });
                // 生命周期钩子失败不应该阻止主流程
            }
        }
    }

    protected async executeWithTimeout<T>(
        fn: () => Promise<T>,
        timeout: number,
        timeoutMessage: string
    ): Promise<T> {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                reject(new Error(timeoutMessage));
            }, timeout);

            fn()
                .then(result => {
                    clearTimeout(timer);
                    resolve(result);
                })
                .catch(error => {
                    clearTimeout(timer);
                    reject(error);
                });
        });
    }

    protected async executeWithRetry<T>(
        fn: () => Promise<T>,
        maxRetries: number = this.config.retry?.times || 3,
        delay: number = this.config.retry?.delay || 1000
    ): Promise<T> {
        let lastError: Error;

        for (let i = 0; i <= maxRetries; i++) {
            try {
                return await fn();
            } catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));

                if (i < maxRetries) {
                    this.logger.warn(`操作失败，${delay}ms 后重试 (${i + 1}/${maxRetries})`, { error });
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        throw lastError!;
    }
}

/**
 * 适配器工厂接口
 */
export interface AdapterFactory {
    /** 工厂名称 */
    readonly name: string;
    /** 支持的框架类型 */
    readonly supportedFrameworks: string[];

    /** 创建适配器实例 */
    createAdapter(config: BaseAdapterConfig): IAdapter;
    /** 检查是否支持指定框架 */
    supports(framework: string): boolean;
}

/**
 * 适配器工厂抽象基类
 */
export abstract class BaseAdapterFactory implements AdapterFactory {
    abstract readonly name: string;
    abstract readonly supportedFrameworks: string[];

    abstract createAdapter(config: BaseAdapterConfig): IAdapter;

    supports(framework: string): boolean {
        return this.supportedFrameworks.includes(framework.toLowerCase());
    }
}

/**
 * 适配器注册表
 */
export class AdapterRegistry {
    private static instance: AdapterRegistry;
    private readonly factories = new Map<string, AdapterFactory>();
    private readonly logger = createLogger('AdapterRegistry');

    private constructor() { }

    static getInstance(): AdapterRegistry {
        if (!AdapterRegistry.instance) {
            AdapterRegistry.instance = new AdapterRegistry();
        }
        return AdapterRegistry.instance;
    }

    /**
     * 注册适配器工厂
     */
    register(factory: AdapterFactory): void {
        this.factories.set(factory.name, factory);
        this.logger.info('适配器工厂注册成功', {
            name: factory.name,
            frameworks: factory.supportedFrameworks
        });
    }

    /**
     * 注销适配器工厂
     */
    unregister(name: string): void {
        if (this.factories.delete(name)) {
            this.logger.info('适配器工厂注销成功', { name });
        }
    }

    /**
     * 获取适配器工厂
     */
    getFactory(name: string): AdapterFactory | undefined {
        return this.factories.get(name);
    }

    /**
     * 根据框架类型查找适配器工厂
     */
    findFactory(framework: string): AdapterFactory | undefined {
        for (const factory of this.factories.values()) {
            if (factory.supports(framework)) {
                return factory;
            }
        }
        return undefined;
    }

    /**
     * 创建适配器实例
     */
    createAdapter(framework: string, config: Omit<BaseAdapterConfig, 'framework'>): IAdapter {
        const factory = this.findFactory(framework);
        if (!factory) {
            throw new Error(`未找到支持框架 ${framework} 的适配器工厂`);
        }

        return factory.createAdapter({
            ...config,
            framework
        });
    }

    /**
     * 获取所有已注册的工厂
     */
    getAllFactories(): AdapterFactory[] {
        return Array.from(this.factories.values());
    }

    /**
     * 获取支持的所有框架
     */
    getSupportedFrameworks(): string[] {
        const frameworks = new Set<string>();
        for (const factory of this.factories.values()) {
            factory.supportedFrameworks.forEach(fw => frameworks.add(fw));
        }
        return Array.from(frameworks);
    }
}

/**
 * 默认导出
 */
export default {
    BaseAdapter,
    BaseAdapterFactory,
    AdapterRegistry,
    AdapterStatus
};