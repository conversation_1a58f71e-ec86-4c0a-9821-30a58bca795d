/**
 * @fileoverview 适配器工厂实现
 * @description 提供适配器的创建、注册和管理功能
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { AdapterDependencies, AdapterFactory, AdapterRegistry, BaseAdapter } from './base';
import { BaseAdapterConfig, detectFramework } from './common';

/**
 * 适配器创建选项
 */
export interface AdapterCreationOptions {
    /** 是否自动检测框架类型 */
    autoDetect?: boolean;
    /** 默认适配器类型 */
    defaultType?: string;
    /** 是否启用缓存 */
    enableCache?: boolean;
}

/**
 * 适配器元数据
 */
export interface AdapterMetadata {
    /** 适配器类型 */
    type: string;
    /** 适配器名称 */
    name: string;
    /** 支持的框架版本 */
    supportedVersions?: string[];
    /** 适配器描述 */
    description?: string;
    /** 适配器作者 */
    author?: string;
    /** 适配器版本 */
    version?: string;
}

/**
 * 增强的适配器工厂实现
 */
export class EnhancedAdapterFactory implements AdapterFactory {
    private adapters = new Map<string, {
        adapterClass: new (config: any, dependencies: AdapterDependencies) => BaseAdapter;
        metadata: AdapterMetadata;
    }>();

    private cache = new Map<string, BaseAdapter>();
    private options: AdapterCreationOptions;

    constructor(options: AdapterCreationOptions = {}) {
        this.options = {
            autoDetect: true,
            defaultType: 'html',
            enableCache: false,
            ...options
        };
    }

    /**
     * 创建适配器实例
     */
    createAdapter<T extends BaseAdapterConfig>(
        type: string,
        config: T,
        dependencies: AdapterDependencies
    ): BaseAdapter<T> {
        // 自动检测框架类型
        let actualType = type;
        if (this.options.autoDetect && config.entry) {
            const detectedType = detectFramework(config.entry);
            if (this.isSupported(detectedType)) {
                actualType = detectedType;
            }
        }

        // 检查缓存
        const cacheKey = `${actualType}:${config.name}`;
        if (this.options.enableCache && this.cache.has(cacheKey)) {
            const cachedAdapter = this.cache.get(cacheKey)!;
            // 更新配置
            (cachedAdapter as any).config = config;
            return cachedAdapter as BaseAdapter<T>;
        }

        // 获取适配器类
        const adapterInfo = this.adapters.get(actualType);
        if (!adapterInfo) {
            // 尝试使用默认适配器
            if (this.options.defaultType && this.isSupported(this.options.defaultType)) {
                const defaultAdapterInfo = this.adapters.get(this.options.defaultType)!;
                const adapter = new defaultAdapterInfo.adapterClass(config, dependencies);

                if (this.options.enableCache) {
                    this.cache.set(cacheKey, adapter);
                }

                return adapter as BaseAdapter<T>;
            }

            throw new Error(`Unsupported adapter type: ${actualType}. Supported types: ${this.getSupportedTypes().join(', ')}`);
        }

        // 创建适配器实例
        const adapter = new adapterInfo.adapterClass(config, dependencies);

        // 缓存适配器实例
        if (this.options.enableCache) {
            this.cache.set(cacheKey, adapter);
        }

        return adapter as BaseAdapter<T>;
    }

    /**
     * 注册适配器类型
     */
    registerAdapter<T extends BaseAdapterConfig>(
        type: string,
        adapterClass: new (config: T, dependencies: AdapterDependencies) => BaseAdapter<T>,
        metadata?: Partial<AdapterMetadata>
    ): void {
        const fullMetadata: AdapterMetadata = {
            type,
            name: adapterClass.name,
            description: `${type} adapter`,
            version: '1.0.0',
            ...metadata
        };

        this.adapters.set(type, {
            adapterClass,
            metadata: fullMetadata
        });
    }

    /**
     * 获取支持的适配器类型列表
     */
    getSupportedTypes(): string[] {
        return Array.from(this.adapters.keys());
    }

    /**
     * 检查是否支持指定类型
     */
    isSupported(type: string): boolean {
        return this.adapters.has(type);
    }

    /**
     * 获取适配器元数据
     * @param type 适配器类型
     * @returns 元数据信息
     */
    getAdapterMetadata(type: string): AdapterMetadata | undefined {
        const adapterInfo = this.adapters.get(type);
        return adapterInfo?.metadata;
    }

    /**
     * 获取所有适配器的元数据
     * @returns 元数据映射
     */
    getAllAdapterMetadata(): Map<string, AdapterMetadata> {
        const metadata = new Map<string, AdapterMetadata>();
        for (const [type, info] of this.adapters.entries()) {
            metadata.set(type, info.metadata);
        }
        return metadata;
    }

    /**
     * 清理缓存
     */
    clearCache(): void {
        this.cache.clear();
    }

    /**
     * 移除缓存中的特定适配器
     * @param type 适配器类型
     * @param appName 应用名称
     */
    removeCachedAdapter(type: string, appName: string): void {
        const cacheKey = `${type}:${appName}`;
        this.cache.delete(cacheKey);
    }

    /**
     * 获取缓存统计信息
     * @returns 缓存统计
     */
    getCacheStats(): {
        size: number;
        keys: string[];
        hitRate?: number;
    } {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }

    /**
     * 验证适配器配置
     * @param type 适配器类型
     * @param config 配置对象
     * @returns 验证结果
     */
    validateConfig<T extends BaseAdapterConfig>(
        type: string,
        config: T
    ): { valid: boolean; errors: string[] } {
        const errors: string[] = [];

        // 基础验证
        if (!config.name) {
            errors.push('应用名称不能为空');
        }

        if (!config.framework) {
            errors.push('框架类型不能为空');
        }

        // 检查适配器是否支持
        if (!this.isSupported(type)) {
            errors.push(`不支持的适配器类型: ${type}`);
        }

        // 特定类型的验证
        switch (type) {
            case 'react':
            case 'vue2':
            case 'vue3':
            case 'angular':
            case 'svelte':
            case 'solid':
                if (!config.entry) {
                    errors.push('SPA 应用必须提供入口地址');
                }
                break;
            case 'html':
                // HTML 应用可以没有入口地址
                break;
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * 批量创建适配器
     * @param configs 配置数组
     * @param dependencies 依赖注入
     * @returns 适配器数组
     */
    createAdapters<T extends BaseAdapterConfig>(
        configs: Array<{ type: string; config: T }>,
        dependencies: AdapterDependencies
    ): BaseAdapter<T>[] {
        return configs.map(({ type, config }) =>
            this.createAdapter(type, config, dependencies)
        );
    }

    /**
     * 克隆工厂实例
     * @param newOptions 新的选项
     * @returns 新的工厂实例
     */
    clone(newOptions?: Partial<AdapterCreationOptions>): EnhancedAdapterFactory {
        const clonedFactory = new EnhancedAdapterFactory({
            ...this.options,
            ...newOptions
        });

        // 复制已注册的适配器
        for (const [type, info] of this.adapters.entries()) {
            clonedFactory.adapters.set(type, info);
        }

        return clonedFactory;
    }
}

/**
 * 适配器工厂管理器
 * @description 管理多个适配器工厂实例
 */
export class AdapterFactoryManager {
    private factories = new Map<string, AdapterFactory>();
    private defaultFactory: AdapterFactory;

    constructor() {
        this.defaultFactory = new EnhancedAdapterFactory();
        this.factories.set('default', this.defaultFactory);
    }

    /**
     * 注册工厂实例
     * @param name 工厂名称
     * @param factory 工厂实例
     */
    registerFactory(name: string, factory: AdapterFactory): void {
        this.factories.set(name, factory);
    }

    /**
     * 获取工厂实例
     * @param name 工厂名称
     * @returns 工厂实例
     */
    getFactory(name: string = 'default'): AdapterFactory {
        const factory = this.factories.get(name);
        if (!factory) {
            throw new Error(`Factory not found: ${name}`);
        }
        return factory;
    }

    /**
     * 获取默认工厂
     * @returns 默认工厂实例
     */
    getDefaultFactory(): AdapterFactory {
        return this.defaultFactory;
    }

    /**
     * 获取所有工厂名称
     * @returns 工厂名称列表
     */
    getFactoryNames(): string[] {
        return Array.from(this.factories.keys());
    }

    /**
     * 移除工厂实例
     * @param name 工厂名称
     */
    removeFactory(name: string): void {
        if (name === 'default') {
            throw new Error('Cannot remove default factory');
        }
        this.factories.delete(name);
    }
}

// 全局工厂管理器实例
const globalFactoryManager = new AdapterFactoryManager();

/**
 * 获取全局适配器工厂管理器
 * @returns 工厂管理器实例
 */
export function getAdapterFactoryManager(): AdapterFactoryManager {
    return globalFactoryManager;
}

/**
 * 创建适配器工厂实例
 * @param options 创建选项
 * @returns 工厂实例
 */
export function createAdapterFactory(options?: AdapterCreationOptions): AdapterFactory {
    return new EnhancedAdapterFactory(options);
}

/**
 * 获取默认适配器工厂
 * @returns 默认工厂实例
 */
export function getDefaultAdapterFactory(): AdapterFactory {
    return globalFactoryManager.getDefaultFactory();
}

/**
 * 注册全局适配器类型
 * @param type 适配器类型
 * @param adapterClass 适配器类
 * @param metadata 元数据
 */
export function registerGlobalAdapter<T extends BaseAdapterConfig>(
    type: string,
    adapterClass: new (config: T, dependencies: AdapterDependencies) => BaseAdapter<T>,
    metadata?: Partial<AdapterMetadata>
): void {
    const defaultFactory = globalFactoryManager.getDefaultFactory();
    if (defaultFactory instanceof EnhancedAdapterFactory) {
        defaultFactory.registerAdapter(type, adapterClass, metadata);
    }

    // 同时注册到全局注册表
    const registry = AdapterRegistry.getInstance();
    registry.registerAdapter(type, adapterClass);
}

/**
 * 适配器工厂构建器
 * @description 提供链式API来配置和创建适配器工厂
 */
export class AdapterFactoryBuilder {
    private options: AdapterCreationOptions = {};
    private adapters: Array<{
        type: string;
        adapterClass: new (config: any, dependencies: AdapterDependencies) => BaseAdapter;
        metadata?: Partial<AdapterMetadata>;
    }> = [];

    /**
     * 设置自动检测选项
     * @param autoDetect 是否自动检测
     * @returns 构建器实例
     */
    withAutoDetect(autoDetect: boolean): AdapterFactoryBuilder {
        this.options.autoDetect = autoDetect;
        return this;
    }

    /**
     * 设置默认适配器类型
     * @param defaultType 默认类型
     * @returns 构建器实例
     */
    withDefaultType(defaultType: string): AdapterFactoryBuilder {
        this.options.defaultType = defaultType;
        return this;
    }

    /**
     * 设置缓存选项
     * @param enableCache 是否启用缓存
     * @returns 构建器实例
     */
    withCache(enableCache: boolean): AdapterFactoryBuilder {
        this.options.enableCache = enableCache;
        return this;
    }

    /**
     * 添加适配器类型
     * @param type 适配器类型
     * @param adapterClass 适配器类
     * @param metadata 元数据
     * @returns 构建器实例
     */
    addAdapter<T extends BaseAdapterConfig>(
        type: string,
        adapterClass: new (config: T, dependencies: AdapterDependencies) => BaseAdapter<T>,
        metadata?: Partial<AdapterMetadata>
    ): AdapterFactoryBuilder {
        this.adapters.push({ type, adapterClass, metadata });
        return this;
    }

    /**
     * 构建工厂实例
     * @returns 工厂实例
     */
    build(): EnhancedAdapterFactory {
        const factory = new EnhancedAdapterFactory(this.options);

        // 注册所有适配器
        for (const { type, adapterClass, metadata } of this.adapters) {
            factory.registerAdapter(type, adapterClass, metadata);
        }

        return factory;
    }
}

/**
 * 创建适配器工厂构建器
 * @returns 构建器实例
 */
export function createAdapterFactoryBuilder(): AdapterFactoryBuilder {
    return new AdapterFactoryBuilder();
}