/**
 * @fileoverview 适配器通用工具函数
 * @description 提供所有适配器共享的通用工具函数，包括错误格式化、配置合并、容器管理等
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

// 定义本地错误码常量，避免循环依赖
const ERROR_CODES = {
    APPLICATION_ERROR: 'APPLICATION_ERROR',
    SANDBOX_ERROR: 'SANDBOX_ERROR',
    ADAPTER_ERROR: 'ADAPTER_ERROR'
} as const;

type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];

/**
 * 简化的错误类，避免循环依赖
 */
class AdapterError extends Error {
    public readonly code: ErrorCode;
    public readonly timestamp: number;
    public readonly context?: Record<string, unknown>;

    constructor(
        code: ErrorCode,
        message: string,
        context?: Record<string, unknown>
    ) {
        super(message);
        this.name = 'AdapterError';
        this.code = code;
        this.timestamp = Date.now();
        this.context = context || {};
    }
}

/**
 * 适配器配置接口
 */
export interface BaseAdapterConfig {
    name: string;
    framework: string;
    entry?: string;
    container?: string | HTMLElement;
    props?: Record<string, any>;
    sandbox?: {
        strictStyleIsolation?: boolean;
        experimentalStyleIsolation?: boolean;
    };
    lifecycle?: {
        beforeLoad?: () => Promise<void> | void;
        beforeMount?: () => Promise<void> | void;
        afterMount?: () => Promise<void> | void;
        beforeUnmount?: () => Promise<void> | void;
        afterUnmount?: () => Promise<void> | void;
    };
}

/**
 * 格式化适配器错误信息
 * @param error 错误对象
 * @param context 错误上下文
 * @returns 格式化后的错误字符串
 */
export function formatAdapterError(error: Error, context?: {
    adapterName?: string;
    appName?: string;
    operation?: string;
}): string {
    const timestamp = new Date().toISOString();
    const adapterName = context?.adapterName || 'unknown';
    const appName = context?.appName || 'unknown';
    const operation = context?.operation || 'unknown';

    return `[${timestamp}] [${adapterName}] [${appName}] [${operation}] ${error.name}: ${error.message}${error.stack ? '\n' + error.stack : ''
        }`;
}

/**
 * 深度合并配置对象
 * @param base 基础配置
 * @param override 覆盖配置
 * @returns 合并后的配置
 */
export function mergeConfigs<T extends Record<string, any>>(
    base: T,
    override: Partial<T>
): T {
    const result = { ...base };

    for (const key in override) {
        if (override.hasOwnProperty(key)) {
            const overrideValue = override[key];
            const baseValue = base[key];

            if (overrideValue === null || overrideValue === undefined) {
                continue;
            }

            if (
                typeof overrideValue === 'object' &&
                !Array.isArray(overrideValue) &&
                typeof baseValue === 'object' &&
                !Array.isArray(baseValue) &&
                baseValue !== null
            ) {
                result[key] = mergeConfigs(baseValue, overrideValue);
            } else {
                result[key] = overrideValue as T[Extract<keyof T, string>];
            }
        }
    }

    return result;
}

/**
 * 创建应用容器
 * @param appName 应用名称
 * @param parentElement 父元素
 * @returns 创建的容器元素
 */
export function createContainer(appName: string, parentElement?: HTMLElement): HTMLElement {
    const container = document.createElement('div');
    container.id = `micro-app-${appName}`;
    container.className = 'micro-app-container';

    // 设置基础样式
    container.style.cssText = `
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
    `;

    // 添加到父元素
    const parent = parentElement || document.body;
    parent.appendChild(container);

    return container;
}

/**
 * 清理应用容器
 * @param container 容器元素
 */
export function cleanupContainer(container: HTMLElement): void {
    if (container && container.parentNode) {
        // 清理事件监听器
        const events = ['click', 'mousedown', 'mouseup', 'keydown', 'keyup'];
        events.forEach(event => {
            container.removeEventListener(event, () => { });
        });

        // 清理内容
        container.innerHTML = '';

        // 从DOM中移除
        container.parentNode.removeChild(container);
    }
}

/**
 * 验证适配器配置
 * @param config 配置对象
 * @param schema 验证模式
 * @returns 验证结果
 */
export function validateConfig<T extends Record<string, any>>(config: T, schema: {
    required?: (keyof T)[];
    optional?: (keyof T)[];
    validators?: Partial<Record<keyof T, (value: any) => boolean>>;
}): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查必需字段
    if (schema.required) {
        for (const field of schema.required) {
            if (!(field in config) || config[field] === undefined || config[field] === null) {
                errors.push(`缺少必需字段: ${String(field)}`);
            }
        }
    }

    // 运行自定义验证器
    if (schema.validators) {
        for (const [field, validator] of Object.entries(schema.validators)) {
            const value = config[field as keyof T];
            if (value !== undefined && value !== null && validator && !validator(value)) {
                errors.push(`字段验证失败: ${field}`);
            }
        }
    }

    return {
        valid: errors.length === 0,
        errors
    };
}

/**
 * 检测框架类型
 * @param entry 入口URL或代码
 * @returns 检测到的框架类型
 */
export function detectFramework(entry: string): string {
    const lowerEntry = entry.toLowerCase();

    // 检测 React
    if (lowerEntry.includes('react') || lowerEntry.includes('jsx')) {
        return 'react';
    }

    // 检测 Vue
    if (lowerEntry.includes('vue')) {
        if (lowerEntry.includes('vue@2') || lowerEntry.includes('vue/dist/vue.js')) {
            return 'vue2';
        }
        return 'vue3';
    }

    // 检测 Angular
    if (lowerEntry.includes('angular') || lowerEntry.includes('@angular')) {
        return 'angular';
    }

    // 检测 Svelte
    if (lowerEntry.includes('svelte')) {
        return 'svelte';
    }

    // 检测 Solid
    if (lowerEntry.includes('solid')) {
        return 'solid';
    }

    // 默认为 HTML
    return 'html';
}

/**
 * 获取框架版本
 * @param framework 框架名称
 * @returns 框架版本信息
 */
export function getFrameworkVersion(framework: string): string | null {
    try {
        switch (framework.toLowerCase()) {
            case 'react':
                return (window as any).React?.version || null;
            case 'vue':
            case 'vue2':
            case 'vue3':
                return (window as any).Vue?.version || null;
            case 'angular':
                return (window as any).ng?.version?.full || null;
            default:
                return null;
        }
    } catch {
        return null;
    }
}

/**
 * 检查框架版本兼容性
 * @param framework 框架名称
 * @param requiredVersion 要求的版本
 * @returns 是否兼容
 */
export function isFrameworkVersionCompatible(framework: string, requiredVersion: string): boolean {
    const currentVersion = getFrameworkVersion(framework);
    if (!currentVersion) {
        return false;
    }

    // 简单的版本比较（实际项目中应使用更完善的版本比较库）
    const current = currentVersion.split('.').map(Number);
    const required = requiredVersion.split('.').map(Number);

    for (let i = 0; i < Math.max(current.length, required.length); i++) {
        const currentPart = current[i] || 0;
        const requiredPart = required[i] || 0;

        if (currentPart > requiredPart) {
            return true;
        } else if (currentPart < requiredPart) {
            return false;
        }
    }

    return true;
}

/**
 * 创建适配器错误
 * @param code 错误码
 * @param message 错误消息
 * @param context 错误上下文
 * @returns AdapterError 实例
 */
export function createAdapterError(
    code: keyof typeof ERROR_CODES,
    message: string,
    context?: Record<string, any>
): AdapterError {
    return new AdapterError(ERROR_CODES[code], message, context);
}

/**
 * 适配器工具类
 */
export class AdapterUtils {
    /**
     * 安全执行异步函数
     * @param fn 要执行的函数
     * @param errorMessage 错误消息
     * @param context 错误上下文
     * @returns 执行结果
     */
    static async safeExecute<T>(
        fn: () => Promise<T>,
        errorMessage: string,
        context?: Record<string, any>
    ): Promise<T> {
        try {
            return await fn();
        } catch (error) {
            throw createAdapterError(
                'APPLICATION_ERROR',
                errorMessage,
                { ...context, originalError: error }
            );
        }
    }

    /**
     * 创建加载状态指示器
     * @param container 容器元素
     * @param message 加载消息
     * @returns 加载指示器元素
     */
    static createLoadingIndicator(container: HTMLElement, message = '加载中...'): HTMLElement {
        const loading = document.createElement('div');
        loading.className = 'micro-app-loading';
        loading.innerHTML = `
            <div style="
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                color: #666;
            ">
                <div style="text-align: center;">
                    <div style="
                        width: 32px;
                        height: 32px;
                        border: 3px solid #f3f3f3;
                        border-top: 3px solid #3498db;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin: 0 auto 12px;
                    "></div>
                    <div>${message}</div>
                </div>
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;

        container.appendChild(loading);
        return loading;
    }

    /**
     * 移除加载状态指示器
     * @param container 容器元素
     */
    static removeLoadingIndicator(container: HTMLElement): void {
        const loading = container.querySelector('.micro-app-loading');
        if (loading) {
            loading.remove();
        }
    }

    /**
     * 创建错误显示组件
     * @param container 容器元素
     * @param error 错误对象
     * @returns 错误显示元素
     */
    static createErrorDisplay(container: HTMLElement, error: Error): HTMLElement {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'micro-app-error';
        errorDiv.innerHTML = `
            <div style="
                padding: 20px;
                border: 1px solid #ff6b6b;
                border-radius: 4px;
                background-color: #ffe0e0;
                color: #d63031;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 20px;
            ">
                <h3 style="margin: 0 0 12px 0; font-size: 16px;">应用加载失败</h3>
                <p style="margin: 0 0 12px 0;"><strong>错误:</strong> ${error.message}</p>
                <details style="margin-top: 12px;">
                    <summary style="cursor: pointer; color: #0066cc;">查看详细信息</summary>
                    <pre style="
                        margin: 8px 0 0 0;
                        padding: 12px;
                        background-color: #f8f8f8;
                        border-radius: 4px;
                        font-size: 12px;
                        overflow-x: auto;
                        white-space: pre-wrap;
                    ">${error.stack || '无堆栈信息'}</pre>
                </details>
            </div>
        `;

        container.appendChild(errorDiv);
        return errorDiv;
    }
}