/**
 * DOM操作工具函数
 * 从各个适配器中提取的通用DOM操作功能
 */

/**
 * 创建微前端应用容器
 * @description 为微前端应用创建DOM容器元素
 * @param appName 应用名称
 * @param parentElement 父元素，默认为document.body
 * @returns 创建的容器元素
 * @example
 * ```typescript
 * const container = createContainer('my-app');
 * const customContainer = createContainer('my-app', document.getElementById('root'));
 * ```
 */
export function createContainer(appName: string, parentElement?: HTMLElement): HTMLElement {
    const container = document.createElement('div');
    container.id = `micro-app-${appName}`;
    container.className = 'micro-app-container';
    container.setAttribute('data-app-name', appName);

    // 设置基础样式
    container.style.cssText = `
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
    `;

    const parent = parentElement || document.body;
    parent.appendChild(container);

    return container;
}

/**
 * 清理微前端应用容器
 * @description 清理并移除微前端应用的DOM容器
 * @param container 要清理的容器元素
 * @example
 * ```typescript
 * const container = createContainer('my-app');
 * // ... 使用容器
 * cleanupContainer(container);
 * ```
 */
export function cleanupContainer(container: HTMLElement): void {
    if (!container || !container.parentNode) {
        return;
    }

    // 清理所有子元素
    while (container.firstChild) {
        container.removeChild(container.firstChild);
    }

    // 移除容器本身
    container.parentNode.removeChild(container);
}

/**
 * 查找微前端应用容器
 * @description 根据应用名称查找对应的容器元素
 * @param appName 应用名称
 * @returns 找到的容器元素，如果不存在则返回null
 * @example
 * ```typescript
 * const container = findContainer('my-app');
 * if (container) {
 *   // 容器存在，可以进行操作
 * }
 * ```
 */
export function findContainer(appName: string): HTMLElement | null {
    return document.getElementById(`micro-app-${appName}`);
}

/**
 * 检查元素是否在视口中
 * @description 检查指定元素是否在当前视口范围内
 * @param element 要检查的元素
 * @param threshold 阈值，0-1之间的数值，表示元素可见的比例
 * @returns 是否在视口中
 * @example
 * ```typescript
 * const element = document.getElementById('my-element');
 * if (isElementInViewport(element, 0.5)) {
 *   // 元素至少50%在视口中
 * }
 * ```
 */
export function isElementInViewport(element: HTMLElement, threshold = 0): boolean {
    if (!element) return false;

    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight || document.documentElement.clientHeight;
    const windowWidth = window.innerWidth || document.documentElement.clientWidth;

    const verticalVisible = Math.max(0, Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0));
    const horizontalVisible = Math.max(0, Math.min(rect.right, windowWidth) - Math.max(rect.left, 0));

    const elementArea = rect.width * rect.height;
    const visibleArea = verticalVisible * horizontalVisible;

    return elementArea > 0 && (visibleArea / elementArea) >= threshold;
}

/**
 * 等待元素出现
 * @description 等待指定选择器的元素出现在DOM中
 * @param selector CSS选择器
 * @param timeout 超时时间（毫秒），默认5000ms
 * @returns Promise，解析为找到的元素
 * @example
 * ```typescript
 * try {
 *   const element = await waitForElement('#my-element', 3000);
 *   // 元素已出现
 * } catch (error) {
 *   // 超时或其他错误
 * }
 * ```
 */
export function waitForElement(selector: string, timeout = 5000): Promise<HTMLElement> {
    return new Promise((resolve, reject) => {
        const element = document.querySelector(selector) as HTMLElement;
        if (element) {
            resolve(element);
            return;
        }

        const observer = new MutationObserver(() => {
            const element = document.querySelector(selector) as HTMLElement;
            if (element) {
                observer.disconnect();
                clearTimeout(timeoutId);
                resolve(element);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        const timeoutId = setTimeout(() => {
            observer.disconnect();
            reject(new Error(`等待元素 "${selector}" 超时`));
        }, timeout);
    });
}