/**
 * DOM 容器管理工具
 * 从适配器包中提取的通用 DOM 操作逻辑
 */

/**
 * 创建微应用容器元素
 */
export function createContainer(appName: string, parentElement?: HTMLElement): HTMLElement {
    const containerId = `micro-app-${appName}`;

    // 检查是否已存在容器
    let container = document.getElementById(containerId);
    if (container) {
        return container;
    }

    // 创建新容器
    container = document.createElement('div');
    container.id = containerId;
    container.className = 'micro-app-container';
    container.setAttribute('data-app-name', appName);
    container.setAttribute('data-created-at', new Date().toISOString());

    // 添加到父元素
    const parent = parentElement || document.body;
    parent.appendChild(container);

    return container;
}

/**
 * 清理微应用容器
 */
export function cleanupContainer(container: HTMLElement): void {
    if (!container) return;

    // 清理事件监听器
    const events = ['click', 'scroll', 'resize', 'focus', 'blur'];
    events.forEach(event => {
        container.removeEventListener(event, () => { });
    });

    // 清理子元素
    while (container.firstChild) {
        container.removeChild(container.firstChild);
    }

    // 移除容器
    if (container.parentNode) {
        container.parentNode.removeChild(container);
    }
}

/**
 * 获取微应用容器
 */
export function getContainer(appName: string): HTMLElement | null {
    return document.getElementById(`micro-app-${appName}`);
}

/**
 * 检查容器是否存在
 */
export function hasContainer(appName: string): boolean {
    return !!getContainer(appName);
}

/**
 * 设置容器样式
 */
export function setContainerStyle(container: HTMLElement, styles: Partial<CSSStyleDeclaration>): void {
    Object.assign(container.style, styles);
}

/**
 * 为容器添加框架特定的类名和属性
 */
export function enhanceContainer(
    container: HTMLElement,
    framework: string,
    options: {
        className?: string;
        attributes?: Record<string, string>;
    } = {}
): void {
    const { className, attributes } = options;

    // 添加框架类名
    container.classList.add(`${framework}-app-container`);
    container.setAttribute('data-framework', framework);

    // 添加自定义类名
    if (className) {
        container.classList.add(className);
    }

    // 添加自定义属性
    if (attributes) {
        Object.entries(attributes).forEach(([key, value]) => {
            container.setAttribute(key, value);
        });
    }
}

/**
 * 创建带框架特定增强的容器
 */
export function createEnhancedContainer(
    appName: string,
    framework: string,
    parentElement?: HTMLElement,
    options?: {
        className?: string;
        attributes?: Record<string, string>;
        styles?: Partial<CSSStyleDeclaration>;
    }
): HTMLElement {
    const container = createContainer(appName, parentElement);

    enhanceContainer(container, framework, {
        className: options?.className,
        attributes: options?.attributes
    });

    if (options?.styles) {
        setContainerStyle(container, options.styles);
    }

    return container;
}