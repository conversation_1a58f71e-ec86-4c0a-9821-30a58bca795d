/**
 * @fileoverview 懒加载系统
 * @description 提供多种懒加载策略和实现
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 懒加载策略枚举
 */
export enum LazyLoadStrategy {
    /** 立即加载 */
    IMMEDIATE = 'immediate',
    /** 延迟加载 */
    DEFERRED = 'deferred',
    /** 按需加载 */
    ON_DEMAND = 'on_demand',
    /** 可见时加载 */
    INTERSECTION = 'intersection',
    /** 空闲时加载 */
    IDLE = 'idle',
    /** 预加载 */
    PRELOAD = 'preload'
}

/**
 * 懒加载配置
 */
export interface LazyLoadConfig {
    /** 加载策略 */
    strategy?: LazyLoadStrategy;
    /** 延迟时间（毫秒） */
    delay?: number;
    /** 交集观察器选项 */
    intersectionOptions?: IntersectionObserverInit;
    /** 空闲回调选项 */
    idleOptions?: IdleRequestOptions;
    /** 预加载优先级 */
    preloadPriority?: 'high' | 'low' | 'auto';
    /** 重试次数 */
    retries?: number;
    /** 重试间隔 */
    retryDelay?: number;
    /** 超时时间 */
    timeout?: number;
    /** 是否启用缓存 */
    cache?: boolean;
    /** 错误处理函数 */
    onError?: (error: Error) => void;
    /** 加载成功回调 */
    onSuccess?: (result: any) => void;
    /** 加载进度回调 */
    onProgress?: (progress: number) => void;
}

/**
 * 懒加载状态
 */
export enum LazyLoadStatus {
    /** 未开始 */
    PENDING = 'pending',
    /** 加载中 */
    LOADING = 'loading',
    /** 已完成 */
    LOADED = 'loaded',
    /** 失败 */
    FAILED = 'failed',
    /** 已取消 */
    CANCELLED = 'cancelled'
}

/**
 * 懒加载项接口
 */
export interface LazyLoadItem<T = any> {
    /** 唯一标识 */
    id: string;
    /** 加载函数 */
    loader: () => Promise<T>;
    /** 配置 */
    config: LazyLoadConfig;
    /** 状态 */
    status: LazyLoadStatus;
    /** 结果 */
    result?: T;
    /** 错误 */
    error?: Error;
    /** 创建时间 */
    createdAt: number;
    /** 开始加载时间 */
    startedAt?: number;
    /** 完成时间 */
    completedAt?: number;
    /** 重试次数 */
    retryCount: number;
}

/**
 * 懒加载器接口
 */
export interface LazyLoader<T = any> {
    /** 加载项 */
    load(id: string, loader: () => Promise<T>, config?: LazyLoadConfig): Promise<T>;
    /** 取消加载 */
    cancel(id: string): boolean;
    /** 获取状态 */
    getStatus(id: string): LazyLoadStatus | undefined;
    /** 获取结果 */
    getResult(id: string): T | undefined;
    /** 清理 */
    cleanup(): void;
    /** 销毁 */
    destroy(): void;
}

/**
 * 基础懒加载器
 */
export abstract class BaseLazyLoader<T = any> implements LazyLoader<T> {
    protected items = new Map<string, LazyLoadItem<T>>();
    protected cache = new Map<string, T>();
    protected defaultConfig: Required<LazyLoadConfig>;

    constructor(config: LazyLoadConfig = {}) {
        this.defaultConfig = {
            strategy: LazyLoadStrategy.DEFERRED,
            delay: 0,
            intersectionOptions: { threshold: 0.1 },
            idleOptions: { timeout: 5000 },
            preloadPriority: 'auto',
            retries: 3,
            retryDelay: 1000,
            timeout: 30000,
            cache: true,
            onError: () => { },
            onSuccess: () => { },
            onProgress: () => { },
            ...config
        };
    }

    /**
     * 加载项
     */
    async load(id: string, loader: () => Promise<T>, config?: LazyLoadConfig): Promise<T> {
        const effectiveConfig = { ...this.defaultConfig, ...config };

        // 检查缓存
        if (effectiveConfig.cache && this.cache.has(id)) {
            return this.cache.get(id)!;
        }

        // 检查是否已存在
        let item = this.items.get(id);
        if (item) {
            if (item.status === LazyLoadStatus.LOADED && item.result !== undefined) {
                return item.result;
            }
            if (item.status === LazyLoadStatus.LOADING) {
                return this.waitForCompletion(item);
            }
        }

        // 创建新项
        item = {
            id,
            loader,
            config: effectiveConfig,
            status: LazyLoadStatus.PENDING,
            createdAt: Date.now(),
            retryCount: 0
        };

        this.items.set(id, item);

        // 根据策略执行加载
        return this.executeLoad(item);
    }

    /**
     * 取消加载
     */
    cancel(id: string): boolean {
        const item = this.items.get(id);
        if (item && item.status === LazyLoadStatus.LOADING) {
            item.status = LazyLoadStatus.CANCELLED;
            return true;
        }
        return false;
    }

    /**
     * 获取状态
     */
    getStatus(id: string): LazyLoadStatus | undefined {
        return this.items.get(id)?.status;
    }

    /**
     * 获取结果
     */
    getResult(id: string): T | undefined {
        return this.items.get(id)?.result;
    }

    /**
     * 清理
     */
    cleanup(): void {
        // 清理已完成或失败的项
        for (const [id, item] of this.items.entries()) {
            if (item.status === LazyLoadStatus.LOADED ||
                item.status === LazyLoadStatus.FAILED ||
                item.status === LazyLoadStatus.CANCELLED) {
                this.items.delete(id);
            }
        }
    }

    /**
     * 销毁
     */
    destroy(): void {
        this.items.clear();
        this.cache.clear();
    }

    /**
     * 抽象方法：执行加载
     */
    protected abstract executeLoad(item: LazyLoadItem<T>): Promise<T>;

    /**
     * 等待完成
     */
    protected async waitForCompletion(item: LazyLoadItem<T>): Promise<T> {
        return new Promise((resolve, reject) => {
            const checkStatus = () => {
                if (item.status === LazyLoadStatus.LOADED && item.result !== undefined) {
                    resolve(item.result);
                } else if (item.status === LazyLoadStatus.FAILED && item.error) {
                    reject(item.error);
                } else if (item.status === LazyLoadStatus.CANCELLED) {
                    reject(new Error('加载已取消'));
                } else {
                    setTimeout(checkStatus, 100);
                }
            };
            checkStatus();
        });
    }

    /**
     * 执行实际加载
     */
    protected async performLoad(item: LazyLoadItem<T>): Promise<T> {
        item.status = LazyLoadStatus.LOADING;
        item.startedAt = Date.now();

        try {
            // 设置超时
            const timeoutPromise = new Promise<never>((_, reject) => {
                setTimeout(() => reject(new Error('加载超时')), item.config.timeout);
            });

            const result = await Promise.race([
                item.loader(),
                timeoutPromise
            ]);

            item.result = result;
            item.status = LazyLoadStatus.LOADED;
            item.completedAt = Date.now();

            // 缓存结果
            if (item.config.cache) {
                this.cache.set(item.id, result);
            }

            // 调用成功回调
            item.config.onSuccess?.(result);

            return result;
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));

            // 重试逻辑
            if (item.retryCount < item.config.retries!) {
                item.retryCount++;
                await this.delay(item.config.retryDelay!);
                return this.performLoad(item);
            }

            item.error = err;
            item.status = LazyLoadStatus.FAILED;
            item.completedAt = Date.now();

            // 调用错误回调
            item.config.onError?.(err);

            throw err;
        }
    }

    /**
     * 延迟执行
     */
    protected delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

/**
 * 立即加载器
 */
export class ImmediateLazyLoader<T = any> extends BaseLazyLoader<T> {
    protected async executeLoad(item: LazyLoadItem<T>): Promise<T> {
        return this.performLoad(item);
    }
}

/**
 * 延迟加载器
 */
export class DeferredLazyLoader<T = any> extends BaseLazyLoader<T> {
    protected async executeLoad(item: LazyLoadItem<T>): Promise<T> {
        if (item.config.delay! > 0) {
            await this.delay(item.config.delay!);
        }
        return this.performLoad(item);
    }
}

/**
 * 按需加载器
 */
export class OnDemandLazyLoader<T = any> extends BaseLazyLoader<T> {
    protected async executeLoad(item: LazyLoadItem<T>): Promise<T> {
        // 按需加载通常由外部触发，这里直接执行
        return this.performLoad(item);
    }
}

/**
 * 交集观察器加载器
 */
export class IntersectionLazyLoader<T = any> extends BaseLazyLoader<T> {
    private observers = new Map<string, IntersectionObserver>();

    protected async executeLoad(item: LazyLoadItem<T>): Promise<T> {
        return new Promise((resolve, reject) => {
            // 创建观察器
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        observer.disconnect();
                        this.observers.delete(item.id);
                        this.performLoad(item).then(resolve).catch(reject);
                    }
                });
            }, item.config.intersectionOptions);

            this.observers.set(item.id, observer);

            // 需要外部提供目标元素
            // 这里假设在配置中提供了目标元素
            const target = (item.config as any).target;
            if (target instanceof Element) {
                observer.observe(target);
            } else {
                reject(new Error('交集观察器需要目标元素'));
            }
        });
    }

    destroy(): void {
        super.destroy();
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
    }
}

/**
 * 空闲时加载器
 */
export class IdleLazyLoader<T = any> extends BaseLazyLoader<T> {
    protected async executeLoad(item: LazyLoadItem<T>): Promise<T> {
        return new Promise((resolve, reject) => {
            if (typeof requestIdleCallback !== 'undefined') {
                requestIdleCallback(() => {
                    this.performLoad(item).then(resolve).catch(reject);
                }, item.config.idleOptions);
            } else {
                // 降级到 setTimeout
                setTimeout(() => {
                    this.performLoad(item).then(resolve).catch(reject);
                }, 0);
            }
        });
    }
}

/**
 * 预加载器
 */
export class PreloadLazyLoader<T = any> extends BaseLazyLoader<T> {
    private preloadQueue: LazyLoadItem<T>[] = [];
    private isProcessing = false;

    protected async executeLoad(item: LazyLoadItem<T>): Promise<T> {
        // 添加到预加载队列
        this.preloadQueue.push(item);
        this.processQueue();

        return this.waitForCompletion(item);
    }

    private async processQueue(): Promise<void> {
        if (this.isProcessing || this.preloadQueue.length === 0) {
            return;
        }

        this.isProcessing = true;

        while (this.preloadQueue.length > 0) {
            const item = this.preloadQueue.shift()!;

            try {
                await this.performLoad(item);
            } catch (error) {
                // 预加载失败不影响队列处理
                console.warn('预加载失败:', error);
            }

            // 让出控制权
            await new Promise(resolve => setTimeout(resolve, 0));
        }

        this.isProcessing = false;
    }
}

/**
 * 懒加载工厂
 */
export class LazyLoaderFactory {
    /**
     * 创建懒加载器
     */
    static create<T = any>(strategy: LazyLoadStrategy, config?: LazyLoadConfig): LazyLoader<T> {
        switch (strategy) {
            case LazyLoadStrategy.IMMEDIATE:
                return new ImmediateLazyLoader<T>(config);
            case LazyLoadStrategy.DEFERRED:
                return new DeferredLazyLoader<T>(config);
            case LazyLoadStrategy.ON_DEMAND:
                return new OnDemandLazyLoader<T>(config);
            case LazyLoadStrategy.INTERSECTION:
                return new IntersectionLazyLoader<T>(config);
            case LazyLoadStrategy.IDLE:
                return new IdleLazyLoader<T>(config);
            case LazyLoadStrategy.PRELOAD:
                return new PreloadLazyLoader<T>(config);
            default:
                throw new Error(`不支持的懒加载策略: ${strategy}`);
        }
    }
}

/**
 * 懒加载管理器
 */
export class LazyLoadManager {
    private loaders = new Map<string, LazyLoader>();
    private defaultStrategy: LazyLoadStrategy;

    constructor(defaultStrategy: LazyLoadStrategy = LazyLoadStrategy.DEFERRED) {
        this.defaultStrategy = defaultStrategy;
    }

    /**
     * 注册加载器
     */
    registerLoader(name: string, loader: LazyLoader): void {
        this.loaders.set(name, loader);
    }

    /**
     * 获取加载器
     */
    getLoader(name: string): LazyLoader | undefined {
        return this.loaders.get(name);
    }

    /**
     * 创建加载器
     */
    createLoader(strategy: LazyLoadStrategy = this.defaultStrategy, config?: LazyLoadConfig): LazyLoader {
        return LazyLoaderFactory.create(strategy, config);
    }

    /**
     * 懒加载
     */
    async load<T>(
        id: string,
        loader: () => Promise<T>,
        strategy: LazyLoadStrategy = this.defaultStrategy,
        config?: LazyLoadConfig
    ): Promise<T> {
        const loaderInstance = this.createLoader(strategy, config);
        return loaderInstance.load(id, loader, config);
    }

    /**
     * 销毁所有加载器
     */
    destroy(): void {
        this.loaders.forEach(loader => loader.destroy());
        this.loaders.clear();
    }
}

/**
 * 懒加载装饰器
 */
export function lazyLoad(strategy: LazyLoadStrategy = LazyLoadStrategy.DEFERRED, config?: LazyLoadConfig) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        const loader = LazyLoaderFactory.create(strategy, config);

        descriptor.value = function (...args: any[]) {
            const id = `${target.constructor.name}.${propertyKey}`;
            return loader.load(id, () => originalMethod.apply(this, args), config);
        };

        return descriptor;
    };
}

/**
 * 懒加载组件高阶函数
 */
export function withLazyLoad<T>(
    loader: () => Promise<T>,
    strategy: LazyLoadStrategy = LazyLoadStrategy.INTERSECTION,
    config?: LazyLoadConfig
): () => Promise<T> {
    const lazyLoader = LazyLoaderFactory.create<T>(strategy, config);
    const id = `lazy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return () => lazyLoader.load(id, loader, config);
}

/**
 * 默认懒加载管理器实例
 */
export const defaultLazyLoadManager = new LazyLoadManager();

/**
 * 便捷的懒加载函数
 */
export const lazyLoadUtils = {
    /**
     * 立即加载
     */
    immediate: <T>(id: string, loader: () => Promise<T>, config?: LazyLoadConfig) =>
        defaultLazyLoadManager.load(id, loader, LazyLoadStrategy.IMMEDIATE, config),

    /**
     * 延迟加载
     */
    deferred: <T>(id: string, loader: () => Promise<T>, delay: number = 1000, config?: LazyLoadConfig) =>
        defaultLazyLoadManager.load(id, loader, LazyLoadStrategy.DEFERRED, { ...config, delay }),

    /**
     * 按需加载
     */
    onDemand: <T>(id: string, loader: () => Promise<T>, config?: LazyLoadConfig) =>
        defaultLazyLoadManager.load(id, loader, LazyLoadStrategy.ON_DEMAND, config),

    /**
     * 可见时加载
     */
    intersection: <T>(id: string, loader: () => Promise<T>, target: Element, config?: LazyLoadConfig) =>
        defaultLazyLoadManager.load(id, loader, LazyLoadStrategy.INTERSECTION, { ...config, target } as any),

    /**
     * 空闲时加载
     */
    idle: <T>(id: string, loader: () => Promise<T>, config?: LazyLoadConfig) =>
        defaultLazyLoadManager.load(id, loader, LazyLoadStrategy.IDLE, config),

    /**
     * 预加载
     */
    preload: <T>(id: string, loader: () => Promise<T>, config?: LazyLoadConfig) =>
        defaultLazyLoadManager.load(id, loader, LazyLoadStrategy.PRELOAD, config)
};

/**
 * 懒加载资源工具
 */
export class LazyResourceLoader {
    private static instance: LazyResourceLoader;
    private manager: LazyLoadManager;

    private constructor() {
        this.manager = new LazyLoadManager();
    }

    static getInstance(): LazyResourceLoader {
        if (!LazyResourceLoader.instance) {
            LazyResourceLoader.instance = new LazyResourceLoader();
        }
        return LazyResourceLoader.instance;
    }

    /**
     * 懒加载脚本
     */
    async loadScript(src: string, config?: LazyLoadConfig): Promise<HTMLScriptElement> {
        return this.manager.load(
            `script_${src}`,
            () => this.createScriptLoader(src),
            LazyLoadStrategy.DEFERRED,
            config
        );
    }

    /**
     * 懒加载样式
     */
    async loadStyle(href: string, config?: LazyLoadConfig): Promise<HTMLLinkElement> {
        return this.manager.load(
            `style_${href}`,
            () => this.createStyleLoader(href),
            LazyLoadStrategy.DEFERRED,
            config
        );
    }

    /**
     * 懒加载图片
     */
    async loadImage(src: string, config?: LazyLoadConfig): Promise<HTMLImageElement> {
        return this.manager.load(
            `image_${src}`,
            () => this.createImageLoader(src),
            LazyLoadStrategy.INTERSECTION,
            config
        );
    }

    /**
     * 懒加载模块
     */
    async loadModule<T = any>(modulePath: string, config?: LazyLoadConfig): Promise<T> {
        return this.manager.load(
            `module_${modulePath}`,
            () => import(modulePath),
            LazyLoadStrategy.ON_DEMAND,
            config
        );
    }

    private createScriptLoader(src: string): Promise<HTMLScriptElement> {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = () => resolve(script);
            script.onerror = () => reject(new Error(`脚本加载失败: ${src}`));
            document.head.appendChild(script);
        });
    }

    private createStyleLoader(href: string): Promise<HTMLLinkElement> {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.onload = () => resolve(link);
            link.onerror = () => reject(new Error(`样式加载失败: ${href}`));
            document.head.appendChild(link);
        });
    }

    private createImageLoader(src: string): Promise<HTMLImageElement> {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = () => reject(new Error(`图片加载失败: ${src}`));
            img.src = src;
        });
    }
}

/**
 * 全局懒加载资源加载器
 */
export const lazyResourceLoader = LazyResourceLoader.getInstance();
