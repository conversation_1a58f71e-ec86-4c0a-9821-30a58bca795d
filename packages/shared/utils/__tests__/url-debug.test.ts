/**
 * @fileoverview Debug URL behavior
 */

describe('URL Debug Test', () => {
    it('should debug URL constructor behavior', () => {
        console.log('Testing URL constructor directly:');

        const testCases = ['', 'invalid-url', '/relative/path', '//example.com', 'https://example.com'];

        testCases.forEach(url => {
            try {
                new URL(url);
                console.log(`'${url}' -> true`);
            } catch (e) {
                console.log(`'${url}' -> false (${e.message})`);
            }
        });

        // Test our function
        function testIsValidUrl(url: string): boolean {
            try {
                new URL(url);
                return true;
            } catch {
                return false;
            }
        }

        console.log('Testing our function:');
        testCases.forEach(url => {
            const result = testIsValidUrl(url);
            console.log(`'${url}' -> ${result}`);
        });

        expect(testIsValidUrl('https://example.com')).toBe(true);
        expect(testIsValidUrl('invalid-url')).toBe(false);
    });
});