/**
 * @fileoverview URL 工具函数测试
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { describe, expect, it } from 'vitest';
import {
    coreUrlUtils,
    isValidUrl,
    URL_FUNCTIONS,
    validateUrlFunctions
} from '../src/url/core';

describe('URL 工具函数', () => {
    describe('isValidUrl', () => {
        it('应该正确识别有效的 HTTP URL', () => {
            expect(isValidUrl('http://example.com')).toBe(true);
            expect(isValidUrl('http://www.example.com')).toBe(true);
            expect(isValidUrl('http://example.com:8080')).toBe(true);
            expect(isValidUrl('http://example.com/path')).toBe(true);
            expect(isValidUrl('http://example.com/path?query=value')).toBe(true);
            expect(isValidUrl('http://example.com/path#fragment')).toBe(true);
        });

        it('应该正确识别有效的 HTTPS URL', () => {
            expect(isValidUrl('https://example.com')).toBe(true);
            expect(isValidUrl('https://www.example.com')).toBe(true);
            expect(isValidUrl('https://example.com:443')).toBe(true);
            expect(isValidUrl('https://example.com/secure/path')).toBe(true);
        });

        it('应该正确识别其他协议的有效 URL', () => {
            expect(isValidUrl('ftp://ftp.example.com')).toBe(true);
            expect(isValidUrl('file:///path/to/file')).toBe(true);
            expect(isValidUrl('mailto:<EMAIL>')).toBe(true);
            expect(isValidUrl('tel:+1234567890')).toBe(true);
        });

        it('应该正确识别无效的 URL', () => {
            expect(isValidUrl('')).toBe(false);
            expect(isValidUrl('invalid-url')).toBe(false);
            expect(isValidUrl('just-text')).toBe(false);
            expect(isValidUrl('/relative/path')).toBe(false);
            expect(isValidUrl('//example.com')).toBe(false);
            expect(isValidUrl('http://')).toBe(false);
            expect(isValidUrl('https://')).toBe(false);
        });

        it('应该处理边界情况', () => {
            expect(isValidUrl('http://localhost')).toBe(true);
            expect(isValidUrl('http://127.0.0.1')).toBe(true);
            expect(isValidUrl('http://[::1]')).toBe(true);
            expect(isValidUrl('http://example.com:0')).toBe(true);
            expect(isValidUrl('http://example.com:65535')).toBe(true);
        });

        it('应该处理特殊字符', () => {
            expect(isValidUrl('http://example.com/path with spaces')).toBe(false);
            expect(isValidUrl('http://example.com/path%20with%20encoded%20spaces')).toBe(true);
            expect(isValidUrl('http://example.com/中文路径')).toBe(true);
        });
    });

    describe('coreUrlUtils', () => {
        it('应该包含所有 URL 工具函数', () => {
            expect(coreUrlUtils.isValidUrl).toBe(isValidUrl);
            expect(typeof coreUrlUtils.isValidUrl).toBe('function');
        });

        it('应该是只读对象', () => {
            expect(Object.isFrozen(coreUrlUtils)).toBe(false); // const assertion 不会冻结对象
            expect(coreUrlUtils).toEqual({ isValidUrl });
        });
    });

    describe('URL_FUNCTIONS', () => {
        it('应该包含所有函数名称', () => {
            expect(URL_FUNCTIONS).toEqual(['isValidUrl']);
            expect(URL_FUNCTIONS.length).toBe(1);
        });

        it('应该是只读数组', () => {
            expect(Array.isArray(URL_FUNCTIONS)).toBe(true);
        });
    });

    describe('validateUrlFunctions', () => {
        it('应该正确验证所有函数可用性', () => {
            const result = validateUrlFunctions();

            expect(result.available).toEqual(['isValidUrl']);
            expect(result.missing).toEqual([]);
            expect(result.allAvailable).toBe(true);
        });

        it('应该返回正确的结构', () => {
            const result = validateUrlFunctions();

            expect(result).toHaveProperty('available');
            expect(result).toHaveProperty('missing');
            expect(result).toHaveProperty('allAvailable');
            expect(Array.isArray(result.available)).toBe(true);
            expect(Array.isArray(result.missing)).toBe(true);
            expect(typeof result.allAvailable).toBe('boolean');
        });
    });

    describe('性能测试', () => {
        it('isValidUrl 应该有良好的性能', () => {
            const urls = [
                'https://example.com',
                'http://localhost:3000',
                'invalid-url',
                'ftp://ftp.example.com',
                ''
            ];

            const startTime = performance.now();

            for (let i = 0; i < 1000; i++) {
                urls.forEach(url => isValidUrl(url));
            }

            const endTime = performance.now();
            const duration = endTime - startTime;

            // 1000次 * 5个URL = 5000次调用应该在合理时间内完成
            expect(duration).toBeLessThan(100); // 100ms
        });
    });

    describe('错误处理', () => {
        it('应该安全处理各种输入类型', () => {
            // 这些调用不应该抛出异常
            expect(() => isValidUrl(null as any)).not.toThrow();
            expect(() => isValidUrl(undefined as any)).not.toThrow();
            expect(() => isValidUrl(123 as any)).not.toThrow();
            expect(() => isValidUrl({} as any)).not.toThrow();
            expect(() => isValidUrl([] as any)).not.toThrow();

            // 但应该返回 false
            expect(isValidUrl(null as any)).toBe(false);
            expect(isValidUrl(undefined as any)).toBe(false);
            expect(isValidUrl(123 as any)).toBe(false);
            expect(isValidUrl({} as any)).toBe(false);
            expect(isValidUrl([] as any)).toBe(false);
        });
    });
});