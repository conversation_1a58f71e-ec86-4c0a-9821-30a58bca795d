/**
 * @fileoverview 类型检查工具函数测试
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { describe, expect, it } from 'vitest';
import {
    isArray,
    isBoolean,
    isEmpty,
    isFunction,
    isNumber,
    isObject,
    isPromise,
    isString
} from '../src/type-check/core';

// 从原始 type-check.ts 导入其他函数
import {
    getType,
    isDate,
    isError,
    isNullOrUndefined,
    isRegExp,
    isSameType,
    typeUtils
} from '../src/type-check';

describe('类型检查工具函数', () => {
    describe('isFunction', () => {
        it('应该正确识别函数', () => {
            expect(isFunction(() => { })).toBe(true);
            expect(isFunction(function () { })).toBe(true);
            expect(isFunction(async () => { })).toBe(true);
            expect(isFunction(Date)).toBe(true);
            expect(isFunction(Array.from)).toBe(true);
        });

        it('应该正确识别非函数', () => {
            expect(isFunction(null)).toBe(false);
            expect(isFunction(undefined)).toBe(false);
            expect(isFunction({})).toBe(false);
            expect(isFunction([])).toBe(false);
            expect(isFunction('function')).toBe(false);
            expect(isFunction(123)).toBe(false);
        });
    });

    describe('isObject', () => {
        it('应该正确识别对象', () => {
            expect(isObject({})).toBe(true);
            expect(isObject({ a: 1 })).toBe(true);
            expect(isObject(new Date())).toBe(true);
            expect(isObject(new RegExp(''))).toBe(true);
        });

        it('应该正确识别非对象', () => {
            expect(isObject(null)).toBe(false);
            expect(isObject(undefined)).toBe(false);
            expect(isObject([])).toBe(false);
            expect(isObject('object')).toBe(false);
            expect(isObject(123)).toBe(false);
            expect(isObject(true)).toBe(false);
        });
    });

    describe('isString', () => {
        it('应该正确识别字符串', () => {
            expect(isString('')).toBe(true);
            expect(isString('hello')).toBe(true);
            expect(isString(String(123))).toBe(true);
            expect(isString(`template`)).toBe(true);
        });

        it('应该正确识别非字符串', () => {
            expect(isString(null)).toBe(false);
            expect(isString(undefined)).toBe(false);
            expect(isString(123)).toBe(false);
            expect(isString(true)).toBe(false);
            expect(isString({})).toBe(false);
            expect(isString([])).toBe(false);
        });
    });

    describe('isNumber', () => {
        it('应该正确识别数字', () => {
            expect(isNumber(0)).toBe(true);
            expect(isNumber(123)).toBe(true);
            expect(isNumber(-123)).toBe(true);
            expect(isNumber(3.14)).toBe(true);
            expect(isNumber(Infinity)).toBe(true);
            expect(isNumber(-Infinity)).toBe(true);
        });

        it('应该正确识别非数字', () => {
            expect(isNumber(NaN)).toBe(false);
            expect(isNumber('123')).toBe(false);
            expect(isNumber(null)).toBe(false);
            expect(isNumber(undefined)).toBe(false);
            expect(isNumber(true)).toBe(false);
            expect(isNumber({})).toBe(false);
        });
    });

    describe('isBoolean', () => {
        it('应该正确识别布尔值', () => {
            expect(isBoolean(true)).toBe(true);
            expect(isBoolean(false)).toBe(true);
            expect(isBoolean(Boolean(1))).toBe(true);
        });

        it('应该正确识别非布尔值', () => {
            expect(isBoolean(1)).toBe(false);
            expect(isBoolean(0)).toBe(false);
            expect(isBoolean('true')).toBe(false);
            expect(isBoolean(null)).toBe(false);
            expect(isBoolean(undefined)).toBe(false);
        });
    });

    describe('isArray', () => {
        it('应该正确识别数组', () => {
            expect(isArray([])).toBe(true);
            expect(isArray([1, 2, 3])).toBe(true);
            expect(isArray(new Array(5))).toBe(true);
            expect(isArray(Array.from('hello'))).toBe(true);
        });

        it('应该正确识别非数组', () => {
            expect(isArray(null)).toBe(false);
            expect(isArray(undefined)).toBe(false);
            expect(isArray({})).toBe(false);
            expect(isArray('array')).toBe(false);
            // 在测试环境中 arguments 不可用，使用类数组对象代替
            expect(isArray({ length: 0 })).toBe(false);
        });
    });

    describe('isPromise', () => {
        it('应该正确识别Promise', () => {
            expect(isPromise(Promise.resolve())).toBe(true);
            expect(isPromise(Promise.reject().catch(() => { }))).toBe(true);
            expect(isPromise(new Promise(() => { }))).toBe(true);
            expect(isPromise({ then: () => { } })).toBe(true);
        });

        it('应该正确识别非Promise', () => {
            expect(isPromise(null)).toBe(false);
            expect(isPromise(undefined)).toBe(false);
            expect(isPromise({})).toBe(false);
            expect(isPromise([])).toBe(false);
            expect(isPromise('promise')).toBe(false);
            expect(isPromise(123)).toBe(false);
        });
    });

    describe('isNullOrUndefined', () => {
        it('应该正确识别null或undefined', () => {
            expect(isNullOrUndefined(null)).toBe(true);
            expect(isNullOrUndefined(undefined)).toBe(true);
        });

        it('应该正确识别非null和非undefined', () => {
            expect(isNullOrUndefined(0)).toBe(false);
            expect(isNullOrUndefined('')).toBe(false);
            expect(isNullOrUndefined(false)).toBe(false);
            expect(isNullOrUndefined({})).toBe(false);
            expect(isNullOrUndefined([])).toBe(false);
        });
    });

    describe('isEmpty', () => {
        it('应该正确识别空值', () => {
            expect(isEmpty(null)).toBe(true);
            expect(isEmpty(undefined)).toBe(true);
            expect(isEmpty('')).toBe(true);
            expect(isEmpty([])).toBe(true);
            expect(isEmpty({})).toBe(true);
        });

        it('应该正确识别非空值', () => {
            expect(isEmpty('hello')).toBe(false);
            expect(isEmpty([1])).toBe(false);
            expect(isEmpty({ a: 1 })).toBe(false);
            expect(isEmpty(0)).toBe(false);
            expect(isEmpty(false)).toBe(false);
        });
    });

    describe('isDate', () => {
        it('应该正确识别日期对象', () => {
            expect(isDate(new Date())).toBe(true);
            expect(isDate(new Date('2023-01-01'))).toBe(true);
        });

        it('应该正确识别无效日期', () => {
            expect(isDate(new Date('invalid'))).toBe(false);
        });

        it('应该正确识别非日期', () => {
            expect(isDate('2023-01-01')).toBe(false);
            expect(isDate(1672531200000)).toBe(false);
            expect(isDate(null)).toBe(false);
        });
    });

    describe('isRegExp', () => {
        it('应该正确识别正则表达式', () => {
            expect(isRegExp(/test/)).toBe(true);
            expect(isRegExp(new RegExp('test'))).toBe(true);
            expect(isRegExp(/test/gi)).toBe(true);
        });

        it('应该正确识别非正则表达式', () => {
            expect(isRegExp('/test/')).toBe(false);
            expect(isRegExp(null)).toBe(false);
            expect(isRegExp({})).toBe(false);
        });
    });

    describe('isError', () => {
        it('应该正确识别错误对象', () => {
            expect(isError(new Error())).toBe(true);
            expect(isError(new TypeError())).toBe(true);
            expect(isError(new ReferenceError())).toBe(true);
        });

        it('应该正确识别非错误对象', () => {
            expect(isError('error')).toBe(false);
            expect(isError({ message: 'error' })).toBe(false);
            expect(isError(null)).toBe(false);
        });
    });

    describe('getType', () => {
        it('应该正确获取类型字符串', () => {
            expect(getType(null)).toBe('null');
            expect(getType(undefined)).toBe('undefined');
            expect(getType('hello')).toBe('string');
            expect(getType(123)).toBe('number');
            expect(getType(true)).toBe('boolean');
            expect(getType({})).toBe('object');
            expect(getType([])).toBe('array');
            expect(getType(() => { })).toBe('function');
            expect(getType(new Date())).toBe('date');
            expect(getType(/test/)).toBe('regexp');
        });
    });

    describe('isSameType', () => {
        it('应该正确比较相同类型', () => {
            expect(isSameType('a', 'b')).toBe(true);
            expect(isSameType(1, 2)).toBe(true);
            expect(isSameType(true, false)).toBe(true);
            expect(isSameType([], {})).toBe(false);
            expect(isSameType(null, undefined)).toBe(false);
        });
    });


    describe('typeUtils 对象', () => {
        it('应该包含所有类型检查函数', () => {
            expect(typeUtils.isFunction).toBeDefined();
            expect(typeUtils.isObject).toBeDefined();
            expect(typeUtils.isString).toBeDefined();
            expect(typeUtils.isNumber).toBeDefined();
            expect(typeUtils.isBoolean).toBeDefined();
            expect(typeUtils.isArray).toBeDefined();
            expect(typeUtils.getType).toBeDefined();
            expect(typeof typeUtils.isFunction).toBe('function');
            expect(typeof typeUtils.isObject).toBe('function');
            expect(typeof typeUtils.isString).toBe('function');
        });
    });
});
