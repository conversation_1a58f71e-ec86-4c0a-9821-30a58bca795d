/**
 * @fileoverview 格式化工具函数测试
 */

import { formatBytes, formatTime, formatError } from '../src/format';
// 添加Jest类型定义
import type { jest } from '@jest/globals';

// 声明全局测试函数
declare global {
    const describe: (name: string, fn: () => void) => void;
    const it: (name: string, fn: () => void) => void;
    const expect: any;
    namespace jest {
        function fn<T extends (...args: any[]) => any>(implementation?: T): jest.Mock<ReturnType<T>, Parameters<T>>;
    }
    interface Mock<T = any, Y extends any[] = any[]> {
        (...args: Y): T;
    }
}

describe('格式化工具函数', () => {
    describe('formatBytes', () => {
        it('应该正确格式化字节数', () => {
            expect(formatBytes(0)).toBe('0 Bytes');
            expect(formatBytes(1024)).toBe('1.00 KB');
            expect(formatBytes(1048576)).toBe('1.00 MB');
            expect(formatBytes(1073741824)).toBe('1.00 GB');
        });

        it('应该支持自定义小数位数', () => {
            expect(formatBytes(1048576, 0)).toBe('1 MB');
            expect(formatBytes(1048576, 1)).toBe('1.0 MB');
            expect(formatBytes(1048576, 3)).toBe('1.000 MB');
        });
    });

    describe('formatTime', () => {
        it('应该正确格式化毫秒数', () => {
            expect(formatTime(500)).toBe('500ms');
            expect(formatTime(1000)).toBe('1.00s');
            expect(formatTime(60000)).toBe('1m');
            expect(formatTime(65000)).toBe('1m 5s');
        });
    });

    describe('formatError', () => {
        it('应该格式化Error对象', () => {
            const error = new Error('测试错误');
            const formatted = formatError(error);
            expect(formatted).toContain('Error: 测试错误');
            expect(formatted).toContain('at ');
        });

        it('应该格式化字符串错误', () => {
            expect(formatError('简单错误')).toBe('简单错误');
        });

        it('应该格式化对象错误', () => {
            const objError = { code: 500, message: '服务器错误' };
            expect(formatError(objError)).toContain('"code": 500');
            expect(formatError(objError)).toContain('"message": "服务器错误"');
        });

        it('应该处理非序列化对象', () => {
            const circularObj: any = {};
            circularObj.self = circularObj;
            expect(formatError(circularObj)).toContain('Non-serializable object');
        });

        it('应该包含上下文信息', () => {
            const error = new Error('测试错误');
            const context = { userId: 123, action: '登录' };
            const formatted = formatError(error, context);
            
            expect(formatted).toContain('Error: 测试错误');
            expect(formatted).toContain('Context:');
            expect(formatted).toContain('"userId": 123');
            expect(formatted).toContain('"action": "登录"');
        });

        it('应该处理非序列化上下文', () => {
            const error = new Error('测试错误');
            const circularContext: any = {};
            circularContext.self = circularContext;
            
            expect(formatError(error, circularContext)).toContain('Non-serializable context');
        });

        it('应该处理带有额外属性的Error对象', () => {
            const error = new Error('测试错误') as any;
            error.code = 'AUTH_FAILED';
            error.details = { reason: '令牌过期' };
            
            const formatted = formatError(error);
            expect(formatted).toContain('Error: 测试错误');
            expect(formatted).toContain('Additional properties:');
            expect(formatted).toContain('"code": "AUTH_FAILED"');
            expect(formatted).toContain('"details": {');
            expect(formatted).toContain('"reason": "令牌过期"');
        });
    });
});