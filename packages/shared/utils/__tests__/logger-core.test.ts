/**
 * @fileoverview 核心日志工具函数测试
 * @description 测试从 @micro-core/core 迁移的日志工具函数
 */

import { vi } from 'vitest';
import {
    coreLoggerUtils,
    createLogger,
    createSimpleLogger,
    logger,
    LOGGER_FUNCTIONS,
    validateLoggerFunctions
} from '../src/logger/core';

// Mock console to capture log output
const originalConsoleLog = console.log;
let logOutput: string[] = [];

beforeEach(() => {
    logOutput = [];
    console.log = vi.fn((...args) => {
        logOutput.push(args.join(' '));
    });
});

afterEach(() => {
    console.log = originalConsoleLog;
});

describe('Core Logger Functions', () => {
    describe('createLogger', () => {
        it('should create a logger with the specified namespace', () => {
            const testLogger = createLogger('TestModule');

            expect(testLogger).toBeDefined();
            expect(typeof testLogger.debug).toBe('function');
            expect(typeof testLogger.info).toBe('function');
            expect(typeof testLogger.warn).toBe('function');
            expect(typeof testLogger.error).toBe('function');
            expect(typeof testLogger.log).toBe('function');
        });

        it('should log messages with correct format', () => {
            const testLogger = createLogger('TestModule');

            testLogger.info('Test message');

            expect(logOutput).toHaveLength(1);
            expect(logOutput[0]).toMatch(/\[.*\] \[TestModule\] \[INFO\] Test message/);
        });

        it('should support different log levels', () => {
            const testLogger = createLogger('TestModule');

            testLogger.debug('Debug message');
            testLogger.info('Info message');
            testLogger.warn('Warn message');
            testLogger.error('Error message');
            testLogger.log('Log message');

            expect(logOutput).toHaveLength(5);
            expect(logOutput[0]).toContain('Debug message');
            expect(logOutput[1]).toContain('Info message');
            expect(logOutput[2]).toContain('Warn message');
            expect(logOutput[3]).toContain('Error message');
            expect(logOutput[4]).toContain('Log message');
        });

        it('should handle additional arguments', () => {
            const testLogger = createLogger('TestModule');

            testLogger.info('Message with data', { key: 'value' }, 123);

            expect(logOutput).toHaveLength(1);
            expect(logOutput[0]).toContain('Message with data');
            expect(logOutput[0]).toContain('[object Object]'); // console.log converts objects to [object Object]
            expect(logOutput[0]).toContain('123');
        });

        it('should create different loggers for different namespaces', () => {
            const logger1 = createLogger('Module1');
            const logger2 = createLogger('Module2');

            logger1.info('Message from module 1');
            logger2.info('Message from module 2');

            expect(logOutput).toHaveLength(2);
            expect(logOutput[0]).toContain('[Module1]');
            expect(logOutput[1]).toContain('[Module2]');
        });
    });

    describe('createSimpleLogger', () => {
        it('should create a simple logger with core-compatible interface', () => {
            const simpleLogger = createSimpleLogger('SimpleModule');

            expect(simpleLogger).toBeDefined();
            expect(typeof simpleLogger.debug).toBe('function');
            expect(typeof simpleLogger.info).toBe('function');
            expect(typeof simpleLogger.warn).toBe('function');
            expect(typeof simpleLogger.error).toBe('function');
            expect(typeof simpleLogger.log).toBe('function');
        });

        it('should log with timestamp and namespace format', () => {
            const simpleLogger = createSimpleLogger('SimpleModule');

            simpleLogger.info('Simple test message');

            expect(logOutput).toHaveLength(1);
            const logMessage = logOutput[0];

            // Check format: [timestamp] [namespace] [level] message
            expect(logMessage).toMatch(/^\[.*\] \[SimpleModule\] \[INFO\] Simple test message/);
        });

        it('should handle all log levels correctly', () => {
            const simpleLogger = createSimpleLogger('SimpleModule');

            simpleLogger.debug('Debug message');
            simpleLogger.info('Info message');
            simpleLogger.warn('Warn message');
            simpleLogger.error('Error message');
            simpleLogger.log('Log message');

            expect(logOutput).toHaveLength(5);
            expect(logOutput[0]).toContain('[DEBUG]');
            expect(logOutput[1]).toContain('[INFO]');
            expect(logOutput[2]).toContain('[WARN]');
            expect(logOutput[3]).toContain('[ERROR]');
            expect(logOutput[4]).toContain('[INFO]'); // log() maps to INFO level
        });

        it('should be compatible with core logger interface', () => {
            const simpleLogger = createSimpleLogger('SimpleModule');
            const regularLogger = createLogger('RegularModule');

            // Both should have the same interface
            const simpleLoggerKeys = Object.keys(simpleLogger).sort();
            const regularLoggerKeys = Object.keys(regularLogger).sort();

            // Note: regularLogger might have additional properties from the adapter
            // so we check that simpleLogger has all the required methods
            expect(typeof simpleLogger.debug).toBe('function');
            expect(typeof simpleLogger.info).toBe('function');
            expect(typeof simpleLogger.warn).toBe('function');
            expect(typeof simpleLogger.error).toBe('function');
            expect(typeof simpleLogger.log).toBe('function');
        });
    });

    describe('default logger', () => {
        it('should provide a default logger instance', () => {
            expect(logger).toBeDefined();
            expect(typeof logger.info).toBe('function');
        });

        it('should use MicroCore as default namespace', () => {
            logger.info('Default logger test');

            expect(logOutput).toHaveLength(1);
            expect(logOutput[0]).toContain('[MicroCore]');
        });
    });

    describe('coreLoggerUtils', () => {
        it('should contain all logger utility functions', () => {
            expect(typeof coreLoggerUtils.createLogger).toBe('function');
            expect(coreLoggerUtils.logger).toBeDefined();
        });

        it('should have the same functions as individual exports', () => {
            expect(coreLoggerUtils.createLogger).toBe(createLogger);
            expect(coreLoggerUtils.logger).toBe(logger);
        });
    });

    describe('LOGGER_FUNCTIONS', () => {
        it('should contain all function names', () => {
            expect(LOGGER_FUNCTIONS).toEqual(['createLogger']);
        });

        it('should be readonly', () => {
            expect(Array.isArray(LOGGER_FUNCTIONS)).toBe(true);
            expect(LOGGER_FUNCTIONS).toHaveLength(1);
        });
    });

    describe('validateLoggerFunctions', () => {
        it('should validate all functions are available', () => {
            const result = validateLoggerFunctions();

            expect(result.allAvailable).toBe(true);
            expect(result.missing).toEqual([]);
            expect(result.available).toEqual(['createLogger']);
        });
    });

    describe('Core Logger Interface Compatibility', () => {
        it('should match exact behavior from core package', () => {
            const testLogger = createLogger('TestModule');

            // Test the exact interface that was in core package
            testLogger.debug('Debug test');
            testLogger.info('Info test');
            testLogger.warn('Warn test');
            testLogger.error('Error test');
            testLogger.log('Log test');

            expect(logOutput).toHaveLength(5);

            // All messages should contain the namespace
            logOutput.forEach(message => {
                expect(message).toContain('[TestModule]');
            });
        });

        it('should handle the same argument patterns as core package', () => {
            const testLogger = createLogger('TestModule');

            // Test various argument patterns that were supported in core
            testLogger.info('Simple message');
            testLogger.info('Message with object', { key: 'value' });
            testLogger.info('Message with multiple args', 'arg1', 'arg2', 123);
            testLogger.error('Error with error object', new Error('Test error'));

            expect(logOutput).toHaveLength(4);

            // Check that all arguments are included in the output
            expect(logOutput[1]).toContain('[object Object]'); // console.log converts objects to [object Object]
            expect(logOutput[2]).toContain('arg1');
            expect(logOutput[2]).toContain('arg2');
            expect(logOutput[2]).toContain('123');
            expect(logOutput[3]).toContain('Test error');
        });

        it('should create loggers with consistent behavior', () => {
            const logger1 = createLogger('Module1');
            const logger2 = createLogger('Module1'); // Same namespace
            const logger3 = createLogger('Module2'); // Different namespace

            logger1.info('Message 1');
            logger2.info('Message 2');
            logger3.info('Message 3');

            expect(logOutput).toHaveLength(3);
            expect(logOutput[0]).toContain('[Module1]');
            expect(logOutput[1]).toContain('[Module1]');
            expect(logOutput[2]).toContain('[Module2]');
        });
    });

    describe('Performance', () => {
        it('should be performant for many log calls', () => {
            const testLogger = createLogger('PerfTest');
            const start = performance.now();

            for (let i = 0; i < 1000; i++) {
                testLogger.info(`Message ${i}`);
            }

            const end = performance.now();
            expect(end - start).toBeLessThan(1000); // Should complete in less than 1 second
            expect(logOutput).toHaveLength(1000);
        });

        it('should handle large messages efficiently', () => {
            const testLogger = createLogger('PerfTest');
            const largeMessage = 'x'.repeat(10000);

            const start = performance.now();
            testLogger.info(largeMessage);
            const end = performance.now();

            expect(end - start).toBeLessThan(100); // Should complete quickly
            expect(logOutput[0]).toContain(largeMessage);
        });
    });

    describe('Error Handling', () => {
        it('should handle undefined and null arguments gracefully', () => {
            const testLogger = createLogger('ErrorTest');

            expect(() => {
                testLogger.info('Message with null', null);
                testLogger.info('Message with undefined', undefined);
                testLogger.info('Message with empty object', {});
            }).not.toThrow();

            expect(logOutput).toHaveLength(3);
        });

        it('should handle circular references in objects', () => {
            const testLogger = createLogger('ErrorTest');
            const circular: any = { name: 'test' };
            circular.self = circular;

            expect(() => {
                testLogger.info('Message with circular reference', circular);
            }).not.toThrow();

            expect(logOutput).toHaveLength(1);
        });

        it('should handle very long argument lists', () => {
            const testLogger = createLogger('ErrorTest');
            const manyArgs = Array.from({ length: 100 }, (_, i) => `arg${i}`);

            expect(() => {
                testLogger.info('Message with many args', ...manyArgs);
            }).not.toThrow();

            expect(logOutput).toHaveLength(1);
            expect(logOutput[0]).toContain('arg0');
            expect(logOutput[0]).toContain('arg99');
        });
    });
});