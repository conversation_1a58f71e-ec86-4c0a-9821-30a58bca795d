/**
 * @fileoverview 简化的 URL 工具函数兼容性测试
 */

import { isValidUrl } from '../src/url/core';

describe('URL Core Simple Test', () => {
    it('should work with basic URLs', () => {
        console.log('Testing isValidUrl function:');
        console.log('isValidUrl function:', isValidUrl.toString());

        const result1 = isValidUrl('https://example.com');
        console.log('https://example.com ->', result1);
        expect(result1).toBe(true);

        const result2 = isValidUrl('http://example.com');
        console.log('http://example.com ->', result2);
        expect(result2).toBe(true);

        const result3 = isValidUrl('invalid-url');
        console.log('invalid-url ->', result3);
        expect(result3).toBe(false);

        const result4 = isValidUrl('');
        console.log('empty string ->', result4);
        expect(result4).toBe(false);
    });
});