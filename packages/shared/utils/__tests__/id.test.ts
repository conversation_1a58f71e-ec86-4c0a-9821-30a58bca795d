/**
 * @fileoverview ID生成工具函数测试
 */

import { generateId } from '../src/id';
// 添加Jest类型定义
import type { jest } from '@jest/globals';

// 声明全局测试函数
declare global {
    const describe: (name: string, fn: () => void) => void;
    const it: (name: string, fn: () => void) => void;
    const expect: any;
    namespace jest {
        function fn<T extends (...args: any[]) => any>(implementation?: T): jest.Mock<ReturnType<T>, Parameters<T>>;
    }
    interface Mock<T = any, Y extends any[] = any[]> {
        (...args: Y): T;
    }
}

describe('ID生成工具函数', () => {
    describe('generateId', () => {
        it('应该生成包含前缀的唯一ID', () => {
            const id = generateId('test-prefix');
            expect(id).toMatch(/^test-prefix_\d+_[a-z0-9]{6}$/);
        });

        it('不提供前缀时应使用默认前缀', () => {
            const id = generateId();
            expect(id).toMatch(/^micro-app_\d+_[a-z0-9]{6}$/);
        });

        it('生成的ID应该是唯一的', () => {
            const id1 = generateId();
            const id2 = generateId();
            expect(id1).not.toEqual(id2);
        });

        it('应该包含时间戳部分', () => {
            const now = Date.now();
            const id = generateId();
            const parts = id.split('_');
            const timestamp = parseInt(parts[1], 10);
            
            // 时间戳应该接近当前时间（允许1秒误差）
            expect(Math.abs(timestamp - now)).toBeLessThan(1000);
        });
    });
});