/**
 * @fileoverview 共享常量测试
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { describe, expect, it } from 'vitest';
import {
    AppStatus,
    BROWSER_FEATURES,
    CONFIG,
    constants,
    CSS_PREFIXES,
    DATA_ATTRIBUTES,
    DEFAULT_APP_CONFIG,
    ENV_KEYS,
    ErrorCodes,
    EventTypes,
    HTTP_STATUS,
    META_KEYS,
    MIME_TYPES,
    REGEX,
    SHARED_CONFIG,
    SharedErrorCodes,
    SharedEventTypes,
    STORAGE_KEYS
} from '../src';

describe('共享常量测试', () => {
    describe('AppStatus 枚举', () => {
        it('应该包含所有应用状态', () => {
            expect(AppStatus.NOT_LOADED).toBe('NOT_LOADED');
            expect(AppStatus.LOADING_SOURCE_CODE).toBe('LOADING_SOURCE_CODE');
            expect(AppStatus.NOT_BOOTSTRAPPED).toBe('NOT_BOOTSTRAPPED');
            expect(AppStatus.BOOTSTRAPPING).toBe('BOOTSTRAPPING');
            expect(AppStatus.NOT_MOUNTED).toBe('NOT_MOUNTED');
            expect(AppStatus.MOUNTING).toBe('MOUNTING');
            expect(AppStatus.MOUNTED).toBe('MOUNTED');
            expect(AppStatus.UNMOUNTING).toBe('UNMOUNTING');
            expect(AppStatus.UPDATING).toBe('UPDATING');
            expect(AppStatus.LOAD_ERROR).toBe('LOAD_ERROR');
            expect(AppStatus.SKIP_BECAUSE_BROKEN).toBe('SKIP_BECAUSE_BROKEN');
        });

        it('应该有正确的状态数量', () => {
            const statusCount = Object.keys(AppStatus).length;
            expect(statusCount).toBe(11);
        });

        it('所有状态值应该是字符串', () => {
            Object.values(AppStatus).forEach(status => {
                expect(typeof status).toBe('string');
                expect(status.length).toBeGreaterThan(0);
            });
        });
    });

    describe('ErrorCodes 枚举', () => {
        it('应该包含系统级错误代码 (1000-1999)', () => {
            expect(ErrorCodes.SYSTEM_INIT_FAILED).toBe(1000);
            expect(ErrorCodes.SYSTEM_CONFIG_ERROR).toBe(1001);
            expect(ErrorCodes.SYSTEM_RESOURCE_ERROR).toBe(1002);
        });

        it('应该包含应用级错误代码 (2000-2999)', () => {
            expect(ErrorCodes.APP_LOAD_FAILED).toBe(2000);
            expect(ErrorCodes.APP_BOOTSTRAP_FAILED).toBe(2001);
            expect(ErrorCodes.APP_MOUNT_FAILED).toBe(2002);
            expect(ErrorCodes.APP_UNMOUNT_FAILED).toBe(2003);
            expect(ErrorCodes.APP_UPDATE_FAILED).toBe(2004);
            expect(ErrorCodes.APP_CONFIG_INVALID).toBe(2005);
        });

        it('应该包含网络级错误代码 (3000-3999)', () => {
            expect(ErrorCodes.NETWORK_CONNECTION_FAILED).toBe(3000);
            expect(ErrorCodes.NETWORK_TIMEOUT).toBe(3001);
            expect(ErrorCodes.RESOURCE_LOAD_FAILED).toBe(3002);
            expect(ErrorCodes.CORS_ERROR).toBe(3003);
        });

        it('应该包含权限级错误代码 (4000-4999)', () => {
            expect(ErrorCodes.PERMISSION_DENIED).toBe(4000);
            expect(ErrorCodes.AUTHENTICATION_FAILED).toBe(4001);
            expect(ErrorCodes.AUTHORIZATION_FAILED).toBe(4002);
        });

        it('应该包含验证级错误代码 (5000-5999)', () => {
            expect(ErrorCodes.VALIDATION_FAILED).toBe(5000);
            expect(ErrorCodes.DATA_FORMAT_ERROR).toBe(5001);
            expect(ErrorCodes.TYPE_CHECK_FAILED).toBe(5002);
        });

        it('错误代码应该在正确的范围内', () => {
            Object.values(ErrorCodes).forEach(code => {
                expect(typeof code).toBe('number');
                expect(code).toBeGreaterThanOrEqual(1000);
                expect(code).toBeLessThan(6000);
            });
        });
    });

    describe('EventTypes 枚举', () => {
        it('应该包含系统事件', () => {
            expect(EventTypes.SYSTEM_INIT_START).toBe('system:init:start');
            expect(EventTypes.SYSTEM_INIT_COMPLETE).toBe('system:init:complete');
            expect(EventTypes.SYSTEM_INIT_FAILED).toBe('system:init:failed');
            expect(EventTypes.SYSTEM_DESTROY).toBe('system:destroy');
            expect(EventTypes.SYSTEM_ERROR).toBe('system:error');
        });

        it('应该包含应用事件', () => {
            expect(EventTypes.APP_REGISTER).toBe('app:register');
            expect(EventTypes.APP_UNREGISTER).toBe('app:unregister');
            expect(EventTypes.APP_STATUS_CHANGE).toBe('app:status:change');
            expect(EventTypes.APP_LOAD_START).toBe('app:load:start');
            expect(EventTypes.APP_LOAD_COMPLETE).toBe('app:load:complete');
        });

        it('应该包含路由事件', () => {
            expect(EventTypes.ROUTE_CHANGE_START).toBe('route:change:start');
            expect(EventTypes.ROUTE_CHANGE_COMPLETE).toBe('route:change:complete');
            expect(EventTypes.ROUTE_CHANGE_FAILED).toBe('route:change:failed');
        });

        it('事件类型应该遵循命名约定', () => {
            Object.values(EventTypes).forEach(eventType => {
                expect(typeof eventType).toBe('string');
                expect(eventType).toMatch(/^[a-z]+:[a-z:]+$/);
            });
        });
    });

    describe('HTTP_STATUS 常量', () => {
        it('应该包含成功状态码', () => {
            expect(HTTP_STATUS.OK).toBe(200);
            expect(HTTP_STATUS.CREATED).toBe(201);
            expect(HTTP_STATUS.ACCEPTED).toBe(202);
            expect(HTTP_STATUS.NO_CONTENT).toBe(204);
        });

        it('应该包含重定向状态码', () => {
            expect(HTTP_STATUS.MOVED_PERMANENTLY).toBe(301);
            expect(HTTP_STATUS.FOUND).toBe(302);
            expect(HTTP_STATUS.NOT_MODIFIED).toBe(304);
        });

        it('应该包含客户端错误状态码', () => {
            expect(HTTP_STATUS.BAD_REQUEST).toBe(400);
            expect(HTTP_STATUS.UNAUTHORIZED).toBe(401);
            expect(HTTP_STATUS.FORBIDDEN).toBe(403);
            expect(HTTP_STATUS.NOT_FOUND).toBe(404);
        });

        it('应该包含服务器错误状态码', () => {
            expect(HTTP_STATUS.INTERNAL_SERVER_ERROR).toBe(500);
            expect(HTTP_STATUS.NOT_IMPLEMENTED).toBe(501);
            expect(HTTP_STATUS.BAD_GATEWAY).toBe(502);
            expect(HTTP_STATUS.SERVICE_UNAVAILABLE).toBe(503);
        });

        it('所有状态码应该是有效的 HTTP 状态码', () => {
            Object.values(HTTP_STATUS).forEach(status => {
                expect(typeof status).toBe('number');
                expect(status).toBeGreaterThanOrEqual(200);
                expect(status).toBeLessThan(600);
            });
        });
    });

    describe('CONFIG 配置常量', () => {
        it('应该包含超时配置', () => {
            expect(CONFIG.TIMEOUT.DEFAULT).toBe(30000);
            expect(CONFIG.TIMEOUT.SHORT).toBe(5000);
            expect(CONFIG.TIMEOUT.LONG).toBe(60000);
            expect(CONFIG.TIMEOUT.RESOURCE_LOAD).toBe(10000);
            expect(CONFIG.TIMEOUT.APP_BOOTSTRAP).toBe(15000);
            expect(CONFIG.TIMEOUT.APP_MOUNT).toBe(10000);
        });

        it('应该包含重试配置', () => {
            expect(CONFIG.RETRY.MAX_ATTEMPTS).toBe(3);
            expect(CONFIG.RETRY.RESOURCE_LOAD).toBe(2);
            expect(CONFIG.RETRY.NETWORK_REQUEST).toBe(3);
            expect(CONFIG.RETRY.DELAY_BASE).toBe(1000);
            expect(CONFIG.RETRY.MAX_DELAY).toBe(10000);
        });

        it('应该包含缓存配置', () => {
            expect(CONFIG.CACHE.DEFAULT_SIZE).toBe(100);
            expect(CONFIG.CACHE.MAX_SIZE).toBe(1000);
            expect(CONFIG.CACHE.DEFAULT_TTL).toBe(3600000);
            expect(CONFIG.CACHE.SHORT_TTL).toBe(300000);
            expect(CONFIG.CACHE.LONG_TTL).toBe(86400000);
        });

        it('应该包含性能配置', () => {
            expect(CONFIG.PERFORMANCE.MEMORY_WARNING_THRESHOLD).toBe(80);
            expect(CONFIG.PERFORMANCE.MEMORY_CRITICAL_THRESHOLD).toBe(90);
            expect(CONFIG.PERFORMANCE.MONITOR_INTERVAL).toBe(5000);
            expect(CONFIG.PERFORMANCE.MAX_CONCURRENT_REQUESTS).toBe(10);
        });

        it('应该包含日志配置', () => {
            expect(CONFIG.LOG.MAX_ENTRIES).toBe(1000);
            expect(CONFIG.LOG.CLEANUP_INTERVAL).toBe(600000);
            expect(CONFIG.LOG.COLORS.DEBUG).toBe('#6B7280');
            expect(CONFIG.LOG.COLORS.INFO).toBe('#3B82F6');
            expect(CONFIG.LOG.COLORS.WARN).toBe('#F59E0B');
            expect(CONFIG.LOG.COLORS.ERROR).toBe('#EF4444');
        });

        it('配置值应该是合理的', () => {
            // 超时时间应该是正数
            Object.values(CONFIG.TIMEOUT).forEach(timeout => {
                expect(timeout).toBeGreaterThan(0);
            });

            // 重试次数应该是正数
            expect(CONFIG.RETRY.MAX_ATTEMPTS).toBeGreaterThan(0);
            expect(CONFIG.RETRY.RESOURCE_LOAD).toBeGreaterThan(0);
            expect(CONFIG.RETRY.NETWORK_REQUEST).toBeGreaterThan(0);

            // 缓存大小应该是正数
            expect(CONFIG.CACHE.DEFAULT_SIZE).toBeGreaterThan(0);
            expect(CONFIG.CACHE.MAX_SIZE).toBeGreaterThan(CONFIG.CACHE.DEFAULT_SIZE);
        });
    });

    describe('REGEX 正则表达式常量', () => {
        it('URL 正则应该正确验证 URL', () => {
            expect(REGEX.URL.test('https://example.com')).toBe(true);
            expect(REGEX.URL.test('http://localhost:3000')).toBe(true);
            expect(REGEX.URL.test('invalid-url')).toBe(false);
            expect(REGEX.URL.test('')).toBe(false);
        });

        it('邮箱正则应该正确验证邮箱', () => {
            expect(REGEX.EMAIL.test('<EMAIL>')).toBe(true);
            expect(REGEX.EMAIL.test('<EMAIL>')).toBe(true);
            expect(REGEX.EMAIL.test('invalid-email')).toBe(false);
            expect(REGEX.EMAIL.test('@example.com')).toBe(false);
        });

        it('手机号正则应该正确验证中国手机号', () => {
            expect(REGEX.PHONE_CN.test('13812345678')).toBe(true);
            expect(REGEX.PHONE_CN.test('15987654321')).toBe(true);
            expect(REGEX.PHONE_CN.test('12345678901')).toBe(false);
            expect(REGEX.PHONE_CN.test('1381234567')).toBe(false);
        });

        it('IPv4 正则应该正确验证 IP 地址', () => {
            expect(REGEX.IPV4.test('***********')).toBe(true);
            expect(REGEX.IPV4.test('127.0.0.1')).toBe(true);
            expect(REGEX.IPV4.test('256.1.1.1')).toBe(false);
            expect(REGEX.IPV4.test('192.168.1')).toBe(false);
        });

        it('语义化版本正则应该正确验证版本号', () => {
            expect(REGEX.SEMVER.test('1.0.0')).toBe(true);
            expect(REGEX.SEMVER.test('1.2.3-alpha.1')).toBe(true);
            expect(REGEX.SEMVER.test('2.0.0+build.123')).toBe(true);
            expect(REGEX.SEMVER.test('1.0')).toBe(false);
            expect(REGEX.SEMVER.test('v1.0.0')).toBe(false);
        });

        it('应用名称正则应该正确验证应用名', () => {
            expect(REGEX.APP_NAME.test('myApp')).toBe(true);
            expect(REGEX.APP_NAME.test('my-app')).toBe(true);
            expect(REGEX.APP_NAME.test('my_app')).toBe(true);
            expect(REGEX.APP_NAME.test('123app')).toBe(false);
            expect(REGEX.APP_NAME.test('-myapp')).toBe(false);
        });
    });

    describe('MIME_TYPES 常量', () => {
        it('应该包含文本类型', () => {
            expect(MIME_TYPES.TEXT_PLAIN).toBe('text/plain');
            expect(MIME_TYPES.TEXT_HTML).toBe('text/html');
            expect(MIME_TYPES.TEXT_CSS).toBe('text/css');
            expect(MIME_TYPES.TEXT_JAVASCRIPT).toBe('text/javascript');
        });

        it('应该包含应用类型', () => {
            expect(MIME_TYPES.APPLICATION_JSON).toBe('application/json');
            expect(MIME_TYPES.APPLICATION_XML).toBe('application/xml');
            expect(MIME_TYPES.APPLICATION_PDF).toBe('application/pdf');
        });

        it('应该包含图片类型', () => {
            expect(MIME_TYPES.IMAGE_JPEG).toBe('image/jpeg');
            expect(MIME_TYPES.IMAGE_PNG).toBe('image/png');
            expect(MIME_TYPES.IMAGE_GIF).toBe('image/gif');
            expect(MIME_TYPES.IMAGE_SVG).toBe('image/svg+xml');
        });

        it('所有 MIME 类型应该符合标准格式', () => {
            Object.values(MIME_TYPES).forEach(mimeType => {
                expect(typeof mimeType).toBe('string');
                expect(mimeType).toMatch(/^[a-z]+\/[a-z0-9\-\+\.]+$/);
            });
        });
    });

    describe('BROWSER_FEATURES 常量', () => {
        it('应该包含存储特性', () => {
            expect(BROWSER_FEATURES.STORAGE).toContain('localStorage');
            expect(BROWSER_FEATURES.STORAGE).toContain('sessionStorage');
            expect(BROWSER_FEATURES.STORAGE).toContain('indexedDB');
        });

        it('应该包含网络特性', () => {
            expect(BROWSER_FEATURES.NETWORK).toContain('fetch');
            expect(BROWSER_FEATURES.NETWORK).toContain('XMLHttpRequest');
            expect(BROWSER_FEATURES.NETWORK).toContain('WebSocket');
        });

        it('应该包含 Worker 特性', () => {
            expect(BROWSER_FEATURES.WORKERS).toContain('Worker');
            expect(BROWSER_FEATURES.WORKERS).toContain('ServiceWorker');
            expect(BROWSER_FEATURES.WORKERS).toContain('SharedWorker');
        });

        it('特性数组应该是只读的', () => {
            expect(Array.isArray(BROWSER_FEATURES.STORAGE)).toBe(true);
            expect(Array.isArray(BROWSER_FEATURES.NETWORK)).toBe(true);
            expect(Array.isArray(BROWSER_FEATURES.WORKERS)).toBe(true);
        });
    });

    describe('DEFAULT_APP_CONFIG 常量', () => {
        it('应该包含所有必需的配置项', () => {
            expect(DEFAULT_APP_CONFIG).toHaveProperty('name');
            expect(DEFAULT_APP_CONFIG).toHaveProperty('entry');
            expect(DEFAULT_APP_CONFIG).toHaveProperty('container');
            expect(DEFAULT_APP_CONFIG).toHaveProperty('activeRule');
            expect(DEFAULT_APP_CONFIG).toHaveProperty('props');
            expect(DEFAULT_APP_CONFIG).toHaveProperty('loader');
            expect(DEFAULT_APP_CONFIG).toHaveProperty('hooks');
        });

        it('加载器配置应该有合理的默认值', () => {
            expect(DEFAULT_APP_CONFIG.loader.timeout).toBe(CONFIG.TIMEOUT.RESOURCE_LOAD);
            expect(DEFAULT_APP_CONFIG.loader.retries).toBe(CONFIG.RETRY.RESOURCE_LOAD);
            expect(DEFAULT_APP_CONFIG.loader.cache).toBe(true);
        });

        it('生命周期钩子应该初始化为 null', () => {
            expect(DEFAULT_APP_CONFIG.hooks.beforeLoad).toBeNull();
            expect(DEFAULT_APP_CONFIG.hooks.afterLoad).toBeNull();
            expect(DEFAULT_APP_CONFIG.hooks.beforeMount).toBeNull();
            expect(DEFAULT_APP_CONFIG.hooks.afterMount).toBeNull();
            expect(DEFAULT_APP_CONFIG.hooks.beforeUnmount).toBeNull();
            expect(DEFAULT_APP_CONFIG.hooks.afterUnmount).toBeNull();
        });
    });

    describe('存储和环境常量', () => {
        it('ENV_KEYS 应该包含环境变量键名', () => {
            expect(ENV_KEYS.NODE_ENV).toBe('NODE_ENV');
            expect(ENV_KEYS.DEBUG).toBe('DEBUG');
            expect(ENV_KEYS.API_BASE_URL).toBe('API_BASE_URL');
            expect(ENV_KEYS.APP_VERSION).toBe('APP_VERSION');
            expect(ENV_KEYS.BUILD_TIME).toBe('BUILD_TIME');
        });

        it('STORAGE_KEYS 应该包含存储键名', () => {
            expect(STORAGE_KEYS.APP_CONFIG).toBe('micro-core:app-config');
            expect(STORAGE_KEYS.USER_SETTINGS).toBe('micro-core:user-settings');
            expect(STORAGE_KEYS.CACHE_DATA).toBe('micro-core:cache');
            expect(STORAGE_KEYS.LOG_DATA).toBe('micro-core:logs');
            expect(STORAGE_KEYS.PERFORMANCE_DATA).toBe('micro-core:performance');
        });

        it('存储键名应该有统一的前缀', () => {
            Object.values(STORAGE_KEYS).forEach(key => {
                expect(key).toMatch(/^micro-core:/);
            });
        });
    });

    describe('CSS 和 DOM 常量', () => {
        it('CSS_PREFIXES 应该包含 CSS 类名前缀', () => {
            expect(CSS_PREFIXES.MICRO_APP).toBe('micro-app');
            expect(CSS_PREFIXES.LOADING).toBe('micro-loading');
            expect(CSS_PREFIXES.ERROR).toBe('micro-error');
            expect(CSS_PREFIXES.HIDDEN).toBe('micro-hidden');
        });

        it('DATA_ATTRIBUTES 应该包含数据属性名', () => {
            expect(DATA_ATTRIBUTES.APP_NAME).toBe('data-micro-app');
            expect(DATA_ATTRIBUTES.APP_STATUS).toBe('data-micro-status');
            expect(DATA_ATTRIBUTES.APP_VERSION).toBe('data-micro-version');
            expect(DATA_ATTRIBUTES.CONTAINER).toBe('data-micro-container');
        });

        it('数据属性应该符合 HTML 标准', () => {
            Object.values(DATA_ATTRIBUTES).forEach(attr => {
                expect(attr).toMatch(/^data-[a-z-]+$/);
            });
        });

        it('META_KEYS 应该包含元数据键名', () => {
            expect(META_KEYS.APP_META).toBe('__MICRO_APP_META__');
            expect(META_KEYS.GLOBAL_CONFIG).toBe('__MICRO_GLOBAL_CONFIG__');
            expect(META_KEYS.SHARED_DATA).toBe('__MICRO_SHARED_DATA__');
            expect(META_KEYS.EVENT_BUS).toBe('__MICRO_EVENT_BUS__');
        });
    });

    describe('常量集合和向后兼容性', () => {
        it('constants 对象应该包含所有常量', () => {
            expect(constants.AppStatus).toBe(AppStatus);
            expect(constants.ErrorCodes).toBe(ErrorCodes);
            expect(constants.EventTypes).toBe(EventTypes);
            expect(constants.HTTP_STATUS).toBe(HTTP_STATUS);
            expect(constants.CONFIG).toBe(CONFIG);
            expect(constants.REGEX).toBe(REGEX);
            expect(constants.MIME_TYPES).toBe(MIME_TYPES);
            expect(constants.BROWSER_FEATURES).toBe(BROWSER_FEATURES);
            expect(constants.DEFAULT_APP_CONFIG).toBe(DEFAULT_APP_CONFIG);
        });

        it('向后兼容的导出应该正确', () => {
            expect(SharedErrorCodes).toBe(ErrorCodes);
            expect(SharedEventTypes).toBe(EventTypes);
            expect(SHARED_CONFIG).toBe(CONFIG);
        });
    });

    describe('常量不变性测试', () => {
        it('枚举值不应该被修改', () => {
            const originalValue = AppStatus.NOT_LOADED;
            expect(() => {
                (AppStatus as any).NOT_LOADED = 'MODIFIED';
            }).toThrow();
            expect(AppStatus.NOT_LOADED).toBe(originalValue);
        });

        it('配置对象应该是深度只读的', () => {
            expect(() => {
                (CONFIG.TIMEOUT as any).DEFAULT = 999999;
            }).toThrow();
        });

        it('正则表达式应该是不可变的', () => {
            const originalRegex = REGEX.URL;
            expect(() => {
                (REGEX as any).URL = /modified/;
            }).toThrow();
            expect(REGEX.URL).toBe(originalRegex);
        });
    });

    describe('性能和内存测试', () => {
        it('常量访问应该有良好的性能', () => {
            const startTime = performance.now();

            for (let i = 0; i < 10000; i++) {
                const _ = AppStatus.MOUNTED;
                const __ = CONFIG.TIMEOUT.DEFAULT;
                const ___ = HTTP_STATUS.OK;
            }

            const endTime = performance.now();
            const duration = endTime - startTime;

            // 10000次访问应该在合理时间内完成
            expect(duration).toBeLessThan(10); // 10ms
        });

        it('常量对象不应该占用过多内存', () => {
            const constantsSize = JSON.stringify(constants).length;

            // 常量序列化后不应该超过 50KB
            expect(constantsSize).toBeLessThan(50000);
        });
    });
});