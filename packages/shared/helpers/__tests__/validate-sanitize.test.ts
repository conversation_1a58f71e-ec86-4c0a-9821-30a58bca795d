/**
 * @fileoverview 数据验证和清理函数测试
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { validateAndSanitize } from '../src/index';

describe('数据验证和清理函数', () => {
    beforeEach(() => {
        vi.resetAllMocks();
    });

    it('应该验证有效数据', () => {
        const data = {
            name: '张三',
            age: 30,
            email: '<EMAIL>'
        };

        const rules = {
            name: { required: true, type: 'string' as const },
            age: { required: true, type: 'number' as const, min: 0 },
            email: { required: true, type: 'string' as const, pattern: /^.+@.+\..+$/ }
        };

        const result = validateAndSanitize(data, rules);

        expect(result.valid).toBe(true);
        expect(result.errors).toEqual([]);
        expect(result.sanitized).toEqual(data);
    });

    it('应该检测验证错误', () => {
        const data = {
            name: '',
            age: -1,
            email: 'invalid-email'
        };

        const rules = {
            name: { required: true, type: 'string' as const, minLength: 1 },
            age: { required: true, type: 'number' as const, min: 0 },
            email: { required: true, type: 'string' as const, pattern: /^.+@.+\..+$/ }
        };

        const result = validateAndSanitize(data, rules);

        expect(result.valid).toBe(false);
        expect(result.errors).toContain("字段 'name' 长度不能少于 1 个字符");
        expect(result.errors).toContain("字段 'age' 不能小于 0");
        expect(result.errors).toContain("字段 'email' 格式不正确");
    });

    it('应该清理字符串数据', () => {
        const data = {
            name: '  张三<script>alert("XSS")</script>  ',
            email: ' <EMAIL> '
        };

        const rules = {
            name: { type: 'string' as const },
            email: { type: 'string' as const }
        };

        const result = validateAndSanitize(data, rules, { sanitize: true });

        expect(result.sanitized?.name).toBe('张三');
        expect(result.sanitized?.email).toBe('<EMAIL>');
    });

    it('应该支持自定义验证', () => {
        const data = {
            password: '123456',
            confirmPassword: '654321'
        };

        const rules = {
            password: { type: 'string' as const, minLength: 6 },
            confirmPassword: {
                type: 'string' as const,
                custom: (value: string, allValues: any) =>
                    value === allValues.password || '两次输入的密码不一致'
            }
        };

        const result = validateAndSanitize(data, rules);

        expect(result.valid).toBe(false);
        expect(result.errors).toContain('两次输入的密码不一致');
    });

    it('应该验证必填字段', () => {
        const data = {
            name: '张三'
        };

        const rules = {
            name: { required: true, type: 'string' as const },
            age: { required: true, type: 'number' as const }
        };

        const result = validateAndSanitize(data, rules);

        expect(result.valid).toBe(false);
        expect(result.errors).toContain("字段 'age' 是必填项");
    });

    it('应该验证字段类型', () => {
        const data = {
            name: 123,
            age: '三十'
        };

        const rules = {
            name: { type: 'string' as const },
            age: { type: 'number' as const }
        };

        const result = validateAndSanitize(data, rules);

        expect(result.valid).toBe(false);
        expect(result.errors).toContain("字段 'name' 应该是 string 类型");
        expect(result.errors).toContain("字段 'age' 应该是 number 类型");
    });

    it('应该验证数值范围', () => {
        const data = {
            age: 150,
            score: -10
        };

        const rules = {
            age: { type: 'number' as const, min: 0, max: 120 },
            score: { type: 'number' as const, min: 0, max: 100 }
        };

        const result = validateAndSanitize(data, rules);

        expect(result.valid).toBe(false);
        expect(result.errors).toContain("字段 'age' 不能大于 120");
        expect(result.errors).toContain("字段 'score' 不能小于 0");
    });

    it('应该验证字符串长度', () => {
        const data = {
            username: 'a',
            bio: '这是一段非常长的个人简介，超过了最大长度限制这是一段非常长的个人简介，超过了最大长度限制'
        };

        const rules = {
            username: { type: 'string' as const, minLength: 3, maxLength: 20 },
            bio: { type: 'string' as const, maxLength: 50 }
        };

        const result = validateAndSanitize(data, rules);

        expect(result.valid).toBe(false);
        expect(result.errors).toContain("字段 'username' 长度不能少于 3 个字符");
        expect(result.errors).toContain("字段 'bio' 长度不能超过 50 个字符");
    });

    it('应该验证数组长度', () => {
        const data = {
            tags: [],
            friends: [1, 2, 3, 4, 5, 6]
        };

        const rules = {
            tags: { type: 'array' as const, minLength: 1 },
            friends: { type: 'array' as const, maxLength: 5 }
        };

        const result = validateAndSanitize(data, rules);

        expect(result.valid).toBe(false);
        expect(result.errors).toContain("字段 'tags' 至少需要 1 个元素");
        expect(result.errors).toContain("字段 'friends' 不能超过 5 个元素");
    });

    it('应该支持嵌套对象验证', () => {
        const data = {
            user: {
                name: '',
                contact: {
                    email: 'invalid-email'
                }
            }
        };

        const rules = {
            user: {
                type: 'object' as const,
                fields: {
                    name: { required: true, type: 'string' as const, minLength: 1 },
                    contact: {
                        type: 'object' as const,
                        fields: {
                            email: { type: 'string' as const, pattern: /^.+@.+\..+$/ }
                        }
                    }
                }
            }
        };

        const result = validateAndSanitize(data, rules);

        expect(result.valid).toBe(false);
        expect(result.errors).toContain("字段 'user.name' 长度不能少于 1 个字符");
        expect(result.errors).toContain("字段 'user.contact.email' 格式不正确");
    });

    it('应该支持数组元素验证', () => {
        const data = {
            numbers: [1, -5, 10],
            users: [
                { name: '张三', age: 30 },
                { name: '', age: -1 }
            ]
        };

        const rules = {
            numbers: {
                type: 'array' as const,
                items: { type: 'number' as const, min: 0 }
            },
            users: {
                type: 'array' as const,
                items: {
                    type: 'object' as const,
                    fields: {
                        name: { required: true, type: 'string' as const, minLength: 1 },
                        age: { required: true, type: 'number' as const, min: 0 }
                    }
                }
            }
        };

        const result = validateAndSanitize(data, rules);

        expect(result.valid).toBe(false);
        expect(result.errors).toContain("字段 'numbers[1]' 不能小于 0");
        expect(result.errors).toContain("字段 'users[1].name' 长度不能少于 1 个字符");
        expect(result.errors).toContain("字段 'users[1].age' 不能小于 0");
    });

    it('应该支持条件验证', () => {
        const data = {
            type: 'company',
            personal: {
                idNumber: '123456'
            },
            company: {
                taxId: ''
            }
        };

        const rules = {
            type: { type: 'string' as const },
            personal: {
                type: 'object' as const,
                fields: {
                    idNumber: { type: 'string' as const, minLength: 18 }
                },
                required: (values: any) => values.type === 'personal'
            },
            company: {
                type: 'object' as const,
                fields: {
                    taxId: { required: true, type: 'string' as const, minLength: 1 }
                },
                required: (values: any) => values.type === 'company'
            }
        };

        const result = validateAndSanitize(data, rules);

        expect(result.valid).toBe(false);
        expect(result.errors).toContain("字段 'company.taxId' 长度不能少于 1 个字符");
        // personal.idNumber 不应该被验证，因为 type 不是 'personal'
        expect(result.errors).not.toContain("字段 'personal.idNumber' 长度不能少于 18 个字符");
    });

    it('应该支持自定义清理函数', () => {
        const data = {
            username: ' admin ',
            role: 'ADMIN'
        };

        const rules = {
            username: {
                type: 'string' as const,
                sanitize: (value: string) => value.trim().toLowerCase()
            },
            role: {
                type: 'string' as const,
                sanitize: (value: string) => value.toLowerCase()
            }
        };

        const result = validateAndSanitize(data, rules, { sanitize: true });

        expect(result.valid).toBe(true);
        expect(result.sanitized?.username).toBe('admin');
        expect(result.sanitized?.role).toBe('admin');
    });
});