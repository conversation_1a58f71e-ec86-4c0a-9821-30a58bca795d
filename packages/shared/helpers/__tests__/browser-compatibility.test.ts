/**
 * @fileoverview 浏览器兼容性检查测试
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { checkBrowserCompatibility, detectEnvironmentFeatures } from '../src/index';

describe('浏览器兼容性检查', () => {
    beforeEach(() => {
        vi.resetAllMocks();
    });

    describe('checkBrowserCompatibility', () => {
        it('应该检查浏览器兼容性', () => {
            // 模拟全局对象
            global.Proxy = class { } as any;
            global.fetch = vi.fn() as any;
            global.Promise = Promise;
            global.localStorage = {} as any;
            global.sessionStorage = {} as any;
            global.MutationObserver = class { } as any;
            global.IntersectionObserver = class { } as any;
            global.ResizeObserver = class { } as any;
            global.requestAnimationFrame = vi.fn() as any;
            global.history = {} as any;
            global.URL = URL;

            const result = checkBrowserCompatibility();

            expect(result).toBeDefined();
            expect(typeof result.compatible).toBe('boolean');
            expect(Array.isArray(result.missing)).toBe(true);
        });

        it('应该检测缺失的特性', () => {
            // 保存原始对象
            const originalProxy = global.Proxy;

            // 模拟缺失的 Proxy
            (global as any).Proxy = undefined;

            const result = checkBrowserCompatibility({ proxy: true });

            expect(result.compatible).toBe(false);
            expect(result.missing).toContain('Proxy');

            // 恢复原始对象
            global.Proxy = originalProxy;
        });

        it('应该支持自定义要求', () => {
            const result = checkBrowserCompatibility({
                customFeature: () => false
            });

            expect(result.compatible).toBe(false);
            expect(result.missing).toContain('customFeature');
        });
    });

    describe('detectEnvironmentFeatures', () => {
        it('应该检测环境特性', () => {
            // 模拟环境
            global.Promise = Promise;
            global.fetch = vi.fn() as any;
            global.localStorage = {} as any;
            global.sessionStorage = {} as any;
            global.indexedDB = {} as any;
            global.WebSocket = class { } as any;
            global.Worker = class { } as any;
            global.crypto = {} as any;

            const features = detectEnvironmentFeatures();

            expect(features).toBeDefined();
            expect(typeof features.es6).toBe('boolean');
            expect(typeof features.fetch).toBe('boolean');
            expect(typeof features.localStorage).toBe('boolean');
            expect(typeof features.sessionStorage).toBe('boolean');
            expect(typeof features.indexedDB).toBe('boolean');
            expect(typeof features.webSockets).toBe('boolean');
            expect(typeof features.workers).toBe('boolean');
            expect(typeof features.crypto).toBe('boolean');
        });

        it('应该处理不支持的特性', () => {
            // 保存原始对象
            const originalFetch = global.fetch;
            const originalIndexedDB = global.indexedDB;

            // 模拟缺失的特性
            (global as any).fetch = undefined;
            (global as any).indexedDB = undefined;

            const features = detectEnvironmentFeatures();

            expect(features.fetch).toBe(false);
            expect(features.indexedDB).toBe(false);

            // 恢复原始对象
            global.fetch = originalFetch;
            global.indexedDB = originalIndexedDB;
        });
    });
});