/**
 * @fileoverview 集成测试工具
 * @description 提供适配器、构建器和端到端测试的集成测试工具
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

import { MicroCoreTestUtils } from './index';

/**
 * 适配器集成测试套件
 */
export class AdapterIntegrationTest {
    private testUtils: MicroCoreTestUtils;
    private container: HTMLElement;

    constructor() {
        this.testUtils = new MicroCoreTestUtils();
        this.container = this.testUtils.createContainer('adapter-integration-test');
    }

    /**
     * 测试适配器生命周期
     */
    async testAdapterLifecycle(adapter: any, config: any): Promise<void> {
        // 测试加载
        const instance = await adapter.load(config);
        expect(instance).toBeDefined();
        expect(instance.name).toBe(config.name);
        expect(instance.status).toBe('loaded');

        // 测试挂载
        await adapter.mount(instance);
        expect(instance.status).toBe('mounted');
        expect(instance.container).toBeDefined();

        // 测试更新
        if (adapter.update) {
            const newProps = { test: 'updated' };
            await adapter.update(instance, newProps);
            expect(instance.props).toEqual(expect.objectContaining(newProps));
        }

        // 测试卸载
        await adapter.unmount(instance);
        expect(instance.status).toBe('unmounted');
    }

    /**
     * 测试适配器错误处理
     */
    async testAdapterErrorHandling(adapter: any): Promise<void> {
        // 测试无效配置
        await expect(adapter.load({})).rejects.toThrow();

        // 测试加载失败
        const invalidConfig = {
            name: 'test-app',
            entry: 'http://invalid-url'
        };
        await expect(adapter.load(invalidConfig)).rejects.toThrow();
    }

    /**
     * 清理测试环境
     */
    cleanup(): void {
        this.testUtils.cleanupContainer(this.container);
        this.testUtils.destroy();
    }
}

/**
 * 构建器集成测试套件
 */
export class BuilderIntegrationTest {
    private testUtils: MicroCoreTestUtils;

    constructor() {
        this.testUtils = new MicroCoreTestUtils();
    }

    /**
     * 测试构建器构建流程
     */
    async testBuilderBuildProcess(builder: any, config: any): Promise<void> {
        // 测试构建
        const result = await builder.build(config);
        expect(result).toBeDefined();
        expect(result.success).toBe(true);
        expect(result.outputs).toBeDefined();
        expect(Array.isArray(result.outputs)).toBe(true);

        // 验证构建产物
        if (result.outputs && result.outputs.length > 0) {
            result.outputs.forEach((output: any) => {
                expect(output.fileName).toBeDefined();
                expect(output.size).toBeGreaterThan(0);
            });
        }
    }

    /**
     * 测试构建器开发服务器
     */
    async testBuilderDevServer(builder: any, config: any): Promise<void> {
        if (!builder.serve) {
            return; // 跳过不支持开发服务器的构建器
        }

        // 启动开发服务器
        await expect(builder.serve(config)).resolves.not.toThrow();

        // 等待服务器启动
        await this.testUtils.waitFor(() => {
            // 这里应该检查服务器是否真正启动
            return Promise.resolve(true);
        }, 5000);

        // 停止服务器
        if (builder.stop) {
            await builder.stop();
        }
    }

    /**
     * 测试构建器错误处理
     */
    async testBuilderErrorHandling(builder: any): Promise<void> {
        // 测试无效配置
        await expect(builder.build({})).rejects.toThrow();

        // 测试不存在的入口文件
        const invalidConfig = {
            entry: './non-existent-file.js',
            outDir: './dist'
        };
        await expect(builder.build(invalidConfig)).rejects.toThrow();
    }

    /**
     * 清理测试环境
     */
    cleanup(): void {
        this.testUtils.destroy();
    }
}

/**
 * 端到端测试套件
 */
export class E2ETestSuite {
    private testUtils: MicroCoreTestUtils;
    private containers: HTMLElement[] = [];

    constructor() {
        this.testUtils = new MicroCoreTestUtils();
    }

    /**
     * 测试完整的应用生命周期
     */
    async testApplicationLifecycle(
        adapter: any,
        builder: any,
        appConfig: any,
        buildConfig: any
    ): Promise<void> {
        // 1. 构建应用
        const buildResult = await builder.build(buildConfig);
        expect(buildResult.success).toBe(true);

        // 2. 创建容器
        const container = this.testUtils.createContainer('e2e-test-container');
        this.containers.push(container);
        appConfig.container = container;

        // 3. 加载应用
        const instance = await adapter.load(appConfig);
        expect(instance).toBeDefined();

        // 4. 挂载应用
        await adapter.mount(instance);
        expect(instance.status).toBe('mounted');

        // 5. 验证应用渲染
        await this.testUtils.waitFor(() => {
            return container.children.length > 0;
        }, 3000);

        // 6. 测试应用交互
        const button = container.querySelector('button');
        if (button) {
            this.testUtils.simulateClick(button);
            await this.testUtils.delay(100);
        }

        // 7. 卸载应用
        await adapter.unmount(instance);
        expect(instance.status).toBe('unmounted');
    }

    /**
     * 测试多应用协同
     */
    async testMultiAppCoordination(
        adapters: any[],
        configs: any[]
    ): Promise<void> {
        const instances: any[] = [];

        try {
            // 加载所有应用
            for (let i = 0; i < adapters.length; i++) {
                const container = this.testUtils.createContainer(`multi-app-${i}`);
                this.containers.push(container);
                configs[i].container = container;

                const instance = await adapters[i].load(configs[i]);
                instances.push(instance);
            }

            // 挂载所有应用
            for (let i = 0; i < adapters.length; i++) {
                await adapters[i].mount(instances[i]);
                expect(instances[i].status).toBe('mounted');
            }

            // 验证所有应用都正常渲染
            for (const container of this.containers) {
                await this.testUtils.waitFor(() => {
                    return container.children.length > 0;
                }, 3000);
            }

            // 卸载所有应用
            for (let i = 0; i < adapters.length; i++) {
                await adapters[i].unmount(instances[i]);
                expect(instances[i].status).toBe('unmounted');
            }
        } catch (error) {
            // 清理已创建的实例
            for (let i = 0; i < instances.length; i++) {
                try {
                    if (instances[i].status === 'mounted') {
                        await adapters[i].unmount(instances[i]);
                    }
                } catch (cleanupError) {
                    console.warn('清理实例失败:', cleanupError);
                }
            }
            throw error;
        }
    }

    /**
     * 性能基准测试
     */
    async testPerformanceBenchmark(
        adapter: any,
        config: any,
        thresholds: {
            loadTime?: number;
            mountTime?: number;
            memoryUsage?: number;
        } = {}
    ): Promise<void> {
        const defaultThresholds = {
            loadTime: 1000,    // 1秒
            mountTime: 500,    // 0.5秒
            memoryUsage: 10 * 1024 * 1024, // 10MB
            ...thresholds
        };

        // 测试加载性能
        const loadStartTime = performance.now();
        const instance = await adapter.load(config);
        const loadTime = performance.now() - loadStartTime;

        expect(loadTime).toBeLessThan(defaultThresholds.loadTime);

        // 测试挂载性能
        const container = this.testUtils.createContainer('perf-test-container');
        this.containers.push(container);
        config.container = container;

        const mountStartTime = performance.now();
        await adapter.mount(instance);
        const mountTime = performance.now() - mountStartTime;

        expect(mountTime).toBeLessThan(defaultThresholds.mountTime);

        // 测试内存使用
        const memoryUsage = this.testUtils.measureMemoryUsage();
        if (memoryUsage) {
            expect(memoryUsage.used).toBeLessThan(defaultThresholds.memoryUsage);
        }

        // 清理
        await adapter.unmount(instance);
    }

    /**
     * 清理测试环境
     */
    cleanup(): void {
        this.containers.forEach(container => {
            this.testUtils.cleanupContainer(container);
        });
        this.containers = [];
        this.testUtils.destroy();
    }
}

/**
 * 集成测试工厂
 */
export class IntegrationTestFactory {
    /**
     * 创建适配器集成测试
     */
    static createAdapterTest(): AdapterIntegrationTest {
        return new AdapterIntegrationTest();
    }

    /**
     * 创建构建器集成测试
     */
    static createBuilderTest(): BuilderIntegrationTest {
        return new BuilderIntegrationTest();
    }

    /**
     * 创建端到端测试
     */
    static createE2ETest(): E2ETestSuite {
        return new E2ETestSuite();
    }

    /**
     * 创建完整的集成测试套件
     */
    static createFullTestSuite() {
        return {
            adapter: new AdapterIntegrationTest(),
            builder: new BuilderIntegrationTest(),
            e2e: new E2ETestSuite()
        };
    }
}

/**
 * 集成测试运行器
 */
export class IntegrationTestRunner {
    private testSuites: {
        adapter: AdapterIntegrationTest;
        builder: BuilderIntegrationTest;
        e2e: E2ETestSuite;
    };

    constructor() {
        this.testSuites = IntegrationTestFactory.createFullTestSuite();
    }

    /**
     * 运行所有集成测试
     */
    async runAllTests(
        adapters: any[],
        builders: any[],
        configs: {
            adapters: any[];
            builders: any[];
        }
    ): Promise<void> {
        try {
            // 运行适配器测试
            for (let i = 0; i < adapters.length; i++) {
                await this.testSuites.adapter.testAdapterLifecycle(
                    adapters[i],
                    configs.adapters[i]
                );
                await this.testSuites.adapter.testAdapterErrorHandling(adapters[i]);
            }

            // 运行构建器测试
            for (let i = 0; i < builders.length; i++) {
                await this.testSuites.builder.testBuilderBuildProcess(
                    builders[i],
                    configs.builders[i]
                );
                await this.testSuites.builder.testBuilderErrorHandling(builders[i]);
            }

            // 运行端到端测试
            if (adapters.length > 0 && builders.length > 0) {
                await this.testSuites.e2e.testApplicationLifecycle(
                    adapters[0],
                    builders[0],
                    configs.adapters[0],
                    configs.builders[0]
                );

                if (adapters.length > 1) {
                    await this.testSuites.e2e.testMultiAppCoordination(
                        adapters.slice(0, 2),
                        configs.adapters.slice(0, 2)
                    );
                }
            }
        } finally {
            this.cleanup();
        }
    }

    /**
     * 清理所有测试套件
     */
    cleanup(): void {
        this.testSuites.adapter.cleanup();
        this.testSuites.builder.cleanup();
        this.testSuites.e2e.cleanup();
    }
}

/**
 * 导出集成测试工具
 */
export {
    AdapterIntegrationTest,
    BuilderIntegrationTest,
    E2ETestSuite,
    IntegrationTestFactory,
    IntegrationTestRunner
};

/**
 * 默认导出
 */
export default {
    AdapterIntegrationTest,
    BuilderIntegrationTest,
    E2ETestSuite,
    IntegrationTestFactory,
    IntegrationTestRunner
};