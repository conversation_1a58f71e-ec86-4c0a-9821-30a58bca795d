/**
 * @fileoverview 自定义 Jest 匹配器
 * @description 提供微核心项目特定的测试匹配器
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 扩展 Jest 匹配器类型
 */
declare global {
    namespace jest {
        interface Matchers<R> {
            /** 断言元素存在 */
            toBeInDOM(): R;
            /** 断言元素可见 */
            toBeVisible(): R;
            /** 断言元素隐藏 */
            toBeHidden(): R;
            /** 断言元素有特定文本 */
            toHaveTextContent(text: string): R;
            /** 断言元素有特定属性 */
            toHaveAttribute(attribute: string, value?: string): R;
            /** 断言元素有特定类名 */
            toHaveClass(className: string): R;
            /** 断言元素有特定样式 */
            toHaveStyle(style: string | Record<string, any>): R;
            /** 断言函数在指定时间内被调用 */
            toHaveBeenCalledWithin(timeout: number): Promise<R>;
            /** 断言对象有特定结构 */
            toMatchStructure(structure: any): R;
            /** 断言数组按特定顺序排列 */
            toBeInOrder(compareFn?: (a: any, b: any) => number): R;
            /** 断言Promise在指定时间内解决 */
            toResolveWithin(timeout: number): Promise<R>;
            /** 断言Promise在指定时间内拒绝 */
            toRejectWithin(timeout: number): Promise<R>;
            /** 断言对象是有效的微应用配置 */
            toBeValidMicroAppConfig(): R;
            /** 断言对象是有效的适配器实例 */
            toBeValidAdapterInstance(): R;
            /** 断言对象是有效的构建结果 */
            toBeValidBuildResult(): R;
            /** 断言性能指标在合理范围内 */
            toHaveReasonablePerformance(thresholds: PerformanceThresholds): R;
        }
    }
}

/**
 * 性能阈值接口
 */
interface PerformanceThresholds {
    /** 最大执行时间（毫秒） */
    maxExecutionTime?: number;
    /** 最大内存使用（字节） */
    maxMemoryUsage?: number;
    /** 最小成功率 */
    minSuccessRate?: number;
}

/**
 * 检查元素是否在 DOM 中
 */
function toBeInDOM(this: jest.MatcherContext, received: Element) {
    const pass = document.body.contains(received);

    if (pass) {
        return {
            message: () => `期望元素不在 DOM 中，但它在`,
            pass: true
        };
    } else {
        return {
            message: () => `期望元素在 DOM 中，但它不在`,
            pass: false
        };
    }
}

/**
 * 检查元素是否可见
 */
function toBeVisible(this: jest.MatcherContext, received: Element) {
    const style = window.getComputedStyle(received);
    const pass = style.display !== 'none' &&
        style.visibility !== 'hidden' &&
        style.opacity !== '0';

    if (pass) {
        return {
            message: () => `期望元素不可见，但它可见`,
            pass: true
        };
    } else {
        return {
            message: () => `期望元素可见，但它不可见`,
            pass: false
        };
    }
}

/**
 * 检查元素是否隐藏
 */
function toBeHidden(this: jest.MatcherContext, received: Element) {
    const style = window.getComputedStyle(received);
    const pass = style.display === 'none' ||
        style.visibility === 'hidden' ||
        style.opacity === '0';

    if (pass) {
        return {
            message: () => `期望元素不隐藏，但它隐藏`,
            pass: true
        };
    } else {
        return {
            message: () => `期望元素隐藏，但它不隐藏`,
            pass: false
        };
    }
}

/**
 * 检查元素文本内容
 */
function toHaveTextContent(this: jest.MatcherContext, received: Element, expected: string) {
    const actualText = received.textContent || '';
    const pass = actualText.includes(expected);

    if (pass) {
        return {
            message: () => `期望元素不包含文本 "${expected}"，但它包含`,
            pass: true
        };
    } else {
        return {
            message: () => `期望元素包含文本 "${expected}"，但实际文本是 "${actualText}"`,
            pass: false
        };
    }
}

/**
 * 检查元素属性
 */
function toHaveAttribute(this: jest.MatcherContext, received: Element, attribute: string, value?: string) {
    const hasAttribute = received.hasAttribute(attribute);

    if (!hasAttribute) {
        return {
            message: () => `期望元素有属性 "${attribute}"，但它没有`,
            pass: false
        };
    }

    if (value !== undefined) {
        const actualValue = received.getAttribute(attribute);
        const pass = actualValue === value;

        if (pass) {
            return {
                message: () => `期望元素属性 "${attribute}" 不等于 "${value}"，但它等于`,
                pass: true
            };
        } else {
            return {
                message: () => `期望元素属性 "${attribute}" 等于 "${value}"，但实际值是 "${actualValue}"`,
                pass: false
            };
        }
    }

    return {
        message: () => `期望元素没有属性 "${attribute}"，但它有`,
        pass: true
    };
}

/**
 * 检查元素类名
 */
function toHaveClass(this: jest.MatcherContext, received: Element, className: string) {
    const pass = received.classList.contains(className);

    if (pass) {
        return {
            message: () => `期望元素不包含类名 "${className}"，但它包含`,
            pass: true
        };
    } else {
        return {
            message: () => `期望元素包含类名 "${className}"，但它不包含`,
            pass: false
        };
    }
}

/**
 * 检查元素样式
 */
function toHaveStyle(this: jest.MatcherContext, received: Element, style: string | Record<string, any>) {
    const computedStyle = window.getComputedStyle(received);

    if (typeof style === 'string') {
        // 解析样式字符串
        const styles = style.split(';').reduce((acc, rule) => {
            const [property, value] = rule.split(':').map(s => s.trim());
            if (property && value) {
                acc[property] = value;
            }
            return acc;
        }, {} as Record<string, string>);

        style = styles;
    }

    const failures: string[] = [];

    Object.entries(style).forEach(([property, expectedValue]) => {
        const actualValue = computedStyle.getPropertyValue(property);
        if (actualValue !== expectedValue) {
            failures.push(`${property}: 期望 "${expectedValue}"，实际 "${actualValue}"`);
        }
    });

    const pass = failures.length === 0;

    if (pass) {
        return {
            message: () => `期望元素不具有指定样式，但它具有`,
            pass: true
        };
    } else {
        return {
            message: () => `期望元素具有指定样式，但有以下差异：\n${failures.join('\n')}`,
            pass: false
        };
    }
}

/**
 * 检查函数在指定时间内被调用
 */
async function toHaveBeenCalledWithin(this: jest.MatcherContext, received: jest.MockedFunction<any>, timeout: number) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
        if (received.mock.calls.length > 0) {
            return {
                message: () => `期望函数在 ${timeout}ms 内不被调用，但它被调用了`,
                pass: true
            };
        }
        await new Promise(resolve => setTimeout(resolve, 10));
    }

    return {
        message: () => `期望函数在 ${timeout}ms 内被调用，但它没有被调用`,
        pass: false
    };
}

/**
 * 检查对象结构
 */
function toMatchStructure(this: jest.MatcherContext, received: any, structure: any) {
    function checkStructure(obj: any, struct: any, path: string = ''): string[] {
        const errors: string[] = [];

        if (typeof struct !== 'object' || struct === null) {
            return errors;
        }

        Object.keys(struct).forEach(key => {
            const currentPath = path ? `${path}.${key}` : key;

            if (!(key in obj)) {
                errors.push(`缺少属性: ${currentPath}`);
                return;
            }

            const expectedType = typeof struct[key];
            const actualType = typeof obj[key];

            if (expectedType !== actualType) {
                errors.push(`类型不匹配 ${currentPath}: 期望 ${expectedType}，实际 ${actualType}`);
                return;
            }

            if (expectedType === 'object' && struct[key] !== null && obj[key] !== null) {
                errors.push(...checkStructure(obj[key], struct[key], currentPath));
            }
        });

        return errors;
    }

    const errors = checkStructure(received, structure);
    const pass = errors.length === 0;

    if (pass) {
        return {
            message: () => `期望对象不匹配结构，但它匹配`,
            pass: true
        };
    } else {
        return {
            message: () => `期望对象匹配结构，但有以下错误：\n${errors.join('\n')}`,
            pass: false
        };
    }
}

/**
 * 检查数组顺序
 */
function toBeInOrder(this: jest.MatcherContext, received: any[], compareFn?: (a: any, b: any) => number) {
    if (!Array.isArray(received)) {
        return {
            message: () => `期望接收到数组，但接收到 ${typeof received}`,
            pass: false
        };
    }

    const defaultCompareFn = (a: any, b: any) => {
        if (a < b) return -1;
        if (a > b) return 1;
        return 0;
    };

    const compare = compareFn || defaultCompareFn;

    for (let i = 1; i < received.length; i++) {
        if (compare(received[i - 1], received[i]) > 0) {
            return {
                message: () => `期望数组按顺序排列，但在索引 ${i - 1} 和 ${i} 处顺序错误`,
                pass: false
            };
        }
    }

    return {
        message: () => `期望数组不按顺序排列，但它按顺序排列`,
        pass: true
    };
}

/**
 * 检查 Promise 在指定时间内解决
 */
async function toResolveWithin(this: jest.MatcherContext, received: Promise<any>, timeout: number) {
    try {
        await Promise.race([
            received,
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error('超时')), timeout)
            )
        ]);

        return {
            message: () => `期望 Promise 在 ${timeout}ms 内不解决，但它解决了`,
            pass: true
        };
    } catch (error) {
        if (error instanceof Error && error.message === '超时') {
            return {
                message: () => `期望 Promise 在 ${timeout}ms 内解决，但它超时了`,
                pass: false
            };
        }

        return {
            message: () => `期望 Promise 在 ${timeout}ms 内解决，但它被拒绝了: ${error}`,
            pass: false
        };
    }
}

/**
 * 检查 Promise 在指定时间内拒绝
 */
async function toRejectWithin(this: jest.MatcherContext, received: Promise<any>, timeout: number) {
    try {
        await Promise.race([
            received.then(() => { throw new Error('Promise 解决了'); }),
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error('超时')), timeout)
            )
        ]);

        return {
            message: () => `期望 Promise 在 ${timeout}ms 内不拒绝，但它拒绝了`,
            pass: true
        };
    } catch (error) {
        if (error instanceof Error && error.message === '超时') {
            return {
                message: () => `期望 Promise 在 ${timeout}ms 内拒绝，但它超时了`,
                pass: false
            };
        }

        if (error instanceof Error && error.message === 'Promise 解决了') {
            return {
                message: () => `期望 Promise 在 ${timeout}ms 内拒绝，但它解决了`,
                pass: false
            };
        }

        return {
            message: () => `期望 Promise 在 ${timeout}ms 内不拒绝，但它拒绝了`,
            pass: true
        };
    }
}

/**
 * 检查是否为有效的微应用配置
 */
function toBeValidMicroAppConfig(this: jest.MatcherContext, received: any) {
    const errors: string[] = [];

    if (typeof received !== 'object' || received === null) {
        errors.push('配置必须是对象');
    } else {
        if (!received.name || typeof received.name !== 'string') {
            errors.push('缺少或无效的 name 属性');
        }

        if (!received.entry || typeof received.entry !== 'string') {
            errors.push('缺少或无效的 entry 属性');
        }

        if (received.container && typeof received.container !== 'string' && !(received.container instanceof HTMLElement)) {
            errors.push('无效的 container 属性');
        }

        if (received.props && typeof received.props !== 'object') {
            errors.push('无效的 props 属性');
        }
    }

    const pass = errors.length === 0;

    if (pass) {
        return {
            message: () => `期望不是有效的微应用配置，但它是`,
            pass: true
        };
    } else {
        return {
            message: () => `期望是有效的微应用配置，但有以下错误：\n${errors.join('\n')}`,
            pass: false
        };
    }
}

/**
 * 检查是否为有效的适配器实例
 */
function toBeValidAdapterInstance(this: jest.MatcherContext, received: any) {
    const errors: string[] = [];

    if (typeof received !== 'object' || received === null) {
        errors.push('适配器实例必须是对象');
    } else {
        if (!received.name || typeof received.name !== 'string') {
            errors.push('缺少或无效的 name 属性');
        }

        if (!received.version || typeof received.version !== 'string') {
            errors.push('缺少或无效的 version 属性');
        }

        if (!received.framework || typeof received.framework !== 'string') {
            errors.push('缺少或无效的 framework 属性');
        }

        if (typeof received.load !== 'function') {
            errors.push('缺少 load 方法');
        }

        if (typeof received.mount !== 'function') {
            errors.push('缺少 mount 方法');
        }

        if (typeof received.unmount !== 'function') {
            errors.push('缺少 unmount 方法');
        }
    }

    const pass = errors.length === 0;

    if (pass) {
        return {
            message: () => `期望不是有效的适配器实例，但它是`,
            pass: true
        };
    } else {
        return {
            message: () => `期望是有效的适配器实例，但有以下错误：\n${errors.join('\n')}`,
            pass: false
        };
    }
}

/**
 * 检查是否为有效的构建结果
 */
function toBeValidBuildResult(this: jest.MatcherContext, received: any) {
    const errors: string[] = [];

    if (typeof received !== 'object' || received === null) {
        errors.push('构建结果必须是对象');
    } else {
        if (typeof received.success !== 'boolean') {
            errors.push('缺少或无效的 success 属性');
        }

        if (received.buildTime && typeof received.buildTime !== 'string') {
            errors.push('无效的 buildTime 属性');
        }

        if (received.outputSize && typeof received.outputSize !== 'string') {
            errors.push('无效的 outputSize 属性');
        }

        if (received.outputs && !Array.isArray(received.outputs)) {
            errors.push('无效的 outputs 属性');
        }

        if (received.assets && !Array.isArray(received.assets)) {
            errors.push('无效的 assets 属性');
        }

        if (received.errors && !Array.isArray(received.errors)) {
            errors.push('无效的 errors 属性');
        }
    }

    const pass = errors.length === 0;

    if (pass) {
        return {
            message: () => `期望不是有效的构建结果，但它是`,
            pass: true
        };
    } else {
        return {
            message: () => `期望是有效的构建结果，但有以下错误：\n${errors.join('\n')}`,
            pass: false
        };
    }
}

/**
 * 检查性能指标是否在合理范围内
 */
function toHaveReasonablePerformance(this: jest.MatcherContext, received: any, thresholds: PerformanceThresholds) {
    const errors: string[] = [];

    if (typeof received !== 'object' || received === null) {
        errors.push('性能数据必须是对象');
        return {
            message: () => `期望接收到性能对象，但接收到 ${typeof received}`,
            pass: false
        };
    }

    // 检查执行时间
    if (thresholds.maxExecutionTime && received.executionTime) {
        if (received.executionTime > thresholds.maxExecutionTime) {
            errors.push(`执行时间超出阈值: ${received.executionTime}ms > ${thresholds.maxExecutionTime}ms`);
        }
    }

    // 检查内存使用
    if (thresholds.maxMemoryUsage && received.memoryUsage) {
        if (received.memoryUsage > thresholds.maxMemoryUsage) {
            errors.push(`内存使用超出阈值: ${received.memoryUsage} bytes > ${thresholds.maxMemoryUsage} bytes`);
        }
    }

    // 检查成功率
    if (thresholds.minSuccessRate && received.successRate !== undefined) {
        if (received.successRate < thresholds.minSuccessRate) {
            errors.push(`成功率低于阈值: ${received.successRate} < ${thresholds.minSuccessRate}`);
        }
    }

    const pass = errors.length === 0;

    if (pass) {
        return {
            message: () => `期望性能不在合理范围内，但它在`,
            pass: true
        };
    } else {
        return {
            message: () => `期望性能在合理范围内，但有以下问题：\n${errors.join('\n')}`,
            pass: false
        };
    }
}

/**
 * 注册所有自定义匹配器
 */
export function setupCustomMatchers(): void {
    expect.extend({
        toBeInDOM,
        toBeVisible,
        toBeHidden,
        toHaveTextContent,
        toHaveAttribute,
        toHaveClass,
        toHaveStyle,
        toHaveBeenCalledWithin,
        toMatchStructure,
        toBeInOrder,
        toResolveWithin,
        toRejectWithin,
        toBeValidMicroAppConfig,
        toBeValidAdapterInstance,
        toBeValidBuildResult,
        toHaveReasonablePerformance
    });
}

/**
 * 导出所有匹配器函数
 */
export {
    toBeHidden, toBeInDOM, toBeInOrder, toBeValidAdapterInstance,
    toBeValidBuildResult, toBeValidMicroAppConfig, toBeVisible, toHaveAttribute, toHaveBeenCalledWithin, toHaveClass, toHaveReasonablePerformance, toHaveStyle, toHaveTextContent, toMatchStructure, toRejectWithin, toResolveWithin
};

/**
 * 默认导出设置函数
 */
export default setupCustomMatchers;
