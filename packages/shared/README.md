# @micro-core/shared

微前端架构的共享包，提供通用的工具函数、类型定义、常量和辅助函数。

## 🏗️ 架构概览

本包是 Micro-Core 微前端架构的核心基础设施，提供了完整的工具链支持：

- **🔧 工具函数** - 类型检查、对象操作、字符串处理、异步工具等
- **📝 类型定义** - 完整的 TypeScript 类型系统
- **📊 常量定义** - 统一的常量和枚举定义
- **🛠️ 辅助函数** - 高级业务逻辑辅助工具
- **⚙️ 配置管理** - 标准化的配置文件和工具链

## 📦 子包结构

```
packages/shared/
├── constants/          # 共享常量定义
│   ├── src/           # 源码
│   │   └── index.ts   # 应用状态、错误代码、事件类型等
│   ├── __tests__/     # 测试文件
│   └── package.json   # 包配置
├── types/             # 共享类型定义
│   ├── src/           # 源码
│   │   ├── index.ts   # 微前端应用相关类型
│   │   └── error.ts   # 错误类型定义
│   ├── __tests__/     # 测试文件
│   └── package.json   # 包配置
├── utils/             # 通用工具函数
│   ├── src/           # 源码
│   │   ├── index.ts   # 工具导出
│   │   ├── type-check.ts    # 类型检查工具
│   │   ├── object.ts        # 对象操作工具
│   │   ├── string.ts        # 字符串处理工具
│   │   ├── array.ts         # 数组操作工具
│   │   ├── function.ts      # 函数工具
│   │   ├── async.ts         # 异步工具
│   │   ├── date.ts          # 日期工具
│   │   ├── url.ts           # URL处理工具
│   │   ├── dom.ts           # DOM操作工具
│   │   ├── storage.ts       # 存储工具
│   │   ├── env.ts           # 环境检测工具
│   │   ├── logger.ts        # 日志工具
│   │   ├── id.ts            # ID生成工具
│   │   ├── format.ts        # 格式化工具
│   │   └── event-bus.ts     # 事件总线
│   ├── __tests__/     # 测试文件
│   └── package.json   # 包配置
├── helpers/           # 高级辅助函数
│   ├── src/           # 源码
│   │   └── index.ts   # 业务逻辑辅助工具
│   ├── __tests__/     # 测试文件
│   └── package.json   # 包配置
├── test-utils/        # 测试工具
├── eslint-config/     # ESLint 配置
├── prettier-config/   # Prettier 配置
├── ts-config/         # TypeScript 配置
├── jest-config/       # Jest 配置
├── vitest-config/     # Vitest 配置
└── test/              # 测试设置
    └── setup.ts       # 测试环境配置
```

## 📦 安装

```bash
# 安装完整共享包
pnpm add @micro-core/shared

# 或安装特定子包
pnpm add @micro-core/shared-constants
pnpm add @micro-core/shared-types
pnpm add @micro-core/shared-utils
pnpm add @micro-core/shared-helpers
```

## 🚀 快速开始

### 基础导入

```typescript
// 从主包导入
import {
  isFunction,
  isObject,
  deepMerge,
  camelCase,
  createLogger,
  EventBus,
  generateId,
  formatError,
  formatBytes,
  formatTime
} from '@micro-core/shared/utils';

// 分模块导入
import {
  AppStatus,
  ErrorCodes,
  EventTypes
} from '@micro-core/shared/constants';

import type {
  MicroAppConfig,
  AppLifecycle,
  EventEmitter
} from '@micro-core/shared/types';

import {
  formatErrorMessage,
  validateAppConfig,
  createPerformanceTimer
} from '@micro-core/shared/helpers';
```

### 核心功能演示

```typescript
// 1. 应用配置定义和验证
const appConfig: MicroAppConfig = {
  name: 'my-micro-app',
  entry: 'http://localhost:3000',
  container: '#app-container',
  activeRule: '/my-app',
  sandbox: {
    enabled: true,
    type: 'proxy'
  },
  styleIsolation: {
    enabled: true,
    type: 'scoped'
  }
};

// 验证配置
const errors = validateAppConfig(appConfig);
if (errors.length > 0) {
  console.error('配置错误:', errors);
}

// 2. 生命周期函数定义
const lifecycle: AppLifecycle = {
  bootstrap: async (props) => {
    console.log('应用启动', props);
  },
  mount: async (props) => {
    console.log('应用挂载', props);
  },
  unmount: async (props) => {
    console.log('应用卸载', props);
  }
};

// 3. 事件通信
const eventBus = new EventBus();
eventBus.on('user-action', (data) => {
  console.log('用户操作:', data);
});
eventBus.emit('user-action', { type: 'click', target: 'button' });

// 4. 性能监控
const timer = createPerformanceTimer('app-load');
timer.start();
// ... 执行应用加载逻辑
const duration = timer.end();
console.log(`应用加载耗时: ${duration.toFixed(2)}ms`);

// 5. 日志记录
const logger = createLogger('my-app');
logger.info('应用初始化完成');
logger.warn('检测到兼容性问题');
logger.error('应用加载失败', error);
```

## 📚 详细文档

### 🔧 工具函数 (utils)

#### 类型检查工具
```typescript
import {
  isFunction,
  isObject,
  isString,
  isNumber,
  isBoolean,
  isArray,
  isPromise,
  isEmpty,
  isDate,
  isRegExp,
  isError,
  getType,
  isSameType
} from '@micro-core/shared/utils';

// 基础类型检查
isFunction(() => {}); // true
isObject({}); // true
isString('hello'); // true
isNumber(123); // true
isBoolean(true); // true
isArray([]); // true
isPromise(Promise.resolve()); // true

// 空值检查
isEmpty(null); // true
isEmpty(''); // true
isEmpty([]); // true
isEmpty({}); // true

// 特殊类型检查
isDate(new Date()); // true
isRegExp(/test/); // true
isError(new Error()); // true

// 类型获取和比较
getType([]); // 'array'
getType({}); // 'object'
isSameType('a', 'b'); // true
```

#### 对象操作工具
```typescript
import {
  deepMerge,
  deepClone,
  get,
  set,
  unset,
  has,
  pick,
  omit,
  keys,
  values,
  entries,
  flatten,
  unflatten
} from '@micro-core/shared/utils';

// 深度操作
const merged = deepMerge({ a: 1 }, { b: { c: 2 } });
const cloned = deepClone({ a: { b: 1 } });

// 属性访问
const value = get(obj, 'a.b.c', 'default');
set(obj, 'a.b.c', 'new-value');
unset(obj, 'a.b.c');
const exists = has(obj, 'a.b.c');

// 属性选择
const picked = pick(obj, ['name', 'age']);
const omitted = omit(obj, ['password']);

// 对象转换
const flattened = flatten({ a: { b: { c: 1 } } }); // { 'a.b.c': 1 }
const unflattened = unflatten({ 'a.b.c': 1 }); // { a: { b: { c: 1 } } }
```

#### 字符串处理工具
```typescript
import {
  camelCase,
  kebabCase,
  snakeCase,
  pascalCase,
  capitalize,
  uncapitalize,
  truncate,
  pad,
  template,
  reverse,
  escapeHtml,
  unescapeHtml,
  uuid,
  formatBytes
} from '@micro-core/shared/utils';

// 命名转换
camelCase('hello-world'); // 'helloWorld'
kebabCase('helloWorld'); // 'hello-world'
snakeCase('helloWorld'); // 'hello_world'
pascalCase('hello-world'); // 'HelloWorld'

// 字符串操作
capitalize('hello'); // 'Hello'
uncapitalize('Hello'); // 'hello'
truncate('hello world', 5); // 'he...'
pad('hello', 10, '*'); // '**hello***'
reverse('hello'); // 'olleh'

// 模板和转义
template('Hello {{name}}!', { name: 'World' }); // 'Hello World!'
escapeHtml('<div>content</div>'); // '&lt;div&gt;content&lt;/div&gt;'

// 工具函数
uuid(); // 生成UUID
formatBytes(1024); // '1 KB'
```

### 📊 常量定义 (constants)

#### 应用状态枚举
```typescript
import { AppStatus } from '@micro-core/shared/constants';

// 应用生命周期状态
console.log(AppStatus.NOT_LOADED); // 'NOT_LOADED'
console.log(AppStatus.LOADING_SOURCE_CODE); // 'LOADING_SOURCE_CODE'
console.log(AppStatus.NOT_BOOTSTRAPPED); // 'NOT_BOOTSTRAPPED'
console.log(AppStatus.BOOTSTRAPPING); // 'BOOTSTRAPPING'
console.log(AppStatus.NOT_MOUNTED); // 'NOT_MOUNTED'
console.log(AppStatus.MOUNTING); // 'MOUNTING'
console.log(AppStatus.MOUNTED); // 'MOUNTED'
console.log(AppStatus.UNMOUNTING); // 'UNMOUNTING'
console.log(AppStatus.UPDATING); // 'UPDATING'
console.log(AppStatus.LOAD_ERROR); // 'LOAD_ERROR'
```

#### 错误代码枚举
```typescript
import { ErrorCodes } from '@micro-core/shared/constants';

// 系统级错误 (1000-1999)
console.log(ErrorCodes.SYSTEM_INIT_FAILED); // 1000
console.log(ErrorCodes.SYSTEM_CONFIG_ERROR); // 1001

// 应用级错误 (2000-2999)
console.log(ErrorCodes.APP_LOAD_FAILED); // 2000
console.log(ErrorCodes.APP_MOUNT_FAILED); // 2002

// 网络级错误 (3000-3999)
console.log(ErrorCodes.NETWORK_TIMEOUT); // 3001
console.log(ErrorCodes.CORS_ERROR); // 3003

// 权限级错误 (4000-4999)
console.log(ErrorCodes.PERMISSION_DENIED); // 4000
```

#### 事件类型枚举
```typescript
import { EventTypes } from '@micro-core/shared/constants';

// 系统事件
console.log(EventTypes.SYSTEM_INIT_START); // 'system:init:start'
console.log(EventTypes.SYSTEM_INIT_COMPLETE); // 'system:init:complete'

// 应用事件
console.log(EventTypes.APP_LOAD_START); // 'app:load:start'
console.log(EventTypes.APP_MOUNT_COMPLETE); // 'app:mount:complete'

// 路由事件
console.log(EventTypes.ROUTE_CHANGE_START); // 'route:change:start'
```

#### 配置常量
```typescript
import { CONFIG, HTTP_STATUS, REGEX } from '@micro-core/shared/constants';

// 超时配置
console.log(CONFIG.TIMEOUT.DEFAULT); // 30000
console.log(CONFIG.TIMEOUT.RESOURCE_LOAD); // 10000

// 重试配置
console.log(CONFIG.RETRY.MAX_ATTEMPTS); // 3
console.log(CONFIG.RETRY.DELAY_BASE); // 1000

// HTTP状态码
console.log(HTTP_STATUS.OK); // 200
console.log(HTTP_STATUS.NOT_FOUND); // 404

// 正则表达式
console.log(REGEX.URL.test('https://example.com')); // true
console.log(REGEX.EMAIL.test('<EMAIL>')); // true
```

### 📝 类型定义 (types)

#### 微前端应用配置
```typescript
import type {
  MicroAppConfig,
  AppEntry,
  LoaderConfig,
  SandboxConfig,
  StyleIsolationConfig,
  AppLifecycleHooks,
  AppMetadata
} from '@micro-core/shared/types';

// 完整的应用配置
const appConfig: MicroAppConfig = {
  name: 'my-micro-app',
  entry: {
    scripts: ['http://localhost:3000/main.js'],
    styles: ['http://localhost:3000/main.css'],
    html: 'http://localhost:3000/index.html'
  },
  container: '#app-container',
  activeRule: (location) => location.pathname.startsWith('/my-app'),
  props: { theme: 'dark', version: '1.0.0' },
  loader: {
    timeout: 30000,
    retries: 3,
    cache: true,
    crossOrigin: 'anonymous'
  },
  sandbox: {
    enabled: true,
    type: 'proxy',
    strict: true,
    globalWhitelist: ['console', 'setTimeout']
  },
  styleIsolation: {
    enabled: true,
    type: 'scoped',
    prefix: 'my-app'
  },
  hooks: {
    beforeLoad: async (app) => console.log('准备加载', app.name),
    afterMount: async (app) => console.log('挂载完成', app.name)
  },
  meta: {
    version: '1.0.0',
    description: '我的微前端应用',
    author: '<EMAIL>'
  }
};
```

#### 应用实例和生命周期
```typescript
import type {
  MicroAppInstance,
  AppLifecycle,
  Sandbox
} from '@micro-core/shared/types';

// 应用实例
const appInstance: MicroAppInstance = {
  name: 'my-app',
  status: AppStatus.MOUNTED,
  config: appConfig,
  container: document.getElementById('app-container'),
  sandbox: proxySandbox,
  lifecycle: {
    bootstrap: async (props) => { /* 启动逻辑 */ },
    mount: async (props) => { /* 挂载逻辑 */ },
    unmount: async (props) => { /* 卸载逻辑 */ }
  },
  props: { theme: 'dark' },
  createdAt: new Date(),
  updatedAt: new Date()
};

// 沙箱接口
const sandbox: Sandbox = {
  name: 'my-app-sandbox',
  active: false,
  proxy: new Proxy(window, {}),
  activate: () => { /* 激活沙箱 */ },
  deactivate: () => { /* 停用沙箱 */ },
  destroy: () => { /* 销毁沙箱 */ }
};
```

#### 通信和事件类型
```typescript
import type {
  Message,
  CommunicationAdapter,
  EventEmitter,
  EventListener
} from '@micro-core/shared/types';

// 消息通信
const message: Message<any> = {
  id: 'msg-001',
  type: 'user-action',
  from: 'main-app',
  to: 'micro-app',
  data: { action: 'navigate', path: '/dashboard' },
  timestamp: Date.now(),
  needReply: true
};

// 事件监听器
const listener: EventListener<string> = (data) => {
  console.log('接收到数据:', data);
};

// 事件发射器
const emitter: EventEmitter = {
  on: (event, listener) => () => {},
  once: (event, listener) => () => {},
  off: (event, listener) => {},
  emit: (event, data) => {}
};
```

### 🛠️ 辅助函数 (helpers)

#### 错误处理和格式化
```typescript
import {
  formatErrorMessage,
  safeExecute
} from '@micro-core/shared/helpers';

// 错误消息格式化
const error = new Error('应用加载失败');
const formatted = formatErrorMessage(error, {
  appName: 'my-app',
  userId: 123,
  timestamp: Date.now()
});
console.log(formatted);
// 输出: 错误: 应用加载失败 | 上下文: appName: "my-app", userId: 123, timestamp: 1640995200000

// 安全执行函数
const result = await safeExecute(
  () => riskyOperation(),
  {
    fallback: 'default-value',
    timeout: 5000,
    retries: 3,
    retryDelay: 1000,
    onError: (error) => console.error('操作失败:', error)
  }
);
```

#### 应用配置验证和规范化
```typescript
import {
  validateAppConfig,
  normalizeAppConfig
} from '@micro-core/shared/helpers';

// 验证应用配置
const config = {
  name: 'my-app',
  entry: 'http://localhost:3000',
  container: '#app'
};

const errors = validateAppConfig(config);
if (errors.length > 0) {
  console.error('配置验证失败:', errors);
  // 可能输出: ['应用名称(name)必须是非空字符串', '应用入口(entry)必须是非空字符串']
}

// 规范化配置
const normalized = normalizeAppConfig(config);
console.log(normalized);
// 输出: { name: 'my-app', entry: 'http://localhost:3000', container: '#my-app', activeRule: '/my-app', props: {} }
```

#### 性能监控和计时
```typescript
import {
  createPerformanceTimer,
  getMemoryInfo,
  createMemoryMonitor
} from '@micro-core/shared/helpers';

// 性能计时器
const timer = createPerformanceTimer('应用加载');
timer.start();
// ... 执行应用加载逻辑
const duration = timer.end();
console.log(`应用加载耗时: ${duration.toFixed(2)}ms`);

// 内存信息获取
const memoryInfo = getMemoryInfo();
if (memoryInfo) {
  console.log(`内存使用: ${memoryInfo.used} / ${memoryInfo.total} (${memoryInfo.percentage.toFixed(1)}%)`);
  if (memoryInfo.warning) {
    console.warn('内存使用率过高!');
  }
}

// 内存监控器
const memoryMonitor = createMemoryMonitor({
  interval: 5000, // 5秒检查一次
  threshold: 80,  // 80%警告阈值
  onWarning: (info) => console.warn('内存警告:', info),
  onCritical: (info) => console.error('内存临界:', info)
});
memoryMonitor.start();
```

#### 浏览器兼容性检查
```typescript
import {
  checkBrowserCompatibility,
  detectEnvironmentFeatures
} from '@micro-core/shared/helpers';

// 检查特定功能兼容性
const compatibility = checkBrowserCompatibility({
  es6: true,
  proxy: true,
  fetch: true,
  customElements: true,
  shadowDOM: true
});

if (!compatibility.compatible) {
  console.warn('浏览器不兼容，缺少功能:', compatibility.missing);
  // 可能输出: ['Custom Elements', 'Shadow DOM']
}

// 检测环境特性
const features = detectEnvironmentFeatures();
console.log('支持的特性:', {
  es6: features.es6,
  fetch: features.fetch,
  webWorkers: features.webWorkers,
  localStorage: features.localStorage,
  crypto: features.crypto
});
```

#### 资源加载和管理
```typescript
import { loadResource } from '@micro-core/shared/helpers';

// 加载脚本资源
try {
  await loadResource('https://cdn.example.com/library.js', 'script', {
    timeout: 10000,
    retries: 3,
    cache: true,
    integrity: 'sha384-...',
    crossOrigin: 'anonymous'
  });
  console.log('脚本加载成功');
} catch (error) {
  console.error('脚本加载失败:', error);
}

// 加载样式资源
try {
  await loadResource('https://cdn.example.com/styles.css', 'style', {
    timeout: 5000,
    cache: true
  });
  console.log('样式加载成功');
} catch (error) {
  console.error('样式加载失败:', error);
}

// 加载JSON数据
try {
  const data = await loadResource('https://api.example.com/config.json', 'json', {
    timeout: 8000,
    retries: 2
  });
  console.log('配置数据:', data);
} catch (error) {
  console.error('数据加载失败:', error);
}
```

#### 数据验证和清理
```typescript
import { validateAndSanitize } from '@micro-core/shared/helpers';

// 定义验证规则
const rules = {
  name: {
    required: true,
    type: 'string' as const,
    minLength: 2,
    maxLength: 50
  },
  age: {
    required: true,
    type: 'number' as const,
    min: 0,
    max: 150
  },
  email: {
    required: true,
    type: 'string' as const,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  password: {
    required: true,
    type: 'string' as const,
    custom: (value: string) => value.length >= 8 || '密码长度至少8位'
  }
};

// 验证和清理数据
const userData = {
  name: '  John<script>alert("xss")</script>  ',
  age: 25,
  email: '<EMAIL>',
  password: 'password123'
};

const result = validateAndSanitize(userData, rules, {
  sanitize: true,
  strict: false
});

if (result.valid) {
  console.log('验证通过，清理后的数据:', result.sanitized);
  // 输出: { name: 'John', age: 25, email: '<EMAIL>', password: 'password123' }
} else {
  console.error('验证失败:', result.errors);
}
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
pnpm test

# 运行特定子包测试
pnpm test:utils
pnpm test:helpers
pnpm test:constants
pnpm test:types

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 监听模式运行测试
pnpm test:watch
```

### 测试覆盖率

当前测试覆盖情况：
- **utils**: 95%+ 覆盖率，包含所有核心工具函数
- **helpers**: 90%+ 覆盖率，包含业务逻辑辅助函数
- **constants**: 100% 覆盖率，验证所有常量定义
- **types**: 100% 覆盖率，验证类型定义正确性

### 测试示例

```typescript
import { describe, it, expect } from 'vitest';
import { isFunction, deepMerge } from '@micro-core/shared/utils';

describe('工具函数测试', () => {
  it('应该正确识别函数类型', () => {
    expect(isFunction(() => {})).toBe(true);
    expect(isFunction('not a function')).toBe(false);
  });

  it('应该正确深度合并对象', () => {
    const target = { a: 1, b: { c: 2 } };
    const source = { b: { d: 3 }, e: 4 };
    const result = deepMerge(target, source);

    expect(result).toEqual({
      a: 1,
      b: { c: 2, d: 3 },
      e: 4
    });
  });
});
```

## 🔧 开发指南

### 项目结构

```
packages/shared/
├── constants/          # 常量定义
│   ├── src/index.ts   # 导出所有常量
│   └── __tests__/     # 常量测试
├── types/             # 类型定义
│   ├── src/index.ts   # 导出所有类型
│   └── __tests__/     # 类型测试
├── utils/             # 工具函数
│   ├── src/           # 按功能分类的工具函数
│   └── __tests__/     # 工具函数测试
├── helpers/           # 辅助函数
│   ├── src/index.ts   # 高级业务逻辑辅助
│   └── __tests__/     # 辅助函数测试
└── test/              # 测试配置
    └── setup.ts       # 测试环境设置
```

### 开发规范

#### 代码风格
- 使用 TypeScript 进行开发
- 遵循 ESLint 和 Prettier 配置
- 使用 JSDoc 注释文档化所有公共 API
- 保持函数单一职责原则

#### 命名规范
- 函数名使用 camelCase
- 常量使用 UPPER_SNAKE_CASE
- 类型名使用 PascalCase
- 文件名使用 kebab-case

#### 测试要求
- 所有新功能必须包含测试用例
- 测试覆盖率不低于 90%
- 使用 describe/it 结构组织测试
- 测试名称应清晰描述测试场景

### 添加新功能

#### 1. 添加工具函数

```typescript
// packages/shared/utils/src/new-feature.ts
/**
 * 新功能描述
 * @param param1 参数1描述
 * @param param2 参数2描述
 * @returns 返回值描述
 */
export function newFeature(param1: string, param2: number): boolean {
  // 实现逻辑
  return true;
}
```

#### 2. 添加类型定义

```typescript
// packages/shared/types/src/index.ts
/**
 * 新类型接口描述
 */
export interface NewInterface {
  /** 属性描述 */
  property: string;
  /** 可选属性描述 */
  optionalProperty?: number;
}
```

#### 3. 添加常量定义

```typescript
// packages/shared/constants/src/index.ts
/**
 * 新常量枚举描述
 */
export enum NewConstants {
  /** 常量1描述 */
  CONSTANT_ONE = 'CONSTANT_ONE',
  /** 常量2描述 */
  CONSTANT_TWO = 'CONSTANT_TWO'
}
```

#### 4. 添加测试用例

```typescript
// packages/shared/utils/__tests__/new-feature.test.ts
import { describe, it, expect } from 'vitest';
import { newFeature } from '../src/new-feature';

describe('newFeature', () => {
  it('应该正确处理正常输入', () => {
    const result = newFeature('test', 123);
    expect(result).toBe(true);
  });

  it('应该处理边界情况', () => {
    const result = newFeature('', 0);
    expect(result).toBe(false);
  });
});
```

## 📋 最佳实践

### 1. 类型安全
```typescript
// ✅ 好的做法：使用严格的类型定义
function processUser(user: { id: number; name: string; email: string }): void {
  // 处理逻辑
}

// ❌ 避免：使用 any 类型
function processUser(user: any): void {
  // 处理逻辑
}
```

### 2. 错误处理
```typescript
// ✅ 好的做法：使用 safeExecute 包装可能失败的操作
const result = await safeExecute(
  () => riskyOperation(),
  {
    fallback: 'default-value',
    onError: (error) => logger.error('操作失败', error)
  }
);

// ❌ 避免：直接调用可能失败的操作
const result = await riskyOperation(); // 可能抛出异常
```

### 3. 性能监控
```typescript
// ✅ 好的做法：监控关键操作的性能
const timer = createPerformanceTimer('关键操作');
timer.start();
await criticalOperation();
const duration = timer.end();
if (duration > 1000) {
  logger.warn(`操作耗时过长: ${duration}ms`);
}
```

### 4. 配置验证
```typescript
// ✅ 好的做法：验证配置后再使用
const errors = validateAppConfig(config);
if (errors.length > 0) {
  throw new Error(`配置无效: ${errors.join(', ')}`);
}
const normalizedConfig = normalizeAppConfig(config);
```

## 🤝 贡献指南

### 提交代码

1. Fork 项目仓库
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -m 'feat: 添加新功能'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建 Pull Request

### 提交信息规范

使用 [Conventional Commits](https://conventionalcommits.org/) 规范：

- `feat:` 新功能
- `fix:` 修复问题
- `docs:` 文档更新
- `style:` 代码格式调整
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建或辅助工具变动

### 代码审查

所有代码更改都需要通过代码审查：

- 确保代码符合项目规范
- 验证测试覆盖率达标
- 检查文档是否完整
- 确认向后兼容性

## 📄 许可证

MIT License - 详见 [LICENSE](../../LICENSE) 文件。

## 🔗 相关链接

- [Micro-Core 主项目](../../README.md)
- [API 文档](./docs/api.md)
- [更新日志](./CHANGELOG.md)
- [问题反馈](../../issues)

---

**Micro-Core Shared** - 为微前端架构提供强大的基础设施支持 🚀
```

#### 性能计时
```typescript
import { createPerformanceTimer } from '@micro-core/shared/helpers';

const timer = createPerformanceTimer();
// ... 执行一些操作
const duration = timer.end();
console.log(`操作耗时: ${duration}ms`);
```

#### 浏览器兼容性检查
```typescript
import { checkBrowserCompatibility } from '@micro-core/shared/helpers';

const { compatible, missing } = checkBrowserCompatibility();
if (!compatible) {
  console.warn('浏览器不兼容，缺少功能:', missing);
}
```

## 🧪 测试

```bash
# 运行测试
pnpm test

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 运行测试 UI
pnpm test:ui
```

## 📝 开发

```bash
# 开发模式
pnpm dev

# 构建
pnpm build

# 类型检查
pnpm type-check

# 代码检查
pnpm lint

# 代码格式化
pnpm lint:fix
```

## 📄 许可证

MIT License - 查看 [LICENSE](../../LICENSE) 文件了解详情。

## 🤝 贡献

欢迎贡献代码！请查看 [贡献指南](../../CONTRIBUTING.md) 了解详情。

## 📞 支持

- 📖 [文档](https://micro-core.dev)
- 🐛 [问题反馈](https://github.com/echo008/micro-core/issues)
- 💬 [讨论](https://github.com/echo008/micro-core/discussions)