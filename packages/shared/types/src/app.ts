/**
 * @fileoverview 微前端应用相关类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 微前端应用配置接口
 */
export interface MicroAppConfig {
    /** 应用名称，必须唯一 */
    name: string;
    
    /** 应用入口地址 */
    entry: string;
    
    /** 应用容器选择器或DOM元素 */
    container?: string | HTMLElement;
    
    /** 应用激活规则 */
    activeRule?: string | ((location: Location) => boolean);
    
    /** 传递给应用的属性 */
    props?: Record<string, any>;
    
    /** 加载器配置 */
    loader?: {
        /** 加载中显示的内容 */
        loading?: HTMLElement | string;
        /** 错误时显示的内容 */
        error?: HTMLElement | string;
    };
    
    /** 沙箱配置 */
    sandbox?: {
        /** 严格样式隔离 */
        strictStyleIsolation?: boolean;
        /** 实验性样式隔离 */
        experimentalStyleIsolation?: boolean;
        /** 资源过滤器 */
        excludeAssetFilter?: (assetUrl: string) => boolean;
        /** 全局上下文 */
        globalContext?: Record<string, any>;
    };
    
    /** 生命周期钩子 */
    lifecycle?: {
        /** 加载前钩子 */
        beforeLoad?: (app: MicroAppConfig) => Promise<void> | void;
        /** 挂载前钩子 */
        beforeMount?: (app: MicroAppConfig) => Promise<void> | void;
        /** 挂载后钩子 */
        afterMount?: (app: MicroAppConfig) => Promise<void> | void;
        /** 卸载前钩子 */
        beforeUnmount?: (app: MicroAppConfig) => Promise<void> | void;
        /** 卸载后钩子 */
        afterUnmount?: (app: MicroAppConfig) => Promise<void> | void;
    };
}

/**
 * 应用状态枚举
 */
export enum AppStatus {
    /** 未加载 */
    NOT_LOADED = 'NOT_LOADED',
    /** 加载中 */
    LOADING = 'LOADING',
    /** 未引导 */
    NOT_BOOTSTRAPPED = 'NOT_BOOTSTRAPPED',
    /** 未挂载 */
    NOT_MOUNTED = 'NOT_MOUNTED',
    /** 已挂载 */
    MOUNTED = 'MOUNTED',
    /** 卸载中 */
    UNMOUNTING = 'UNMOUNTING',
    /** 卸载中 */
    UNLOADING = 'UNLOADING',
    /** 跳过（损坏） */
    SKIP_BECAUSE_BROKEN = 'SKIP_BECAUSE_BROKEN'
}

/**
 * 应用实例接口
 */
export interface AppInstance {
    /** 应用名称 */
    name: string;
    /** 应用状态 */
    status: AppStatus;
    /** 应用配置 */
    config: MicroAppConfig;
    /** 应用容器 */
    container?: HTMLElement;
    /** 应用实例 */
    instance?: any;
    /** 错误信息 */
    error?: Error;
    /** 加载时间 */
    loadTime?: number;
    /** 挂载时间 */
    mountTime?: number;
}

/**
 * 应用注册表接口
 */
export interface AppRegistry {
    /** 注册应用 */
    register(config: MicroAppConfig): void;
    /** 注销应用 */
    unregister(name: string): void;
    /** 获取应用 */
    getApp(name: string): AppInstance | undefined;
    /** 获取所有应用 */
    getApps(): AppInstance[];
    /** 获取活跃应用 */
    getActiveApps(): AppInstance[];
}