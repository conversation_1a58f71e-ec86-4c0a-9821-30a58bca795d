/**
 * @fileoverview Sidecar 相关类型定义
 * @description 边车模式的核心类型定义，包括代理、隔离、桥接等配置
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 边车代理配置
 */
export interface SidecarProxyConfig {
    /** 代理端口 */
    port?: number;
    /** 代理主机 */
    host?: string;
    /** 是否启用HTTPS */
    https?: boolean;
    /** 代理规则 */
    rules?: ProxyRule[];
    /** 中间件 */
    middleware?: ProxyMiddleware[];
    /** 缓存配置 */
    cache?: CacheConfig;
    /** 安全配置 */
    security?: SecurityConfig;
}

/**
 * 代理规则
 */
export interface ProxyRule {
    /** 匹配路径 */
    path: string | RegExp;
    /** 目标地址 */
    target: string;
    /** 是否改变源 */
    changeOrigin?: boolean;
    /** 路径重写规则 */
    pathRewrite?: Record<string, string>;
    /** 自定义请求头 */
    headers?: Record<string, string>;
    /** 超时时间 */
    timeout?: number;
    /** 重试次数 */
    retry?: number;
}

/**
 * 代理中间件
 */
export interface ProxyMiddleware {
    /** 中间件名称 */
    name: string;
    /** 处理函数 */
    handler: (req: any, res: any, next: () => void) => void;
    /** 优先级 */
    priority?: number;
}

/**
 * 缓存配置
 */
export interface CacheConfig {
    /** 是否启用缓存 */
    enabled?: boolean;
    /** 缓存类型 */
    type?: 'memory' | 'redis' | 'file';
    /** 缓存时间 (秒) */
    ttl?: number;
    /** 缓存前缀 */
    prefix?: string;
    /** 最大缓存大小 */
    maxSize?: number;
}

/**
 * 安全配置
 */
export interface SecurityConfig {
    /** CORS配置 */
    cors?: {
        origin?: string | string[] | boolean;
        methods?: string[];
        headers?: string[];
        credentials?: boolean;
    };
    /** CSP配置 */
    csp?: {
        directives?: Record<string, string[]>;
    };
    /** 请求限制 */
    rateLimit?: {
        windowMs?: number;
        max?: number;
        message?: string;
    };
}

/**
 * 资源隔离配置
 */
export interface IsolationConfig {
    /** 样式隔离 */
    style?: StyleIsolationConfig;
    /** 脚本隔离 */
    script?: ScriptIsolationConfig;
    /** 全局变量隔离 */
    global?: GlobalIsolationConfig;
    /** 事件隔离 */
    event?: EventIsolationConfig;
}

/**
 * 样式隔离配置
 */
export interface StyleIsolationConfig {
    /** 隔离模式 */
    mode?: 'scoped' | 'shadow' | 'namespace';
    /** 命名空间前缀 */
    prefix?: string;
    /** 排除选择器 */
    excludeSelectors?: string[];
    /** 是否隔离全局样式 */
    isolateGlobal?: boolean;
}

/**
 * 脚本隔离配置
 */
export interface ScriptIsolationConfig {
    /** 隔离模式 */
    mode?: 'proxy' | 'snapshot' | 'iframe';
    /** 白名单变量 */
    whitelist?: string[];
    /** 黑名单变量 */
    blacklist?: string[];
    /** 是否严格模式 */
    strict?: boolean;
}

/**
 * 全局变量隔离配置
 */
export interface GlobalIsolationConfig {
    /** 共享变量 */
    shared?: string[];
    /** 私有变量 */
    private?: string[];
    /** 变量映射 */
    mapping?: Record<string, string>;
}

/**
 * 事件隔离配置
 */
export interface EventIsolationConfig {
    /** 是否隔离DOM事件 */
    dom?: boolean;
    /** 是否隔离自定义事件 */
    custom?: boolean;
    /** 事件命名空间 */
    namespace?: string;
}

/**
 * 通信桥接配置
 */
export interface BridgeConfig {
    /** 通信协议 */
    protocol?: 'postMessage' | 'customEvent' | 'sharedWorker';
    /** 消息格式 */
    format?: 'json' | 'binary' | 'custom';
    /** 序列化器 */
    serializer?: MessageSerializer;
    /** 消息过滤器 */
    filter?: MessageFilter;
    /** 超时时间 */
    timeout?: number;
    /** 重试配置 */
    retry?: RetryConfig;
}

/**
 * 消息序列化器
 */
export interface MessageSerializer {
    /** 序列化方法 */
    serialize: (data: any) => string | ArrayBuffer;
    /** 反序列化方法 */
    deserialize: (data: string | ArrayBuffer) => any;
}

/**
 * 消息过滤器
 */
export interface MessageFilter {
    /** 过滤函数 */
    filter: (message: BridgeMessage) => boolean;
    /** 过滤规则 */
    rules?: FilterRule[];
}

/**
 * 过滤规则
 */
export interface FilterRule {
    /** 规则类型 */
    type: 'allow' | 'deny';
    /** 匹配条件 */
    condition: string | RegExp | ((message: BridgeMessage) => boolean);
}

/**
 * 重试配置
 */
export interface RetryConfig {
    /** 最大重试次数 */
    maxAttempts?: number;
    /** 重试延迟 (毫秒) */
    delay?: number;
    /** 退避策略 */
    backoff?: 'linear' | 'exponential';
    /** 重试条件 */
    condition?: (error: Error) => boolean;
}

/**
 * 桥接消息
 */
export interface BridgeMessage {
    /** 消息ID */
    id: string;
    /** 消息类型 */
    type: string;
    /** 发送方 */
    from: string;
    /** 接收方 */
    to: string;
    /** 消息数据 */
    data: any;
    /** 时间戳 */
    timestamp: number;
    /** 元数据 */
    meta?: Record<string, any>;
}

/**
 * 边车状态
 */
export type SidecarStatus = 'idle' | 'starting' | 'running' | 'stopping' | 'stopped' | 'error';

/**
 * 边车实例配置
 */
export interface SidecarConfig {
    /** 应用信息 */
    app?: {
        name: string;
        version: string;
        entry: string;
        [key: string]: any;
    };
    /** 代理配置 */
    proxy?: SidecarProxyConfig;
    /** 隔离配置 */
    isolation?: IsolationConfig;
    /** 桥接配置 */
    bridge?: BridgeConfig;
    /** 是否自动启动 */
    autoStart?: boolean;
    /** 是否启用调试 */
    debug?: boolean;
    /** 错误处理器 */
    errorHandler?: (error: Error) => void;
}

/**
 * 边车事件
 */
export interface SidecarEvents {
    'status-change': { status: SidecarStatus; previous: SidecarStatus };
    'proxy-start': { port: number; host: string };
    'proxy-stop': {};
    'message-receive': { message: BridgeMessage };
    'message-send': { message: BridgeMessage };
    'error': { error: Error; context?: string };
}

/**
 * 边车统计信息
 */
export interface SidecarStats {
    /** 启动时间 */
    startTime?: number;
    /** 运行时间 */
    uptime: number;
    /** 代理请求数 */
    proxyRequests: number;
    /** 发送消息数 */
    messagesSent: number;
    /** 接收消息数 */
    messagesReceived: number;
    /** 错误数 */
    errors: number;
    /** 内存使用情况 */
    memoryUsage?: NodeJS.MemoryUsage;
}

/**
 * 边车实例接口
 */
export interface ISidecar {
    /** 边车ID */
    readonly id: string;
    /** 应用信息 */
    readonly app: any; // 使用 any 避免循环依赖
    /** 当前状态 */
    readonly status: SidecarStatus;
    /** 配置信息 */
    readonly config: SidecarConfig;

    /** 启动边车 */
    start(): Promise<void>;
    /** 停止边车 */
    stop(): Promise<void>;
    /** 重启边车 */
    restart(): Promise<void>;
    /** 发送消息 */
    sendMessage(message: Omit<BridgeMessage, 'id' | 'timestamp' | 'from'>): Promise<void>;
    /** 获取代理URL */
    getProxyUrl(): string | null;
    /** 获取统计信息 */
    getStats(): SidecarStats;
}

/**
 * 边车管理器配置
 */
export interface SidecarManagerConfig {
    /** 最大边车数量 */
    maxSidecars?: number;
    /** 默认代理配置 */
    defaultProxy?: SidecarProxyConfig;
    /** 默认隔离配置 */
    defaultIsolation?: IsolationConfig;
    /** 默认桥接配置 */
    defaultBridge?: BridgeConfig;
    /** 健康检查间隔 */
    healthCheckInterval?: number;
    /** 是否自动清理 */
    autoCleanup?: boolean;
}

/**
 * 边车管理器接口
 */
export interface ISidecarManager {
    /** 创建边车 */
    createSidecar(config: SidecarConfig): Promise<ISidecar>;
    /** 获取边车 */
    getSidecar(appName: string): ISidecar | null;
    /** 获取所有边车 */
    getAllSidecars(): ISidecar[];
    /** 销毁边车 */
    destroySidecar(appName: string): Promise<void>;
    /** 启动所有边车 */
    startAll(): Promise<void>;
    /** 停止所有边车 */
    stopAll(): Promise<void>;
    /** 获取管理器统计 */
    getStats(): SidecarManagerStats;
}

/**
 * 边车管理器统计
 */
export interface SidecarManagerStats {
    /** 总边车数 */
    totalSidecars: number;
    /** 运行中边车数 */
    runningSidecars: number;
    /** 总代理请求数 */
    totalProxyRequests: number;
    /** 总消息数 */
    totalMessages: number;
    /** 总错误数 */
    totalErrors: number;
}

/**
 * 代理服务器接口
 */
export interface IProxyServer {
    /** 启动服务器 */
    start(): Promise<void>;
    /** 停止服务器 */
    stop(): Promise<void>;
    /** 获取服务器URL */
    getUrl(): string;
    /** 添加代理规则 */
    addRule(rule: ProxyRule): void;
    /** 移除代理规则 */
    removeRule(path: string): void;
    /** 获取统计信息 */
    getStats(): ProxyStats;
}

/**
 * 代理统计信息
 */
export interface ProxyStats {
    /** 总请求数 */
    totalRequests: number;
    /** 成功请求数 */
    successRequests: number;
    /** 失败请求数 */
    failedRequests: number;
    /** 平均响应时间 */
    averageResponseTime: number;
    /** 缓存命中率 */
    cacheHitRate: number;
}

/**
 * 隔离容器接口
 */
export interface IIsolationContainer {
    /** 初始化容器 */
    initialize(): Promise<void>;
    /** 销毁容器 */
    destroy(): Promise<void>;
    /** 执行代码 */
    execute(code: string): Promise<any>;
    /** 设置全局变量 */
    setGlobal(name: string, value: any): void;
    /** 获取全局变量 */
    getGlobal(name: string): any;
    /** 清理全局变量 */
    clearGlobals(): void;
}

/**
 * 通信桥接器接口
 */
export interface IBridge {
    /** 初始化桥接器 */
    initialize(): Promise<void>;
    /** 销毁桥接器 */
    destroy(): Promise<void>;
    /** 发送消息 */
    send(message: BridgeMessage): Promise<void>;
    /** 监听消息 */
    listen(callback: (message: BridgeMessage) => void): () => void;
    /** 获取统计信息 */
    getStats(): BridgeStats;
}

/**
 * 桥接统计信息
 */
export interface BridgeStats {
    /** 发送消息数 */
    messagesSent: number;
    /** 接收消息数 */
    messagesReceived: number;
    /** 失败消息数 */
    failedMessages: number;
    /** 平均延迟 */
    averageLatency: number;
}
