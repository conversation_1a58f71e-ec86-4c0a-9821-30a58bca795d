/**
 * @fileoverview Builder Types
 * @description 构建器相关的统一类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

/**
 * 基础构建器配置
 */
export interface BaseBuilderConfig {
    /** 构建器名称 */
    name: string;
    /** 框架类型 */
    framework?: string;
    /** 入口文件 */
    entry?: string;
    /** 输出目录 */
    outDir?: string;
    /** 构建模式 */
    mode?: 'development' | 'production';
    /** 是否生成 source map */
    sourcemap?: boolean;
    /** 是否压缩代码 */
    minify?: boolean;
    /** 外部依赖 */
    externals?: Record<string, string>;
    /** 路径别名 */
    alias?: Record<string, string>;
    /** 环境变量 */
    define?: Record<string, any>;
}

/**
 * 构建结果
 */
export interface BuildResult {
    /** 构建是否成功 */
    success: boolean;
    /** 构建时间 */
    buildTime?: string;
    /** 输出大小 */
    outputSize?: string;
    /** 输出文件列表 */
    outputs?: BuildOutput[];
    /** 资源列表 */
    assets?: BuildAsset[];
    /** 警告信息 */
    warnings?: string[];
    /** 错误信息 */
    errors?: BuildError[];
    /** 构建统计信息 */
    stats?: BuildStats;
    /** 元数据 */
    metadata?: Record<string, any>;
}

/**
 * 构建输出
 */
export interface BuildOutput {
    /** 输出类型 */
    type: 'chunk' | 'asset';
    /** 文件名 */
    fileName: string;
    /** 文件大小 */
    size: number;
    /** 代码内容（仅 chunk 类型） */
    code?: string;
    /** source map */
    map?: string;
}

/**
 * 构建资源
 */
export interface BuildAsset {
    /** 资源名称 */
    name: string;
    /** 资源大小 */
    size: string;
    /** 资源类型 */
    type: 'javascript' | 'stylesheet' | 'html' | 'image' | 'font' | 'other';
    /** 是否为入口文件 */
    isEntry?: boolean;
}

/**
 * 构建错误
 */
export interface BuildError {
    /** 错误消息 */
    message: string;
    /** 错误位置 */
    location?: string;
    /** 错误堆栈 */
    stack?: string;
}

/**
 * 构建统计信息
 */
export interface BuildStats {
    /** 构建时长 */
    duration: number;
    /** 文件数量 */
    fileCount: number;
    /** 总大小 */
    totalSize: number;
    /** 错误数量 */
    errors: number;
    /** 警告数量 */
    warnings: number;
}

/**
 * 开发服务器配置
 */
export interface DevServerConfig {
    /** 端口号 */
    port?: number;
    /** 主机地址 */
    host?: string;
    /** 是否启用 HTTPS */
    https?: boolean;
    /** 是否启用 CORS */
    cors?: boolean;
    /** 代理配置 */
    proxy?: Record<string, any>;
    /** 是否自动打开浏览器 */
    open?: boolean;
}

/**
 * 构建器接口
 */
export interface Builder {
    /** 构建器名称 */
    readonly name: string;
    /** 构建器版本 */
    readonly version: string;

    /** 构建应用 */
    build(): Promise<BuildResult>;
    /** 启动开发服务器 */
    serve?(): Promise<void>;
    /** 监听文件变化 */
    watch?(callback?: (event: string, file?: string) => void): Promise<void>;
    /** 停止构建器 */
    stop(): Promise<void>;
    /** 获取构建器信息 */
    getInfo(): any;
}

/**
 * Vite 构建器配置
 */
export interface ViteBuilderConfig extends BaseBuilderConfig {
    /** Vite 配置 */
    vite?: any;
    /** 微应用配置 */
    microApp?: {
        entry?: string;
        outDir?: string;
        publicPath?: string;
        federation?: boolean;
        codeSplitting?: boolean;
        externals?: string[];
        shared?: Record<string, any>;
    };
    /** 开发服务器配置 */
    devServer?: DevServerConfig & {
        hmr?: boolean;
    };
    /** 构建配置 */
    build?: {
        target?: string;
        format?: string;
        lib?: {
            entry: string;
            name: string;
            formats?: string[];
            fileName?: string;
        };
        rollupOptions?: any;
        minify?: boolean;
        sourcemap?: boolean;
    };
}

/**
 * Webpack 构建器配置
 */
export interface WebpackBuilderConfig extends BaseBuilderConfig {
    /** Webpack 配置 */
    webpackConfig?: any;
    /** 是否启用开发服务器 */
    devServer?: boolean;
    /** 开发服务器配置 */
    devServerConfig?: DevServerConfig & {
        hot?: boolean;
        static?: {
            directory: string;
            publicPath?: string;
        };
        historyApiFallback?: boolean;
        compress?: boolean;
    };
    /** 是否启用热更新 */
    hmr?: boolean;
    /** 输出目录 */
    outputDir?: string;
    /** 公共路径 */
    publicPath?: string;
    /** 是否分析包大小 */
    analyze?: boolean;
    /** HTML 模板路径 */
    template?: string;
}

/**
 * Rollup 构建器配置
 */
export interface RollupBuilderConfig extends BaseBuilderConfig {
    /** Rollup 配置 */
    rollupConfig?: any;
    /** 输出配置 */
    output?: any | any[];
    /** 是否启用监听模式 */
    watch?: boolean;
    /** 监听配置 */
    watchOptions?: any;
    /** 输出格式 */
    format?: 'es' | 'cjs' | 'umd' | 'iife';
    /** 外部依赖 */
    external?: string[] | ((id: string) => boolean);
    /** 平台 */
    platform?: 'browser' | 'node' | 'neutral';
}

/**
 * ESBuild 构建器配置
 */
export interface ESBuildBuilderConfig extends BaseBuilderConfig {
    /** ESBuild 配置 */
    esbuildConfig?: any;
    /** 是否启用监听模式 */
    watch?: boolean;
    /** 是否启用开发服务器 */
    serve?: boolean;
    /** 开发服务器配置 */
    serveOptions?: any;
    /** 输出目录 */
    outdir?: string;
    /** 输出文件 */
    outfile?: string;
    /** 目标环境 */
    target?: string | string[];
    /** 输出格式 */
    format?: 'iife' | 'cjs' | 'esm';
    /** 是否分割代码 */
    splitting?: boolean;
    /** 外部依赖 */
    external?: string[];
    /** 平台 */
    platform?: 'browser' | 'node' | 'neutral';
}

/**
 * 构建器工厂接口
 */
export interface BuilderFactory {
    /** 构建器类型 */
    type: string;
    /** 创建构建器实例 */
    create: (config: any) => Builder;
    /** 元数据 */
    metadata: {
        name: string;
        version: string;
        description: string;
        author?: string;
    };
}

/**
 * 构建器注册表
 */
export interface BuilderRegistry {
    /** 注册构建器 */
    register(factory: BuilderFactory): void;
    /** 注销构建器 */
    unregister(type: string): void;
    /** 获取构建器工厂 */
    get(type: string): BuilderFactory | undefined;
    /** 获取所有构建器工厂 */
    getAll(): BuilderFactory[];
    /** 检查构建器是否存在 */
    has(type: string): boolean;
    /** 创建构建器实例 */
    create(type: string, config: any): Builder;
}
