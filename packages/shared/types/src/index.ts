/**
 * @fileoverview 共享类型定义入口
 * @description 统一导出所有共享类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

// 基础类型
export * from './base';

// 应用相关类型
export * from './app';

// 适配器相关类型
export * from './adapter';

// 构建器相关类型
export * from './builder';

// 沙箱相关类型
export * from './sandbox';

// 生命周期相关类型
export * from './lifecycle';

/**
 * 常用工具类型重新导出
 * 这些类型在 base.ts 中已定义，这里重新导出以保持向后兼容
 */

// 深度只读类型
export type { DeepReadonly } from './base';

// 深度必需类型
export type { DeepRequired } from './base';

// 深度可选类型
export type { DeepPartial } from './base';

// 可空类型
export type { Nullable } from './base';

// 可选类型
export type { Optional } from './base';

// 可能类型
export type { Maybe } from './base';

// 键值对类型
export type { KeyValue } from './base';

// 函数类型
export type { AsyncFn, Fn } from './base';

// 构造函数类型
export type { AbstractConstructor, Constructor } from './base';

// 数组或单个元素类型
export type { ArrayOrSingle } from './base';

// Promise解包类型
export type { Awaited, DeepAwaited } from './base';

// 联合转交集类型
export type { UnionToIntersection } from './base';

// 值类型
export type { ValueOf } from './base';

/**
 * 框架特定类型定义
 */

// React 相关类型
export interface ReactConfig {
    /** React 版本 */
    version?: string;
    /** 是否启用严格模式 */
    strictMode?: boolean;
    /** 是否启用并发特性 */
    concurrent?: boolean;
    /** 自定义渲染器 */
    renderer?: 'react-dom' | 'react-native' | string;
}

// Vue 相关类型
export interface VueConfig {
    /** Vue 版本 */
    version?: '2' | '3';
    /** 是否启用生产提示 */
    productionTip?: boolean;
    /** 全局配置 */
    globalProperties?: Record<string, any>;
    /** 编译器选项 */
    compilerOptions?: Record<string, any>;
}

// Angular 相关类型
export interface AngularConfig {
    /** Angular 版本 */
    version?: string;
    /** 是否启用生产模式 */
    production?: boolean;
    /** 区域配置 */
    zone?: {
        enabled?: boolean;
        config?: Record<string, any>;
    };
    /** 平台配置 */
    platform?: 'browser' | 'server' | 'webworker';
}

/**
 * 构建器特定类型定义
 */

// Webpack 特定配置
export interface WebpackSpecificConfig {
    /** Webpack 版本 */
    version?: string;
    /** 是否启用模块联邦 */
    moduleFederation?: boolean;
    /** 开发服务器配置 */
    devServer?: {
        port?: number;
        host?: string;
        hot?: boolean;
        open?: boolean;
    };
    /** 优化配置 */
    optimization?: {
        splitChunks?: boolean;
        minimize?: boolean;
        sideEffects?: boolean;
    };
}

// Vite 特定配置
export interface ViteSpecificConfig {
    /** Vite 版本 */
    version?: string;
    /** 是否启用 HMR */
    hmr?: boolean;
    /** 服务器配置 */
    server?: {
        port?: number;
        host?: string;
        open?: boolean;
        cors?: boolean;
    };
    /** 构建配置 */
    build?: {
        target?: string;
        format?: string;
        lib?: boolean;
    };
}

// Rollup 特定配置
export interface RollupSpecificConfig {
    /** Rollup 版本 */
    version?: string;
    /** 输出格式 */
    format?: 'es' | 'cjs' | 'umd' | 'iife';
    /** 是否启用监听模式 */
    watch?: boolean;
    /** 外部依赖处理 */
    external?: string[] | ((id: string) => boolean);
}

// ESBuild 特定配置
export interface ESBuildSpecificConfig {
    /** ESBuild 版本 */
    version?: string;
    /** 目标环境 */
    target?: string | string[];
    /** 输出格式 */
    format?: 'iife' | 'cjs' | 'esm';
    /** 是否启用代码分割 */
    splitting?: boolean;
    /** 平台 */
    platform?: 'browser' | 'node' | 'neutral';
}

/**
 * 错误和监控相关类型
 */

// 错误级别
export type ErrorLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal';

// 错误类型
export type ErrorType = 'system' | 'network' | 'business' | 'validation' | 'permission' | 'timeout' | 'unknown';

// 健康状态
export type HealthStatus = 'healthy' | 'warning' | 'unhealthy' | 'unknown';

// 性能指标类型
export type MetricType = 'counter' | 'gauge' | 'histogram' | 'timer';

/**
 * 缓存相关类型
 */

// 缓存策略
export type CacheStrategy = 'lru' | 'fifo' | 'lfu' | 'ttl';

// 缓存配置
export interface CacheConfig {
    /** 缓存策略 */
    strategy?: CacheStrategy;
    /** 最大缓存数量 */
    maxSize?: number;
    /** 默认TTL（毫秒） */
    defaultTTL?: number;
    /** 是否启用统计 */
    enableStats?: boolean;
}

/**
 * 路由相关类型
 */

// 路由模式
export type RouterMode = 'hash' | 'history' | 'memory';

// 路由配置
export interface RouteConfig {
    /** 路由路径 */
    path: string;
    /** 路由名称 */
    name?: string;
    /** 路由组件 */
    component?: any;
    /** 子路由 */
    children?: RouteConfig[];
    /** 路由元信息 */
    meta?: Record<string, any>;
    /** 路由守卫 */
    beforeEnter?: (to: any, from: any, next: any) => void;
}

/**
 * 插件相关类型
 */

// 插件类型
export type PluginType = 'loader' | 'transformer' | 'optimizer' | 'analyzer' | 'reporter';

// 插件配置
export interface PluginConfig {
    /** 插件名称 */
    name: string;
    /** 插件类型 */
    type: PluginType;
    /** 插件选项 */
    options?: Record<string, any>;
    /** 是否启用 */
    enabled?: boolean;
    /** 执行顺序 */
    order?: number;
}

/**
 * 中间件相关类型
 */

// 中间件上下文
export interface MiddlewareContext<T = any> {
    /** 请求数据 */
    request?: T;
    /** 响应数据 */
    response?: any;
    /** 状态数据 */
    state?: Record<string, any>;
    /** 元数据 */
    metadata?: Record<string, any>;
}

/**
 * 事件相关类型
 */

// 事件类型
export type EventType = 'load' | 'mount' | 'unmount' | 'update' | 'error' | 'destroy';

// 事件数据
export interface EventData<T = any> {
    /** 事件类型 */
    type: EventType;
    /** 事件目标 */
    target?: string;
    /** 事件数据 */
    data?: T;
    /** 时间戳 */
    timestamp: number;
}

/**
 * 配置相关类型
 */

// 环境类型
export type Environment = 'development' | 'production' | 'test';

// 日志级别
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// 全局配置
export interface GlobalConfig {
    /** 环境 */
    env?: Environment;
    /** 日志级别 */
    logLevel?: LogLevel;
    /** 是否启用调试 */
    debug?: boolean;
    /** 基础路径 */
    basePath?: string;
    /** 公共路径 */
    publicPath?: string;
    /** 超时时间 */
    timeout?: number;
    /** 重试次数 */
    retries?: number;
}

/**
 * 兼容性类型别名
 * 为了保持向后兼容性，保留一些旧的类型名称
 */

/** @deprecated 使用 BaseAdapterConfig 替代 */
export type AdapterConfig<T = any> = BaseAdapterConfig<T>;

/** @deprecated 使用 BaseAppInstance 替代 */
export type AppInstance<T = any> = BaseAppInstance<T>;

/** @deprecated 使用 BaseLifecycleHooks 替代 */
export type LifecycleHooks<T = any> = BaseLifecycleHooks<T>;

/** @deprecated 使用 BaseAdapter 替代 */
export type Adapter<T = any> = BaseAdapter<T>;

/** @deprecated 使用 BaseBuilder 替代 */
export type Builder<T = any> = BaseBuilder<T>;

/** @deprecated 使用 BaseBuilderConfig 替代 */
export type BuilderConfig<T = any> = BaseBuilderConfig<T>;