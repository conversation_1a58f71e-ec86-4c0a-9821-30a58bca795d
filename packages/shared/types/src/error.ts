/**
 * 错误类型枚举
 */
export enum ErrorType {
    INITIALIZATION = 'INITIALIZATION',
    CONFIGURATION = 'CONFIGURATION',
    NETWORK = 'NETWORK',
    COMPATIBILITY = 'COMPATIBILITY',
    SECURITY = 'SECURITY',
    PERFORMANCE = 'PERFORMANCE',
    APPLICATION = 'APPLICATION',
    PLUGIN = 'PLUGIN',
    SANDBOX = 'SANDBOX',
    RESOURCE = 'RESOURCE',
    UNKNOWN = 'UNKNOWN'
}

/**
 * 错误严重程度枚举
 */
export enum ErrorSeverity {
    LOW = 'LOW',
    MEDIUM = 'MEDIUM',
    HIGH = 'HIGH',
    CRITICAL = 'CRITICAL'
}

/**
 * 错误上下文接口
 */
export interface ErrorContext {
    /** 组件名称 */
    component?: string;
    /** 方法名称 */
    method?: string;
    /** 用户代理 */
    userAgent?: string;
    /** 时间戳 */
    timestamp?: number;
    /** 附加数据 */
    data?: Record<string, any>;
}

/**
 * 错误恢复策略接口
 */
export interface ErrorRecoveryStrategy {
    /** 策略名称 */
    name: string;
    /** 判断是否可以恢复 */
    canRecover: (error: Error, context?: ErrorContext) => boolean;
    /** 执行恢复操作 */
    recover: (error: Error, context?: ErrorContext) => Promise<boolean>;
    /** 优先级 */
    priority: number;
}

/**
 * Base Micro-Core error
 */
export class MicroCoreError extends Error {
    code: string;
    details?: any;
    public readonly type: ErrorType;
    public readonly severity: ErrorSeverity;
    public readonly context: ErrorContext;
    public readonly timestamp: number;
    public readonly recoverable: boolean;

    constructor(
        message: string, 
        code?: string, 
        details?: any,
        type: ErrorType = ErrorType.UNKNOWN,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: ErrorContext = {},
        recoverable: boolean = false
    ) {
        super(message);
        this.name = 'MicroCoreError';
        this.code = code || 'UNKNOWN_ERROR';
        this.details = details;
        this.type = type;
        this.severity = severity;
        this.context = {
            timestamp: Date.now(),
            userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
            ...context
        };
        this.timestamp = Date.now();
        this.recoverable = recoverable;

        // 保持堆栈跟踪
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, MicroCoreError);
        }
    }

    /**
     * 转换为 JSON 格式
     */
    toJSON(): Record<string, any> {
        return {
            name: this.name,
            message: this.message,
            code: this.code,
            type: this.type,
            severity: this.severity,
            context: this.context,
            timestamp: this.timestamp,
            recoverable: this.recoverable,
            stack: this.stack,
            details: this.details
        };
    }

    /**
     * 创建初始化错误
     */
    static initialization(message: string, context?: ErrorContext): MicroCoreError {
        return new MicroCoreError(
            message,
            'INITIALIZATION_ERROR',
            undefined,
            ErrorType.INITIALIZATION,
            ErrorSeverity.CRITICAL,
            context,
            true
        );
    }

    /**
     * 创建配置错误
     */
    static configuration(message: string, context?: ErrorContext): MicroCoreError {
        return new MicroCoreError(
            message,
            'CONFIGURATION_ERROR',
            undefined,
            ErrorType.CONFIGURATION,
            ErrorSeverity.HIGH,
            context,
            true
        );
    }

    /**
     * 创建网络错误
     */
    static network(message: string, context?: ErrorContext): MicroCoreError {
        return new MicroCoreError(
            message,
            'NETWORK_ERROR',
            undefined,
            ErrorType.NETWORK,
            ErrorSeverity.MEDIUM,
            context,
            true
        );
    }

    /**
     * 创建兼容性错误
     */
    static compatibility(message: string, context?: ErrorContext): MicroCoreError {
        return new MicroCoreError(
            message,
            'COMPATIBILITY_ERROR',
            undefined,
            ErrorType.COMPATIBILITY,
            ErrorSeverity.HIGH,
            context,
            false
        );
    }

    /**
     * 创建安全错误
     */
    static security(message: string, context?: ErrorContext): MicroCoreError {
        return new MicroCoreError(
            message,
            'SECURITY_ERROR',
            undefined,
            ErrorType.SECURITY,
            ErrorSeverity.CRITICAL,
            context,
            false
        );
    }

    /**
     * 创建性能错误
     */
    static performance(message: string, context?: ErrorContext): MicroCoreError {
        return new MicroCoreError(
            message,
            'PERFORMANCE_ERROR',
            undefined,
            ErrorType.PERFORMANCE,
            ErrorSeverity.MEDIUM,
            context,
            true
        );
    }
}

/**
 * Application error
 */
export class ApplicationError extends MicroCoreError {
    appName: string;

    constructor(appName: string, message: string, details?: any) {
        super(message, 'APPLICATION_ERROR', details);
        this.name = 'ApplicationError';
        this.appName = appName;
    }
}

/**
 * Plugin error
 */
export class PluginError extends MicroCoreError {
    pluginName: string;

    constructor(pluginName: string, message: string, details?: any) {
        super(message, 'PLUGIN_ERROR', details);
        this.name = 'PluginError';
        this.pluginName = pluginName;
    }
}

/**
 * Sandbox error
 */
export class SandboxError extends MicroCoreError {
    sandboxName: string;

    constructor(sandboxName: string, message: string, details?: any) {
        super(message, 'SANDBOX_ERROR', details);
        this.name = 'SandboxError';
        this.sandboxName = sandboxName;
    }
}

/**
 * Resource error
 */
export class ResourceError extends MicroCoreError {
    url: string;

    constructor(url: string, message: string, details?: any) {
        super(message, 'RESOURCE_ERROR', details);
        this.name = 'ResourceError';
        this.url = url;
    }
}