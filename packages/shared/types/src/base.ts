/**
 * @fileoverview 基础类型定义
 * @description 提供泛型化的基础类型和接口
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 基础配置接口
 */
export interface BaseConfig {
    /** 名称 */
    name: string;
    /** 版本 */
    version?: string;
    /** 描述 */
    description?: string;
    /** 是否启用 */
    enabled?: boolean;
    /** 元数据 */
    metadata?: Record<string, any>;
}

/**
 * 基础适配器配置
 */
export interface BaseAdapterConfig<T = any> extends BaseConfig {
    /** 框架类型 */
    framework: string;
    /** 入口文件 */
    entry?: string;
    /** 容器选择器或元素 */
    container?: string | HTMLElement;
    /** 传递给应用的属性 */
    props?: Record<string, any>;
    /** 框架特定配置 */
    frameworkConfig?: T;
    /** 沙箱配置 */
    sandbox?: {
        /** 严格样式隔离 */
        strictStyleIsolation?: boolean;
        /** 实验性样式隔离 */
        experimentalStyleIsolation?: boolean;
        /** 资源过滤器 */
        excludeAssetFilter?: (assetUrl: string) => boolean;
        /** 全局上下文 */
        globalContext?: Record<string, any>;
    };
}

/**
 * 基础应用实例
 */
export interface BaseAppInstance<T = any> {
    /** 应用名称 */
    name: string;
    /** 应用状态 */
    status: 'loading' | 'loaded' | 'mounting' | 'mounted' | 'unmounting' | 'unmounted' | 'error';
    /** 应用配置 */
    config: BaseAdapterConfig<T>;
    /** 应用容器 */
    container?: HTMLElement;
    /** 框架实例 */
    instance?: T;
    /** 错误信息 */
    error?: Error;
    /** 创建时间 */
    createdAt: number;
    /** 更新时间 */
    updatedAt: number;
    /** 加载时间 */
    loadTime?: number;
    /** 挂载时间 */
    mountTime?: number;
}

/**
 * 基础生命周期钩子
 */
export interface BaseLifecycleHooks<T = any> {
    /** 应用加载前 */
    beforeLoad?: (config: BaseAdapterConfig<T>) => Promise<void> | void;
    /** 应用加载后 */
    afterLoad?: (instance: BaseAppInstance<T>) => Promise<void> | void;
    /** 应用挂载前 */
    beforeMount?: (instance: BaseAppInstance<T>) => Promise<void> | void;
    /** 应用挂载后 */
    afterMount?: (instance: BaseAppInstance<T>) => Promise<void> | void;
    /** 应用卸载前 */
    beforeUnmount?: (instance: BaseAppInstance<T>) => Promise<void> | void;
    /** 应用卸载后 */
    afterUnmount?: (instance: BaseAppInstance<T>) => Promise<void> | void;
    /** 应用更新前 */
    beforeUpdate?: (instance: BaseAppInstance<T>) => Promise<void> | void;
    /** 应用更新后 */
    afterUpdate?: (instance: BaseAppInstance<T>) => Promise<void> | void;
    /** 应用错误处理 */
    onError?: (error: Error, instance: BaseAppInstance<T>) => Promise<void> | void;
}

/**
 * 基础适配器接口
 */
export interface BaseAdapter<T = any> {
    /** 适配器名称 */
    readonly name: string;
    /** 适配器版本 */
    readonly version: string;
    /** 支持的框架 */
    readonly framework: string;

    /** 加载应用 */
    load(config: BaseAdapterConfig<T>): Promise<BaseAppInstance<T>>;
    /** 挂载应用 */
    mount(instance: BaseAppInstance<T>): Promise<void>;
    /** 卸载应用 */
    unmount(instance: BaseAppInstance<T>): Promise<void>;
    /** 更新应用 */
    update?(instance: BaseAppInstance<T>, props?: Record<string, any>): Promise<void>;
    /** 获取应用信息 */
    getInfo(instance: BaseAppInstance<T>): any;
    /** 销毁适配器 */
    destroy?(): Promise<void>;
}

/**
 * 基础构建器配置
 */
export interface BaseBuilderConfig<T = any> extends BaseConfig {
    /** 构建器类型 */
    type: string;
    /** 入口文件 */
    entry?: string | string[] | Record<string, string>;
    /** 输出目录 */
    outDir?: string;
    /** 构建模式 */
    mode?: 'development' | 'production';
    /** 是否生成 source map */
    sourcemap?: boolean;
    /** 是否压缩代码 */
    minify?: boolean;
    /** 外部依赖 */
    externals?: Record<string, string> | string[];
    /** 路径别名 */
    alias?: Record<string, string>;
    /** 环境变量 */
    define?: Record<string, any>;
    /** 构建器特定配置 */
    builderConfig?: T;
}

/**
 * 基础构建器接口
 */
export interface BaseBuilder<T = any> {
    /** 构建器名称 */
    readonly name: string;
    /** 构建器版本 */
    readonly version: string;
    /** 构建器类型 */
    readonly type: string;

    /** 构建应用 */
    build(config: BaseBuilderConfig<T>): Promise<any>;
    /** 启动开发服务器 */
    serve?(config: BaseBuilderConfig<T>): Promise<void>;
    /** 监听文件变化 */
    watch?(config: BaseBuilderConfig<T>, callback?: (event: string, file?: string) => void): Promise<void>;
    /** 停止构建器 */
    stop(): Promise<void>;
    /** 获取构建器信息 */
    getInfo(): any;
}

/**
 * 基础插件接口
 */
export interface BasePlugin<T = any> {
    /** 插件名称 */
    readonly name: string;
    /** 插件版本 */
    readonly version: string;
    /** 插件类型 */
    readonly type: string;

    /** 应用插件 */
    apply(context: T): void | Promise<void>;
    /** 插件配置 */
    configure?(options: any): void;
    /** 销毁插件 */
    destroy?(): void | Promise<void>;
}

/**
 * 基础服务接口
 */
export interface BaseService<T = any> {
    /** 服务名称 */
    readonly name: string;
    /** 服务版本 */
    readonly version: string;
    /** 服务状态 */
    readonly status: 'stopped' | 'starting' | 'running' | 'stopping' | 'error';

    /** 启动服务 */
    start(config?: T): Promise<void>;
    /** 停止服务 */
    stop(): Promise<void>;
    /** 重启服务 */
    restart?(): Promise<void>;
    /** 获取服务信息 */
    getInfo(): any;
    /** 健康检查 */
    healthCheck?(): Promise<boolean>;
}

/**
 * 基础工厂接口
 */
export interface BaseFactory<T, K = any> {
    /** 工厂类型 */
    readonly type: string;
    /** 创建实例 */
    create(config: K): T;
    /** 验证配置 */
    validate?(config: K): boolean;
    /** 获取默认配置 */
    getDefaultConfig?(): K;
}

/**
 * 基础注册表接口
 */
export interface BaseRegistry<T> {
    /** 注册项目 */
    register(name: string, item: T): void;
    /** 注销项目 */
    unregister(name: string): void;
    /** 获取项目 */
    get(name: string): T | undefined;
    /** 获取所有项目 */
    getAll(): Map<string, T>;
    /** 检查项目是否存在 */
    has(name: string): boolean;
    /** 清空注册表 */
    clear(): void;
}

/**
 * 基础事件发射器接口
 */
export interface BaseEventEmitter<T = any> {
    /** 监听事件 */
    on(event: string, listener: (data: T) => void): void;
    /** 监听一次事件 */
    once(event: string, listener: (data: T) => void): void;
    /** 移除事件监听器 */
    off(event: string, listener?: (data: T) => void): void;
    /** 发射事件 */
    emit(event: string, data?: T): void;
    /** 获取事件监听器数量 */
    listenerCount(event: string): number;
    /** 移除所有监听器 */
    removeAllListeners(event?: string): void;
}

/**
 * 基础中间件接口
 */
export interface BaseMiddleware<T = any, K = any> {
    /** 中间件名称 */
    readonly name: string;
    /** 执行中间件 */
    execute(context: T, next: () => Promise<K>): Promise<K>;
    /** 中间件优先级 */
    priority?: number;
    /** 是否启用 */
    enabled?: boolean;
}

/**
 * 基础缓存接口
 */
export interface BaseCache<T = any> {
    /** 获取缓存 */
    get(key: string): T | undefined;
    /** 设置缓存 */
    set(key: string, value: T, ttl?: number): void;
    /** 删除缓存 */
    delete(key: string): boolean;
    /** 检查缓存是否存在 */
    has(key: string): boolean;
    /** 清空缓存 */
    clear(): void;
    /** 获取缓存大小 */
    size(): number;
    /** 获取所有键 */
    keys(): string[];
}

/**
 * 基础队列接口
 */
export interface BaseQueue<T = any> {
    /** 入队 */
    enqueue(item: T): void;
    /** 出队 */
    dequeue(): T | undefined;
    /** 查看队首元素 */
    peek(): T | undefined;
    /** 检查队列是否为空 */
    isEmpty(): boolean;
    /** 获取队列大小 */
    size(): number;
    /** 清空队列 */
    clear(): void;
}

/**
 * 基础状态管理接口
 */
export interface BaseStateManager<T = any> {
    /** 获取状态 */
    getState(): T;
    /** 设置状态 */
    setState(state: Partial<T>): void;
    /** 订阅状态变化 */
    subscribe(listener: (state: T) => void): () => void;
    /** 重置状态 */
    reset(): void;
}

/**
 * 基础路由接口
 */
export interface BaseRouter {
    /** 当前路由 */
    readonly currentRoute: string;
    /** 导航到指定路由 */
    navigate(path: string): void;
    /** 返回上一页 */
    back(): void;
    /** 前进到下一页 */
    forward(): void;
    /** 监听路由变化 */
    onRouteChange(listener: (route: string) => void): () => void;
}

/**
 * 基础验证器接口
 */
export interface BaseValidator<T = any> {
    /** 验证数据 */
    validate(data: T): boolean;
    /** 获取验证错误 */
    getErrors(): string[];
    /** 验证规则 */
    rules: Record<string, any>;
}

/**
 * 基础序列化器接口
 */
export interface BaseSerializer<T = any> {
    /** 序列化 */
    serialize(data: T): string;
    /** 反序列化 */
    deserialize(data: string): T;
    /** 支持的格式 */
    readonly format: string;
}

/**
 * 基础日志器接口
 */
export interface BaseLogger {
    /** 调试日志 */
    debug(message: string, ...args: any[]): void;
    /** 信息日志 */
    info(message: string, ...args: any[]): void;
    /** 警告日志 */
    warn(message: string, ...args: any[]): void;
    /** 错误日志 */
    error(message: string, ...args: any[]): void;
    /** 设置日志级别 */
    setLevel(level: 'debug' | 'info' | 'warn' | 'error'): void;
}

/**
 * 基础HTTP客户端接口
 */
export interface BaseHttpClient {
    /** GET请求 */
    get<T = any>(url: string, config?: any): Promise<T>;
    /** POST请求 */
    post<T = any>(url: string, data?: any, config?: any): Promise<T>;
    /** PUT请求 */
    put<T = any>(url: string, data?: any, config?: any): Promise<T>;
    /** DELETE请求 */
    delete<T = any>(url: string, config?: any): Promise<T>;
    /** 设置默认配置 */
    setDefaults(config: any): void;
    /** 添加请求拦截器 */
    addRequestInterceptor(interceptor: (config: any) => any): void;
    /** 添加响应拦截器 */
    addResponseInterceptor(interceptor: (response: any) => any): void;
}

/**
 * 工具类型
 */

/** 深度只读 */
export type DeepReadonly<T> = {
    readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/** 深度必需 */
export type DeepRequired<T> = {
    [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

/** 深度可选 */
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/** 选择性必需 */
export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;

/** 选择性可选 */
export type OptionalKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/** 函数类型 */
export type Fn<T = any, R = any> = (...args: T[]) => R;

/** 异步函数类型 */
export type AsyncFn<T = any, R = any> = (...args: T[]) => Promise<R>;

/** 构造函数类型 */
export type Constructor<T = any> = new (...args: any[]) => T;

/** 抽象构造函数类型 */
export type AbstractConstructor<T = any> = abstract new (...args: any[]) => T;

/** 键值对类型 */
export type KeyValue<K extends string | number | symbol = string, V = any> = Record<K, V>;

/** 可空类型 */
export type Nullable<T> = T | null;

/** 可未定义类型 */
export type Optional<T> = T | undefined;

/** 可空或未定义类型 */
export type Maybe<T> = T | null | undefined;

/** 数组或单个元素类型 */
export type ArrayOrSingle<T> = T | T[];

/** 字符串键类型 */
export type StringKeys<T> = Extract<keyof T, string>;

/** 数字键类型 */
export type NumberKeys<T> = Extract<keyof T, number>;

/** 符号键类型 */
export type SymbolKeys<T> = Extract<keyof T, symbol>;

/** 值类型 */
export type ValueOf<T> = T[keyof T];

/** 联合转交集 */
export type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I : never;

/** 排除函数属性 */
export type NonFunctionKeys<T> = {
    [K in keyof T]: T[K] extends Function ? never : K;
}[keyof T];

/** 仅函数属性 */
export type FunctionKeys<T> = {
    [K in keyof T]: T[K] extends Function ? K : never;
}[keyof T];

/** 排除函数的类型 */
export type NonFunctionProperties<T> = Pick<T, NonFunctionKeys<T>>;

/** 仅函数的类型 */
export type FunctionProperties<T> = Pick<T, FunctionKeys<T>>;

/** Promise解包类型 */
export type Awaited<T> = T extends Promise<infer U> ? U : T;

/** 递归Promise解包类型 */
export type DeepAwaited<T> = T extends Promise<infer U> ? DeepAwaited<U> : T;