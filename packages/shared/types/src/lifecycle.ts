/**
 * @fileoverview 生命周期相关类型定义
 * @description 定义应用和组件生命周期相关的类型和接口
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 生命周期阶段
 */
export enum LifecyclePhase {
    /** 初始化 */
    INITIALIZE = 'initialize',
    /** 加载 */
    LOAD = 'load',
    /** 引导 */
    BOOTSTRAP = 'bootstrap',
    /** 挂载 */
    MOUNT = 'mount',
    /** 更新 */
    UPDATE = 'update',
    /** 卸载 */
    UNMOUNT = 'unmount',
    /** 销毁 */
    DESTROY = 'destroy',
    /** 错误 */
    ERROR = 'error'
}

/**
 * 生命周期状态
 */
export enum LifecycleStatus {
    /** 待处理 */
    PENDING = 'pending',
    /** 进行中 */
    RUNNING = 'running',
    /** 已完成 */
    COMPLETED = 'completed',
    /** 已跳过 */
    SKIPPED = 'skipped',
    /** 失败 */
    FAILED = 'failed',
    /** 已取消 */
    CANCELLED = 'cancelled'
}

/**
 * 生命周期事件数据
 */
export interface LifecycleEventData<T = any> {
    /** 事件阶段 */
    phase: LifecyclePhase;
    /** 事件状态 */
    status: LifecycleStatus;
    /** 目标对象 */
    target: T;
    /** 事件数据 */
    data?: any;
    /** 时间戳 */
    timestamp: number;
    /** 持续时间 */
    duration?: number;
    /** 错误信息 */
    error?: Error;
    /** 元数据 */
    metadata?: Record<string, any>;
}

/**
 * 生命周期钩子函数类型
 */
export type LifecycleHook<T = any> = (data: LifecycleEventData<T>) => Promise<void> | void;

/**
 * 生命周期钩子集合
 */
export interface LifecycleHooks<T = any> {
    /** 初始化前 */
    beforeInitialize?: LifecycleHook<T>;
    /** 初始化后 */
    afterInitialize?: LifecycleHook<T>;
    /** 加载前 */
    beforeLoad?: LifecycleHook<T>;
    /** 加载后 */
    afterLoad?: LifecycleHook<T>;
    /** 引导前 */
    beforeBootstrap?: LifecycleHook<T>;
    /** 引导后 */
    afterBootstrap?: LifecycleHook<T>;
    /** 挂载前 */
    beforeMount?: LifecycleHook<T>;
    /** 挂载后 */
    afterMount?: LifecycleHook<T>;
    /** 更新前 */
    beforeUpdate?: LifecycleHook<T>;
    /** 更新后 */
    afterUpdate?: LifecycleHook<T>;
    /** 卸载前 */
    beforeUnmount?: LifecycleHook<T>;
    /** 卸载后 */
    afterUnmount?: LifecycleHook<T>;
    /** 销毁前 */
    beforeDestroy?: LifecycleHook<T>;
    /** 销毁后 */
    afterDestroy?: LifecycleHook<T>;
    /** 错误处理 */
    onError?: LifecycleHook<T>;
}

/**
 * 生命周期管理器接口
 */
export interface LifecycleManager<T = any> {
    /** 注册生命周期钩子 */
    registerHooks(hooks: LifecycleHooks<T>): void;
    /** 注销生命周期钩子 */
    unregisterHooks(hooks: LifecycleHooks<T>): void;
    /** 触发生命周期事件 */
    trigger(phase: LifecyclePhase, target: T, data?: any): Promise<void>;
    /** 获取当前阶段 */
    getCurrentPhase(): LifecyclePhase | null;
    /** 获取历史记录 */
    getHistory(): LifecycleEventData<T>[];
    /** 清除历史记录 */
    clearHistory(): void;
    /** 是否正在执行 */
    isRunning(): boolean;
    /** 取消当前执行 */
    cancel(): void;
}

/**
 * 应用生命周期钩子
 */
export interface AppLifecycleHooks {
    /** 应用初始化前 */
    beforeAppInitialize?: (config: any) => Promise<void> | void;
    /** 应用初始化后 */
    afterAppInitialize?: (app: any) => Promise<void> | void;
    /** 应用加载前 */
    beforeAppLoad?: (config: any) => Promise<void> | void;
    /** 应用加载后 */
    afterAppLoad?: (app: any) => Promise<void> | void;
    /** 应用引导前 */
    beforeAppBootstrap?: (app: any) => Promise<void> | void;
    /** 应用引导后 */
    afterAppBootstrap?: (app: any) => Promise<void> | void;
    /** 应用挂载前 */
    beforeAppMount?: (app: any) => Promise<void> | void;
    /** 应用挂载后 */
    afterAppMount?: (app: any) => Promise<void> | void;
    /** 应用更新前 */
    beforeAppUpdate?: (app: any, props?: any) => Promise<void> | void;
    /** 应用更新后 */
    afterAppUpdate?: (app: any, props?: any) => Promise<void> | void;
    /** 应用卸载前 */
    beforeAppUnmount?: (app: any) => Promise<void> | void;
    /** 应用卸载后 */
    afterAppUnmount?: (app: any) => Promise<void> | void;
    /** 应用销毁前 */
    beforeAppDestroy?: (app: any) => Promise<void> | void;
    /** 应用销毁后 */
    afterAppDestroy?: (app: any) => Promise<void> | void;
    /** 应用错误处理 */
    onAppError?: (error: Error, app: any) => Promise<void> | void;
}

/**
 * 组件生命周期钩子
 */
export interface ComponentLifecycleHooks {
    /** 组件创建前 */
    beforeComponentCreate?: (component: any) => Promise<void> | void;
    /** 组件创建后 */
    afterComponentCreate?: (component: any) => Promise<void> | void;
    /** 组件挂载前 */
    beforeComponentMount?: (component: any) => Promise<void> | void;
    /** 组件挂载后 */
    afterComponentMount?: (component: any) => Promise<void> | void;
    /** 组件更新前 */
    beforeComponentUpdate?: (component: any, props?: any) => Promise<void> | void;
    /** 组件更新后 */
    afterComponentUpdate?: (component: any, props?: any) => Promise<void> | void;
    /** 组件卸载前 */
    beforeComponentUnmount?: (component: any) => Promise<void> | void;
    /** 组件卸载后 */
    afterComponentUnmount?: (component: any) => Promise<void> | void;
    /** 组件销毁前 */
    beforeComponentDestroy?: (component: any) => Promise<void> | void;
    /** 组件销毁后 */
    afterComponentDestroy?: (component: any) => Promise<void> | void;
    /** 组件错误处理 */
    onComponentError?: (error: Error, component: any) => Promise<void> | void;
}

/**
 * 路由生命周期钩子
 */
export interface RouteLifecycleHooks {
    /** 路由进入前 */
    beforeRouteEnter?: (to: any, from: any, next: any) => Promise<void> | void;
    /** 路由更新前 */
    beforeRouteUpdate?: (to: any, from: any, next: any) => Promise<void> | void;
    /** 路由离开前 */
    beforeRouteLeave?: (to: any, from: any, next: any) => Promise<void> | void;
    /** 路由进入后 */
    afterRouteEnter?: (to: any, from: any) => Promise<void> | void;
    /** 路由更新后 */
    afterRouteUpdate?: (to: any, from: any) => Promise<void> | void;
    /** 路由离开后 */
    afterRouteLeave?: (to: any, from: any) => Promise<void> | void;
    /** 路由错误处理 */
    onRouteError?: (error: Error, to: any, from: any) => Promise<void> | void;
}

/**
 * 生命周期执行器接口
 */
export interface LifecycleExecutor<T = any> {
    /** 执行器名称 */
    readonly name: string;
    /** 执行生命周期钩子 */
    execute(phase: LifecyclePhase, target: T, hooks: LifecycleHooks<T>): Promise<void>;
    /** 是否支持该阶段 */
    supports(phase: LifecyclePhase): boolean;
    /** 获取执行优先级 */
    getPriority(): number;
}

/**
 * 生命周期中间件接口
 */
export interface LifecycleMiddleware<T = any> {
    /** 中间件名称 */
    readonly name: string;
    /** 执行中间件 */
    execute(
        phase: LifecyclePhase,
        target: T,
        next: () => Promise<void>
    ): Promise<void>;
    /** 中间件优先级 */
    priority?: number;
    /** 是否启用 */
    enabled?: boolean;
}

/**
 * 生命周期观察者接口
 */
export interface LifecycleObserver<T = any> {
    /** 观察者名称 */
    readonly name: string;
    /** 观察生命周期事件 */
    observe(event: LifecycleEventData<T>): Promise<void> | void;
    /** 过滤条件 */
    filter?(event: LifecycleEventData<T>): boolean;
}

/**
 * 生命周期配置
 */
export interface LifecycleConfig {
    /** 是否启用生命周期管理 */
    enabled?: boolean;
    /** 超时时间（毫秒） */
    timeout?: number;
    /** 是否并行执行钩子 */
    parallel?: boolean;
    /** 错误处理策略 */
    errorHandling?: 'continue' | 'stop' | 'retry';
    /** 重试次数 */
    retries?: number;
    /** 重试间隔（毫秒） */
    retryDelay?: number;
    /** 是否记录历史 */
    recordHistory?: boolean;
    /** 最大历史记录数 */
    maxHistorySize?: number;
}

/**
 * 生命周期统计信息
 */
export interface LifecycleStats {
    /** 总执行次数 */
    totalExecutions: number;
    /** 成功次数 */
    successCount: number;
    /** 失败次数 */
    failureCount: number;
    /** 跳过次数 */
    skipCount: number;
    /** 平均执行时间 */
    averageExecutionTime: number;
    /** 最长执行时间 */
    maxExecutionTime: number;
    /** 最短执行时间 */
    minExecutionTime: number;
    /** 各阶段统计 */
    phaseStats: Record<LifecyclePhase, {
        count: number;
        averageTime: number;
        successRate: number;
    }>;
}

/**
 * 生命周期工厂接口
 */
export interface LifecycleFactory<T = any> {
    /** 创建生命周期管理器 */
    createManager(config?: LifecycleConfig): LifecycleManager<T>;
    /** 创建生命周期执行器 */
    createExecutor(name: string): LifecycleExecutor<T>;
    /** 创建生命周期中间件 */
    createMiddleware(name: string, handler: LifecycleMiddleware<T>['execute']): LifecycleMiddleware<T>;
    /** 创建生命周期观察者 */
    createObserver(name: string, handler: LifecycleObserver<T>['observe']): LifecycleObserver<T>;
}

/**
 * 生命周期注册表接口
 */
export interface LifecycleRegistry<T = any> {
    /** 注册生命周期管理器 */
    registerManager(name: string, manager: LifecycleManager<T>): void;
    /** 注销生命周期管理器 */
    unregisterManager(name: string): void;
    /** 获取生命周期管理器 */
    getManager(name: string): LifecycleManager<T> | undefined;
    /** 获取所有管理器 */
    getAllManagers(): Map<string, LifecycleManager<T>>;
    /** 注册执行器 */
    registerExecutor(executor: LifecycleExecutor<T>): void;
    /** 注销执行器 */
    unregisterExecutor(name: string): void;
    /** 获取执行器 */
    getExecutor(name: string): LifecycleExecutor<T> | undefined;
    /** 获取所有执行器 */
    getAllExecutors(): LifecycleExecutor<T>[];
}

/**
 * React 生命周期钩子
 */
export interface ReactLifecycleHooks {
    /** 组件将要挂载 */
    componentWillMount?: () => void;
    /** 组件已挂载 */
    componentDidMount?: () => void;
    /** 组件将要更新 */
    componentWillUpdate?: (nextProps: any, nextState: any) => void;
    /** 组件已更新 */
    componentDidUpdate?: (prevProps: any, prevState: any) => void;
    /** 组件将要卸载 */
    componentWillUnmount?: () => void;
    /** 组件捕获错误 */
    componentDidCatch?: (error: Error, errorInfo: any) => void;
    /** 获取派生状态 */
    getDerivedStateFromError?: (error: Error) => any;
    /** 获取快照 */
    getSnapshotBeforeUpdate?: (prevProps: any, prevState: any) => any;
}

/**
 * Vue 生命周期钩子
 */
export interface VueLifecycleHooks {
    /** 创建前 */
    beforeCreate?: () => void;
    /** 创建后 */
    created?: () => void;
    /** 挂载前 */
    beforeMount?: () => void;
    /** 挂载后 */
    mounted?: () => void;
    /** 更新前 */
    beforeUpdate?: () => void;
    /** 更新后 */
    updated?: () => void;
    /** 激活 */
    activated?: () => void;
    /** 停用 */
    deactivated?: () => void;
    /** 销毁前 */
    beforeDestroy?: () => void;
    /** 销毁后 */
    destroyed?: () => void;
    /** 错误捕获 */
    errorCaptured?: (error: Error, instance: any, info: string) => boolean | void;
}

/**
 * Angular 生命周期钩子
 */
export interface AngularLifecycleHooks {
    /** 初始化 */
    ngOnInit?: () => void;
    /** 变更检测 */
    ngOnChanges?: (changes: any) => void;
    /** 内容初始化 */
    ngAfterContentInit?: () => void;
    /** 内容检查 */
    ngAfterContentChecked?: () => void;
    /** 视图初始化 */
    ngAfterViewInit?: () => void;
    /** 视图检查 */
    ngAfterViewChecked?: () => void;
    /** 销毁 */
    ngOnDestroy?: () => void;
}

/**
 * 生命周期装饰器选项
 */
export interface LifecycleDecoratorOptions {
    /** 阶段 */
    phase: LifecyclePhase;
    /** 优先级 */
    priority?: number;
    /** 是否异步 */
    async?: boolean;
    /** 超时时间 */
    timeout?: number;
    /** 错误处理 */
    errorHandling?: 'ignore' | 'throw' | 'log';
}

/**
 * 生命周期元数据
 */
export interface LifecycleMetadata {
    /** 目标对象 */
    target: any;
    /** 方法名 */
    methodName: string;
    /** 装饰器选项 */
    options: LifecycleDecoratorOptions;
    /** 原始方法 */
    originalMethod: Function;
}

/**
 * 生命周期工具类型
 */
export type LifecycleHookFunction<T = any> = (target: T, ...args: any[]) => Promise<void> | void;
export type LifecyclePhaseMap<T = any> = Record<LifecyclePhase, LifecycleHookFunction<T>[]>;
export type LifecycleEventMap<T = any> = Record<LifecyclePhase, LifecycleEventData<T>[]>;

/**
 * 生命周期常量
 */
export const LIFECYCLE_PHASES = Object.values(LifecyclePhase);
export const LIFECYCLE_STATUSES = Object.values(LifecycleStatus);

/**
 * 默认生命周期配置
 */
export const DEFAULT_LIFECYCLE_CONFIG: Required<LifecycleConfig> = {
    enabled: true,
    timeout: 30000,
    parallel: false,
    errorHandling: 'stop',
    retries: 3,
    retryDelay: 1000,
    recordHistory: true,
    maxHistorySize: 100
};

/**
 * 生命周期工具函数类型
 */
export interface LifecycleUtils {
    /** 创建生命周期事件数据 */
    createEventData<T>(
        phase: LifecyclePhase,
        status: LifecycleStatus,
        target: T,
        data?: any
    ): LifecycleEventData<T>;

    /** 格式化生命周期阶段 */
    formatPhase(phase: LifecyclePhase): string;

    /** 格式化生命周期状态 */
    formatStatus(status: LifecycleStatus): string;

    /** 验证生命周期钩子 */
    validateHooks<T>(hooks: LifecycleHooks<T>): boolean;

    /** 合并生命周期钩子 */
    mergeHooks<T>(...hooks: LifecycleHooks<T>[]): LifecycleHooks<T>;

    /** 过滤生命周期钩子 */
    filterHooks<T>(
        hooks: LifecycleHooks<T>,
        predicate: (phase: LifecyclePhase, hook: LifecycleHook<T>) => boolean
    ): LifecycleHooks<T>;
}
