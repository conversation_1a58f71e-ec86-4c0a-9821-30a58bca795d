/**
 * @fileoverview 沙箱相关类型定义
 * @description 定义沙箱隔离相关的类型和接口
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 沙箱类型
 */
export enum SandboxType {
    /** 快照沙箱 */
    SNAPSHOT = 'snapshot',
    /** 代理沙箱 */
    PROXY = 'proxy',
    /** iframe沙箱 */
    IFRAME = 'iframe',
    /** Web Components沙箱 */
    WEB_COMPONENTS = 'web-components',
    /** 无沙箱 */
    NONE = 'none'
}

/**
 * 沙箱状态
 */
export enum SandboxStatus {
    /** 未初始化 */
    NOT_INITIALIZED = 'not_initialized',
    /** 初始化中 */
    INITIALIZING = 'initializing',
    /** 已初始化 */
    INITIALIZED = 'initialized',
    /** 激活中 */
    ACTIVATING = 'activating',
    /** 已激活 */
    ACTIVE = 'active',
    /** 停用中 */
    DEACTIVATING = 'deactivating',
    /** 已停用 */
    INACTIVE = 'inactive',
    /** 销毁中 */
    DESTROYING = 'destroying',
    /** 已销毁 */
    DESTROYED = 'destroyed',
    /** 错误状态 */
    ERROR = 'error'
}

/**
 * 沙箱配置
 */
export interface SandboxConfig {
    /** 沙箱类型 */
    type?: SandboxType;
    /** 沙箱名称 */
    name?: string;
    /** 是否启用严格样式隔离 */
    strictStyleIsolation?: boolean;
    /** 是否启用实验性样式隔离 */
    experimentalStyleIsolation?: boolean;
    /** 是否启用JavaScript隔离 */
    jsIsolation?: boolean;
    /** 全局变量白名单 */
    globalWhitelist?: string[];
    /** 全局变量黑名单 */
    globalBlacklist?: string[];
    /** 资源过滤器 */
    excludeAssetFilter?: (assetUrl: string) => boolean;
    /** 全局上下文 */
    globalContext?: Record<string, any>;
    /** 是否启用沙箱 */
    enabled?: boolean;
    /** 沙箱选项 */
    options?: Record<string, any>;
}

/**
 * 沙箱实例接口
 */
export interface SandboxInstance {
    /** 沙箱名称 */
    readonly name: string;
    /** 沙箱类型 */
    readonly type: SandboxType;
    /** 沙箱状态 */
    readonly status: SandboxStatus;
    /** 沙箱配置 */
    readonly config: SandboxConfig;

    /** 初始化沙箱 */
    initialize(): Promise<void>;
    /** 激活沙箱 */
    activate(): Promise<void>;
    /** 停用沙箱 */
    deactivate(): Promise<void>;
    /** 销毁沙箱 */
    destroy(): Promise<void>;
    /** 获取沙箱全局对象 */
    getGlobal(): any;
    /** 设置全局变量 */
    setGlobal(key: string, value: any): void;
    /** 获取全局变量 */
    getGlobalValue(key: string): any;
    /** 删除全局变量 */
    deleteGlobal(key: string): void;
    /** 执行代码 */
    execScript(script: string): any;
    /** 获取沙箱信息 */
    getInfo(): SandboxInfo;
}

/**
 * 沙箱信息
 */
export interface SandboxInfo {
    /** 沙箱名称 */
    name: string;
    /** 沙箱类型 */
    type: SandboxType;
    /** 沙箱状态 */
    status: SandboxStatus;
    /** 创建时间 */
    createdAt: number;
    /** 激活时间 */
    activatedAt?: number;
    /** 停用时间 */
    deactivatedAt?: number;
    /** 销毁时间 */
    destroyedAt?: number;
    /** 全局变量数量 */
    globalCount: number;
    /** 内存使用情况 */
    memoryUsage?: {
        used: number;
        total: number;
    };
    /** 错误信息 */
    error?: Error;
}

/**
 * 沙箱事件类型
 */
export enum SandboxEventType {
    /** 初始化前 */
    BEFORE_INITIALIZE = 'before_initialize',
    /** 初始化后 */
    AFTER_INITIALIZE = 'after_initialize',
    /** 激活前 */
    BEFORE_ACTIVATE = 'before_activate',
    /** 激活后 */
    AFTER_ACTIVATE = 'after_activate',
    /** 停用前 */
    BEFORE_DEACTIVATE = 'before_deactivate',
    /** 停用后 */
    AFTER_DEACTIVATE = 'after_deactivate',
    /** 销毁前 */
    BEFORE_DESTROY = 'before_destroy',
    /** 销毁后 */
    AFTER_DESTROY = 'after_destroy',
    /** 全局变量变更 */
    GLOBAL_CHANGED = 'global_changed',
    /** 错误发生 */
    ERROR = 'error'
}

/**
 * 沙箱事件数据
 */
export interface SandboxEventData {
    /** 事件类型 */
    type: SandboxEventType;
    /** 沙箱实例 */
    sandbox: SandboxInstance;
    /** 事件数据 */
    data?: any;
    /** 时间戳 */
    timestamp: number;
    /** 错误信息 */
    error?: Error;
}

/**
 * 沙箱生命周期钩子
 */
export interface SandboxLifecycleHooks {
    /** 初始化前 */
    beforeInitialize?: (sandbox: SandboxInstance) => Promise<void> | void;
    /** 初始化后 */
    afterInitialize?: (sandbox: SandboxInstance) => Promise<void> | void;
    /** 激活前 */
    beforeActivate?: (sandbox: SandboxInstance) => Promise<void> | void;
    /** 激活后 */
    afterActivate?: (sandbox: SandboxInstance) => Promise<void> | void;
    /** 停用前 */
    beforeDeactivate?: (sandbox: SandboxInstance) => Promise<void> | void;
    /** 停用后 */
    afterDeactivate?: (sandbox: SandboxInstance) => Promise<void> | void;
    /** 销毁前 */
    beforeDestroy?: (sandbox: SandboxInstance) => Promise<void> | void;
    /** 销毁后 */
    afterDestroy?: (sandbox: SandboxInstance) => Promise<void> | void;
    /** 错误处理 */
    onError?: (error: Error, sandbox: SandboxInstance) => Promise<void> | void;
}

/**
 * 样式隔离配置
 */
export interface StyleIsolationConfig {
    /** 是否启用样式隔离 */
    enabled?: boolean;
    /** 隔离模式 */
    mode?: 'strict' | 'scoped' | 'shadow-dom';
    /** CSS前缀 */
    prefix?: string;
    /** 排除的选择器 */
    excludeSelectors?: string[];
    /** 包含的选择器 */
    includeSelectors?: string[];
    /** 是否隔离字体 */
    isolateFonts?: boolean;
    /** 是否隔离动画 */
    isolateAnimations?: boolean;
}

/**
 * JavaScript隔离配置
 */
export interface JSIsolationConfig {
    /** 是否启用JS隔离 */
    enabled?: boolean;
    /** 隔离模式 */
    mode?: 'proxy' | 'snapshot' | 'iframe';
    /** 全局变量白名单 */
    globalWhitelist?: string[];
    /** 全局变量黑名单 */
    globalBlacklist?: string[];
    /** 是否隔离定时器 */
    isolateTimers?: boolean;
    /** 是否隔离事件监听器 */
    isolateEventListeners?: boolean;
    /** 是否隔离网络请求 */
    isolateNetworkRequests?: boolean;
}

/**
 * 资源隔离配置
 */
export interface ResourceIsolationConfig {
    /** 是否启用资源隔离 */
    enabled?: boolean;
    /** 资源过滤器 */
    filter?: (url: string, type: string) => boolean;
    /** 资源代理 */
    proxy?: (url: string, type: string) => string;
    /** 缓存配置 */
    cache?: {
        enabled?: boolean;
        maxSize?: number;
        ttl?: number;
    };
}

/**
 * 沙箱工厂接口
 */
export interface SandboxFactory {
    /** 工厂类型 */
    readonly type: SandboxType;
    /** 创建沙箱实例 */
    create(config: SandboxConfig): SandboxInstance;
    /** 验证配置 */
    validate(config: SandboxConfig): boolean;
    /** 获取默认配置 */
    getDefaultConfig(): SandboxConfig;
    /** 是否支持当前环境 */
    isSupported(): boolean;
}

/**
 * 沙箱管理器接口
 */
export interface SandboxManager {
    /** 创建沙箱 */
    create(name: string, config: SandboxConfig): Promise<SandboxInstance>;
    /** 获取沙箱 */
    get(name: string): SandboxInstance | undefined;
    /** 获取所有沙箱 */
    getAll(): SandboxInstance[];
    /** 销毁沙箱 */
    destroy(name: string): Promise<void>;
    /** 销毁所有沙箱 */
    destroyAll(): Promise<void>;
    /** 注册沙箱工厂 */
    registerFactory(factory: SandboxFactory): void;
    /** 注销沙箱工厂 */
    unregisterFactory(type: SandboxType): void;
    /** 设置生命周期钩子 */
    setLifecycleHooks(hooks: SandboxLifecycleHooks): void;
    /** 监听沙箱事件 */
    on(event: SandboxEventType, listener: (data: SandboxEventData) => void): void;
    /** 移除事件监听器 */
    off(event: SandboxEventType, listener?: (data: SandboxEventData) => void): void;
}

/**
 * 快照沙箱特定配置
 */
export interface SnapshotSandboxConfig extends SandboxConfig {
    /** 快照间隔 */
    snapshotInterval?: number;
    /** 最大快照数量 */
    maxSnapshots?: number;
    /** 是否自动清理快照 */
    autoCleanup?: boolean;
}

/**
 * 代理沙箱特定配置
 */
export interface ProxySandboxConfig extends SandboxConfig {
    /** 代理处理器 */
    proxyHandlers?: {
        get?: (target: any, key: string) => any;
        set?: (target: any, key: string, value: any) => boolean;
        has?: (target: any, key: string) => boolean;
        deleteProperty?: (target: any, key: string) => boolean;
    };
    /** 是否启用深度代理 */
    deepProxy?: boolean;
}

/**
 * iframe沙箱特定配置
 */
export interface IframeSandboxConfig extends SandboxConfig {
    /** iframe属性 */
    iframeAttributes?: Record<string, string>;
    /** 是否启用同源策略 */
    sameOrigin?: boolean;
    /** 通信协议 */
    communication?: {
        protocol?: 'postMessage' | 'custom';
        timeout?: number;
        retries?: number;
    };
}

/**
 * Web Components沙箱特定配置
 */
export interface WebComponentsSandboxConfig extends SandboxConfig {
    /** 自定义元素名称 */
    elementName?: string;
    /** Shadow DOM模式 */
    shadowMode?: 'open' | 'closed';
    /** 是否启用样式封装 */
    styleEncapsulation?: boolean;
}