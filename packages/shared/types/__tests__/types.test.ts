/**
 * @fileoverview 共享类型定义测试
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { describe, expect, it } from 'vitest';
import type {
    AngularConfig,
    ArrayOrSingle,
    CacheConfig,
    CacheStrategy,
    Constructor,
    DeepPartial,
    DeepReadonly,
    DeepRequired,
    Environment,
    ErrorLevel,
    ErrorType,
    ESBuildSpecificConfig,
    EventData,
    EventType,
    GlobalConfig,
    HealthStatus,
    KeyValue,
    LogLevel,
    Maybe,
    MetricType,
    MiddlewareContext,
    Nullable,
    Optional,
    PluginConfig,
    PluginType,
    ReactConfig,
    RollupSpecificConfig,
    RouteConfig,
    RouterMode,
    ValueOf,
    ViteSpecificConfig,
    VueConfig,
    WebpackSpecificConfig
} from '../src';

describe('共享类型定义测试', () => {
    describe('基础工具类型', () => {
        it('DeepReadonly 应该使对象深度只读', () => {
            type TestObj = {
                a: string;
                b: {
                    c: number;
                    d: boolean[];
                };
            };

            type ReadonlyTestObj = DeepReadonly<TestObj>;

            // 类型检查 - 这些应该在编译时报错
            const obj: ReadonlyTestObj = {
                a: 'test',
                b: {
                    c: 1,
                    d: [true, false]
                }
            };

            // 运行时验证
            expect(typeof obj).toBe('object');
            expect(obj.a).toBe('test');
            expect(obj.b.c).toBe(1);
        });

        it('DeepRequired 应该使对象深度必需', () => {
            type TestObj = {
                a?: string;
                b?: {
                    c?: number;
                    d?: boolean[];
                };
            };

            type RequiredTestObj = DeepRequired<TestObj>;

            const obj: RequiredTestObj = {
                a: 'test',
                b: {
                    c: 1,
                    d: [true, false]
                }
            };

            expect(obj.a).toBeDefined();
            expect(obj.b).toBeDefined();
            expect(obj.b.c).toBeDefined();
            expect(obj.b.d).toBeDefined();
        });

        it('DeepPartial 应该使对象深度可选', () => {
            type TestObj = {
                a: string;
                b: {
                    c: number;
                    d: boolean[];
                };
            };

            type PartialTestObj = DeepPartial<TestObj>;

            const obj1: PartialTestObj = {};
            const obj2: PartialTestObj = { a: 'test' };
            const obj3: PartialTestObj = { b: { c: 1 } };

            expect(obj1).toEqual({});
            expect(obj2.a).toBe('test');
            expect(obj3.b?.c).toBe(1);
        });

        it('Nullable 应该允许 null 值', () => {
            type NullableString = Nullable<string>;

            const value1: NullableString = 'test';
            const value2: NullableString = null;

            expect(value1).toBe('test');
            expect(value2).toBeNull();
        });

        it('Optional 应该允许 undefined 值', () => {
            type OptionalString = Optional<string>;

            const value1: OptionalString = 'test';
            const value2: OptionalString = undefined;

            expect(value1).toBe('test');
            expect(value2).toBeUndefined();
        });

        it('Maybe 应该允许 null 或 undefined', () => {
            type MaybeString = Maybe<string>;

            const value1: MaybeString = 'test';
            const value2: MaybeString = null;
            const value3: MaybeString = undefined;

            expect(value1).toBe('test');
            expect(value2).toBeNull();
            expect(value3).toBeUndefined();
        });

        it('ArrayOrSingle 应该允许数组或单个值', () => {
            type StringOrArray = ArrayOrSingle<string>;

            const value1: StringOrArray = 'test';
            const value2: StringOrArray = ['test1', 'test2'];

            expect(value1).toBe('test');
            expect(Array.isArray(value2)).toBe(true);
            expect(value2).toEqual(['test1', 'test2']);
        });

        it('KeyValue 应该表示键值对', () => {
            type StringKeyValue = KeyValue<string, number>;

            const kv: StringKeyValue = { key: 'test', value: 123 };

            expect(kv.key).toBe('test');
            expect(kv.value).toBe(123);
        });

        it('ValueOf 应该提取对象值的类型', () => {
            const obj = {
                a: 'string',
                b: 123,
                c: true
            } as const;

            type ObjValue = ValueOf<typeof obj>;

            const value1: ObjValue = 'string';
            const value2: ObjValue = 123;
            const value3: ObjValue = true;

            expect(value1).toBe('string');
            expect(value2).toBe(123);
            expect(value3).toBe(true);
        });

        it('Constructor 应该表示构造函数类型', () => {
            class TestClass {
                constructor(public value: string) { }
            }

            const ctor: Constructor<TestClass> = TestClass;
            const instance = new ctor('test');

            expect(instance).toBeInstanceOf(TestClass);
            expect(instance.value).toBe('test');
        });
    });

    describe('框架特定类型', () => {
        it('ReactConfig 应该有正确的结构', () => {
            const config: ReactConfig = {
                version: '18.0.0',
                strictMode: true,
                concurrent: true,
                renderer: 'react-dom'
            };

            expect(config.version).toBe('18.0.0');
            expect(config.strictMode).toBe(true);
            expect(config.concurrent).toBe(true);
            expect(config.renderer).toBe('react-dom');
        });

        it('VueConfig 应该有正确的结构', () => {
            const config: VueConfig = {
                version: '3',
                productionTip: false,
                globalProperties: { $test: 'value' },
                compilerOptions: { isCustomElement: () => false }
            };

            expect(config.version).toBe('3');
            expect(config.productionTip).toBe(false);
            expect(config.globalProperties?.$test).toBe('value');
            expect(typeof config.compilerOptions?.isCustomElement).toBe('function');
        });

        it('AngularConfig 应该有正确的结构', () => {
            const config: AngularConfig = {
                version: '15.0.0',
                production: true,
                zone: {
                    enabled: true,
                    config: { enableLongStackTrace: false }
                },
                platform: 'browser'
            };

            expect(config.version).toBe('15.0.0');
            expect(config.production).toBe(true);
            expect(config.zone?.enabled).toBe(true);
            expect(config.platform).toBe('browser');
        });
    });

    describe('构建器特定类型', () => {
        it('WebpackSpecificConfig 应该有正确的结构', () => {
            const config: WebpackSpecificConfig = {
                version: '5.0.0',
                moduleFederation: true,
                devServer: {
                    port: 3000,
                    host: 'localhost',
                    hot: true,
                    open: false
                },
                optimization: {
                    splitChunks: true,
                    minimize: true,
                    sideEffects: false
                }
            };

            expect(config.version).toBe('5.0.0');
            expect(config.moduleFederation).toBe(true);
            expect(config.devServer?.port).toBe(3000);
            expect(config.optimization?.splitChunks).toBe(true);
        });

        it('ViteSpecificConfig 应该有正确的结构', () => {
            const config: ViteSpecificConfig = {
                version: '4.0.0',
                hmr: true,
                server: {
                    port: 5173,
                    host: '0.0.0.0',
                    open: true,
                    cors: true
                },
                build: {
                    target: 'es2015',
                    format: 'es',
                    lib: false
                }
            };

            expect(config.version).toBe('4.0.0');
            expect(config.hmr).toBe(true);
            expect(config.server?.port).toBe(5173);
            expect(config.build?.target).toBe('es2015');
        });

        it('RollupSpecificConfig 应该有正确的结构', () => {
            const config: RollupSpecificConfig = {
                version: '3.0.0',
                format: 'es',
                watch: false,
                external: ['react', 'vue']
            };

            expect(config.version).toBe('3.0.0');
            expect(config.format).toBe('es');
            expect(config.watch).toBe(false);
            expect(Array.isArray(config.external)).toBe(true);
        });

        it('ESBuildSpecificConfig 应该有正确的结构', () => {
            const config: ESBuildSpecificConfig = {
                version: '0.18.0',
                target: ['es2020', 'chrome80'],
                format: 'esm',
                splitting: true,
                platform: 'browser'
            };

            expect(config.version).toBe('0.18.0');
            expect(Array.isArray(config.target)).toBe(true);
            expect(config.format).toBe('esm');
            expect(config.splitting).toBe(true);
            expect(config.platform).toBe('browser');
        });
    });

    describe('枚举和联合类型', () => {
        it('ErrorLevel 应该包含所有错误级别', () => {
            const levels: ErrorLevel[] = ['debug', 'info', 'warn', 'error', 'fatal'];

            levels.forEach(level => {
                expect(['debug', 'info', 'warn', 'error', 'fatal']).toContain(level);
            });
        });

        it('ErrorType 应该包含所有错误类型', () => {
            const types: ErrorType[] = ['system', 'network', 'business', 'validation', 'permission', 'timeout', 'unknown'];

            types.forEach(type => {
                expect(['system', 'network', 'business', 'validation', 'permission', 'timeout', 'unknown']).toContain(type);
            });
        });

        it('HealthStatus 应该包含所有健康状态', () => {
            const statuses: HealthStatus[] = ['healthy', 'warning', 'unhealthy', 'unknown'];

            statuses.forEach(status => {
                expect(['healthy', 'warning', 'unhealthy', 'unknown']).toContain(status);
            });
        });

        it('MetricType 应该包含所有指标类型', () => {
            const types: MetricType[] = ['counter', 'gauge', 'histogram', 'timer'];

            types.forEach(type => {
                expect(['counter', 'gauge', 'histogram', 'timer']).toContain(type);
            });
        });

        it('CacheStrategy 应该包含所有缓存策略', () => {
            const strategies: CacheStrategy[] = ['lru', 'fifo', 'lfu', 'ttl'];

            strategies.forEach(strategy => {
                expect(['lru', 'fifo', 'lfu', 'ttl']).toContain(strategy);
            });
        });

        it('RouterMode 应该包含所有路由模式', () => {
            const modes: RouterMode[] = ['hash', 'history', 'memory'];

            modes.forEach(mode => {
                expect(['hash', 'history', 'memory']).toContain(mode);
            });
        });

        it('PluginType 应该包含所有插件类型', () => {
            const types: PluginType[] = ['loader', 'transformer', 'optimizer', 'analyzer', 'reporter'];

            types.forEach(type => {
                expect(['loader', 'transformer', 'optimizer', 'analyzer', 'reporter']).toContain(type);
            });
        });

        it('EventType 应该包含所有事件类型', () => {
            const types: EventType[] = ['load', 'mount', 'unmount', 'update', 'error', 'destroy'];

            types.forEach(type => {
                expect(['load', 'mount', 'unmount', 'update', 'error', 'destroy']).toContain(type);
            });
        });

        it('Environment 应该包含所有环境类型', () => {
            const envs: Environment[] = ['development', 'production', 'test'];

            envs.forEach(env => {
                expect(['development', 'production', 'test']).toContain(env);
            });
        });

        it('LogLevel 应该包含所有日志级别', () => {
            const levels: LogLevel[] = ['debug', 'info', 'warn', 'error'];

            levels.forEach(level => {
                expect(['debug', 'info', 'warn', 'error']).toContain(level);
            });
        });
    });

    describe('配置接口类型', () => {
        it('CacheConfig 应该有正确的结构', () => {
            const config: CacheConfig = {
                strategy: 'lru',
                maxSize: 1000,
                defaultTTL: 3600000,
                enableStats: true
            };

            expect(config.strategy).toBe('lru');
            expect(config.maxSize).toBe(1000);
            expect(config.defaultTTL).toBe(3600000);
            expect(config.enableStats).toBe(true);
        });

        it('RouteConfig 应该有正确的结构', () => {
            const config: RouteConfig = {
                path: '/test',
                name: 'test-route',
                component: 'TestComponent',
                children: [
                    {
                        path: '/child',
                        name: 'child-route'
                    }
                ],
                meta: { requiresAuth: true },
                beforeEnter: (to, from, next) => next()
            };

            expect(config.path).toBe('/test');
            expect(config.name).toBe('test-route');
            expect(config.component).toBe('TestComponent');
            expect(Array.isArray(config.children)).toBe(true);
            expect(config.meta?.requiresAuth).toBe(true);
            expect(typeof config.beforeEnter).toBe('function');
        });

        it('PluginConfig 应该有正确的结构', () => {
            const config: PluginConfig = {
                name: 'test-plugin',
                type: 'transformer',
                options: { transform: true },
                enabled: true,
                order: 1
            };

            expect(config.name).toBe('test-plugin');
            expect(config.type).toBe('transformer');
            expect(config.options?.transform).toBe(true);
            expect(config.enabled).toBe(true);
            expect(config.order).toBe(1);
        });

        it('GlobalConfig 应该有正确的结构', () => {
            const config: GlobalConfig = {
                env: 'production',
                logLevel: 'info',
                debug: false,
                basePath: '/app',
                publicPath: '/static/',
                timeout: 30000,
                retries: 3
            };

            expect(config.env).toBe('production');
            expect(config.logLevel).toBe('info');
            expect(config.debug).toBe(false);
            expect(config.basePath).toBe('/app');
            expect(config.publicPath).toBe('/static/');
            expect(config.timeout).toBe(30000);
            expect(config.retries).toBe(3);
        });
    });

    describe('事件和中间件类型', () => {
        it('EventData 应该有正确的结构', () => {
            const eventData: EventData<string> = {
                type: 'load',
                target: 'test-app',
                data: 'test-data',
                timestamp: Date.now()
            };

            expect(eventData.type).toBe('load');
            expect(eventData.target).toBe('test-app');
            expect(eventData.data).toBe('test-data');
            expect(typeof eventData.timestamp).toBe('number');
        });

        it('MiddlewareContext 应该有正确的结构', () => {
            const context: MiddlewareContext<{ id: string }> = {
                request: { id: 'test-id' },
                response: { success: true },
                state: { authenticated: true },
                metadata: { version: '1.0.0' }
            };

            expect(context.request?.id).toBe('test-id');
            expect(context.response?.success).toBe(true);
            expect(context.state?.authenticated).toBe(true);
            expect(context.metadata?.version).toBe('1.0.0');
        });
    });

    describe('类型兼容性和一致性', () => {
        it('所有配置类型都应该是可选的', () => {
            // 这些应该都能编译通过
            const reactConfig: ReactConfig = {};
            const vueConfig: VueConfig = {};
            const angularConfig: AngularConfig = {};
            const cacheConfig: CacheConfig = {};

            expect(typeof reactConfig).toBe('object');
            expect(typeof vueConfig).toBe('object');
            expect(typeof angularConfig).toBe('object');
            expect(typeof cacheConfig).toBe('object');
        });

        it('枚举类型应该是字符串字面量', () => {
            const errorLevel: ErrorLevel = 'error';
            const errorType: ErrorType = 'system';
            const healthStatus: HealthStatus = 'healthy';

            expect(typeof errorLevel).toBe('string');
            expect(typeof errorType).toBe('string');
            expect(typeof healthStatus).toBe('string');
        });

        it('配置对象应该支持部分更新', () => {
            const baseConfig: GlobalConfig = {
                env: 'development',
                debug: true
            };

            const updatedConfig: GlobalConfig = {
                ...baseConfig,
                logLevel: 'warn',
                timeout: 5000
            };

            expect(updatedConfig.env).toBe('development');
            expect(updatedConfig.debug).toBe(true);
            expect(updatedConfig.logLevel).toBe('warn');
            expect(updatedConfig.timeout).toBe(5000);
        });
    });

    describe('类型安全性测试', () => {
        it('应该防止无效的枚举值', () => {
            // 这些在 TypeScript 编译时会报错，但在运行时我们可以验证类型
            const validErrorLevel: ErrorLevel = 'error';
            const validEnvironment: Environment = 'production';
            const validLogLevel: LogLevel = 'info';

            expect(['debug', 'info', 'warn', 'error', 'fatal']).toContain(validErrorLevel);
            expect(['development', 'production', 'test']).toContain(validEnvironment);
            expect(['debug', 'info', 'warn', 'error']).toContain(validLogLevel);
        });

        it('应该正确处理泛型类型', () => {
            interface TestData {
                id: string;
                value: number;
            }

            const eventData: EventData<TestData> = {
                type: 'update',
                data: { id: 'test', value: 123 },
                timestamp: Date.now()
            };

            const context: MiddlewareContext<TestData> = {
                request: { id: 'test', value: 456 }
            };

            expect(eventData.data?.id).toBe('test');
            expect(eventData.data?.value).toBe(123);
            expect(context.request?.id).toBe('test');
            expect(context.request?.value).toBe(456);
        });
    });
});