{"entryPoints": ["packages/shared/types/src/index.ts", "packages/shared/utils/src/index.ts", "packages/shared/test-utils/src/index.ts", "packages/core/src/index.ts", "packages/adapters/*/src/index.ts", "packages/builders/*/src/index.ts"], "out": "docs/api", "theme": "default", "name": "Micro Core API 文档", "readme": "README.md", "includeVersion": true, "excludePrivate": true, "excludeProtected": false, "excludeExternals": true, "hideGenerator": true, "sort": ["source-order"], "kindSortOrder": ["Project", "<PERSON><PERSON><PERSON>", "Namespace", "Enum", "Class", "Interface", "Type alias", "<PERSON><PERSON><PERSON><PERSON>", "Property", "Method", "Function", "Accessor", "Variable"], "categorizeByGroup": true, "categoryOrder": ["核心", "适配器", "构建器", "工具", "类型", "测试", "*"], "plugin": ["typedoc-plugin-markdown"], "gitRevision": "main", "gitRemote": "origin", "gaID": "G-XXXXXXXXXX", "gaSite": "micro-core.dev"}