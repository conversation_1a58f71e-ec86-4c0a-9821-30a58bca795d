#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 获取所有包的路径
const packagesDir = path.join(__dirname, 'packages');
const packages = fs.readdirSync(packagesDir).filter(dir => {
    const packagePath = path.join(packagesDir, dir);
    return fs.statSync(packagePath).isDirectory() &&
        fs.existsSync(path.join(packagePath, 'package.json'));
});

console.log('🧪 开始运行所有包的测试覆盖率检查...\n');

const results = [];

packages.forEach(pkg => {
    console.log(`📦 检查包: ${pkg}`);

    const packagePath = path.join(packagesDir, pkg);
    const testDir = path.join(packagePath, '__tests__');
    const testsDir = path.join(packagePath, 'tests');
    const srcDir = path.join(packagePath, 'src');

    // 检查是否有源代码
    if (!fs.existsSync(srcDir)) {
        console.log(`   ⚠️  没有 src 目录，跳过`);
        return;
    }

    // 检查是否有测试文件
    const hasTests = fs.existsSync(testDir) || fs.existsSync(testsDir);
    if (!hasTests) {
        console.log(`   ❌ 没有测试文件`);
        results.push({ package: pkg, status: 'no-tests', coverage: 0 });
        return;
    }

    // 统计源文件数量
    const sourceFiles = getSourceFiles(srcDir);
    const testFiles = [];

    if (fs.existsSync(testDir)) {
        testFiles.push(...getTestFiles(testDir));
    }
    if (fs.existsSync(testsDir)) {
        testFiles.push(...getTestFiles(testsDir));
    }

    console.log(`   📄 源文件: ${sourceFiles.length} 个`);
    console.log(`   🧪 测试文件: ${testFiles.length} 个`);

    // 简单的覆盖率估算（基于测试文件数量）
    const estimatedCoverage = Math.min(100, (testFiles.length / sourceFiles.length) * 100);

    results.push({
        package: pkg,
        status: 'has-tests',
        sourceFiles: sourceFiles.length,
        testFiles: testFiles.length,
        coverage: Math.round(estimatedCoverage)
    });

    console.log(`   📊 估算覆盖率: ${Math.round(estimatedCoverage)}%\n`);
});

// 输出汇总报告
console.log('\n📋 测试覆盖率汇总报告');
console.log('='.repeat(50));

let totalPackages = 0;
let packagesWithTests = 0;
let totalCoverage = 0;

results.forEach(result => {
    totalPackages++;
    const status = result.status === 'has-tests' ? '✅' : '❌';
    const coverage = result.coverage || 0;

    console.log(`${status} ${result.package.padEnd(25)} ${coverage}%`);

    if (result.status === 'has-tests') {
        packagesWithTests++;
        totalCoverage += coverage;
    }
});

console.log('='.repeat(50));
console.log(`📊 总计: ${totalPackages} 个包`);
console.log(`✅ 有测试: ${packagesWithTests} 个包`);
console.log(`❌ 无测试: ${totalPackages - packagesWithTests} 个包`);
console.log(`📈 平均覆盖率: ${packagesWithTests > 0 ? Math.round(totalCoverage / packagesWithTests) : 0}%`);

// 识别需要补充测试的包
const needsTests = results.filter(r => r.status === 'no-tests' || r.coverage < 100);
if (needsTests.length > 0) {
    console.log('\n🎯 需要补充测试的包:');
    needsTests.forEach(pkg => {
        if (pkg.status === 'no-tests') {
            console.log(`   ❌ ${pkg.package} - 缺少测试文件`);
        } else {
            console.log(`   📈 ${pkg.package} - 覆盖率 ${pkg.coverage}% (需要达到 100%)`);
        }
    });
}

function getSourceFiles(dir) {
    const files = [];

    function traverse(currentDir) {
        const items = fs.readdirSync(currentDir);

        items.forEach(item => {
            const itemPath = path.join(currentDir, item);
            const stat = fs.statSync(itemPath);

            if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
                traverse(itemPath);
            } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx')) && !item.endsWith('.d.ts')) {
                files.push(itemPath);
            }
        });
    }

    traverse(dir);
    return files;
}

function getTestFiles(dir) {
    const files = [];

    function traverse(currentDir) {
        const items = fs.readdirSync(currentDir);

        items.forEach(item => {
            const itemPath = path.join(currentDir, item);
            const stat = fs.statSync(itemPath);

            if (stat.isDirectory() && !item.startsWith('.')) {
                traverse(itemPath);
            } else if (stat.isFile() && (item.includes('.test.') || item.includes('.spec.'))) {
                files.push(itemPath);
            }
        });
    }

    traverse(dir);
    return files;
}