# Micro Core 迁移指南

## 概述

本指南将帮助您从旧版本的 Micro Core 迁移到新的优化版本。新版本在保持向后兼容性的同时，提供了更好的类型安全、性能和开发体验。

## 主要变更

### 1. 包结构重组

#### 之前
```
packages/
├── core/
├── adapters/
└── builders/
```

#### 现在
```
packages/
├── shared/           # 新增共享包
│   ├── types/       # 统一类型定义
│   ├── utils/       # 通用工具函数
│   └── test-utils/  # 测试工具
├── core/            # 核心包（保持兼容）
├── adapters/        # 适配器包
└── builders/        # 构建器包
```

### 2. 类型系统升级

#### 泛型化类型
```typescript
// 之前
interface AdapterConfig {
  name: string;
  entry: string;
}

// 现在
interface BaseAdapterConfig<T = any> {
  name: string;
  entry: string;
  frameworkConfig?: T;
}

// React 适配器
type ReactAdapterConfig = BaseAdapterConfig<{
  strictMode?: boolean;
  concurrent?: boolean;
}>;
```

#### 统一的生命周期类型
```typescript
// 之前 - 各适配器有不同的生命周期定义

// 现在 - 统一的生命周期接口
interface BaseLifecycleHooks<T = any> {
  beforeLoad?: (config: BaseAdapterConfig<T>) => Promise<void> | void;
  afterLoad?: (instance: BaseAppInstance<T>) => Promise<void> | void;
  beforeMount?: (instance: BaseAppInstance<T>) => Promise<void> | void;
  afterMount?: (instance: BaseAppInstance<T>) => Promise<void> | void;
  // ... 其他生命周期钩子
}
```

### 3. 工具函数迁移

#### 推荐的导入方式
```typescript
// 推荐：从 shared 包导入
import { isObject, isFunction, formatBytes } from '@micro-core/shared/utils';
import { BaseAdapterConfig, LifecyclePhase } from '@micro-core/shared/types';

// 仍然支持：从 core 包导入（向后兼容）
import { isObject, isFunction } from '@micro-core/core';
```

#### 新增的工具函数
```typescript
// 缓存管理
import { CacheFactory, CacheStrategy } from '@micro-core/shared/utils';
const cache = CacheFactory.create(CacheStrategy.LRU, { maxSize: 1000 });

// 懒加载
import { lazyLoadUtils } from '@micro-core/shared/utils';
const component = await lazyLoadUtils.intersection('my-component', () => import('./Component'));

// 性能监控
import { globalPerformanceMonitor } from '@micro-core/shared/utils';
globalPerformanceMonitor.recordMetric('load-time', 150);

// 错误处理
import { handleError, createError } from '@micro-core/shared/utils';
handleError(createError.network('网络请求失败'));
```

## 迁移步骤

### 步骤 1: 更新依赖

```bash
# 安装新的 shared 包
npm install @micro-core/shared

# 更新现有包到最新版本
npm update @micro-core/core @micro-core/adapters-* @micro-core/builders-*
```

### 步骤 2: 更新类型导入

```typescript
// 替换类型导入
// 之前
import { MicroAppConfig, AppLifecycleHooks } from '@micro-core/core';

// 现在
import { BaseAdapterConfig, BaseLifecycleHooks } from '@micro-core/shared/types';
// 或者使用兼容别名
import { AdapterConfig, LifecycleHooks } from '@micro-core/shared/types';
```

### 步骤 3: 更新工具函数导入

```typescript
// 替换工具函数导入
// 之前
import { isObject, formatBytes, createLogger } from '@micro-core/core';

// 现在
import { isObject, formatBytes, createLogger } from '@micro-core/shared/utils';
```

### 步骤 4: 利用新功能

#### 使用新的错误处理系统
```typescript
import { globalErrorHandler, createError } from '@micro-core/shared/utils';

// 设置全局错误处理
globalErrorHandler.setContext({
  appName: 'my-app',
  userId: 'user123'
});

// 处理错误
try {
  // 业务逻辑
} catch (error) {
  globalErrorHandler.handle(createError.business('业务逻辑错误', { context: { action: 'save-data' } }));
}
```

#### 使用性能监控
```typescript
import { globalPerformanceMonitor, performanceMonitor } from '@micro-core/shared/utils';

// 装饰器方式
class MyService {
  @performanceMonitor('my-service.process-data')
  async processData(data: any) {
    // 处理数据
  }
}

// 手动记录
const timer = globalPerformanceMonitor.createTimer('manual-operation');
// 执行操作
timer(); // 记录时间
```

#### 使用缓存系统
```typescript
import { CacheFactory, CacheStrategy } from '@micro-core/shared/utils';

// 创建 LRU 缓存
const cache = CacheFactory.createLRU({ maxSize: 100 });

// 使用缓存
cache.set('user:123', userData, 300000); // 5分钟过期
const user = cache.get('user:123');
```

#### 使用懒加载
```typescript
import { lazyLoadUtils, withLazyLoad } from '@micro-core/shared/utils';

// 懒加载组件
const LazyComponent = withLazyLoad(
  () => import('./HeavyComponent'),
  LazyLoadStrategy.INTERSECTION
);

// 懒加载资源
const script = await lazyLoadUtils.deferred('analytics', () => 
  import('./analytics'), 2000
);
```

### 步骤 5: 更新测试

```typescript
// 使用新的测试工具
import { TestUtils, Assertions, setupCustomMatchers } from '@micro-core/shared/test-utils';

// 设置自定义匹配器
setupCustomMatchers();

describe('我的组件', () => {
  let testUtils: TestUtils;
  
  beforeEach(() => {
    testUtils = new TestUtils();
  });
  
  afterEach(() => {
    testUtils.destroy();
  });
  
  test('应该渲染正确的内容', async () => {
    const container = testUtils.createContainer();
    // 渲染组件
    
    // 使用自定义匹配器
    expect(container.querySelector('.my-component')).toBeInDOM();
    expect(container.querySelector('.title')).toHaveTextContent('Hello World');
  });
});
```

## 破坏性变更

### 无破坏性变更
新版本完全向后兼容，所有现有的 API 都继续工作。

### 废弃警告
在开发环境中，使用旧的导入路径会显示废弃警告：

```
[DEPRECATED] 从 @micro-core/core 导入 isObject 已废弃，请使用 @micro-core/shared/utils
```

## 性能改进

### 包体积优化
- 通过代码复用，整体包体积减少约 15%
- 支持按需导入，进一步减少打包体积

### 运行时性能
- 缓存系统提供更好的数据访问性能
- 懒加载减少初始加载时间
- 性能监控帮助识别瓶颈

### 构建性能
- 统一的类型定义减少 TypeScript 编译时间
- 优化的依赖关系减少构建时间

## 最佳实践

### 1. 渐进式迁移
```typescript
// 可以混合使用新旧导入方式
import { isObject } from '@micro-core/core'; // 旧方式，仍然工作
import { CacheFactory } from '@micro-core/shared/utils'; // 新功能
```

### 2. 类型安全
```typescript
// 使用泛型类型获得更好的类型安全
interface MyFrameworkConfig {
  theme: 'light' | 'dark';
  locale: string;
}

const config: BaseAdapterConfig<MyFrameworkConfig> = {
  name: 'my-app',
  entry: './src/main.js',
  frameworkConfig: {
    theme: 'dark',
    locale: 'zh-CN'
  }
};
```

### 3. 错误处理
```typescript
// 使用统一的错误处理
import { globalErrorHandler, createError } from '@micro-core/shared/utils';

// 在应用启动时设置错误处理
globalErrorHandler.setContext({
  appName: 'my-micro-app',
  version: '1.0.0'
});

// 在组件中处理错误
try {
  await loadData();
} catch (error) {
  globalErrorHandler.handle(
    createError.network('数据加载失败', { 
      context: { component: 'DataLoader' } 
    })
  );
}
```

### 4. 性能监控
```typescript
// 监控关键操作
import { performanceMonitor } from '@micro-core/shared/utils';

class DataService {
  @performanceMonitor('data-service.fetch-users')
  async fetchUsers() {
    // 获取用户数据
  }
  
  @performanceMonitor('data-service.process-data')
  processData(data: any[]) {
    // 处理数据
  }
}
```

## 故障排除

### 常见问题

#### 1. 类型错误
```
错误: Property 'frameworkConfig' does not exist on type 'AdapterConfig'
```
**解决方案**: 使用新的泛型类型 `BaseAdapterConfig<T>`

#### 2. 导入错误
```
错误: Module '@micro-core/shared/utils' not found
```
**解决方案**: 确保安装了 `@micro-core/shared` 包

#### 3. 测试失败
```
错误: expect(...).toBeInDOM is not a function
```
**解决方案**: 在测试设置中调用 `setupCustomMatchers()`

### 获取帮助

- 查看 [API 文档](./api/index.html)
- 提交 [Issue](https://github.com/micro-core/micro-core/issues)
- 加入 [讨论](https://github.com/micro-core/micro-core/discussions)

## 总结

新版本的 Micro Core 在保持完全向后兼容的同时，提供了更强大的功能和更好的开发体验。通过渐进式迁移，您可以逐步享受新功能带来的好处，同时保持现有代码的稳定运行。

建议优先迁移以下功能：
1. 类型导入 - 获得更好的类型安全
2. 错误处理 - 提高应用稳定性  
3. 性能监控 - 优化应用性能
4. 缓存系统 - 提升数据访问效率

如有任何问题，请参考文档或联系我们的技术支持团队。
