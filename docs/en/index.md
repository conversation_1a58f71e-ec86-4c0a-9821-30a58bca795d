---
layout: home

hero:
  name: "Micro-Core"
  text: "Next-Generation Micro-Frontend Architecture"
  tagline: "Micro-Kernel + Sidecar Pattern + Multi-Project Design for Modern Micro-Frontend Applications"
  image:
    src: /logo.svg
    alt: Micro-Core
  actions:
    - theme: brand
      text: Get Started
      link: /en/guide/getting-started
    - theme: alt
      text: View Examples
      link: /en/examples/
    - theme: alt
      text: GitHub
      link: https://github.com/echo008/micro-core

features:
  - icon: 🚀
    title: High-Performance Architecture
    details: Built on micro-kernel architecture with application-level sandbox isolation, ensuring non-interference between apps and excellent performance.
  - icon: 🔧
    title: Flexible Adapter System
    details: Built-in adapters for React, Vue, Angular and other mainstream frameworks, supporting multi-framework hybrid development with seamless integration.
  - icon: 🛡️
    title: Comprehensive Sandbox Mechanism
    details: Provides multi-layer isolation including JS sandbox, CSS sandbox, and DOM sandbox to ensure secure and stable application operation.
  - icon: 📡
    title: Powerful Communication System
    details: Supports event bus, state sharing, message passing and other communication methods for convenient inter-application collaboration.
  - icon: 🔌
    title: Rich Plugin Ecosystem
    details: Provides core plugins for routing, authentication, state management, and supports custom plugin development with strong extensibility.
  - icon: ⚡
    title: Sidecar Pattern Support
    details: Innovative sidecar pattern design supporting independent deployment and operation, reducing system complexity and improving development efficiency.
  - icon: 🛠️
    title: Complete Development Tools
    details: Provides debug panel, performance analysis, configuration generator and other development tools to enhance development experience and efficiency.
  - icon: 📚
    title: Comprehensive Documentation and Examples
    details: Complete API documentation, best practice guides, and rich example code for quick and barrier-free onboarding.
---

## Quick Start

```bash
# Install core package
npm install @micro-core/core

# Install adapters (choose as needed)
npm install @micro-core/adapter-react
npm install @micro-core/adapter-vue
npm install @micro-core/adapter-angular
```

```typescript
import { MicroCore } from '@micro-core/core'
import { ReactAdapter } from '@micro-core/adapter-react'

// Create micro-frontend instance
const microCore = new MicroCore({
  container: '#app',
  adapters: [new ReactAdapter()]
})

// Register application
microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001',
  container: '#react-container',
  activeWhen: '/react'
})

// Start application
microCore.start()
```

## Core Features

### 🏗️ Micro-Kernel Architecture

Adopts micro-kernel architecture design with streamlined and efficient core functionality, extending features through plugins and adapters to ensure system stability and scalability.

```
┌─────────────────────────────────────────┐
│              Micro-Kernel                │
├─────────────────────────────────────────┤
│ App Mgmt │ Lifecycle │ Router │ Sandbox │
├─────────────────────────────────────────┤
│         Plugin System │ Adapter System   │
├─────────────────────────────────────────┤
│   React   │   Vue   │  Angular  │ ...   │
└─────────────────────────────────────────┘
```

### 🔄 Sidecar Pattern

Innovative sidecar pattern design supporting independent application deployment and operation, reducing system coupling:

- **Independent Deployment**: Each application can be deployed and updated independently
- **Resource Isolation**: Complete resource isolation between applications with no mutual interference
- **On-Demand Loading**: Supports on-demand application loading for improved performance
- **Version Management**: Supports application version management and rollback

### 🛡️ Multi-Layer Sandbox Isolation

Provides comprehensive sandbox isolation mechanisms to ensure application security:

- **JavaScript Sandbox**: Isolates global variables and functions
- **CSS Sandbox**: Prevents style pollution and conflicts
- **DOM Sandbox**: Isolates DOM operations and events
- **Network Sandbox**: Controls network requests and resource access

## Ecosystem

### Official Adapters

- **@micro-core/adapter-react** - React application adapter
- **@micro-core/adapter-vue** - Vue application adapter
- **@micro-core/adapter-angular** - Angular application adapter
- **@micro-core/adapter-svelte** - Svelte application adapter
- **@micro-core/adapter-solid** - Solid.js application adapter

### Official Plugins

- **@micro-core/plugin-router** - Routing management plugin
- **@micro-core/plugin-communication** - Application communication plugin
- **@micro-core/plugin-auth** - Authentication plugin
- **@micro-core/plugin-state** - State management plugin

### Build Tool Integration

- **@micro-core/builder-vite** - Vite build integration
- **@micro-core/builder-webpack** - Webpack build integration
- **@micro-core/builder-rollup** - Rollup build integration

## Community Support

- [GitHub Repository](https://github.com/echo008/micro-core) - Source code and issue feedback
- [Discussions](https://github.com/echo008/micro-core/discussions) - Technical discussions and exchanges
- [Example Projects](https://github.com/echo008/micro-core-examples) - Complete example projects
- [Changelog](/en/CHANGELOG) - Version update records

## Open Source License

Micro-Core is open source under the [MIT License](https://github.com/echo008/micro-core/blob/main/LICENSE). Contributions and feedback are welcome.