# Best Practices

This section covers recommended patterns, practices, and guidelines for building robust micro-frontend applications with Micro-Core.

## Architecture Best Practices

### 1. Application Boundaries

Define clear boundaries between micro-applications:

```typescript
// ✅ Good: Clear domain boundaries
const apps = [
  {
    name: 'user-management',
    domain: 'users',
    routes: ['/users', '/users/*'],
    responsibilities: ['user CRUD', 'user profiles', 'user settings']
  },
  {
    name: 'order-management', 
    domain: 'orders',
    routes: ['/orders', '/orders/*'],
    responsibilities: ['order processing', 'order history', 'payments']
  }
];

// ❌ Bad: Overlapping responsibilities
const badApps = [
  {
    name: 'user-app',
    routes: ['/users', '/orders/user-orders'], // Mixed concerns
    responsibilities: ['users', 'user orders', 'user payments'] // Too broad
  }
];
```

### 2. Dependency Management

Manage shared dependencies properly:

```typescript
// ✅ Good: Shared dependencies configuration
const microCore = new MicroCore({
  sharedDependencies: {
    react: {
      version: '^18.0.0',
      singleton: true,
      eager: true
    },
    'react-dom': {
      version: '^18.0.0',
      singleton: true,
      eager: true
    },
    '@company/design-system': {
      version: '^2.0.0',
      singleton: true
    }
  }
});

// ❌ Bad: Each app bundles its own React
// This leads to multiple React instances and increased bundle size
```

### 3. State Management Strategy

Implement a clear state management strategy:

```typescript
// ✅ Good: Layered state management
const stateStrategy = {
  // Global state: Authentication, user info, theme
  global: ['auth', 'user', 'theme', 'locale'],
  
  // Shared state: Cross-app data that needs synchronization
  shared: ['notifications', 'shopping-cart', 'recent-activities'],
  
  // Local state: App-specific state that doesn't need sharing
  local: ['form-data', 'ui-state', 'temporary-data']
};

// Configure global state
GlobalState.configure({
  persistence: {
    keys: ['auth', 'user', 'theme'],
    storage: 'localStorage'
  },
  validation: {
    auth: (value) => value && typeof value.token === 'string',
    user: (value) => value && typeof value.id === 'string'
  }
});
```

## Performance Best Practices

### 1. Lazy Loading and Code Splitting

Implement proper lazy loading:

```typescript
// ✅ Good: Lazy loading with preloading strategy
const microCore = new MicroCore({
  loader: {
    strategy: 'lazy',
    preload: {
      // Preload on hover
      hover: ['.nav-link[href="/users"]'],
      
      // Preload on idle
      idle: ['user-management'],
      
      // Preload based on user behavior
      predictive: {
        enabled: true,
        threshold: 0.7 // 70% probability
      }
    },
    
    // Resource hints
    resourceHints: {
      prefetch: ['user-management', 'order-management'],
      preconnect: ['https://api.example.com']
    }
  }
});

// ❌ Bad: Loading all apps upfront
const badConfig = {
  loader: {
    strategy: 'eager', // Loads all apps immediately
    preload: 'all' // Preloads everything
  }
};
```

### 2. Bundle Optimization

Optimize bundle sizes:

```typescript
// vite.config.ts - Optimized build configuration
export default defineConfig({
  build: {
    rollupOptions: {
      external: [
        'react',
        'react-dom',
        '@company/design-system',
        'lodash'
      ],
      output: {
        // Manual chunk splitting
        manualChunks: {
          vendor: ['react', 'react-dom'],
          utils: ['lodash', 'date-fns'],
          ui: ['@company/design-system']
        }
      }
    },
    
    // Enable tree shaking
    treeshake: true,
    
    // Minification
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
});
```

### 3. Caching Strategy

Implement effective caching:

```typescript
// ✅ Good: Multi-layer caching
const microCore = new MicroCore({
  cache: {
    // Memory cache for frequently accessed resources
    memory: {
      enabled: true,
      maxSize: 50 * 1024 * 1024, // 50MB
      ttl: 30 * 60 * 1000 // 30 minutes
    },
    
    // HTTP cache for static resources
    http: {
      enabled: true,
      headers: {
        'Cache-Control': 'public, max-age=31536000', // 1 year for static assets
        'ETag': true
      }
    },
    
    // Service Worker cache
    serviceWorker: {
      enabled: true,
      strategy: 'cache-first',
      resources: [
        '/static/js/*.js',
        '/static/css/*.css',
        '/static/images/*'
      ]
    }
  }
});
```

## Security Best Practices

### 1. Sandbox Configuration

Configure secure sandboxes:

```typescript
// ✅ Good: Strict sandbox configuration
const microCore = new MicroCore({
  sandbox: {
    type: 'proxy',
    strict: true,
    
    // Whitelist only necessary globals
    whitelist: [
      'console',
      'fetch',
      'setTimeout',
      'setInterval',
      'clearTimeout',
      'clearInterval',
      'Promise',
      'URL',
      'URLSearchParams'
    ],
    
    // Blacklist dangerous APIs
    blacklist: [
      'eval',
      'Function',
      'WebAssembly',
      'importScripts',
      'document.write',
      'document.writeln'
    ],
    
    // CSP configuration
    contentSecurityPolicy: {
      'default-src': ["'self'"],
      'script-src': ["'self'", "'unsafe-inline'"],
      'style-src': ["'self'", "'unsafe-inline'"],
      'img-src': ["'self'", 'data:', 'https:'],
      'connect-src': ["'self'", 'https://api.example.com'],
      'frame-src': ["'none'"],
      'object-src': ["'none'"]
    }
  }
});
```

### 2. Communication Security

Secure inter-app communication:

```typescript
// ✅ Good: Secure communication
EventBus.configure({
  // Event validation
  validation: {
    enabled: true,
    schema: {
      'user:login': {
        type: 'object',
        properties: {
          userId: { type: 'string' },
          timestamp: { type: 'number' }
        },
        required: ['userId', 'timestamp']
      }
    }
  },
  
  // Rate limiting
  rateLimit: {
    maxEvents: 100,
    timeWindow: 1000,
    blockDuration: 5000
  },
  
  // Event encryption for sensitive data
  encryption: {
    enabled: true,
    algorithm: 'AES-GCM',
    keyRotation: 24 * 60 * 60 * 1000 // 24 hours
  }
});
```

## Testing Best Practices

### 1. Testing Strategy

Implement comprehensive testing:

```typescript
// ✅ Good: Multi-level testing strategy
describe('Micro-Frontend Testing', () => {
  // Unit tests for individual components
  describe('Unit Tests', () => {
    test('should render user profile component', () => {
      const { getByText } = render(<UserProfile user={mockUser} />);
      expect(getByText(mockUser.name)).toBeInTheDocument();
    });
  });
  
  // Integration tests for app interactions
  describe('Integration Tests', () => {
    test('should communicate between apps', async () => {
      const microCore = new MicroCore({ container: document.body });
      
      // Register test apps
      microCore.registerApp(testApp1);
      microCore.registerApp(testApp2);
      
      await microCore.start();
      
      // Test communication
      EventBus.emit('test:event', { data: 'test' });
      
      await waitFor(() => {
        expect(mockEventHandler).toHaveBeenCalledWith({ data: 'test' });
      });
    });
  });
  
  // E2E tests for complete user flows
  describe('E2E Tests', () => {
    test('should complete user registration flow', async () => {
      await page.goto('/register');
      await page.fill('[data-testid="email"]', '<EMAIL>');
      await page.fill('[data-testid="password"]', 'password123');
      await page.click('[data-testid="submit"]');
      
      await expect(page).toHaveURL('/dashboard');
      await expect(page.locator('[data-testid="welcome"]')).toBeVisible();
    });
  });
});
```

## Deployment Best Practices

### 1. CI/CD Pipeline

Set up proper CI/CD:

```yaml
# .github/workflows/deploy.yml
name: Deploy Micro-Frontend

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - run: pnpm install
      - run: pnpm test
      - run: pnpm build
      
      # Test integration
      - run: pnpm test:integration
      
      # E2E tests
      - run: pnpm test:e2e

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      # Build and deploy each micro-app
      - name: Deploy Main App
        run: |
          cd apps/main
          pnpm build
          aws s3 sync dist/ s3://main-app-bucket/
          
      - name: Deploy User App
        run: |
          cd apps/user-management
          pnpm build
          aws s3 sync dist/ s3://user-app-bucket/
          
      # Update CDN
      - name: Invalidate CDN
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ secrets.CLOUDFRONT_ID }} \
            --paths "/*"
```

### 2. Environment Configuration

Manage environments properly:

```typescript
// ✅ Good: Environment-specific configuration
const getConfig = () => {
  const env = process.env.NODE_ENV || 'development';
  
  const configs = {
    development: {
      apps: {
        'user-management': 'http://localhost:3001',
        'order-management': 'http://localhost:3002'
      },
      api: {
        baseUrl: 'http://localhost:8000/api'
      }
    },
    
    staging: {
      apps: {
        'user-management': 'https://staging-user-app.example.com',
        'order-management': 'https://staging-order-app.example.com'
      },
      api: {
        baseUrl: 'https://staging-api.example.com'
      }
    },
    
    production: {
      apps: {
        'user-management': 'https://user-app.example.com',
        'order-management': 'https://order-app.example.com'
      },
      api: {
        baseUrl: 'https://api.example.com'
      }
    }
  };
  
  return configs[env];
};
```

## Monitoring and Debugging

### 1. Error Tracking

Implement comprehensive error tracking:

```typescript
// ✅ Good: Error tracking setup
const microCore = new MicroCore({
  errorHandler: {
    onLoadError: (error, app) => {
      // Log to monitoring service
      errorTracker.captureException(error, {
        tags: {
          type: 'app-load-error',
          appName: app.name
        },
        extra: {
          appConfig: app.config,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        }
      });
      
      // Show user-friendly error
      showErrorNotification({
        title: 'Application Load Failed',
        message: `Failed to load ${app.name}. Please refresh the page.`,
        action: () => window.location.reload()
      });
    },
    
    onMountError: (error, app) => {
      errorTracker.captureException(error, {
        tags: { type: 'app-mount-error', appName: app.name }
      });
      
      // Show fallback UI
      showFallbackUI(app.container, {
        title: 'Service Unavailable',
        message: 'This service is temporarily unavailable.',
        retry: () => microCore.remountApp(app.name)
      });
    }
  }
});
```

### 2. Performance Monitoring

Monitor performance metrics:

```typescript
// ✅ Good: Performance monitoring
const performanceMonitor = new PerformanceMonitor({
  metrics: {
    // Core Web Vitals
    coreWebVitals: true,
    
    // Custom metrics
    appLoadTime: true,
    appMountTime: true,
    routeChangeTime: true,
    
    // Resource metrics
    bundleSize: true,
    memoryUsage: true,
    networkRequests: true
  },
  
  // Reporting
  reporting: {
    endpoint: '/api/metrics',
    interval: 30000, // 30 seconds
    batchSize: 10
  },
  
  // Alerts
  alerts: {
    slowAppLoad: {
      threshold: 3000, // 3 seconds
      action: (metric) => {
        console.warn(`Slow app load detected: ${metric.appName} took ${metric.duration}ms`);
      }
    },
    highMemoryUsage: {
      threshold: 100 * 1024 * 1024, // 100MB
      action: (metric) => {
        console.warn(`High memory usage: ${metric.usage} bytes`);
      }
    }
  }
});
```

## Common Anti-Patterns to Avoid

### 1. Tight Coupling

```typescript
// ❌ Bad: Tight coupling between apps
// App A directly calling App B's methods
window.appB.updateUserProfile(userData);

// ✅ Good: Loose coupling through events
EventBus.emit('user:profile:update', userData);
```

### 2. Shared Mutable State

```typescript
// ❌ Bad: Shared mutable objects
window.sharedData = {
  user: { id: 1, name: 'John' }
};
// App A modifies: window.sharedData.user.name = 'Jane'
// App B gets unexpected changes

// ✅ Good: Immutable state updates
GlobalState.set('user', { id: 1, name: 'Jane' });
// All apps get notified of the change
```

### 3. Blocking Operations

```typescript
// ❌ Bad: Synchronous app loading
const app = loadAppSync('user-management'); // Blocks UI

// ✅ Good: Asynchronous loading with loading states
const loadApp = async () => {
  showLoadingSpinner();
  try {
    const app = await loadAppAsync('user-management');
    hideLoadingSpinner();
    return app;
  } catch (error) {
    showErrorMessage('Failed to load application');
  }
};
```

## Next Steps

- [Architecture Guidelines](./architecture.md) - Detailed architecture patterns
- [Performance Optimization](./performance.md) - Advanced performance techniques
- [Testing Strategies](./testing.md) - Comprehensive testing approaches
- [Deployment Patterns](./deployment.md) - Production deployment strategies
- [Error Handling](./error-handling.md) - Robust error handling patterns

## Resources

- 📚 [Official Documentation](https://micro-core.dev/docs)
- 🎯 [Best Practices Repository](https://github.com/micro-core/best-practices)
- 💬 [Community Discussions](https://github.com/micro-core/micro-core/discussions)
- 📧 [Support Email](mailto:<EMAIL>)