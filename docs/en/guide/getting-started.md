# Getting Started

Welcome to Micro-Core! This guide will help you quickly set up and run your first micro-frontend application using Micro-Core.

## What is Micro-Core?

Micro-Core is a next-generation micro-frontend architecture solution that provides:

- 🏗️ **Micro-kernel Architecture**: Lightweight core with plugin-based extensions
- 🛡️ **Multi-layer Sandbox Isolation**: Complete isolation between applications
- 📡 **Powerful Communication System**: Multiple communication methods between apps
- 🔧 **Framework Agnostic**: Support for React, Vue, Angular, and more
- ⚡ **High Performance**: Optimized loading and runtime performance

## Prerequisites

Before getting started, make sure you have:

- Node.js >= 18.0.0
- npm >= 8.0.0 or pnpm >= 8.0.0 (recommended)
- Basic knowledge of JavaScript/TypeScript
- Familiarity with at least one frontend framework (React, Vue, Angular)

## Quick Installation

### 1. Install Core Package

```bash
# Using npm
npm install @micro-core/core

# Using yarn
yarn add @micro-core/core

# Using pnpm (recommended)
pnpm add @micro-core/core
```

### 2. Install Framework Adapters

Choose the adapters you need based on your applications:

```bash
# React adapter
npm install @micro-core/adapter-react

# Vue adapter
npm install @micro-core/adapter-vue

# Angular adapter
npm install @micro-core/adapter-angular

# Svelte adapter
npm install @micro-core/adapter-svelte
```

## Your First Micro-Frontend Application

### 1. Create Main Application

Create a new file `main.ts` for your main application:

```typescript
import { MicroCore } from '@micro-core/core';
import { ReactAdapter } from '@micro-core/adapter-react';

// Create micro-core instance
const microCore = new MicroCore({
  container: '#app',
  adapters: [new ReactAdapter()]
});

// Register micro-applications
microCore.registerApp({
  name: 'header-app',
  entry: 'http://localhost:3001',
  container: '#header',
  activeWhen: '/'
});

microCore.registerApp({
  name: 'sidebar-app',
  entry: 'http://localhost:3002',
  container: '#sidebar',
  activeWhen: '/'
});

microCore.registerApp({
  name: 'content-app',
  entry: 'http://localhost:3003',
  container: '#content',
  activeWhen: ['/dashboard', '/users', '/settings']
});

// Start the micro-frontend system
microCore.start().then(() => {
  console.log('Micro-frontend system started successfully!');
});
```

### 2. Create HTML Template

Create an `index.html` file:

```html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Micro-Core Application</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
    }
    
    .layout {
      display: grid;
      grid-template-areas: 
        "header header"
        "sidebar content";
      grid-template-rows: 60px 1fr;
      grid-template-columns: 250px 1fr;
      height: 100vh;
    }
    
    #header { grid-area: header; background: #f0f0f0; }
    #sidebar { grid-area: sidebar; background: #e0e0e0; }
    #content { grid-area: content; background: #fff; }
  </style>
</head>
<body>
  <div id="app" class="layout">
    <div id="header"></div>
    <div id="sidebar"></div>
    <div id="content"></div>
  </div>
  
  <script type="module" src="./main.ts"></script>
</body>
</html>
```

### 3. Create Micro-Applications

#### Header Application (React)

Create a simple React application for the header:

```typescript
// header-app/src/index.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';

const Header: React.FC = () => {
  return (
    <header style={{ padding: '1rem', borderBottom: '1px solid #ccc' }}>
      <h1>My Micro-Frontend App</h1>
    </header>
  );
};

// Micro-Core lifecycle functions
export async function bootstrap() {
  console.log('Header app bootstrapped');
}

export async function mount(props: any) {
  const container = props.container || document.getElementById('header');
  const root = ReactDOM.createRoot(container);
  root.render(<Header />);
}

export async function unmount(props: any) {
  const container = props.container || document.getElementById('header');
  ReactDOM.unmountComponentAtNode(container);
}
```

#### Sidebar Application (Vue)

Create a Vue application for the sidebar:

```typescript
// sidebar-app/src/main.ts
import { createApp, App as VueApp } from 'vue';
import Sidebar from './Sidebar.vue';

let app: VueApp<Element> | null = null;

export async function bootstrap() {
  console.log('Sidebar app bootstrapped');
}

export async function mount(props: any) {
  const container = props.container || document.getElementById('sidebar');
  app = createApp(Sidebar);
  app.mount(container);
}

export async function unmount() {
  if (app) {
    app.unmount();
    app = null;
  }
}
```

```vue
<!-- sidebar-app/src/Sidebar.vue -->
<template>
  <nav class="sidebar">
    <ul>
      <li><a href="/dashboard">Dashboard</a></li>
      <li><a href="/users">Users</a></li>
      <li><a href="/settings">Settings</a></li>
    </ul>
  </nav>
</template>

<style scoped>
.sidebar {
  padding: 1rem;
}

.sidebar ul {
  list-style: none;
  padding: 0;
}

.sidebar li {
  margin-bottom: 0.5rem;
}

.sidebar a {
  text-decoration: none;
  color: #333;
  padding: 0.5rem;
  display: block;
  border-radius: 4px;
}

.sidebar a:hover {
  background-color: #f0f0f0;
}
</style>
```

## Configuration Options

### Basic Configuration

```typescript
const microCore = new MicroCore({
  // Main container element
  container: '#app',
  
  // Framework adapters
  adapters: [
    new ReactAdapter(),
    new VueAdapter(),
    new AngularAdapter()
  ],
  
  // Sandbox configuration
  sandbox: {
    type: 'proxy', // 'proxy' | 'iframe' | 'webcomponent'
    strictMode: true,
    css: true,
    js: true
  },
  
  // Router configuration
  router: {
    mode: 'browser', // 'browser' | 'hash'
    base: '/',
    caseSensitive: false
  },
  
  // Communication configuration
  communication: {
    eventBus: {
      maxListeners: 100,
      enableDebug: false
    },
    globalState: {
      persistence: true,
      storage: 'localStorage'
    }
  },
  
  // Performance configuration
  performance: {
    prefetch: true,
    preload: ['header-app', 'sidebar-app'],
    cache: {
      enabled: true,
      maxAge: 30 * 60 * 1000 // 30 minutes
    }
  }
});
```

### Advanced Configuration

```typescript
const microCore = new MicroCore({
  container: '#app',
  adapters: [new ReactAdapter()],
  
  // Advanced sandbox configuration
  sandbox: {
    type: 'proxy',
    strictMode: true,
    css: {
      isolation: 'scoped',
      prefixing: true
    },
    js: {
      whitelist: ['console', 'fetch', 'setTimeout'],
      blacklist: ['eval', 'Function']
    }
  },
  
  // Advanced router configuration
  router: {
    mode: 'browser',
    base: '/',
    guards: {
      beforeEach: async (to, from, next) => {
        // Authentication check
        if (to.meta?.requiresAuth && !isAuthenticated()) {
          next('/login');
          return;
        }
        next();
      }
    }
  },
  
  // Error handling
  errorHandler: {
    onLoadError: (error, app) => {
      console.error(`Failed to load ${app.name}:`, error);
      // Show fallback UI
    },
    onMountError: (error, app) => {
      console.error(`Failed to mount ${app.name}:`, error);
      // Show error boundary
    }
  },
  
  // Lifecycle hooks
  hooks: {
    beforeLoad: async (app) => {
      console.log(`Loading ${app.name}...`);
    },
    afterMount: async (app) => {
      console.log(`${app.name} mounted successfully`);
    }
  }
});
```

## Development Workflow

### 1. Development Server Setup

For development, you'll typically run multiple development servers:

```bash
# Terminal 1: Main application
cd main-app
npm run dev # Usually runs on http://localhost:3000

# Terminal 2: Header application
cd header-app
npm run dev # Usually runs on http://localhost:3001

# Terminal 3: Sidebar application
cd sidebar-app
npm run dev # Usually runs on http://localhost:3002

# Terminal 4: Content application
cd content-app
npm run dev # Usually runs on http://localhost:3003
```

### 2. Build Configuration

Each micro-application needs proper build configuration. Here's an example for Vite:

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  build: {
    lib: {
      entry: 'src/index.tsx',
      name: 'HeaderApp',
      formats: ['umd']
    },
    rollupOptions: {
      external: ['react', 'react-dom'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM'
        }
      }
    }
  },
  define: {
    'process.env.NODE_ENV': '"production"'
  }
});
```

## Testing Your Setup

### 1. Basic Functionality Test

```typescript
// test/basic.test.ts
import { MicroCore } from '@micro-core/core';

describe('Micro-Core Basic Functionality', () => {
  let microCore: MicroCore;
  
  beforeEach(() => {
    microCore = new MicroCore({
      container: document.createElement('div')
    });
  });
  
  afterEach(() => {
    microCore.destroy();
  });
  
  test('should register application successfully', () => {
    microCore.registerApp({
      name: 'test-app',
      entry: 'http://localhost:3001',
      container: '#test',
      activeWhen: '/test'
    });
    
    expect(microCore.getApp('test-app')).toBeDefined();
  });
  
  test('should start micro-core system', async () => {
    await expect(microCore.start()).resolves.not.toThrow();
  });
});
```

### 2. Integration Test

```typescript
// test/integration.test.ts
import { MicroCore } from '@micro-core/core';
import { ReactAdapter } from '@micro-core/adapter-react';

describe('Integration Tests', () => {
  test('should load and mount React application', async () => {
    const container = document.createElement('div');
    document.body.appendChild(container);
    
    const microCore = new MicroCore({
      container,
      adapters: [new ReactAdapter()]
    });
    
    microCore.registerApp({
      name: 'react-app',
      entry: 'http://localhost:3001',
      container: '#react-app',
      activeWhen: '/react'
    });
    
    await microCore.start();
    
    // Navigate to trigger app loading
    window.history.pushState({}, '', '/react');
    
    // Wait for app to load and mount
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    expect(container.querySelector('#react-app')).toBeTruthy();
    
    microCore.destroy();
    document.body.removeChild(container);
  });
});
```

## Common Issues and Solutions

### 1. CORS Issues

If you encounter CORS issues during development:

```typescript
// Add CORS headers to your development server
// For Vite:
export default defineConfig({
  server: {
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  }
});
```

### 2. Module Loading Issues

For module loading issues:

```typescript
// Ensure proper module format in build configuration
export default defineConfig({
  build: {
    lib: {
      entry: 'src/index.ts',
      formats: ['umd', 'es']
    },
    rollupOptions: {
      output: {
        format: 'umd',
        name: 'MyApp'
      }
    }
  }
});
```

### 3. Style Conflicts

To avoid style conflicts:

```typescript
const microCore = new MicroCore({
  sandbox: {
    css: {
      isolation: 'scoped',
      prefixing: true,
      sanitization: true
    }
  }
});
```

## Next Steps

Now that you have a basic micro-frontend setup running:

1. **[Explore Core Features](./features/)** - Learn about routing, communication, and sandbox isolation
2. **[Best Practices](./best-practices/)** - Follow recommended patterns and practices
3. **[API Reference](../api/)** - Dive deep into the API documentation
4. **[Examples](../examples/)** - Check out more complex examples
5. **[Migration Guide](../migration/)** - Migrate from other micro-frontend solutions

## Getting Help

If you need help:

- 📚 [Documentation](https://micro-core.dev/docs)
- 🐙 [GitHub Issues](https://github.com/echo008/micro-core/issues)
- 💬 [Discord Community](https://discord.gg/micro-core)
- 📧 [Email Support](mailto:<EMAIL>)

Welcome to the Micro-Core community! 🚀