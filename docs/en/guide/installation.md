# Installation

This guide covers different ways to install and set up Micro-Core in your project.

## Prerequisites

Before installing Micro-Core, ensure you have:

- **Node.js**: Version 18.0.0 or higher
- **Package Manager**: npm 8.0.0+, yarn 1.22.0+, or pnpm 8.0.0+ (recommended)
- **Browser Support**: Modern browsers with ES2020 support

## Package Installation

### Core Package

Install the core Micro-Core package:

```bash
# Using npm
npm install @micro-core/core

# Using yarn
yarn add @micro-core/core

# Using pnpm (recommended)
pnpm add @micro-core/core
```

### Framework Adapters

Install adapters for your target frameworks:

```bash
# React adapter
npm install @micro-core/adapter-react

# Vue adapter
npm install @micro-core/adapter-vue

# Angular adapter
npm install @micro-core/adapter-angular

# Svelte adapter
npm install @micro-core/adapter-svelte

# Solid.js adapter
npm install @micro-core/adapter-solid
```

### Build Tool Integration

Install build tool integrations:

```bash
# Vite integration
npm install @micro-core/builder-vite

# Webpack integration
npm install @micro-core/builder-webpack

# Rollup integration
npm install @micro-core/builder-rollup
```

### Official Plugins

Install official plugins as needed:

```bash
# Router plugin
npm install @micro-core/plugin-router

# Communication plugin
npm install @micro-core/plugin-communication

# Authentication plugin
npm install @micro-core/plugin-auth

# State management plugin
npm install @micro-core/plugin-state
```

## CDN Installation

For quick prototyping or simple setups, you can use CDN:

```html
<!-- Core library -->
<script src="https://unpkg.com/@micro-core/core@latest/dist/micro-core.umd.js"></script>

<!-- React adapter -->
<script src="https://unpkg.com/@micro-core/adapter-react@latest/dist/adapter-react.umd.js"></script>

<!-- Vue adapter -->
<script src="https://unpkg.com/@micro-core/adapter-vue@latest/dist/adapter-vue.umd.js"></script>
```

### CDN Usage Example

```html
<!DOCTYPE html>
<html>
<head>
  <title>Micro-Core CDN Example</title>
</head>
<body>
  <div id="app"></div>
  
  <script src="https://unpkg.com/@micro-core/core@latest/dist/micro-core.umd.js"></script>
  <script src="https://unpkg.com/@micro-core/adapter-react@latest/dist/adapter-react.umd.js"></script>
  
  <script>
    const { MicroCore } = window.MicroCore;
    const { ReactAdapter } = window.MicroCoreReactAdapter;
    
    const microCore = new MicroCore({
      container: '#app',
      adapters: [new ReactAdapter()]
    });
    
    microCore.registerApp({
      name: 'my-app',
      entry: 'https://my-app.example.com',
      container: '#my-app',
      activeWhen: '/'
    });
    
    microCore.start();
  </script>
</body>
</html>
```

## TypeScript Support

Micro-Core includes built-in TypeScript support. Install type definitions:

```bash
# Types are included in the main packages
npm install @micro-core/core
# TypeScript definitions are automatically available
```

### TypeScript Configuration

Add to your `tsconfig.json`:

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "strict": true,
    "types": ["@micro-core/core"]
  },
  "include": [
    "src/**/*",
    "types/**/*"
  ]
}
```

## Development Setup

### 1. Create Project Structure

```bash
mkdir my-micro-frontend
cd my-micro-frontend

# Initialize package.json
npm init -y

# Install dependencies
npm install @micro-core/core @micro-core/adapter-react
npm install -D typescript @types/node vite
```

### 2. Basic Configuration

Create `src/main.ts`:

```typescript
import { MicroCore } from '@micro-core/core';
import { ReactAdapter } from '@micro-core/adapter-react';

const microCore = new MicroCore({
  container: '#app',
  adapters: [new ReactAdapter()]
});

// Register your micro-applications
microCore.registerApp({
  name: 'header-app',
  entry: 'http://localhost:3001',
  container: '#header',
  activeWhen: '/'
});

microCore.start();
```

### 3. HTML Template

Create `index.html`:

```html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>My Micro-Frontend</title>
</head>
<body>
  <div id="app">
    <div id="header"></div>
    <div id="content"></div>
  </div>
  <script type="module" src="/src/main.ts"></script>
</body>
</html>
```

### 4. Build Configuration

Create `vite.config.ts`:

```typescript
import { defineConfig } from 'vite';

export default defineConfig({
  server: {
    port: 3000,
    cors: true
  },
  build: {
    target: 'es2020',
    rollupOptions: {
      external: ['react', 'react-dom'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM'
        }
      }
    }
  }
});
```

## Monorepo Setup

For larger projects, consider a monorepo structure:

```bash
my-micro-frontend/
├── packages/
│   ├── main-app/          # Main application
│   ├── user-app/          # User management micro-app
│   ├── order-app/         # Order management micro-app
│   └── shared/            # Shared utilities
├── package.json
├── pnpm-workspace.yaml    # For pnpm workspaces
└── lerna.json             # For Lerna
```

### Workspace Configuration

Create `pnpm-workspace.yaml`:

```yaml
packages:
  - 'packages/*'
```

Create root `package.json`:

```json
{
  "name": "my-micro-frontend",
  "private": true,
  "scripts": {
    "dev": "pnpm -r --parallel dev",
    "build": "pnpm -r build",
    "test": "pnpm -r test"
  },
  "devDependencies": {
    "@micro-core/core": "^0.1.0",
    "typescript": "^5.0.0",
    "vite": "^4.0.0"
  }
}
```

## Docker Setup

For containerized development:

Create `Dockerfile`:

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY pnpm-lock.yaml ./

# Install pnpm
RUN npm install -g pnpm

# Install dependencies
RUN pnpm install

# Copy source code
COPY . .

# Build application
RUN pnpm build

# Expose port
EXPOSE 3000

# Start application
CMD ["pnpm", "start"]
```

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  main-app:
    build: ./packages/main-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    volumes:
      - ./packages/main-app:/app
      - /app/node_modules

  user-app:
    build: ./packages/user-app
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
    volumes:
      - ./packages/user-app:/app
      - /app/node_modules

  order-app:
    build: ./packages/order-app
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
    volumes:
      - ./packages/order-app:/app
      - /app/node_modules
```

## Verification

Verify your installation:

```typescript
// test-installation.js
import { MicroCore } from '@micro-core/core';

console.log('Micro-Core version:', MicroCore.version);

const microCore = new MicroCore({
  container: document.createElement('div')
});

console.log('Installation successful!');
```

Run the test:

```bash
node test-installation.js
```

## Troubleshooting

### Common Issues

1. **Module Resolution Errors**
   ```bash
   # Clear node_modules and reinstall
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **TypeScript Errors**
   ```bash
   # Ensure TypeScript is properly configured
   npx tsc --noEmit
   ```

3. **Build Errors**
   ```bash
   # Check build configuration
   npm run build -- --verbose
   ```

### Getting Help

If you encounter issues:

- 📚 [Documentation](https://micro-core.dev/docs)
- 🐙 [GitHub Issues](https://github.com/echo008/micro-core/issues)
- 💬 [Discord Community](https://discord.gg/micro-core)
- 📧 [Support Email](mailto:<EMAIL>)

## Next Steps

After installation:

1. [Getting Started Guide](./getting-started.md) - Build your first micro-frontend
2. [Core Concepts](./core-concepts.md) - Understand the fundamentals
3. [Configuration](./configuration.md) - Learn about configuration options
4. [Examples](../examples/) - Explore example projects

## Version Compatibility

| Micro-Core | Node.js | TypeScript | React | Vue | Angular |
|------------|---------|------------|-------|-----|---------|
| 0.1.x | 18+ | 4.5+ | 16.8+ | 2.7+ | 12+ |
| 1.0.x | 18+ | 5.0+ | 17+ | 3.0+ | 14+ |

Stay updated with the latest releases on [GitHub](https://github.com/echo008/micro-core/releases).