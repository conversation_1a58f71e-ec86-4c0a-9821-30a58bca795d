# Core Features

Micro-Core provides a comprehensive set of features for building modern micro-frontend applications. This section covers all the core capabilities and how to use them effectively.

## Feature Overview

### 🏗️ Application Management
Complete lifecycle management for micro-applications, from registration to destruction.

- **Application Registration**: Register and configure micro-applications
- **Lifecycle Management**: Control application loading, mounting, and unmounting
- **Dynamic Loading**: Load applications on-demand based on routes or conditions
- **Error Handling**: Robust error handling and recovery mechanisms

[Learn more about Application Management →](./app-management.md)

### 🧭 Routing System
Unified routing coordination between main and micro-applications.

- **Route Configuration**: Flexible route matching and activation rules
- **Dynamic Routing**: Runtime route registration and management
- **Route Guards**: Authentication and authorization controls
- **Route Caching**: Performance optimization through intelligent caching

[Learn more about Routing System →](./routing.md)

### 📡 Inter-Application Communication
Multiple communication methods for seamless data exchange between applications.

- **Event Bus**: Publish-subscribe pattern for event-driven communication
- **Global State**: Reactive global state management across applications
- **Direct Communication**: Direct method calls between application instances
- **Props Passing**: Initial data passing through application configuration

[Learn more about Communication →](./communication.md)

### 🛡️ Sandbox Isolation
Multi-layer isolation mechanisms to ensure application security and stability.

- **Proxy Sandbox**: High-performance JavaScript isolation using ES6 Proxy
- **Iframe Sandbox**: Maximum isolation using iframe containers
- **WebComponent Sandbox**: Style isolation using Shadow DOM
- **Custom Sandboxes**: Extensible sandbox system for specific needs

[Learn more about Sandbox Isolation →](./sandbox.md)

### 🔄 Application Lifecycle
Comprehensive lifecycle management with hooks and monitoring.

- **Lifecycle Hooks**: Before/after hooks for all lifecycle phases
- **State Monitoring**: Real-time application state tracking
- **Performance Monitoring**: Lifecycle performance analysis
- **Error Recovery**: Automatic error handling and recovery strategies

[Learn more about Application Lifecycle →](./lifecycle.md)

### 🗃️ State Management
Advanced state management with persistence and synchronization.

- **Global State**: Centralized state management across applications
- **State Persistence**: Local storage and session storage integration
- **State Synchronization**: Cross-application state synchronization
- **State Validation**: Schema-based state validation and middleware

[Learn more about State Management →](./state-management.md)

## Feature Comparison Matrix

| Feature | Proxy Sandbox | Iframe Sandbox | WebComponent | Namespace |
|---------|---------------|----------------|--------------|-----------|
| **JavaScript Isolation** | ✅ High | ✅ Maximum | ⚠️ Limited | ⚠️ Basic |
| **CSS Isolation** | ❌ None | ✅ Complete | ✅ Shadow DOM | ❌ None |
| **Performance** | ✅ Excellent | ⚠️ Good | ✅ Very Good | ✅ Excellent |
| **Browser Support** | ✅ Modern | ✅ All | ✅ Modern | ✅ All |
| **Communication** | ✅ Direct | ⚠️ PostMessage | ✅ Direct | ✅ Direct |
| **Memory Usage** | ✅ Low | ⚠️ High | ✅ Medium | ✅ Very Low |

## Integration Patterns

### 1. Basic Integration

```typescript
import { MicroCore } from '@micro-core/core';

const microCore = new MicroCore({
  container: '#app',
  sandbox: 'proxy',
  router: {
    mode: 'browser',
    base: '/'
  }
});

// Register applications
microCore.registerApp({
  name: 'user-management',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeWhen: '/users'
});

// Start the system
microCore.start();
```

### 2. Advanced Integration

```typescript
import { 
  MicroCore, 
  ProxySandboxPlugin,
  RouterPlugin,
  CommunicationPlugin,
  StatePlugin
} from '@micro-core/core';

const microCore = new MicroCore();

// Configure plugins
microCore.use(ProxySandboxPlugin, {
  strict: true,
  whitelist: ['console', 'fetch']
});

microCore.use(RouterPlugin, {
  mode: 'browser',
  guards: {
    beforeEach: async (to, from, next) => {
      // Authentication check
      if (to.meta.requiresAuth && !isAuthenticated()) {
        next('/login');
        return;
      }
      next();
    }
  }
});

microCore.use(CommunicationPlugin, {
  eventBus: {
    maxListeners: 100,
    enableDebug: true
  },
  globalState: {
    persistence: {
      storage: 'localStorage',
      prefix: 'micro-core-'
    }
  }
});

microCore.use(StatePlugin, {
  validation: true,
  middleware: ['logging', 'performance', 'caching']
});
```

## Performance Considerations

### 1. Application Loading

```typescript
// Optimize application loading
microCore.configure({
  loader: {
    // Parallel loading
    concurrency: 3,
    
    // Preloading strategy
    preload: {
      idle: true,
      hover: '.nav-link',
      viewport: true
    },
    
    // Caching strategy
    cache: {
      enabled: true,
      strategy: 'memory',
      maxAge: 30 * 60 * 1000 // 30 minutes
    }
  }
});
```

### 2. State Management

```typescript
// Optimize state operations
GlobalState.configure({
  // Batch updates
  batchUpdates: true,
  batchDelay: 16, // One frame
  
  // Computed state
  computedState: {
    'user.displayName': () => {
      const user = GlobalState.get('user.profile');
      return `${user.firstName} ${user.lastName}`;
    }
  },
  
  // State persistence
  persistence: {
    debounce: 1000,
    compress: true
  }
});
```

### 3. Communication Optimization

```typescript
// Optimize event communication
EventBus.configure({
  // Event batching
  batchEvents: true,
  batchSize: 10,
  
  // Rate limiting
  rateLimit: {
    maxEvents: 100,
    timeWindow: 1000
  },
  
  // Memory management
  maxListeners: 50,
  autoCleanup: true
});
```

## Security Best Practices

### 1. Sandbox Configuration

```typescript
// Secure sandbox setup
microCore.use(ProxySandboxPlugin, {
  strict: true,
  
  // Whitelist only necessary globals
  whitelist: [
    'console',
    'fetch',
    'setTimeout',
    'setInterval',
    'Promise'
  ],
  
  // Blacklist dangerous APIs
  blacklist: [
    'eval',
    'Function',
    'WebAssembly',
    'importScripts'
  ],
  
  // CSP integration
  contentSecurityPolicy: {
    'script-src': ["'self'", "'unsafe-inline'"],
    'style-src': ["'self'", "'unsafe-inline'"],
    'img-src': ["'self'", 'data:', 'https:']
  }
});
```

### 2. Communication Security

```typescript
// Secure communication setup
microCore.use(CommunicationPlugin, {
  eventBus: {
    // Event validation
    validateEvents: true,
    
    // Rate limiting
    rateLimit: {
      maxEvents: 100,
      timeWindow: 1000
    },
    
    // Event encryption (for sensitive data)
    encryption: {
      enabled: true,
      algorithm: 'AES-GCM',
      keyRotation: 24 * 60 * 60 * 1000 // 24 hours
    }
  }
});
```

## Debugging and Monitoring

### 1. Development Tools

```typescript
if (process.env.NODE_ENV === 'development') {
  // Enable debug mode
  microCore.enableDebug({
    lifecycle: true,
    communication: true,
    state: true,
    routing: true,
    sandbox: true
  });
  
  // Expose debugging tools
  window.__MICRO_CORE_DEBUG__ = {
    microCore,
    apps: microCore.getApps(),
    state: GlobalState.getAll(),
    events: EventBus.getEventHistory(),
    performance: microCore.getPerformanceMetrics()
  };
}
```

### 2. Production Monitoring

```typescript
// Production monitoring setup
microCore.use(MonitoringPlugin, {
  // Error tracking
  errorTracking: {
    enabled: true,
    endpoint: '/api/errors',
    sampleRate: 0.1 // 10% sampling
  },
  
  // Performance monitoring
  performance: {
    enabled: true,
    endpoint: '/api/metrics',
    interval: 60000 // 1 minute
  },
  
  // User analytics
  analytics: {
    enabled: true,
    trackPageViews: true,
    trackUserActions: true
  }
});
```

## Migration Guide

### From Single-SPA

```typescript
// Single-SPA to Micro-Core migration
import { createMicroCoreAdapter } from '@micro-core/adapter-single-spa';

const adapter = createMicroCoreAdapter({
  // Map Single-SPA lifecycle methods
  lifecycleMapping: {
    bootstrap: 'beforeMount',
    mount: 'afterMount',
    unmount: 'beforeUnmount'
  },
  
  // Convert Single-SPA configuration
  configTransform: (singleSpaConfig) => ({
    name: singleSpaConfig.name,
    entry: singleSpaConfig.app,
    activeWhen: singleSpaConfig.activeWhen,
    container: singleSpaConfig.domElementGetter?.() || '#app'
  })
});

// Use adapter to migrate existing apps
const migratedApps = adapter.migrateApps(existingSingleSpaApps);
migratedApps.forEach(app => microCore.registerApp(app));
```

### From qiankun

```typescript
// qiankun to Micro-Core migration
import { createQiankunAdapter } from '@micro-core/adapter-qiankun';

const adapter = createQiankunAdapter({
  // Convert qiankun sandbox to Micro-Core sandbox
  sandboxMapping: {
    'strictStyleIsolation': 'webcomponent',
    'experimentalStyleIsolation': 'proxy'
  },
  
  // Convert qiankun communication
  communicationMapping: {
    'initGlobalState': 'GlobalState',
    'onGlobalStateChange': 'GlobalState.watch'
  }
});

// Migrate qiankun configuration
const migratedConfig = adapter.migrateConfig(qiankunConfig);
microCore.configure(migratedConfig);
```

## Next Steps

Now that you understand the core features, you can:

1. **[Get Started](../getting-started.md)** - Set up your first micro-frontend application
2. **[Best Practices](../best-practices/)** - Learn recommended patterns and practices
3. **[API Reference](../../api/)** - Explore detailed API documentation
4. **[Examples](../../examples/)** - See real-world implementation examples
5. **[Migration Guide](../../migration/)** - Migrate from other micro-frontend solutions

## Community and Support

- 📚 [Documentation](https://micro-core.dev/docs)
- 🐙 [GitHub Repository](https://github.com/echo008/micro-core)
- 💬 [Discord Community](https://discord.gg/micro-core)
- 🎯 [Issue Tracker](https://github.com/echo008/micro-core/issues)
- 📧 [Email Support](mailto:<EMAIL>)
