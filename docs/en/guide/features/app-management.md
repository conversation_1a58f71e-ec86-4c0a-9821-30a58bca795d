# Application Management

Application management is one of the core features of Micro-Core, responsible for micro-application registration, discovery, loading, lifecycle management, and other key operations. This chapter will detail how to effectively manage micro-frontend applications.

## Application Registration

### Basic Registration

```typescript
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore()

// Register a single application
microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001',
  container: '#react-container',
  activeWhen: '/react'
})
```

### Batch Registration

```typescript
const apps = [
  {
    name: 'react-app',
    entry: 'http://localhost:3001',
    container: '#react-container',
    activeWhen: '/react'
  },
  {
    name: 'vue-app',
    entry: 'http://localhost:3002',
    container: '#vue-container',
    activeWhen: '/vue'
  }
]

// Register applications in batch
microCore.registerApps(apps)
```

### Dynamic Registration

```typescript
// Dynamically register applications at runtime
async function loadDynamicApp() {
  const appConfig = await fetch('/api/apps/dynamic-app').then(res => res.json())
  
  microCore.registerApp({
    name: appConfig.name,
    entry: appConfig.entry,
    container: '#dynamic-container',
    activeWhen: appConfig.route,
    props: appConfig.props
  })
}
```

## Application Configuration

### Complete Configuration Options

```typescript
interface AppConfig {
  // Basic configuration
  name: string                    // Application name (required)
  entry: string | AppEntry       // Application entry (required)
  container: string | Element    // Mount container (required)
  activeWhen: ActiveRule         // Activation rule (required)
  
  // Optional configuration
  props?: Record<string, any>    // Properties passed to application
  loader?: CustomLoader          // Custom loader
  sandbox?: SandboxConfig        // Sandbox configuration
  prefetch?: boolean | PrefetchConfig  // Prefetch configuration
  
  // Lifecycle hooks
  beforeLoad?: LifecycleHook
  afterLoad?: LifecycleHook
  beforeMount?: LifecycleHook
  afterMount?: LifecycleHook
  beforeUnmount?: LifecycleHook
  afterUnmount?: LifecycleHook
  beforeUpdate?: LifecycleHook
  afterUpdate?: LifecycleHook
}
```

### Entry Configuration

```typescript
// String entry
entry: 'http://localhost:3001'

// Object entry
entry: {
  scripts: ['http://localhost:3001/static/js/main.js'],
  styles: ['http://localhost:3001/static/css/main.css'],
  html: 'http://localhost:3001/index.html'
}

// Function entry
entry: async () => {
  const manifest = await fetch('/api/app-manifest').then(res => res.json())
  return {
    scripts: manifest.scripts,
    styles: manifest.styles
  }
}
```

### Activation Rules

```typescript
// Path matching
activeWhen: '/react-app'

// Regular expression
activeWhen: /^\/react-app/

// Function matching
activeWhen: (location) => {
  return location.pathname.startsWith('/react-app') && 
         location.search.includes('module=react')
}

// Multiple condition matching
activeWhen: ['/react-app', '/react/*', (location) => location.hash === '#react']
```

## Application Lifecycle

### Lifecycle States

```typescript
enum AppStatus {
  NOT_LOADED = 'NOT_LOADED',       // Not loaded
  LOADING = 'LOADING',             // Loading
  LOAD_ERROR = 'LOAD_ERROR',       // Load error
  LOADED = 'LOADED',               // Loaded
  BOOTSTRAPPING = 'BOOTSTRAPPING', // Bootstrapping
  NOT_MOUNTED = 'NOT_MOUNTED',     // Not mounted
  MOUNTING = 'MOUNTING',           // Mounting
  MOUNTED = 'MOUNTED',             // Mounted
  UNMOUNTING = 'UNMOUNTING',       // Unmounting
  UPDATING = 'UPDATING',           // Updating
  SKIP_BECAUSE_BROKEN = 'SKIP_BECAUSE_BROKEN' // Skip (broken)
}
```

### Lifecycle Flow Diagram

```
┌─────────────┐
│ NOT_LOADED  │
└──────┬──────┘
       │ load()
       ▼
┌─────────────┐    error    ┌─────────────┐
│   LOADING   │────────────▶│ LOAD_ERROR  │
└──────┬──────┘             └─────────────┘
       │ success
       ▼
┌─────────────┐
│   LOADED    │
└──────┬──────┘
       │ bootstrap()
       ▼
┌─────────────┐
│BOOTSTRAPPING│
└──────┬──────┘
       │
       ▼
┌─────────────┐    mount()    ┌─────────────┐
│ NOT_MOUNTED │──────────────▶│  MOUNTING   │
└─────────────┘               └──────┬──────┘
       ▲                             │
       │ unmount()                   ▼
       │                      ┌─────────────┐
┌─────────────┐               │   MOUNTED   │
│ UNMOUNTING  │◀──────────────┤             │
└─────────────┘               └──────┬──────┘
                                     │ update()
                                     ▼
                              ┌─────────────┐
                              │  UPDATING   │
                              └─────────────┘
```

### Lifecycle Hooks

```typescript
microCore.registerApp({
  name: 'my-app',
  entry: 'http://localhost:3001',
  container: '#container',
  activeWhen: '/my-app',
  
  // Before load hook
  beforeLoad: async (app) => {
    console.log(`Preparing to load application: ${app.name}`)
    // Can do preparation work here, such as permission check
    const hasPermission = await checkPermission(app.name)
    if (!hasPermission) {
      throw new Error('No permission to access this application')
    }
  },
  
  // After load hook
  afterLoad: (app) => {
    console.log(`Application loaded: ${app.name}`)
    // Can do follow-up processing here, such as analytics tracking
    analytics.track('app_loaded', { appName: app.name })
  },
  
  // Before mount hook
  beforeMount: (app) => {
    console.log(`Preparing to mount application: ${app.name}`)
    // Can set application initial state here
    app.props = {
      ...app.props,
      user: getCurrentUser(),
      theme: getTheme()
    }
  },
  
  // After mount hook
  afterMount: (app) => {
    console.log(`Application mounted: ${app.name}`)
    // Can do DOM operations or event binding here
    setupAppAnalytics(app.container)
  },
  
  // Before unmount hook
  beforeUnmount: (app) => {
    console.log(`Preparing to unmount application: ${app.name}`)
    // Can do cleanup work here
    cleanupAppAnalytics(app.container)
  },
  
  // After unmount hook
  afterUnmount: (app) => {
    console.log(`Application unmounted: ${app.name}`)
    // Can do follow-up cleanup here
    clearAppCache(app.name)
  }
})
```

## Application Query and Management

### Get Application Information

```typescript
// Get single application information
const app = microCore.getApp('react-app')
console.log(app.status, app.props, app.container)

// Get all applications
const allApps = microCore.getApps()
console.log('Registered applications:', allApps.map(app => app.name))

// Get active applications
const activeApps = microCore.getActiveApps()
console.log('Currently active applications:', activeApps.map(app => app.name))

// Filter applications by status
const mountedApps = microCore.getAppsByStatus(AppStatus.MOUNTED)
console.log('Mounted applications:', mountedApps.map(app => app.name))
```

### Application Status Monitoring

```typescript
// Listen to application status changes
microCore.onAppStatusChange((app, newStatus, oldStatus) => {
  console.log(`Application ${app.name} status changed from ${oldStatus} to ${newStatus}`)
  
  // Execute corresponding operations based on status changes
  switch (newStatus) {
    case AppStatus.MOUNTED:
      // Application mounted
      onAppMounted(app)
      break
    case AppStatus.UNMOUNTING:
      // Application starting to unmount
      onAppUnmounting(app)
      break
    case AppStatus.LOAD_ERROR:
      // Application load failed
      onAppLoadError(app)
      break
  }
})

// Listen to specific application status changes
microCore.onAppStatusChange('react-app', (app, newStatus, oldStatus) => {
  console.log(`React application status change: ${oldStatus} -> ${newStatus}`)
})
```

### Manual Application Control

```typescript
// Manually load application
await microCore.loadApp('react-app')

// Manually mount application
await microCore.mountApp('react-app')

// Manually unmount application
await microCore.unmountApp('react-app')

// Manually update application
await microCore.updateApp('react-app', { 
  props: { theme: 'dark' } 
})

// Reload application
await microCore.reloadApp('react-app')
```

## Application Unregistration and Cleanup

### Unregister Applications

```typescript
// Unregister single application
microCore.unregisterApp('react-app')

// Batch unregister applications
microCore.unregisterApps(['react-app', 'vue-app'])

// Unregister all applications
microCore.unregisterAllApps()
```

### Application Cleanup

```typescript
// Clear application cache
microCore.clearAppCache('react-app')

// Clear all cache
microCore.clearAllCache()

// Force clean application (including DOM and events)
microCore.forceCleanApp('react-app')
```

## Error Handling

### Application Load Errors

```typescript
microCore.registerApp({
  name: 'error-prone-app',
  entry: 'http://localhost:3001',
  container: '#container',
  activeWhen: '/error-app',
  
  // Load error handling
  onLoadError: (error, app) => {
    console.error(`Application ${app.name} failed to load:`, error)
    
    // Show error page
    showErrorPage(app.container, {
      title: 'Application Load Failed',
      message: 'Please try again later or contact administrator',
      retry: () => microCore.reloadApp(app.name)
    })
  },
  
  // Mount error handling
  onMountError: (error, app) => {
    console.error(`Application ${app.name} failed to mount:`, error)
    
    // Fallback to default page
    showFallbackPage(app.container)
  }
})
```

### Global Error Handling

```typescript
// Set global error handler
microCore.setErrorHandler((error, app, phase) => {
  console.error(`Application ${app?.name} error in ${phase} phase:`, error)
  
  // Send error report
  errorReporting.report({
    error: error.message,
    stack: error.stack,
    app: app?.name,
    phase,
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
    url: location.href
  })
  
  // Take different strategies based on error type
  if (error.name === 'ChunkLoadError') {
    // Resource load failed, try to reload
    microCore.reloadApp(app.name)
  } else if (error.name === 'NetworkError') {
    // Network error, show offline message
    showOfflineMessage()
  }
})
```

## Performance Optimization

### Prefetch Strategy

```typescript
// Configure prefetch
microCore.registerApp({
  name: 'dashboard-app',
  entry: 'http://localhost:3001',
  container: '#dashboard',
  activeWhen: '/dashboard',
  
  // Enable prefetch
  prefetch: true,
  
  // Or configure detailed prefetch strategy
  prefetch: {
    // Preload during idle time
    idle: true,
    
    // Preload on hover
    hover: '.dashboard-link',
    
    // Preload in viewport
    viewport: true,
    
    // Delay prefetch (milliseconds)
    delay: 2000
  }
})
```

### Application Caching

```typescript
// Configure application caching
microCore.configure({
  cache: {
    // Enable application cache
    enabled: true,
    
    // Cache strategy
    strategy: 'memory', // 'memory' | 'localStorage' | 'sessionStorage'
    
    // Cache size limit
    maxSize: 50, // MB
    
    // Cache expiration time
    maxAge: 30 * 60 * 1000, // 30 minutes
    
    // Cache key generator function
    keyGenerator: (app) => `${app.name}@${app.version || 'latest'}`
  }
})
```

### Resource Optimization

```typescript
// Configure resource loading optimization
microCore.configure({
  loader: {
    // Concurrent loading count
    concurrency: 3,
    
    // Timeout
    timeout: 30000,
    
    // Retry count
    retries: 2,
    
    // Resource integrity check
    integrity: true,
    
    // Cross-origin configuration
    crossOrigin: 'anonymous'
  }
})
```

## Best Practices

### 1. Application Naming Convention

```typescript
// Recommended naming convention
const apps = [
  { name: 'user-management', ... },    // Use kebab-case
  { name: 'order-system', ... },       // Descriptive names
  { name: 'dashboard-v2', ... },       // Include version info
]

// Avoid these naming styles
const badApps = [
  { name: 'app1', ... },               // Meaningless names
  { name: 'UserManagement', ... },     // Using PascalCase
  { name: 'user_management', ... },    // Using snake_case
]
```

### 2. Container Management

```typescript
// Recommended: Create independent containers for each application
<div id="app-container">
  <div id="user-management-container"></div>
  <div id="order-system-container"></div>
  <div id="dashboard-container"></div>
</div>

// Avoid: Multiple applications sharing the same container
<div id="shared-container"></div> <!-- Not recommended -->
```

### 3. Lifecycle Management

```typescript
// Recommended: Complete lifecycle handling
microCore.registerApp({
  name: 'my-app',
  // ... other configuration
  
  beforeMount: (app) => {
    // Set application context
    setupAppContext(app)
  },
  
  afterMount: (app) => {
    // Initialize application-specific features
    initializeAppFeatures(app)
  },
  
  beforeUnmount: (app) => {
    // Clean up application resources
    cleanupAppResources(app)
  },
  
  afterUnmount: (app) => {
    // Final cleanup
    finalCleanup(app)
  }
})
```

### 4. Error Boundaries

```typescript
// Recommended: Set error boundaries for each application
microCore.registerApp({
  name: 'my-app',
  // ... other configuration
  
  errorBoundary: {
    // Error fallback component
    fallback: ErrorFallback,
    
    // Error handling function
    onError: (error, errorInfo) => {
      console.error('Application error:', error, errorInfo)
      reportError(error, errorInfo)
    }
  }
})
```

### 5. Performance Monitoring

```typescript
// Recommended: Monitor application performance
microCore.onAppStatusChange((app, newStatus, oldStatus) => {
  const timestamp = Date.now()
  
  // Record performance metrics
  performance.mark(`${app.name}-${newStatus}-${timestamp}`)
  
  if (newStatus === AppStatus.MOUNTED) {
    // Calculate load time
    const loadTime = timestamp - app.loadStartTime
    analytics.track('app_load_time', {
      appName: app.name,
      loadTime
    })
  }
})
```

## Common Issues

### Q: How to handle application load failures?

A: Can be handled in multiple ways:

```typescript
// 1. Set retry mechanism
microCore.registerApp({
  name: 'my-app',
  // ... other configuration
  
  onLoadError: async (error, app) => {
    // Retry 3 times
    for (let i = 0; i < 3; i++) {
      try {
        await microCore.reloadApp(app.name)
        break
      } catch (retryError) {
        if (i === 2) {
          // Last retry failed, show error page
          showErrorPage(app.container)
        }
      }
    }
  }
})

// 2. Set fallback solution
microCore.registerApp({
  name: 'my-app',
  // ... other configuration
  
  fallback: {
    // Fallback to static page
    html: '<div>Application temporarily unavailable, please try again later</div>',
    
    // Or fallback to another application
    app: 'fallback-app'
  }
})
```

### Q: How to implement hot reload for applications?

A: Can be implemented in the following ways:

```typescript
// 1. Listen to application update events
microCore.on('app:update-available', async (app) => {
  const shouldUpdate = await confirm(`Application ${app.name} has a new version, update now?`)
  
  if (shouldUpdate) {
    // Hot reload application
    await microCore.hotReload(app.name)
  }
})

// 2. Periodically check for updates
setInterval(async () => {
  const apps = microCore.getApps()
  
  for (const app of apps) {
    const hasUpdate = await checkAppUpdate(app)
    if (hasUpdate) {
      microCore.emit('app:update-available', app)
    }
  }
}, 5 * 60 * 1000) // Check every 5 minutes
```

### Q: How to handle dependencies between applications?

A: Can be managed through dependency configuration:

```typescript
microCore.registerApp({
  name: 'child-app',
  // ... other configuration
  
  // Declare dependencies
  dependencies: ['parent-app', 'shared-lib'],
  
  // Load current application after dependencies are loaded
  waitForDependencies: true
})

// Or manually control loading order
async function loadAppsWithDependencies() {
  // Load dependency applications first
  await microCore.loadApp('parent-app')
  await microCore.loadApp('shared-lib')
  
  // Then load child application
  await microCore.loadApp('child-app')
}
```

## Summary

Application management is the core of micro-frontend architecture. Through proper application registration, lifecycle management, error handling, and performance optimization, you can build stable and efficient micro-frontend systems.

Key points:
- Use clear naming conventions and container management
- Complete lifecycle hook handling
- Robust error handling and fallback mechanisms
- Reasonable performance optimization strategies
- Complete monitoring and logging

## Next Steps

- [Routing System](./routing.md) - Learn micro-frontend routing configuration
- [Sandbox Isolation](./sandbox.md) - Understand application isolation mechanisms
- [Application Communication](./communication.md) - Master inter-application communication
- [Performance Optimization](../best-practices/performance.md) - Deep dive into performance optimization techniques
