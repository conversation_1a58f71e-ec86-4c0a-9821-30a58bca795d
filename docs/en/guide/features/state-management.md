# State Management

Micro-Core provides a comprehensive state management system that supports global state sharing, local state isolation, and reactive state updates across micro-applications.

## State Management Overview

### State Architecture

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core State Architecture                 │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │  Global State   │    │  Shared State   │    │  Local State    ││
│  │                 │    │                 │    │                 ││
│  │ • User info     │    │ • Theme         │    │ • Form data     ││
│  │ • Auth status   │    │ • Language      │    │ • UI state      ││
│  │ • Permissions   │    │ • Config        │    │ • Cache         ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│           │                       │                       │       │
│           └───────────────────────┼───────────────────────┘       │
│                                   │                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    State Synchronization                    │ │
│  │  Global ↔ Shared ↔ Local with Reactive Updates            │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Global State Management

### Basic Global State Operations

```typescript
import { GlobalState } from '@micro-core/core';

// Set global state
GlobalState.set('user', {
  id: '123',
  name: '<PERSON>',
  email: '<EMAIL>',
  role: 'admin'
});

GlobalState.set('theme', 'dark');
GlobalState.set('language', 'en');

// Get global state
const user = GlobalState.get('user');
const theme = GlobalState.get('theme');

// Check if state exists
if (GlobalState.has('user')) {
  console.log('User is logged in');
}

// Remove state
GlobalState.remove('tempData');

// Clear all state
GlobalState.clear();
```

### Nested State Management

```typescript
// Set nested state
GlobalState.set('app.settings.theme', 'dark');
GlobalState.set('app.settings.language', 'en');
GlobalState.set('user.profile.avatar', 'avatar.jpg');
GlobalState.set('user.preferences.notifications', true);

// Get nested state
const theme = GlobalState.get('app.settings.theme');
const avatar = GlobalState.get('user.profile.avatar');
const notifications = GlobalState.get('user.preferences.notifications');

// Update partial nested state
GlobalState.merge('user.profile', {
  avatar: 'new-avatar.jpg',
  bio: 'Software Developer'
});

// Get entire nested object
const userProfile = GlobalState.get('user.profile');
const appSettings = GlobalState.get('app.settings');
```

### Reactive State Updates

```typescript
// Watch specific state changes
const unwatch = GlobalState.watch('user', (newUser, oldUser) => {
  console.log('User changed:', { newUser, oldUser });
  
  // Update UI based on user changes
  updateUserInterface(newUser);
  
  // Sync with other applications
  EventBus.emit('user:changed', newUser);
});

// Watch nested state changes
GlobalState.watch('app.settings.theme', (newTheme, oldTheme) => {
  console.log(`Theme changed from ${oldTheme} to ${newTheme}`);
  
  // Apply theme changes
  document.body.className = `theme-${newTheme}`;
  
  // Notify all applications
  EventBus.emit('theme:changed', newTheme);
});

// Watch multiple state paths
const unwatchMultiple = GlobalState.watchMultiple([
  'user.profile',
  'app.settings',
  'permissions'
], (changes) => {
  console.log('Multiple states changed:', changes);
  
  changes.forEach(({ path, newValue, oldValue }) => {
    console.log(`${path}: ${oldValue} -> ${newValue}`);
  });
});

// Watch all state changes
const unwatchAll = GlobalState.watchAll((path, newValue, oldValue) => {
  console.log(`Global state changed: ${path}`, { newValue, oldValue });
  
  // Log state changes for debugging
  if (process.env.NODE_ENV === 'development') {
    console.log('[State Debug]', { path, newValue, oldValue });
  }
});

// Unwatch when no longer needed
unwatch();
unwatchMultiple();
unwatchAll();
```

## State Persistence

### Local Storage Persistence

```typescript
import { PersistentState } from '@micro-core/core';

// Create persistent state with localStorage
const persistentState = new PersistentState({
  storage: 'localStorage',
  prefix: 'micro-core-',
  serialize: JSON.stringify,
  deserialize: JSON.parse,
  
  // Define which states to persist
  persistKeys: [
    'user.profile',
    'app.settings',
    'user.preferences'
  ],
  
  // Exclude sensitive data
  excludeKeys: [
    'user.password',
    'auth.token',
    'temp.*'
  ]
});

// Initialize persistent state
await persistentState.init();

// Set persistent state
persistentState.set('user.preferences.theme', 'dark');
persistentState.set('app.settings.language', 'en');

// State will be automatically saved to localStorage
```

### Session Storage Persistence

```typescript
// Create session-based persistent state
const sessionState = new PersistentState({
  storage: 'sessionStorage',
  prefix: 'session-',
  
  // Auto-save interval (milliseconds)
  autoSaveInterval: 5000,
  
  // Compression for large data
  compress: true,
  
  // Encryption for sensitive data
  encrypt: true,
  encryptionKey: 'your-encryption-key'
});

await sessionState.init();
```

### Custom Storage Persistence

```typescript
// Implement custom storage adapter
class CustomStorageAdapter {
  async getItem(key: string): Promise<string | null> {
    // Custom get implementation
    return await customAPI.getState(key);
  }
  
  async setItem(key: string, value: string): Promise<void> {
    // Custom set implementation
    await customAPI.setState(key, value);
  }
  
  async removeItem(key: string): Promise<void> {
    // Custom remove implementation
    await customAPI.removeState(key);
  }
  
  async clear(): Promise<void> {
    // Custom clear implementation
    await customAPI.clearState();
  }
}

// Use custom storage
const customPersistentState = new PersistentState({
  storage: new CustomStorageAdapter(),
  prefix: 'custom-'
});
```

## State Synchronization

### Cross-Application State Sync

```typescript
class StateSync {
  private syncRules = new Map();
  private syncInProgress = new Set();

  constructor(private microCore: MicroCore) {
    this.setupStateSynchronization();
  }

  // Add synchronization rule
  addSyncRule(statePath: string, targetApps: string[], options: SyncOptions = {}) {
    this.syncRules.set(statePath, {
      targetApps,
      bidirectional: options.bidirectional || false,
      transform: options.transform,
      debounce: options.debounce || 100
    });

    // Watch for state changes
    GlobalState.watch(statePath, this.createSyncHandler(statePath));
  }

  private createSyncHandler(statePath: string) {
    return debounce((newValue: any, oldValue: any) => {
      if (this.syncInProgress.has(statePath)) {
        return; // Avoid circular sync
      }

      this.syncStateToApps(statePath, newValue);
    }, this.getSyncRule(statePath).debounce);
  }

  private async syncStateToApps(statePath: string, value: any) {
    const rule = this.getSyncRule(statePath);
    if (!rule) return;

    this.syncInProgress.add(statePath);

    try {
      const syncPromises = rule.targetApps.map(async (appName) => {
        const app = this.microCore.getApp(appName);
        if (!app || app.status !== 'MOUNTED') return;

        // Transform value if transformer provided
        const transformedValue = rule.transform 
          ? rule.transform(value, appName) 
          : value;

        // Send state update to application
        await this.sendStateUpdate(app, statePath, transformedValue);
      });

      await Promise.all(syncPromises);
    } finally {
      this.syncInProgress.delete(statePath);
    }
  }

  private async sendStateUpdate(app: any, statePath: string, value: any) {
    // Send state update via different methods based on app type
    if (app.instance && app.instance.updateState) {
      // Direct method call
      app.instance.updateState(statePath, value);
    } else {
      // Event-based update
      EventBus.emit(`state:update:${app.name}`, {
        path: statePath,
        value
      });
    }
  }

  private getSyncRule(statePath: string) {
    return this.syncRules.get(statePath);
  }

  private setupStateSynchronization() {
    // Listen for state sync requests from applications
    EventBus.on('state:sync:request', (data) => {
      const { appName, statePath, value } = data;
      
      if (!this.syncInProgress.has(statePath)) {
        GlobalState.set(statePath, value);
      }
    });
  }
}

// Usage example
const stateSync = new StateSync(microCore);

// Sync user state to all applications
stateSync.addSyncRule('user', ['app1', 'app2', 'app3'], {
  bidirectional: true,
  debounce: 200
});

// Sync theme with transformation
stateSync.addSyncRule('app.settings.theme', ['ui-app', 'dashboard-app'], {
  transform: (theme, appName) => {
    // Transform theme for specific apps
    if (appName === 'legacy-app') {
      return theme === 'dark' ? 'night' : 'day';
    }
    return theme;
  }
});
```

### State Conflict Resolution

```typescript
class StateConflictResolver {
  private conflictStrategies = new Map();

  // Register conflict resolution strategy
  registerStrategy(statePath: string, strategy: ConflictStrategy) {
    this.conflictStrategies.set(statePath, strategy);
  }

  // Resolve state conflict
  async resolveConflict(
    statePath: string, 
    localValue: any, 
    remoteValue: any, 
    metadata: ConflictMetadata
  ): Promise<any> {
    const strategy = this.conflictStrategies.get(statePath) || 'lastWrite';
    
    switch (strategy) {
      case 'lastWrite':
        return this.resolveLastWrite(localValue, remoteValue, metadata);
      
      case 'merge':
        return this.resolveMerge(localValue, remoteValue);
      
      case 'userChoice':
        return this.resolveUserChoice(localValue, remoteValue, metadata);
      
      case 'custom':
        const customResolver = this.conflictStrategies.get(`${statePath}:custom`);
        return customResolver ? customResolver(localValue, remoteValue, metadata) : localValue;
      
      default:
        return localValue;
    }
  }

  private resolveLastWrite(localValue: any, remoteValue: any, metadata: ConflictMetadata): any {
    return metadata.remoteTimestamp > metadata.localTimestamp ? remoteValue : localValue;
  }

  private resolveMerge(localValue: any, remoteValue: any): any {
    if (typeof localValue === 'object' && typeof remoteValue === 'object') {
      return { ...localValue, ...remoteValue };
    }
    return remoteValue;
  }

  private async resolveUserChoice(localValue: any, remoteValue: any, metadata: ConflictMetadata): Promise<any> {
    return new Promise((resolve) => {
      showConflictDialog({
        localValue,
        remoteValue,
        metadata,
        onResolve: resolve
      });
    });
  }
}
```

## State Validation

### Schema-Based Validation

```typescript
import { StateValidator } from '@micro-core/core';

// Define state schema
const stateSchema = {
  user: {
    type: 'object',
    required: ['id', 'name', 'email'],
    properties: {
      id: { type: 'string' },
      name: { type: 'string', minLength: 1 },
      email: { type: 'string', format: 'email' },
      role: { type: 'string', enum: ['admin', 'user', 'guest'] },
      profile: {
        type: 'object',
        properties: {
          avatar: { type: 'string', format: 'uri' },
          bio: { type: 'string', maxLength: 500 }
        }
      }
    }
  },
  
  app: {
    type: 'object',
    properties: {
      settings: {
        type: 'object',
        properties: {
          theme: { type: 'string', enum: ['light', 'dark'] },
          language: { type: 'string', pattern: '^[a-z]{2}$' }
        }
      }
    }
  }
};

// Create validator
const validator = new StateValidator(stateSchema);

// Validate state before setting
GlobalState.setValidator(validator);

// This will throw validation error
try {
  GlobalState.set('user', {
    id: 123, // Should be string
    name: '', // Should not be empty
    email: 'invalid-email' // Should be valid email
  });
} catch (error) {
  console.error('State validation failed:', error.details);
}
```

### Custom Validation Rules

```typescript
// Add custom validation rules
validator.addRule('user.email', (value) => {
  if (!value.endsWith('@company.com')) {
    throw new Error('Email must be from company domain');
  }
});

validator.addRule('user.role', (value, context) => {
  const currentUser = context.get('currentUser');
  if (currentUser.role !== 'admin' && value === 'admin') {
    throw new Error('Only admins can set admin role');
  }
});

// Async validation
validator.addAsyncRule('user.id', async (value) => {
  const exists = await checkUserExists(value);
  if (!exists) {
    throw new Error('User ID does not exist');
  }
});
```

## State Middleware

### Logging Middleware

```typescript
class StateLoggingMiddleware {
  constructor(private options: LoggingOptions = {}) {}

  before(action: StateAction): StateAction {
    if (this.options.logBefore) {
      console.log(`[State] Before ${action.type}:`, {
        path: action.path,
        value: action.value,
        timestamp: Date.now()
      });
    }
    return action;
  }

  after(action: StateAction, result: any): void {
    if (this.options.logAfter) {
      console.log(`[State] After ${action.type}:`, {
        path: action.path,
        result,
        timestamp: Date.now()
      });
    }
  }

  error(action: StateAction, error: Error): void {
    console.error(`[State] Error in ${action.type}:`, {
      path: action.path,
      error: error.message,
      stack: error.stack,
      timestamp: Date.now()
    });
  }
}

// Apply middleware
GlobalState.use(new StateLoggingMiddleware({
  logBefore: true,
  logAfter: true
}));
```

### Performance Middleware

```typescript
class StatePerformanceMiddleware {
  private performanceData = new Map();

  before(action: StateAction): StateAction {
    this.performanceData.set(action.id, {
      startTime: performance.now(),
      action
    });
    return action;
  }

  after(action: StateAction): void {
    const data = this.performanceData.get(action.id);
    if (data) {
      const duration = performance.now() - data.startTime;
      
      if (duration > 10) { // Log slow operations
        console.warn(`[State Performance] Slow operation: ${action.type} took ${duration}ms`);
      }
      
      this.performanceData.delete(action.id);
    }
  }
}

GlobalState.use(new StatePerformanceMiddleware());
```

### Caching Middleware

```typescript
class StateCachingMiddleware {
  private cache = new Map();
  private cacheConfig = new Map();

  constructor() {
    this.setupCacheConfig();
  }

  private setupCacheConfig() {
    // Configure caching for specific paths
    this.cacheConfig.set('user.profile', {
      ttl: 5 * 60 * 1000, // 5 minutes
      maxSize: 100
    });
    
    this.cacheConfig.set('app.settings', {
      ttl: 30 * 60 * 1000, // 30 minutes
      maxSize: 50
    });
  }

  before(action: StateAction): StateAction {
    if (action.type === 'GET') {
      const cached = this.getFromCache(action.path);
      if (cached) {
        action.cached = true;
        action.value = cached.value;
      }
    }
    return action;
  }

  after(action: StateAction, result: any): void {
    if (action.type === 'SET' && this.shouldCache(action.path)) {
      this.setCache(action.path, result);
    }
  }

  private getFromCache(path: string): any {
    const cacheKey = this.getCacheKey(path);
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached;
    }
    
    // Remove expired cache
    if (cached) {
      this.cache.delete(cacheKey);
    }
    
    return null;
  }

  private setCache(path: string, value: any): void {
    const config = this.getCacheConfig(path);
    if (!config) return;

    const cacheKey = this.getCacheKey(path);
    this.cache.set(cacheKey, {
      value,
      timestamp: Date.now(),
      ttl: config.ttl
    });

    // Cleanup old cache entries
    this.cleanupCache(config.maxSize);
  }

  private shouldCache(path: string): boolean {
    return this.cacheConfig.has(path) || this.cacheConfig.has(this.getPathPattern(path));
  }

  private getCacheConfig(path: string) {
    return this.cacheConfig.get(path) || this.cacheConfig.get(this.getPathPattern(path));
  }

  private getCacheKey(path: string): string {
    return `state:${path}`;
  }

  private getPathPattern(path: string): string {
    // Convert specific path to pattern (e.g., user.profile.123 -> user.profile.*)
    return path.replace(/\.\d+/g, '.*');
  }

  private cleanupCache(maxSize: number): void {
    if (this.cache.size <= maxSize) return;

    // Remove oldest entries
    const entries = Array.from(this.cache.entries())
      .sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    const toRemove = entries.slice(0, this.cache.size - maxSize);
    toRemove.forEach(([key]) => this.cache.delete(key));
  }
}

GlobalState.use(new StateCachingMiddleware());
```

## State Debugging

### State Inspector

```typescript
class StateInspector {
  private history: StateChange[] = [];
  private maxHistorySize = 1000;
  private filters = new Set<string>();

  constructor() {
    this.setupStateWatcher();
  }

  private setupStateWatcher() {
    GlobalState.watchAll((path, newValue, oldValue) => {
      if (this.shouldRecord(path)) {
        this.recordChange({
          path,
          newValue,
          oldValue,
          timestamp: Date.now(),
          stack: new Error().stack
        });
      }
    });
  }

  private shouldRecord(path: string): boolean {
    if (this.filters.size === 0) return true;
    return Array.from(this.filters).some(filter => path.startsWith(filter));
  }

  private recordChange(change: StateChange): void {
    this.history.push(change);
    
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    }
  }

  // Add path filter
  addFilter(pathPrefix: string): void {
    this.filters.add(pathPrefix);
  }

  // Remove path filter
  removeFilter(pathPrefix: string): void {
    this.filters.delete(pathPrefix);
  }

  // Get state change history
  getHistory(pathFilter?: string): StateChange[] {
    if (!pathFilter) return [...this.history];
    
    return this.history.filter(change => 
      change.path.startsWith(pathFilter)
    );
  }

  // Get current state snapshot
  getSnapshot(): any {
    return GlobalState.getAll();
  }

  // Export state for debugging
  exportState(): string {
    return JSON.stringify({
      snapshot: this.getSnapshot(),
      history: this.history,
      timestamp: Date.now()
    }, null, 2);
  }

  // Import state for debugging
  importState(stateData: string): void {
    try {
      const data = JSON.parse(stateData);
      
      // Restore state
      Object.entries(data.snapshot).forEach(([path, value]) => {
        GlobalState.set(path, value);
      });
      
      console.log('State imported successfully');
    } catch (error) {
      console.error('Failed to import state:', error);
    }
  }

  // Time travel debugging
  timeTravel(targetTimestamp: number): void {
    const targetChange = this.history.find(change => 
      change.timestamp <= targetTimestamp
    );
    
    if (targetChange) {
      // Restore state to target time
      GlobalState.set(targetChange.path, targetChange.oldValue);
      console.log(`Time traveled to ${new Date(targetTimestamp)}`);
    }
  }
}

// Create global state inspector
const stateInspector = new StateInspector();

// Expose to global for debugging
if (process.env.NODE_ENV === 'development') {
  window.__STATE_INSPECTOR__ = stateInspector;
}
```

## Framework Integration

### React Integration

```typescript
// React hooks for state management
import { useState, useEffect } from 'react';
import { GlobalState } from '@micro-core/core';

// Custom hook for global state
function useGlobalState<T>(path: string, defaultValue?: T): [T, (value: T) => void] {
  const [state, setState] = useState<T>(() => 
    GlobalState.get(path) ?? defaultValue
  );

  useEffect(() => {
    const unwatch = GlobalState.watch(path, (newValue) => {
      setState(newValue);
    });

    return unwatch;
  }, [path]);

  const setGlobalState = (value: T) => {
    GlobalState.set(path, value);
  };

  return [state, setGlobalState];
}

// Usage in React component
function UserProfile() {
  const [user, setUser] = useGlobalState('user', null);
  const [theme, setTheme] = useGlobalState('app.settings.theme', 'light');

  return (
    <div className={`profile theme-${theme}`}>
      <h1>Welcome, {user?.name}</h1>
      <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>
        Toggle Theme
      </button>
    </div>
  );
}
```

### Vue Integration

```typescript
// Vue composition API for state management
import { ref, watch, onUnmounted } from 'vue';
import { GlobalState } from '@micro-core/core';

// Composable for global state
function useGlobalState<T>(path: string, defaultValue?: T) {
  const state = ref<T>(GlobalState.get(path) ?? defaultValue);
  
  const unwatch = GlobalState.watch(path, (newValue) => {
    state.value = newValue;
  });

  const setGlobalState = (value: T) => {
    GlobalState.set(path, value);
  };

  onUnmounted(() => {
    unwatch();
  });

  return {
    state: readonly(state),
    setState: setGlobalState
  };
}

// Usage in Vue component
export default {
  setup() {
    const { state: user, setState: setUser } = useGlobalState('user');
    const { state: theme, setState: setTheme } = useGlobalState('app.settings.theme', 'light');

    const toggleTheme = () => {
      setTheme(theme.value === 'light' ? 'dark' : 'light');
    };

    return {
      user,
      theme,
      toggleTheme
    };
  }
};
```

## Best Practices

### 1. State Structure Design

```typescript
// Recommended state structure
const recommendedStateStructure = {
  // User-related state
  user: {
    profile: {
      id: 'string',
      name: 'string',
      email: 'string',
      avatar: 'string'
    },
    preferences: {
      theme: 'light' | 'dark',
      language: 'string',
      notifications: 'boolean'
    },
    permissions: ['string']
  },
  
  // Application-wide settings
  app: {
    settings: {
      theme: 'light' | 'dark',
      language: 'string',
      timezone: 'string'
    },
    ui: {
      sidebarCollapsed: 'boolean',
      activeTab: 'string'
    }
  },
  
  // Feature-specific state
  features: {
    dashboard: {
      widgets: ['object'],
      layout: 'string'
    },
    notifications: {
      unreadCount: 'number',
      items: ['object']
    }
  }
};
```

### 2. State Naming Conventions

```typescript
// Good naming conventions
GlobalState.set('user.profile.firstName', 'John');
GlobalState.set('app.settings.theme', 'dark');
GlobalState.set('features.dashboard.activeWidget', 'analytics');

// Avoid these patterns
GlobalState.set('userData', {}); // Too generic
GlobalState.set('temp123', {}); // Meaningless names
GlobalState.set('THEME', 'dark'); // All caps
```

### 3. Performance Optimization

```typescript
// Batch state updates
GlobalState.batch(() => {
  GlobalState.set('user.profile.name', 'John Doe');
  GlobalState.set('user.profile.email', '<EMAIL>');
  GlobalState.set('user.profile.avatar', 'avatar.jpg');
});

// Use computed state for derived values
const computedState = GlobalState.computed('user.fullName', () => {
  const profile = GlobalState.get('user.profile');
  return `${profile.firstName} ${profile.lastName}`;
});

// Debounce frequent updates
const debouncedUpdate = debounce((value) => {
  GlobalState.set('search.query', value);
}, 300);
```

## Common Issues and Solutions

### Q: How to handle state conflicts between applications?

A: Implement proper conflict resolution strategies:

```typescript
// Use namespaced state for application-specific data
GlobalState.set('apps.userApp.localState', data);
GlobalState.set('apps.dashboardApp.localState', data);

// Use conflict resolution for shared state
const conflictResolver = new StateConflictResolver();
conflictResolver.registerStrategy('user.profile', 'merge');
conflictResolver.registerStrategy('app.settings', 'lastWrite');
```

### Q: How to optimize state performance for large datasets?

A: Use pagination and lazy loading:

```typescript
// Implement paginated state
class PaginatedState {
  private pageSize = 50;
  private cache = new Map();

  async getPage(path: string, page: number) {
    const cacheKey = `${path}:${page}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    const data = await this.loadPage(path, page);
    this.cache.set(cacheKey, data);
    
    return data;
  }
  
  private async loadPage(path: string, page: number) {
    // Load data from API or other source
    return await api.getData(path, page, this.pageSize);
  }
}
```

### Q: How to debug state synchronization issues?

A: Use comprehensive debugging tools:

```typescript
// Enable state debugging
if (process.env.NODE_ENV === 'development') {
  GlobalState.enableDebug({
    logStateChanges: true,
    logSyncEvents: true,
    trackPerformance: true
  });
  
  // Add state inspector
  const inspector = new StateInspector();
  inspector.addFilter('user');
  inspector.addFilter('app.settings');
  
  window.__DEBUG_STATE__ = {
    inspector,
    exportState: () => inspector.exportState(),
    importState: (data) => inspector.importState(data)
  };
}
```

## Summary

State management is crucial for micro-frontend applications. Through proper global state management, synchronization, validation, and debugging tools, you can build maintainable and scalable state systems.

Key points:
- Use structured and namespaced state organization
- Implement proper state synchronization between applications
- Add validation and middleware for robust state management
- Use debugging tools to troubleshoot state issues
- Follow performance best practices for large-scale applications

## Next Steps

- [Application Communication](./communication.md) - Learn inter-application communication
- [Routing System](./routing.md) - Understand routing integration with state
- [Performance Optimization](../best-practices/performance.md) - Optimize state performance
- [Testing Strategies](../best-practices/testing.md) - Test state management systems
