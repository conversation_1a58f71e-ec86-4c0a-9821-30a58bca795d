# Sandbox Isolation

Micro-Core provides multiple sandbox isolation strategies to ensure complete isolation between micro-applications, preventing global variable pollution, style conflicts, and script interference.

## Sandbox Types Overview

### 1. Proxy Sandbox (Recommended)

High-performance sandbox based on ES6 Proxy, providing complete JavaScript isolation.

```typescript
import { ProxySandboxPlugin } from '@micro-core/plugin-sandbox-proxy';

kernel.use(ProxySandboxPlugin, {
  strict: true,                    // Strict mode
  whitelist: ['console', 'fetch'], // Whitelist global variables
  blacklist: ['eval'],             // Blacklist global variables
  enablePerformanceMonitor: true   // Enable performance monitoring
});

kernel.registerApplication({
  name: 'app1',
  entry: 'http://localhost:3001',
  container: '#app1',
  activeWhen: '/app1',
  sandbox: 'proxy' // Use Proxy sandbox
});
```

### 2. Iframe Sandbox

Strong isolation sandbox based on iframe, providing the highest level of isolation.

```typescript
import { IframeSandboxPlugin } from '@micro-core/plugin-sandbox-iframe';

kernel.use(IframeSandboxPlugin, {
  allowSameOrigin: false,          // Allow same origin
  allowScripts: true,              // Allow script execution
  allowForms: true,                // Allow form submission
  allowPopups: false,              // Allow popups
  communicationProtocol: 'postMessage' // Communication protocol
});

kernel.registerApplication({
  name: 'app2',
  entry: 'http://localhost:3002',
  container: '#app2',
  activeWhen: '/app2',
  sandbox: 'iframe' // Use Iframe sandbox
});
```

### 3. WebComponent Sandbox

Style isolation sandbox based on Web Components.

```typescript
import { WebComponentSandboxPlugin } from '@micro-core/plugin-sandbox-webcomponent';

kernel.use(WebComponentSandboxPlugin, {
  shadowDOM: true,                 // Enable Shadow DOM
  styleIsolation: true,            // Style isolation
  customElements: true,            // Support custom elements
  slotSupport: true                // Support slots
});

kernel.registerApplication({
  name: 'app3',
  entry: 'http://localhost:3003',
  container: '#app3',
  activeWhen: '/app3',
  sandbox: 'webcomponent' // Use WebComponent sandbox
});
```

### 4. DefineProperty Sandbox

Compatibility sandbox based on Object.defineProperty.

```typescript
import { DefinePropertySandboxPlugin } from '@micro-core/plugin-sandbox-defineproperty';

kernel.use(DefinePropertySandboxPlugin, {
  proxyWindow: true,               // Proxy window object
  proxyDocument: false,            // Don't proxy document object
  restoreGlobals: true,            // Restore globals on unmount
  trackChanges: true               // Track changes
});

kernel.registerApplication({
  name: 'app4',
  entry: 'http://localhost:3004',
  container: '#app4',
  activeWhen: '/app4',
  sandbox: 'defineProperty' // Use DefineProperty sandbox
});
```

### 5. Namespace Sandbox

Lightweight namespace isolation sandbox.

```typescript
import { NamespaceSandboxPlugin } from '@micro-core/plugin-sandbox-namespace';

kernel.use(NamespaceSandboxPlugin, {
  namespace: 'MicroApps',          // Namespace name
  autoCleanup: true,               // Auto cleanup
  conflictResolution: 'warn'       // Conflict resolution strategy
});

kernel.registerApplication({
  name: 'app5',
  entry: 'http://localhost:3005',
  container: '#app5',
  activeWhen: '/app5',
  sandbox: 'namespace' // Use namespace sandbox
});
```

### 6. Federated Component Sandbox

Component-level sandbox supporting module federation.

```typescript
import { FederatedSandboxPlugin } from '@micro-core/plugin-sandbox-federated';

kernel.use(FederatedSandboxPlugin, {
  moduleMap: {                     // Module mapping
    'shared-components': 'http://localhost:4000/remoteEntry.js',
    'shared-utils': 'http://localhost:4001/remoteEntry.js'
  },
  sharedDependencies: ['react', 'react-dom'], // Shared dependencies
  isolationLevel: 'component'      // Isolation level
});

kernel.registerApplication({
  name: 'app6',
  entry: 'http://localhost:3006',
  container: '#app6',
  activeWhen: '/app6',
  sandbox: 'federated' // Use federated component sandbox
});
```

## Proxy Sandbox Deep Dive

### Basic Principles

Proxy sandbox creates a proxy of the window object, intercepting all property access and modification operations.

```typescript
class ProxySandbox {
  private proxyWindow: Window;
  private fakeWindow: Record<string, any> = {};
  private addedPropsMap = new Map<string, any>();
  private modifiedPropsMap = new Map<string, any>();

  constructor(name: string, options: ProxySandboxOptions) {
    this.proxyWindow = new Proxy(window, {
      get: (target, prop) => {
        // Get from sandbox environment first
        if (this.fakeWindow.hasOwnProperty(prop)) {
          return this.fakeWindow[prop];
        }
        
        // Get from original window
        const value = target[prop];
        
        // Bind function's this context
        if (typeof value === 'function' && !value.bind) {
          return value.bind(target);
        }
        
        return value;
      },
      
      set: (target, prop, value) => {
        // Record added properties
        if (!target.hasOwnProperty(prop)) {
          this.addedPropsMap.set(prop, value);
        } else {
          // Record modified properties
          if (!this.modifiedPropsMap.has(prop)) {
            this.modifiedPropsMap.set(prop, target[prop]);
          }
        }
        
        // Set to sandbox environment
        this.fakeWindow[prop] = value;
        return true;
      },
      
      has: (target, prop) => {
        return this.fakeWindow.hasOwnProperty(prop) || target.hasOwnProperty(prop);
      },
      
      deleteProperty: (target, prop) => {
        if (this.fakeWindow.hasOwnProperty(prop)) {
          delete this.fakeWindow[prop];
          return true;
        }
        return false;
      }
    });
  }

  activate() {
    // Activate sandbox
    this.active = true;
  }

  deactivate() {
    // Deactivate sandbox, restore global environment
    this.active = false;
    
    // Clean up added properties
    this.addedPropsMap.forEach((_, prop) => {
      delete window[prop];
    });
    
    // Restore modified properties
    this.modifiedPropsMap.forEach((originalValue, prop) => {
      window[prop] = originalValue;
    });
  }

  getProxy() {
    return this.proxyWindow;
  }
}
```

### Advanced Configuration

```typescript
kernel.use(ProxySandboxPlugin, {
  // Strict mode configuration
  strict: true,
  
  // Whitelist: allowed global variables
  whitelist: [
    'console',
    'fetch',
    'setTimeout',
    'setInterval',
    'clearTimeout',
    'clearInterval',
    'Promise',
    'URL',
    'URLSearchParams'
  ],
  
  // Blacklist: forbidden global variables
  blacklist: [
    'eval',
    'Function',
    'WebAssembly'
  ],
  
  // Custom property interceptors
  interceptors: {
    get: (target, prop, receiver, sandbox) => {
      // Custom get logic
      if (prop === 'location') {
        return createVirtualLocation(sandbox.name);
      }
      return Reflect.get(target, prop, receiver);
    },
    
    set: (target, prop, value, receiver, sandbox) => {
      // Custom set logic
      if (prop === 'document') {
        console.warn(`Application ${sandbox.name} attempted to modify document object`);
        return false;
      }
      return Reflect.set(target, prop, value, receiver);
    }
  },
  
  // Performance monitoring
  enablePerformanceMonitor: true,
  performanceThreshold: 10, // Operations over 10ms will be logged
  
  // Error handling
  onError: (error, sandbox) => {
    console.error(`Sandbox ${sandbox.name} error:`, error);
  }
});
```

## Iframe Sandbox Deep Dive

### Basic Implementation

```typescript
class IframeSandbox {
  private iframe: HTMLIFrameElement;
  private iframeWindow: Window;
  private messageHandlers = new Map();

  constructor(name: string, options: IframeSandboxOptions) {
    this.iframe = document.createElement('iframe');
    this.iframe.src = 'about:blank';
    this.iframe.style.display = 'none';
    
    // Set sandbox attributes
    const sandboxFlags = [];
    if (options.allowScripts) sandboxFlags.push('allow-scripts');
    if (options.allowSameOrigin) sandboxFlags.push('allow-same-origin');
    if (options.allowForms) sandboxFlags.push('allow-forms');
    if (options.allowPopups) sandboxFlags.push('allow-popups');
    
    this.iframe.sandbox = sandboxFlags.join(' ');
    
    document.body.appendChild(this.iframe);
    this.iframeWindow = this.iframe.contentWindow!;
    
    // Setup communication mechanism
    this.setupCommunication();
  }

  private setupCommunication() {
    // Listen to messages from iframe
    window.addEventListener('message', (event) => {
      if (event.source === this.iframeWindow) {
        this.handleMessage(event.data);
      }
    });
  }

  private handleMessage(data: any) {
    const { type, payload, id } = data;
    
    switch (type) {
      case 'API_CALL':
        this.handleApiCall(payload, id);
        break;
      case 'DOM_OPERATION':
        this.handleDomOperation(payload, id);
        break;
      case 'EVENT_EMIT':
        this.handleEventEmit(payload);
        break;
    }
  }

  private handleApiCall(payload: any, id: string) {
    // Handle API calls
    const { method, args } = payload;
    
    try {
      const result = this.executeApiCall(method, args);
      this.postMessage({
        type: 'API_RESPONSE',
        payload: result,
        id
      });
    } catch (error) {
      this.postMessage({
        type: 'API_ERROR',
        payload: error.message,
        id
      });
    }
  }

  private postMessage(data: any) {
    this.iframeWindow.postMessage(data, '*');
  }

  activate() {
    this.iframe.style.display = 'block';
  }

  deactivate() {
    this.iframe.style.display = 'none';
  }

  destroy() {
    if (this.iframe.parentNode) {
      this.iframe.parentNode.removeChild(this.iframe);
    }
  }
}
```

### Communication Bridge

```typescript
// Communication bridge code inside iframe
class IframeBridge {
  private messageId = 0;
  private pendingCalls = new Map();

  constructor() {
    this.setupMessageListener();
    this.injectApis();
  }

  private setupMessageListener() {
    window.addEventListener('message', (event) => {
      const { type, payload, id } = event.data;
      
      switch (type) {
        case 'API_RESPONSE':
          this.resolveApiCall(id, payload);
          break;
        case 'API_ERROR':
          this.rejectApiCall(id, payload);
          break;
      }
    });
  }

  private injectApis() {
    // Inject main application APIs
    window.parent.fetch = this.createApiProxy('fetch');
    window.parent.localStorage = this.createStorageProxy('localStorage');
    window.parent.sessionStorage = this.createStorageProxy('sessionStorage');
  }

  private createApiProxy(apiName: string) {
    return (...args: any[]) => {
      return new Promise((resolve, reject) => {
        const id = `${apiName}_${++this.messageId}`;
        
        this.pendingCalls.set(id, { resolve, reject });
        
        parent.postMessage({
          type: 'API_CALL',
          payload: { method: apiName, args },
          id
        }, '*');
      });
    };
  }

  private resolveApiCall(id: string, result: any) {
    const call = this.pendingCalls.get(id);
    if (call) {
      call.resolve(result);
      this.pendingCalls.delete(id);
    }
  }

  private rejectApiCall(id: string, error: any) {
    const call = this.pendingCalls.get(id);
    if (call) {
      call.reject(new Error(error));
      this.pendingCalls.delete(id);
    }
  }
}

// Initialize bridge in iframe
new IframeBridge();
```

## WebComponent Sandbox Deep Dive

### Shadow DOM Isolation

```typescript
class WebComponentSandbox {
  private customElement: HTMLElement;
  private shadowRoot: ShadowRoot;

  constructor(name: string, container: HTMLElement, options: WebComponentSandboxOptions) {
    // Create custom element
    this.customElement = document.createElement(`micro-app-${name}`);
    
    // Create Shadow DOM
    this.shadowRoot = this.customElement.attachShadow({ 
      mode: options.shadowDOM ? 'closed' : 'open' 
    });
    
    // Setup style isolation
    if (options.styleIsolation) {
      this.setupStyleIsolation();
    }
    
    // Mount to container
    container.appendChild(this.customElement);
  }

  private setupStyleIsolation() {
    // Create style isolation
    const style = document.createElement('style');
    style.textContent = `
      :host {
        display: block;
        width: 100%;
        height: 100%;
        contain: layout style paint;
      }
      
      /* Reset styles to prevent external style interference */
      * {
        box-sizing: border-box;
      }
    `;
    
    this.shadowRoot.appendChild(style);
  }

  loadApplication(html: string, css: string, js: string) {
    // Load CSS
    if (css) {
      const style = document.createElement('style');
      style.textContent = css;
      this.shadowRoot.appendChild(style);
    }
    
    // Load HTML
    if (html) {
      const container = document.createElement('div');
      container.innerHTML = html;
      this.shadowRoot.appendChild(container);
    }
    
    // Load JavaScript
    if (js) {
      this.executeScript(js);
    }
  }

  private executeScript(js: string) {
    // Execute script in isolated context
    const script = document.createElement('script');
    script.textContent = `
      (function() {
        // Create isolated execution environment
        const isolatedWindow = {
          document: this.shadowRoot,
          console: window.console,
          setTimeout: window.setTimeout.bind(window),
          setInterval: window.setInterval.bind(window),
          fetch: window.fetch.bind(window)
        };
        
        // Execute code in isolated environment
        with (isolatedWindow) {
          ${js}
        }
      }).call(this);
    `;
    
    this.shadowRoot.appendChild(script);
  }

  destroy() {
    if (this.customElement.parentNode) {
      this.customElement.parentNode.removeChild(this.customElement);
    }
  }
}
```

## Sandbox Performance Optimization

### 1. Lazy Sandbox Loading

```typescript
class LazySandboxManager {
  private sandboxCache = new Map();
  private activeSandboxes = new Set();

  async getSandbox(name: string, type: string, options: any) {
    const cacheKey = `${name}-${type}`;
    
    // Check cache
    if (this.sandboxCache.has(cacheKey)) {
      const sandbox = this.sandboxCache.get(cacheKey);
      this.activeSandboxes.add(sandbox);
      return sandbox;
    }
    
    // Dynamically load sandbox implementation
    const SandboxClass = await this.loadSandboxClass(type);
    const sandbox = new SandboxClass(name, options);
    
    // Cache sandbox instance
    this.sandboxCache.set(cacheKey, sandbox);
    this.activeSandboxes.add(sandbox);
    
    return sandbox;
  }

  private async loadSandboxClass(type: string) {
    switch (type) {
      case 'proxy':
        return (await import('@micro-core/plugin-sandbox-proxy')).ProxySandbox;
      case 'iframe':
        return (await import('@micro-core/plugin-sandbox-iframe')).IframeSandbox;
      case 'webcomponent':
        return (await import('@micro-core/plugin-sandbox-webcomponent')).WebComponentSandbox;
      default:
        throw new Error(`Unknown sandbox type: ${type}`);
    }
  }

  releaseSandbox(sandbox: any) {
    this.activeSandboxes.delete(sandbox);
    
    // Consider cleaning cache if no active sandbox instances
    if (this.activeSandboxes.size === 0) {
      this.cleanupInactiveSandboxes();
    }
  }

  private cleanupInactiveSandboxes() {
    // Clean up inactive sandbox instances
    setTimeout(() => {
      if (this.activeSandboxes.size === 0) {
        this.sandboxCache.clear();
      }
    }, 30000); // Clean up after 30 seconds
  }
}
```

### 2. Sandbox Pool Management

```typescript
class SandboxPool {
  private pools = new Map();
  private maxPoolSize = 5;

  getFromPool(type: string, options: any) {
    const pool = this.pools.get(type) || [];
    
    if (pool.length > 0) {
      const sandbox = pool.pop();
      sandbox.reset(options);
      return sandbox;
    }
    
    return null;
  }

  returnToPool(type: string, sandbox: any) {
    const pool = this.pools.get(type) || [];
    
    if (pool.length < this.maxPoolSize) {
      sandbox.cleanup();
      pool.push(sandbox);
      this.pools.set(type, pool);
    } else {
      sandbox.destroy();
    }
  }

  warmupPool(type: string, count: number, options: any) {
    const pool = [];
    
    for (let i = 0; i < count; i++) {
      const sandbox = this.createSandbox(type, options);
      pool.push(sandbox);
    }
    
    this.pools.set(type, pool);
  }

  private createSandbox(type: string, options: any) {
    // Factory method for creating sandbox instances
    switch (type) {
      case 'proxy':
        return new ProxySandbox('pool-instance', options);
      case 'iframe':
        return new IframeSandbox('pool-instance', options);
      default:
        throw new Error(`Unsupported sandbox type: ${type}`);
    }
  }
}
```

## Sandbox Debugging Tools

### Sandbox Monitoring Panel

```typescript
class SandboxMonitor {
  private sandboxes = new Map();
  private performanceData = new Map();

  registerSandbox(name: string, sandbox: any) {
    this.sandboxes.set(name, {
      sandbox,
      createdAt: Date.now(),
      activations: 0,
      errors: [],
      performance: {
        activationTime: [],
        deactivationTime: [],
        memoryUsage: []
      }
    });
  }

  recordActivation(name: string) {
    const data = this.sandboxes.get(name);
    if (data) {
      const startTime = performance.now();
      data.activations++;
      
      return () => {
        const endTime = performance.now();
        data.performance.activationTime.push(endTime - startTime);
      };
    }
  }

  recordError(name: string, error: Error) {
    const data = this.sandboxes.get(name);
    if (data) {
      data.errors.push({
        error: error.message,
        stack: error.stack,
        timestamp: Date.now()
      });
    }
  }

  getMonitorData(name?: string) {
    if (name) {
      return this.sandboxes.get(name);
    }
    
    return Array.from(this.sandboxes.entries()).map(([name, data]) => ({
      name,
      ...data
    }));
  }

  generateReport() {
    const report = {
      totalSandboxes: this.sandboxes.size,
      activeSandboxes: 0,
      totalErrors: 0,
      averageActivationTime: 0,
      memoryUsage: 0
    };

    let totalActivationTime = 0;
    let activationCount = 0;

    this.sandboxes.forEach((data, name) => {
      if (data.sandbox.active) {
        report.activeSandboxes++;
      }
      
      report.totalErrors += data.errors.length;
      
      data.performance.activationTime.forEach(time => {
        totalActivationTime += time;
        activationCount++;
      });
    });

    if (activationCount > 0) {
      report.averageActivationTime = totalActivationTime / activationCount;
    }

    return report;
  }
}

// Global monitoring instance
const sandboxMonitor = new SandboxMonitor();

// Expose to global in development environment
if (process.env.NODE_ENV === 'development') {
  window.__SANDBOX_MONITOR__ = sandboxMonitor;
}
```

## Best Practices

### 1. Sandbox Selection Guide

```typescript
// Select appropriate sandbox based on application characteristics
function selectSandbox(appConfig: ApplicationConfig) {
  const { 
    trustLevel,      // Trust level
    performanceReq,  // Performance requirements
    isolationLevel,  // Isolation level
    compatibility    // Compatibility requirements
  } = appConfig;

  if (isolationLevel === 'maximum') {
    return 'iframe'; // Maximum isolation level
  }
  
  if (performanceReq === 'high' && compatibility === 'modern') {
    return 'proxy'; // High performance modern browsers
  }
  
  if (appConfig.hasStyleConflicts) {
    return 'webcomponent'; // Serious style conflicts
  }
  
  if (compatibility === 'legacy') {
    return 'defineProperty'; // Compatible with legacy browsers
  }
  
  return 'proxy'; // Default choice
}
```

### 2. Sandbox Configuration Optimization

```typescript
// Production environment sandbox configuration
const productionSandboxConfig = {
  proxy: {
    strict: true,
    whitelist: [
      'console', 'fetch', 'setTimeout', 'setInterval',
      'clearTimeout', 'clearInterval', 'Promise'
    ],
    blacklist: ['eval', 'Function'],
    enablePerformanceMonitor: false
  },
  
  iframe: {
    allowSameOrigin: false,
    allowScripts: true,
    allowForms: true,
    allowPopups: false
  },
  
  webcomponent: {
    shadowDOM: true,
    styleIsolation: true,
    customElements: true
  }
};

// Development environment sandbox configuration
const developmentSandboxConfig = {
  proxy: {
    strict: false,
    enablePerformanceMonitor: true,
    onError: (error, sandbox) => {
      console.error(`Sandbox error [${sandbox.name}]:`, error);
    }
  }
};
```

### 3. Error Handling and Recovery

```typescript
// Sandbox error handling strategy
class SandboxErrorHandler {
  private retryCount = new Map();
  private maxRetries = 3;

  handleSandboxError(error: Error, sandbox: any, app: any) {
    const retries = this.retryCount.get(app.name) || 0;
    
    console.error(`Sandbox error [${app.name}]:`, error);
    
    if (retries < this.maxRetries) {
      // Try to recreate sandbox
      this.recreateSandbox(sandbox, app);
      this.retryCount.set(app.name, retries + 1);
    } else {
      // Fallback to simpler sandbox
      this.fallbackToSimpleSandbox(app);
      this.retryCount.delete(app.name);
    }
  }

  private async recreateSandbox(sandbox: any, app: any) {
    try {
      // Destroy current sandbox
      sandbox.destroy();
      
      // Create new sandbox
      const newSandbox = await this.createSandbox(app.sandboxType, app.sandboxOptions);
      
      // Replace sandbox instance
      app.sandbox = newSandbox;
      
      console.log(`Sandbox [${app.name}] recreated successfully`);
    } catch (error) {
      console.error(`Sandbox [${app.name}] recreation failed:`, error);
      throw error;
    }
  }

  private async fallbackToSimpleSandbox(app: any) {
    console.warn(`Sandbox [${app.name}] falling back to namespace sandbox`);
    
    // Fallback to simplest namespace sandbox
    const fallbackSandbox = await this.createSandbox('namespace', {
      namespace: `fallback_${app.name}`
    });
    
    app.sandbox = fallbackSandbox;
    app.sandboxType = 'namespace';
  }
}
```

## Summary

Sandbox isolation is a core feature of micro-frontend architecture. Through proper sandbox selection, configuration optimization, and error handling, you can build secure and stable micro-frontend applications.

Key points:
- Choose appropriate sandbox type based on application requirements
- Configure sandbox parameters for optimal performance and security
- Implement comprehensive error handling and recovery mechanisms
- Monitor sandbox performance and resource usage
- Follow best practices for sandbox management

## Next Steps

- [Application Communication](./communication.md) - Learn inter-application communication
- [State Management](./state-management.md) - Understand global state management
- [Performance Optimization](../best-practices/performance.md) - Optimize sandbox performance
- [Security Best Practices](../best-practices/security.md) - Enhance application security
