# Routing System

Micro-Core provides a unified routing system that coordinates routing between the main application and micro-applications, supporting dynamic routing, nested routing, route guards, and route caching.

## Routing Architecture

### Unified Routing Management

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core Routing Architecture               │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   Main Router   │    │  Route Manager  │    │  Micro Routers  ││
│  │                 │    │                 │    │                 ││
│  │ • Global routes │    │ • Route coord   │    │ • App routes    ││
│  │ • Route guards  │    │ • State sync    │    │ • Local nav     ││
│  │ • Navigation    │    │ • Cache mgmt    │    │ • Route params  ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│           │                       │                       │       │
│           └───────────────────────┼───────────────────────┘       │
│                                   │                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Route Flow                               │ │
│  │  URL Change → Route Match → App Load → Route Sync          │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Basic Routing Configuration

### Main Application Router Setup

```typescript
import { MicroCore, RouterPlugin } from '@micro-core/core'
import { createBrowserRouter } from '@micro-core/plugin-router'

const microCore = new MicroCore()

// Install router plugin
microCore.use(RouterPlugin, {
  mode: 'browser',           // 'browser' | 'hash' | 'memory'
  base: '/',                 // Base path
  strict: true,              // Strict mode
  sensitive: false,          // Case sensitive
  fallback: '/404'           // Fallback route
})

// Register applications with routing
microCore.registerApp({
  name: 'user-management',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeWhen: ['/users', '/users/*'],  // Route matching rules
  props: {
    basePath: '/users'
  }
})

microCore.registerApp({
  name: 'order-system',
  entry: 'http://localhost:3002',
  container: '#order-app',
  activeWhen: (location) => {
    return location.pathname.startsWith('/orders') && 
           location.search.includes('module=orders')
  }
})
```

### Route Matching Rules

```typescript
// 1. String matching
activeWhen: '/dashboard'

// 2. Array matching (multiple paths)
activeWhen: ['/users', '/profile', '/settings']

// 3. Wildcard matching
activeWhen: '/admin/*'

// 4. Regular expression matching
activeWhen: /^\/products\/\d+$/

// 5. Function matching (custom logic)
activeWhen: (location) => {
  const { pathname, search, hash } = location
  return pathname.startsWith('/shop') && 
         search.includes('category=electronics') &&
         !hash.includes('preview')
}

// 6. Complex matching configuration
activeWhen: {
  path: '/dashboard/*',
  exact: false,
  strict: true,
  sensitive: false,
  conditions: {
    userRole: 'admin',
    feature: 'dashboard-v2'
  }
}
```

## Dynamic Routing

### Route Registration and Management

```typescript
class DynamicRouteManager {
  private microCore: MicroCore
  private routes = new Map()

  constructor(microCore: MicroCore) {
    this.microCore = microCore
  }

  // Add route dynamically
  async addRoute(routeConfig: RouteConfig) {
    const { path, appName, component, meta } = routeConfig

    // Register micro-application if not exists
    if (!this.microCore.getApp(appName)) {
      await this.microCore.registerApp({
        name: appName,
        entry: meta.entry,
        container: meta.container,
        activeWhen: path
      })
    }

    // Add route to router
    this.microCore.router.addRoute({
      path,
      name: `${appName}-${path}`,
      component,
      meta: {
        ...meta,
        appName,
        requiresAuth: meta.requiresAuth || false
      }
    })

    this.routes.set(path, routeConfig)
  }

  // Remove route dynamically
  removeRoute(path: string) {
    const routeConfig = this.routes.get(path)
    if (routeConfig) {
      this.microCore.router.removeRoute(`${routeConfig.appName}-${path}`)
      this.routes.delete(path)
    }
  }

  // Update route
  async updateRoute(path: string, updates: Partial<RouteConfig>) {
    const existingRoute = this.routes.get(path)
    if (existingRoute) {
      const updatedRoute = { ...existingRoute, ...updates }
      this.removeRoute(path)
      await this.addRoute(updatedRoute)
    }
  }

  // Get all dynamic routes
  getRoutes() {
    return Array.from(this.routes.values())
  }
}

// Usage example
const routeManager = new DynamicRouteManager(microCore)

// Add route from API
async function loadRoutesFromAPI() {
  const routes = await fetch('/api/routes').then(res => res.json())
  
  for (const route of routes) {
    await routeManager.addRoute({
      path: route.path,
      appName: route.appName,
      component: route.component,
      meta: {
        entry: route.entry,
        container: route.container,
        title: route.title,
        requiresAuth: route.requiresAuth
      }
    })
  }
}
```

### Nested Routing

```typescript
// Main application nested routes
const nestedRoutes = [
  {
    path: '/admin',
    component: AdminLayout,
    children: [
      {
        path: 'users',
        appName: 'user-management',
        entry: 'http://localhost:3001',
        container: '#admin-content'
      },
      {
        path: 'orders',
        appName: 'order-system',
        entry: 'http://localhost:3002',
        container: '#admin-content'
      },
      {
        path: 'analytics',
        appName: 'analytics-dashboard',
        entry: 'http://localhost:3003',
        container: '#admin-content'
      }
    ]
  }
]

// Register nested routes
microCore.router.addRoutes(nestedRoutes)
```

## Route Guards

### Global Route Guards

```typescript
// Global before guard
microCore.router.beforeEach(async (to, from, next) => {
  console.log(`Navigating from ${from.path} to ${to.path}`)

  // Authentication check
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login')
    return
  }

  // Permission check
  if (to.meta.requiredRole && !hasRole(to.meta.requiredRole)) {
    next('/unauthorized')
    return
  }

  // Load application if needed
  if (to.meta.appName) {
    const app = microCore.getApp(to.meta.appName)
    if (app && app.status === 'NOT_LOADED') {
      try {
        await microCore.loadApp(to.meta.appName)
      } catch (error) {
        console.error(`Failed to load app ${to.meta.appName}:`, error)
        next('/error')
        return
      }
    }
  }

  next()
})

// Global after guard
microCore.router.afterEach((to, from) => {
  // Update page title
  if (to.meta.title) {
    document.title = to.meta.title
  }

  // Analytics tracking
  analytics.track('page_view', {
    path: to.path,
    appName: to.meta.appName,
    timestamp: Date.now()
  })

  // Update breadcrumbs
  updateBreadcrumbs(to)
})
```

### Application-Level Route Guards

```typescript
// Application-specific route guards
microCore.registerApp({
  name: 'user-management',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeWhen: '/users/*',
  
  // Application route guards
  beforeEnter: async (to, from, next) => {
    // Check user management permissions
    const hasPermission = await checkUserManagementPermission()
    if (!hasPermission) {
      next('/access-denied')
      return
    }

    // Preload user data
    try {
      await preloadUserData()
      next()
    } catch (error) {
      console.error('Failed to preload user data:', error)
      next()
    }
  },

  beforeLeave: (to, from, next) => {
    // Check for unsaved changes
    if (hasUnsavedChanges()) {
      const shouldLeave = confirm('You have unsaved changes. Are you sure you want to leave?')
      if (!shouldLeave) {
        next(false)
        return
      }
    }

    // Cleanup resources
    cleanupUserManagementResources()
    next()
  }
})
```

## Route State Management

### Route State Synchronization

```typescript
class RouteStateManager {
  private currentRoute: RouteInfo | null = null
  private routeHistory: RouteInfo[] = []
  private maxHistorySize = 50

  constructor(private microCore: MicroCore) {
    this.setupRouteListener()
  }

  private setupRouteListener() {
    this.microCore.router.afterEach((to, from) => {
      this.updateCurrentRoute(to)
      this.addToHistory(to)
      this.syncRouteState(to)
    })
  }

  private updateCurrentRoute(route: RouteInfo) {
    this.currentRoute = {
      path: route.path,
      name: route.name,
      params: route.params,
      query: route.query,
      meta: route.meta,
      timestamp: Date.now()
    }
  }

  private addToHistory(route: RouteInfo) {
    this.routeHistory.push(this.currentRoute!)
    
    // Limit history size
    if (this.routeHistory.length > this.maxHistorySize) {
      this.routeHistory.shift()
    }
  }

  private syncRouteState(route: RouteInfo) {
    // Sync route state to all applications
    EventBus.emit('route:changed', {
      current: route,
      previous: this.routeHistory[this.routeHistory.length - 2]
    })

    // Update global state
    GlobalState.set('route.current', route)
    GlobalState.set('route.history', this.routeHistory)
  }

  // Get current route
  getCurrentRoute(): RouteInfo | null {
    return this.currentRoute
  }

  // Get route history
  getRouteHistory(): RouteInfo[] {
    return [...this.routeHistory]
  }

  // Navigate programmatically
  async navigate(path: string, options?: NavigationOptions) {
    try {
      await this.microCore.router.push(path, options)
    } catch (error) {
      console.error('Navigation failed:', error)
      throw error
    }
  }

  // Go back
  goBack(steps: number = 1) {
    this.microCore.router.go(-steps)
  }

  // Go forward
  goForward(steps: number = 1) {
    this.microCore.router.go(steps)
  }
}

// Initialize route state manager
const routeStateManager = new RouteStateManager(microCore)
```

### Route Caching

```typescript
class RouteCacheManager {
  private cache = new Map()
  private cacheConfig = new Map()

  constructor(private microCore: MicroCore) {
    this.setupCacheInterceptor()
  }

  private setupCacheInterceptor() {
    this.microCore.router.beforeEach((to, from, next) => {
      // Check if route should be cached
      if (this.shouldCache(to)) {
        this.cacheRoute(to)
      }

      // Restore cached route if available
      if (this.hasCache(to)) {
        this.restoreRoute(to)
      }

      next()
    })
  }

  // Configure route caching
  setCacheConfig(path: string, config: RouteCacheConfig) {
    this.cacheConfig.set(path, {
      enabled: true,
      maxAge: 5 * 60 * 1000, // 5 minutes
      strategy: 'memory',     // 'memory' | 'sessionStorage' | 'localStorage'
      ...config
    })
  }

  private shouldCache(route: RouteInfo): boolean {
    const config = this.cacheConfig.get(route.path)
    return config?.enabled || route.meta?.cache === true
  }

  private cacheRoute(route: RouteInfo) {
    const config = this.cacheConfig.get(route.path)
    const cacheKey = this.generateCacheKey(route)
    
    const cacheData = {
      route,
      timestamp: Date.now(),
      data: this.extractRouteData(route)
    }

    switch (config?.strategy) {
      case 'sessionStorage':
        sessionStorage.setItem(cacheKey, JSON.stringify(cacheData))
        break
      case 'localStorage':
        localStorage.setItem(cacheKey, JSON.stringify(cacheData))
        break
      default:
        this.cache.set(cacheKey, cacheData)
    }
  }

  private hasCache(route: RouteInfo): boolean {
    const cacheKey = this.generateCacheKey(route)
    const config = this.cacheConfig.get(route.path)
    
    let cacheData
    switch (config?.strategy) {
      case 'sessionStorage':
        cacheData = sessionStorage.getItem(cacheKey)
        break
      case 'localStorage':
        cacheData = localStorage.getItem(cacheKey)
        break
      default:
        cacheData = this.cache.get(cacheKey)
    }

    if (!cacheData) return false

    // Check cache expiration
    const data = typeof cacheData === 'string' ? JSON.parse(cacheData) : cacheData
    const isExpired = Date.now() - data.timestamp > (config?.maxAge || 5 * 60 * 1000)
    
    if (isExpired) {
      this.clearCache(cacheKey, config?.strategy)
      return false
    }

    return true
  }

  private restoreRoute(route: RouteInfo) {
    const cacheKey = this.generateCacheKey(route)
    const config = this.cacheConfig.get(route.path)
    
    let cacheData
    switch (config?.strategy) {
      case 'sessionStorage':
        cacheData = JSON.parse(sessionStorage.getItem(cacheKey)!)
        break
      case 'localStorage':
        cacheData = JSON.parse(localStorage.getItem(cacheKey)!)
        break
      default:
        cacheData = this.cache.get(cacheKey)
    }

    if (cacheData) {
      this.applyRouteData(route, cacheData.data)
    }
  }

  private generateCacheKey(route: RouteInfo): string {
    return `route:${route.path}:${JSON.stringify(route.params)}`
  }

  private extractRouteData(route: RouteInfo): any {
    // Extract cacheable data from route
    return {
      scrollPosition: window.scrollY,
      formData: this.extractFormData(),
      componentState: this.extractComponentState(route)
    }
  }

  private applyRouteData(route: RouteInfo, data: any) {
    // Restore cached data
    if (data.scrollPosition) {
      setTimeout(() => {
        window.scrollTo(0, data.scrollPosition)
      }, 100)
    }

    if (data.formData) {
      this.restoreFormData(data.formData)
    }

    if (data.componentState) {
      this.restoreComponentState(route, data.componentState)
    }
  }

  private clearCache(cacheKey: string, strategy?: string) {
    switch (strategy) {
      case 'sessionStorage':
        sessionStorage.removeItem(cacheKey)
        break
      case 'localStorage':
        localStorage.removeItem(cacheKey)
        break
      default:
        this.cache.delete(cacheKey)
    }
  }

  // Clear all cache
  clearAllCache() {
    this.cache.clear()
    
    // Clear storage cache
    Object.keys(sessionStorage).forEach(key => {
      if (key.startsWith('route:')) {
        sessionStorage.removeItem(key)
      }
    })
    
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('route:')) {
        localStorage.removeItem(key)
      }
    })
  }
}
```

## Route Preloading

### Intelligent Route Preloading

```typescript
class RoutePreloader {
  private preloadQueue = new Set()
  private preloadedRoutes = new Set()
  private observer: IntersectionObserver

  constructor(private microCore: MicroCore) {
    this.setupIntersectionObserver()
    this.setupHoverPreload()
  }

  private setupIntersectionObserver() {
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const link = entry.target as HTMLElement
          const href = link.getAttribute('href')
          if (href) {
            this.preloadRoute(href)
          }
        }
      })
    }, {
      rootMargin: '100px'
    })
  }

  private setupHoverPreload() {
    document.addEventListener('mouseover', (event) => {
      const target = event.target as HTMLElement
      const link = target.closest('a[href]') as HTMLAnchorElement
      
      if (link && this.shouldPreload(link.href)) {
        this.preloadRoute(link.href)
      }
    })
  }

  private shouldPreload(href: string): boolean {
    // Check if route should be preloaded
    return !this.preloadedRoutes.has(href) && 
           !this.preloadQueue.has(href) &&
           this.isInternalRoute(href)
  }

  private isInternalRoute(href: string): boolean {
    try {
      const url = new URL(href, window.location.origin)
      return url.origin === window.location.origin
    } catch {
      return false
    }
  }

  async preloadRoute(path: string) {
    if (this.preloadedRoutes.has(path) || this.preloadQueue.has(path)) {
      return
    }

    this.preloadQueue.add(path)

    try {
      // Find matching application
      const matchedApp = this.findMatchingApp(path)
      
      if (matchedApp && matchedApp.status === 'NOT_LOADED') {
        console.log(`Preloading route: ${path}`)
        
        // Preload application resources
        await this.preloadAppResources(matchedApp)
        
        this.preloadedRoutes.add(path)
        console.log(`Route preloaded: ${path}`)
      }
    } catch (error) {
      console.error(`Failed to preload route ${path}:`, error)
    } finally {
      this.preloadQueue.delete(path)
    }
  }

  private findMatchingApp(path: string) {
    const apps = this.microCore.getApps()
    
    return apps.find(app => {
      if (typeof app.activeWhen === 'string') {
        return path.startsWith(app.activeWhen)
      } else if (Array.isArray(app.activeWhen)) {
        return app.activeWhen.some(rule => 
          typeof rule === 'string' ? path.startsWith(rule) : rule.test(path)
        )
      } else if (typeof app.activeWhen === 'function') {
        return app.activeWhen({ pathname: path } as Location)
      }
      
      return false
    })
  }

  private async preloadAppResources(app: any) {
    // Preload application without mounting
    await this.microCore.preloadApp(app.name)
  }

  // Enable viewport-based preloading
  enableViewportPreload() {
    document.querySelectorAll('a[href]').forEach(link => {
      this.observer.observe(link)
    })
  }

  // Disable preloading
  disable() {
    this.observer.disconnect()
    this.preloadQueue.clear()
  }
}

// Initialize route preloader
const routePreloader = new RoutePreloader(microCore)
routePreloader.enableViewportPreload()
```

## Route Transition Animations

### Animation Configuration

```typescript
// Configure route transition animations
microCore.router.setTransition({
  // Default transition
  default: {
    enter: 'slide-in-right',
    leave: 'slide-out-left',
    duration: 300
  },
  
  // Specific route transitions
  routes: {
    '/dashboard': {
      enter: 'fade-in',
      leave: 'fade-out',
      duration: 200
    },
    '/users/*': {
      enter: 'slide-up',
      leave: 'slide-down',
      duration: 400
    }
  },
  
  // Conditional transitions
  conditions: [
    {
      from: '/login',
      to: '/dashboard',
      transition: {
        enter: 'zoom-in',
        leave: 'fade-out',
        duration: 500
      }
    }
  ]
})
```

### Custom Animation Implementation

```typescript
class RouteTransitionManager {
  private currentTransition: Animation | null = null

  constructor(private microCore: MicroCore) {
    this.setupTransitionInterceptor()
  }

  private setupTransitionInterceptor() {
    this.microCore.router.beforeEach(async (to, from, next) => {
      if (from.path !== to.path) {
        await this.performTransition(from, to)
      }
      next()
    })
  }

  private async performTransition(from: RouteInfo, to: RouteInfo) {
    const transitionConfig = this.getTransitionConfig(from, to)
    
    if (!transitionConfig) {
      return
    }

    const fromContainer = this.getRouteContainer(from)
    const toContainer = this.getRouteContainer(to)

    if (fromContainer && toContainer) {
      await this.animateTransition(fromContainer, toContainer, transitionConfig)
    }
  }

  private getTransitionConfig(from: RouteInfo, to: RouteInfo) {
    // Get transition configuration based on routes
    const config = this.microCore.router.getTransitionConfig()
    
    // Check conditional transitions
    const conditionalTransition = config.conditions?.find(condition => 
      this.matchesCondition(condition, from, to)
    )
    
    if (conditionalTransition) {
      return conditionalTransition.transition
    }

    // Check route-specific transitions
    const routeTransition = config.routes?.[to.path] || config.routes?.[from.path]
    if (routeTransition) {
      return routeTransition
    }

    // Use default transition
    return config.default
  }

  private async animateTransition(
    fromContainer: HTMLElement, 
    toContainer: HTMLElement, 
    config: TransitionConfig
  ) {
    // Cancel current transition
    if (this.currentTransition) {
      this.currentTransition.cancel()
    }

    // Prepare containers
    fromContainer.style.position = 'absolute'
    toContainer.style.position = 'absolute'
    toContainer.style.opacity = '0'

    // Create animation
    this.currentTransition = fromContainer.animate([
      { opacity: 1, transform: 'translateX(0)' },
      { opacity: 0, transform: 'translateX(-100%)' }
    ], {
      duration: config.duration,
      easing: 'ease-in-out'
    })

    const enterAnimation = toContainer.animate([
      { opacity: 0, transform: 'translateX(100%)' },
      { opacity: 1, transform: 'translateX(0)' }
    ], {
      duration: config.duration,
      easing: 'ease-in-out'
    })

    // Wait for animations to complete
    await Promise.all([
      this.currentTransition.finished,
      enterAnimation.finished
    ])

    // Cleanup
    fromContainer.style.position = ''
    toContainer.style.position = ''
    toContainer.style.opacity = ''
    
    this.currentTransition = null
  }

  private getRouteContainer(route: RouteInfo): HTMLElement | null {
    const app = this.microCore.getApp(route.meta?.appName)
    return app?.container as HTMLElement || null
  }

  private matchesCondition(condition: any, from: RouteInfo, to: RouteInfo): boolean {
    return condition.from === from.path && condition.to === to.path
  }
}
```

## Framework Integration

### React Router Integration

```typescript
// React application router integration
import { BrowserRouter, Routes, Route, useNavigate } from 'react-router-dom'
import { useMicroCore } from '@micro-core/adapter-react'

function ReactApp() {
  const { microCore, routeState } = useMicroCore()
  const navigate = useNavigate()

  useEffect(() => {
    // Listen to main router changes
    const unsubscribe = microCore.router.afterEach((to, from) => {
      if (to.meta.appName === 'react-app') {
        // Sync with React Router
        const localPath = to.path.replace('/react-app', '')
        navigate(localPath, { replace: true })
      }
    })

    return unsubscribe
  }, [microCore, navigate])

  return (
    <Routes>
      <Route path="/" element={<Dashboard />} />
      <Route path="/users" element={<UserList />} />
      <Route path="/users/:id" element={<UserDetail />} />
    </Routes>
  )
}
```

### Vue Router Integration

```typescript
// Vue application router integration
import { createRouter, createWebHistory } from 'vue-router'
import { useMicroCore } from '@micro-core/adapter-vue'

const router = createRouter({
  history: createWebHistory('/vue-app'),
  routes: [
    { path: '/', component: Dashboard },
    { path: '/products', component: ProductList },
    { path: '/products/:id', component: ProductDetail }
  ]
})

// Sync with main router
const { microCore } = useMicroCore()

microCore.router.afterEach((to, from) => {
  if (to.meta.appName === 'vue-app') {
    const localPath = to.path.replace('/vue-app', '')
    router.push(localPath)
  }
})

router.afterEach((to, from) => {
  // Notify main router of local navigation
  microCore.router.updateAppRoute('vue-app', {
    path: `/vue-app${to.path}`,
    params: to.params,
    query: to.query
  })
})
```

## Best Practices

### 1. Route Organization

```typescript
// Recommended route structure
const routeStructure = {
  '/': 'main-app',           // Main application
  '/auth/*': 'auth-app',     // Authentication
  '/dashboard/*': 'dashboard-app', // Dashboard
  '/users/*': 'user-management',   // User management
  '/orders/*': 'order-system',     // Order system
  '/admin/*': 'admin-panel'        // Admin panel
}

// Avoid deep nesting
// ❌ Bad
'/admin/users/management/list/active/premium'

// ✅ Good
'/admin/users?status=active&type=premium'
```

### 2. Route Naming Convention

```typescript
// Consistent naming convention
const routes = [
  { name: 'user-list', path: '/users' },
  { name: 'user-detail', path: '/users/:id' },
  { name: 'user-edit', path: '/users/:id/edit' },
  { name: 'order-list', path: '/orders' },
  { name: 'order-detail', path: '/orders/:id' }
]
```

### 3. Error Handling

```typescript
// Global route error handling
microCore.router.onError((error, to, from) => {
  console.error('Route error:', error)
  
  // Handle different error types
  if (error.name === 'NavigationDuplicated') {
    // Ignore duplicate navigation
    return
  }
  
  if (error.name === 'NavigationAborted') {
    // Handle navigation abortion
    showNotification('Navigation was cancelled')
    return
  }
  
  // Fallback to error page
  microCore.router.push('/error', { 
    query: { 
      message: error.message,
      from: from.path 
    } 
  })
})
```

## Common Issues and Solutions

### Q: How to handle route conflicts between applications?

A: Use route namespacing and priority configuration:

```typescript
// Configure route priorities
microCore.registerApp({
  name: 'primary-app',
  activeWhen: '/dashboard',
  priority: 1  // Higher priority
})

microCore.registerApp({
  name: 'secondary-app',
  activeWhen: '/dashboard/*',
  priority: 2  // Lower priority
})
```

### Q: How to implement route-based code splitting?

A: Use dynamic imports with route-based loading:

```typescript
const routes = [
  {
    path: '/users',
    component: () => import('./UserManagement'),
    appName: 'user-management'
  },
  {
    path: '/orders',
    component: () => import('./OrderSystem'),
    appName: 'order-system'
  }
]
```

### Q: How to handle browser back/forward buttons?

A: Implement proper history management:

```typescript
// Handle browser navigation
window.addEventListener('popstate', (event) => {
  const currentPath = window.location.pathname
  const matchedApp = findMatchingApp(currentPath)
  
  if (matchedApp) {
    microCore.activateApp(matchedApp.name)
  }
})
```

## Summary

The Micro-Core routing system provides:

- **Unified Management**: Centralized routing coordination between main and micro applications
- **Dynamic Routing**: Runtime route registration and management
- **Route Guards**: Comprehensive authentication and authorization
- **State Synchronization**: Seamless route state sharing
- **Performance Optimization**: Route caching and intelligent preloading
- **Framework Integration**: Support for React Router, Vue Router, and other routing libraries

Key benefits:
- Seamless navigation between micro applications
- Consistent routing behavior across the entire system
- Flexible route configuration and management
- Enhanced user experience with smooth transitions

## Next Steps

- [Sandbox Isolation](./sandbox.md) - Learn about application isolation mechanisms
- [Application Communication](./communication.md) - Master inter-application communication
- [State Management](./state-management.md) - Understand global state management
- [Performance Optimization](../best-practices/performance.md) - Deep dive into performance optimization
