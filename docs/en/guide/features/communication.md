# Inter-Application Communication

Micro-Core provides multiple communication methods to support data exchange and event passing between the main application and micro-applications, as well as between micro-applications.

## Communication Methods Overview

### 1. Global Event Bus (EventBus)

The most commonly used communication method, based on the publish-subscribe pattern.

```typescript
import { EventBus } from '@micro-core/core';

// Send event
EventBus.emit('user-login', { userId: '123', username: 'john' });

// Listen to event
EventBus.on('user-login', (userData) => {
  console.log('User logged in:', userData);
});

// Remove listener
EventBus.off('user-login', handler);
```

### 2. Global State Management (GlobalState)

Used for sharing application state with reactive updates support.

```typescript
import { GlobalState } from '@micro-core/core';

// Set global state
GlobalState.set('currentUser', {
  id: '123',
  name: '<PERSON>',
  role: 'admin'
});

// Get global state
const user = GlobalState.get('currentUser');

// Watch state changes
GlobalState.watch('currentUser', (newUser, oldUser) => {
  console.log('User state changed:', newUser, oldUser);
});
```

### 3. Direct Communication

Direct method calls through application instances.

```typescript
import { MicroCoreKernel } from '@micro-core/core';

const kernel = new MicroCoreKernel();

// Get application instance
const reactApp = kernel.getApplication('react-app');

// Direct method call
if (reactApp && reactApp.instance) {
  reactApp.instance.updateTheme('dark');
}
```

### 4. Props Passing

Pass initial data through application configuration.

```typescript
kernel.registerApplication({
  name: 'child-app',
  entry: 'http://localhost:3001',
  container: '#child-container',
  activeWhen: '/child',
  props: {
    theme: 'light',
    userInfo: { id: '123', name: 'John' },
    apiBaseUrl: 'https://api.example.com'
  }
});
```

## Detailed Usage Guide

### EventBus Detailed Usage

#### Basic Event Operations

```typescript
import { EventBus } from '@micro-core/core';

// 1. Send simple event
EventBus.emit('theme-change', 'dark');

// 2. Send complex data
EventBus.emit('user-action', {
  type: 'click',
  target: 'button',
  timestamp: Date.now(),
  data: { buttonId: 'submit-btn' }
});

// 3. Listen to events
const handleThemeChange = (theme) => {
  document.body.className = `theme-${theme}`;
};

EventBus.on('theme-change', handleThemeChange);

// 4. One-time listener
EventBus.once('app-ready', () => {
  console.log('Application is ready');
});

// 5. Remove specific listener
EventBus.off('theme-change', handleThemeChange);

// 6. Remove all listeners
EventBus.off('theme-change');
```

#### Namespaced Events

```typescript
// Use namespaces to avoid event conflicts
EventBus.emit('user:login', userData);
EventBus.emit('user:logout', {});
EventBus.emit('app:error', errorInfo);
EventBus.emit('route:change', routeInfo);

// Listen to namespaced events
EventBus.on('user:*', (eventName, data) => {
  console.log(`User event: ${eventName}`, data);
});
```

#### Event Priority

```typescript
// High priority event listener
EventBus.on('critical-error', handler, { priority: 'high' });

// Normal priority
EventBus.on('user-action', handler, { priority: 'normal' });

// Low priority
EventBus.on('analytics', handler, { priority: 'low' });
```

### GlobalState Detailed Usage

#### Basic State Operations

```typescript
import { GlobalState } from '@micro-core/core';

// 1. Set state
GlobalState.set('theme', 'dark');
GlobalState.set('user', { id: 1, name: 'John' });

// 2. Get state
const theme = GlobalState.get('theme');
const user = GlobalState.get('user');

// 3. Check if state exists
if (GlobalState.has('user')) {
  console.log('User is logged in');
}

// 4. Remove state
GlobalState.remove('tempData');

// 5. Clear all state
GlobalState.clear();
```

#### Deep State Operations

```typescript
// Set nested state
GlobalState.set('app.settings.theme', 'dark');
GlobalState.set('user.profile.avatar', 'avatar.jpg');

// Get nested state
const theme = GlobalState.get('app.settings.theme');
const avatar = GlobalState.get('user.profile.avatar');

// Update partial state
GlobalState.merge('user', { lastLogin: Date.now() });
```

#### State Watching

```typescript
// Watch specific state changes
GlobalState.watch('theme', (newTheme, oldTheme) => {
  console.log(`Theme changed from ${oldTheme} to ${newTheme}`);
});

// Watch nested state changes
GlobalState.watch('user.profile', (newProfile, oldProfile) => {
  console.log('User profile updated:', newProfile);
});

// Watch all state changes
GlobalState.watchAll((key, newValue, oldValue) => {
  console.log(`State ${key} changed:`, newValue);
});

// Cancel watching
const unwatch = GlobalState.watch('theme', handler);
unwatch(); // Cancel watching
```

### Main Application Communication Example

```typescript
// main.ts - Main application
import { MicroCoreKernel, EventBus, GlobalState } from '@micro-core/core';

const kernel = new MicroCoreKernel();

// Initialize global state
GlobalState.set('currentUser', null);
GlobalState.set('theme', 'light');

// Listen to child application events
EventBus.on('child:user-login', (userData) => {
  GlobalState.set('currentUser', userData);
  
  // Notify other child applications
  EventBus.emit('user:login-success', userData);
});

EventBus.on('child:theme-change', (theme) => {
  GlobalState.set('theme', theme);
  
  // Update main application theme
  document.body.className = `theme-${theme}`;
  
  // Notify all child applications
  EventBus.emit('theme:changed', theme);
});

// Register child applications
kernel.registerApplication({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeWhen: '/user',
  props: {
    // Pass initial data
    initialTheme: GlobalState.get('theme'),
    apiConfig: {
      baseUrl: 'https://api.example.com',
      timeout: 5000
    }
  }
});
```

### Child Application Communication Examples

#### React Child Application

```typescript
// React child application
import React, { useEffect, useState } from 'react';
import { EventBus, GlobalState } from '@micro-core/core';

const UserApp: React.FC = () => {
  const [theme, setTheme] = useState('light');
  const [user, setUser] = useState(null);

  useEffect(() => {
    // Get initial state
    const initialTheme = GlobalState.get('theme');
    const currentUser = GlobalState.get('currentUser');
    
    setTheme(initialTheme);
    setUser(currentUser);

    // Listen to theme changes
    const handleThemeChange = (newTheme) => {
      setTheme(newTheme);
    };

    // Listen to user state changes
    const handleUserChange = (newUser) => {
      setUser(newUser);
    };

    EventBus.on('theme:changed', handleThemeChange);
    GlobalState.watch('currentUser', handleUserChange);

    return () => {
      EventBus.off('theme:changed', handleThemeChange);
      // GlobalState.watch returns unwatch function automatically
    };
  }, []);

  const handleLogin = (userData) => {
    // Notify main application of user login
    EventBus.emit('child:user-login', userData);
  };

  const handleThemeToggle = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    // Notify main application of theme change
    EventBus.emit('child:theme-change', newTheme);
  };

  return (
    <div className={`user-app theme-${theme}`}>
      <h1>User Application</h1>
      <button onClick={handleThemeToggle}>
        Toggle Theme ({theme})
      </button>
      {user ? (
        <div>Welcome, {user.name}!</div>
      ) : (
        <button onClick={() => handleLogin({ id: 1, name: 'John' })}>
          Login
        </button>
      )}
    </div>
  );
};

export default UserApp;
```

#### Vue Child Application

```vue
<!-- Vue child application -->
<template>
  <div :class="`shopping-app theme-${theme}`">
    <h1>Shopping Application</h1>
    <div>Current user: {{ user?.name || 'Not logged in' }}</div>
    <div>Cart items: {{ cartCount }}</div>
    <button @click="addToCart">Add Item</button>
  </div>
</template>

<script>
import { EventBus, GlobalState } from '@micro-core/core';

export default {
  name: 'ShoppingApp',
  data() {
    return {
      theme: 'light',
      user: null,
      cartCount: 0
    };
  },
  mounted() {
    // Get initial state
    this.theme = GlobalState.get('theme') || 'light';
    this.user = GlobalState.get('currentUser');

    // Listen to events
    EventBus.on('theme:changed', this.handleThemeChange);
    EventBus.on('user:login-success', this.handleUserLogin);
    
    // Watch global state
    GlobalState.watch('currentUser', this.handleUserChange);
  },
  beforeUnmount() {
    // Clean up listeners
    EventBus.off('theme:changed', this.handleThemeChange);
    EventBus.off('user:login-success', this.handleUserLogin);
  },
  methods: {
    handleThemeChange(newTheme) {
      this.theme = newTheme;
    },
    handleUserLogin(userData) {
      this.user = userData;
    },
    handleUserChange(newUser) {
      this.user = newUser;
    },
    addToCart() {
      this.cartCount++;
      
      // Notify other applications of cart changes
      EventBus.emit('cart:item-added', {
        userId: this.user?.id,
        itemCount: this.cartCount,
        timestamp: Date.now()
      });
      
      // Update global cart state
      GlobalState.set('cart.itemCount', this.cartCount);
    }
  }
};
</script>
```

## Communication Best Practices

### 1. Event Naming Convention

```typescript
// Recommended event naming convention
EventBus.emit('user:login');           // User-related events
EventBus.emit('cart:item-added');      // Cart-related events
EventBus.emit('route:change');         // Route-related events
EventBus.emit('app:error');            // Application-related events
EventBus.emit('theme:changed');        // Theme-related events

// Avoid these naming styles
EventBus.emit('login');                // Too simple, easy to conflict
EventBus.emit('userLoginSuccess');     // CamelCase not clear enough
EventBus.emit('USER_LOGIN');           // All caps not friendly
```

### 2. State Management Convention

```typescript
// Recommended state structure
GlobalState.set('user.profile', { id: 1, name: 'John' });
GlobalState.set('app.settings.theme', 'dark');
GlobalState.set('cart.items', []);
GlobalState.set('route.current', '/dashboard');

// Avoid these state structures
GlobalState.set('userProfile', {}); // Flat structure not clear enough
GlobalState.set('data', {});         // Name too generic
```

### 3. Error Handling

```typescript
// Event error handling
EventBus.on('user:login', (userData) => {
  try {
    // Process login logic
    processUserLogin(userData);
  } catch (error) {
    console.error('Failed to process user login:', error);
    EventBus.emit('app:error', {
      type: 'user-login-error',
      error: error.message
    });
  }
});

// State update error handling
try {
  GlobalState.set('user.profile', userData);
} catch (error) {
  console.error('Failed to update user state:', error);
}
```

### 4. Memory Leak Prevention

```typescript
// React Hook example
import { useEffect } from 'react';
import { EventBus, GlobalState } from '@micro-core/core';

const useGlobalCommunication = () => {
  useEffect(() => {
    const handleUserChange = (userData) => {
      // Handle user changes
    };

    const handleThemeChange = (theme) => {
      // Handle theme changes
    };

    // Add listeners
    EventBus.on('user:changed', handleUserChange);
    EventBus.on('theme:changed', handleThemeChange);
    
    const unwatchUser = GlobalState.watch('currentUser', handleUserChange);

    // Cleanup function
    return () => {
      EventBus.off('user:changed', handleUserChange);
      EventBus.off('theme:changed', handleThemeChange);
      unwatchUser();
    };
  }, []);
};
```

## Debugging and Monitoring

### 1. Communication Debugging

```typescript
// Enable debug mode
if (process.env.NODE_ENV === 'development') {
  // Listen to all events
  EventBus.onAny((eventName, ...args) => {
    console.log(`[EventBus] ${eventName}:`, args);
  });

  // Listen to all state changes
  GlobalState.watchAll((key, newValue, oldValue) => {
    console.log(`[GlobalState] ${key}:`, { newValue, oldValue });
  });
}
```

### 2. Performance Monitoring

```typescript
// Event performance monitoring
const originalEmit = EventBus.emit;
EventBus.emit = function(eventName, ...args) {
  const startTime = performance.now();
  const result = originalEmit.call(this, eventName, ...args);
  const endTime = performance.now();
  
  if (endTime - startTime > 10) { // Events taking more than 10ms
    console.warn(`[Performance] Event ${eventName} took ${endTime - startTime}ms`);
  }
  
  return result;
};
```

## Common Issues

### Q: How to avoid event loops?

A: Use event namespaces and clear data flow direction:

```typescript
// Avoid loops
EventBus.on('user:update', (userData) => {
  // Don't emit user:update event again here
  // EventBus.emit('user:update', userData); // ❌ Will cause loop
  
  // Should emit different event
  EventBus.emit('user:updated', userData); // ✅ Correct
});
```

### Q: How to handle asynchronous communication?

A: Use Promise or async/await:

```typescript
// Asynchronous event handling
EventBus.on('user:login', async (credentials) => {
  try {
    const userData = await loginAPI(credentials);
    EventBus.emit('user:login-success', userData);
  } catch (error) {
    EventBus.emit('user:login-error', error);
  }
});
```

### Q: How to ensure type safety in communication?

A: Use TypeScript interfaces:

```typescript
// Define event types
interface EventMap {
  'user:login': { username: string; password: string };
  'user:logout': void;
  'theme:change': 'light' | 'dark';
}

// Type-safe event emission
EventBus.emit<'user:login'>('user:login', {
  username: 'john',
  password: 'secret'
});
```

## Summary

Inter-application communication is crucial for micro-frontend architecture. Through proper event management, state sharing, and error handling, you can build a robust and efficient communication system.

Key points:
- Use clear naming conventions and data structures
- Implement comprehensive error handling and cleanup
- Monitor performance and debug communication issues
- Ensure type safety and prevent memory leaks

## Next Steps

- [Sandbox Isolation](./sandbox.md) - Learn about application isolation mechanisms
- [State Management](./state-management.md) - Deep dive into state management
- [Performance Optimization](../best-practices/performance.md) - Optimize communication performance
- [Testing Strategies](../best-practices/testing.md) - Test communication between applications