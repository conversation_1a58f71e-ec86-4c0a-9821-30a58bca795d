# Configuration

This guide covers all configuration options available in Micro-Core, from basic setup to advanced customization.

## Basic Configuration

### Minimal Setup

The simplest Micro-Core configuration:

```typescript
import { MicroCore } from '@micro-core/core';

const microCore = new MicroCore({
  container: '#app'
});

microCore.registerApp({
  name: 'my-app',
  entry: 'http://localhost:3001',
  container: '#my-app',
  activeWhen: '/'
});

microCore.start();
```

### Standard Setup

A typical production-ready configuration:

```typescript
import { MicroCore } from '@micro-core/core';
import { ReactAdapter } from '@micro-core/adapter-react';
import { VueAdapter } from '@micro-core/adapter-vue';

const microCore = new MicroCore({
  // Main container
  container: '#app',
  
  // Framework adapters
  adapters: [
    new ReactAdapter(),
    new VueAdapter()
  ],
  
  // Sandbox configuration
  sandbox: {
    type: 'proxy',
    strict: true
  },
  
  // Router configuration
  router: {
    mode: 'browser',
    base: '/'
  }
});
```

## Core Configuration Options

### Container Configuration

```typescript
const microCore = new MicroCore({
  // String selector
  container: '#app',
  
  // DOM element
  container: document.getElementById('app'),
  
  // Function returning element
  container: () => document.querySelector('#app')
});
```

### Adapter Configuration

```typescript
import { ReactAdapter } from '@micro-core/adapter-react';
import { VueAdapter } from '@micro-core/adapter-vue';
import { AngularAdapter } from '@micro-core/adapter-angular';

const microCore = new MicroCore({
  adapters: [
    // React adapter with options
    new ReactAdapter({
      version: '18',
      strictMode: true,
      errorBoundary: true
    }),
    
    // Vue adapter with options
    new VueAdapter({
      version: '3',
      devtools: process.env.NODE_ENV === 'development'
    }),
    
    // Angular adapter with options
    new AngularAdapter({
      version: '16',
      enableIvy: true
    })
  ]
});
```

## Sandbox Configuration

### Proxy Sandbox

High-performance JavaScript isolation:

```typescript
const microCore = new MicroCore({
  sandbox: {
    type: 'proxy',
    
    // Strict mode
    strict: true,
    
    // Global variable whitelist
    whitelist: [
      'console',
      'fetch',
      'setTimeout',
      'setInterval',
      'clearTimeout',
      'clearInterval',
      'Promise',
      'URL',
      'URLSearchParams'
    ],
    
    // Dangerous API blacklist
    blacklist: [
      'eval',
      'Function',
      'WebAssembly',
      'importScripts',
      'document.write'
    ],
    
    // Custom property interceptors
    interceptors: {
      get: (target, prop, receiver, sandbox) => {
        // Custom get logic
        if (prop === 'location') {
          return createVirtualLocation(sandbox.name);
        }
        return Reflect.get(target, prop, receiver);
      },
      
      set: (target, prop, value, receiver, sandbox) => {
        // Custom set logic
        if (prop === 'document') {
          console.warn(`App ${sandbox.name} tried to modify document`);
          return false;
        }
        return Reflect.set(target, prop, value, receiver);
      }
    }
  }
});
```

### Iframe Sandbox

Maximum isolation using iframe:

```typescript
const microCore = new MicroCore({
  sandbox: {
    type: 'iframe',
    
    // Sandbox permissions
    allowSameOrigin: false,
    allowScripts: true,
    allowForms: true,
    allowPopups: false,
    
    // Communication protocol
    communicationProtocol: 'postMessage',
    
    // Iframe attributes
    attributes: {
      width: '100%',
      height: '100%',
      frameborder: '0'
    },
    
    // Security headers
    securityHeaders: {
      'X-Frame-Options': 'SAMEORIGIN',
      'Content-Security-Policy': "default-src 'self'"
    }
  }
});
```

### WebComponent Sandbox

Style isolation using Shadow DOM:

```typescript
const microCore = new MicroCore({
  sandbox: {
    type: 'webcomponent',
    
    // Shadow DOM configuration
    shadowDOM: true,
    shadowDOMMode: 'closed', // 'open' or 'closed'
    
    // Style isolation
    styleIsolation: true,
    stylePrefixing: true,
    
    // Custom elements
    customElements: true,
    
    // Slot support
    slotSupport: true,
    
    // CSS custom properties
    cssCustomProperties: {
      '--primary-color': '#007bff',
      '--secondary-color': '#6c757d'
    }
  }
});
```

## Router Configuration

### Basic Router Setup

```typescript
const microCore = new MicroCore({
  router: {
    // Router mode
    mode: 'browser', // 'browser' | 'hash' | 'memory'
    
    // Base path
    base: '/',
    
    // Case sensitivity
    caseSensitive: false,
    
    // Trailing slash handling
    trailingSlash: 'auto', // 'auto' | 'always' | 'never'
    
    // Scroll behavior
    scrollBehavior: 'auto' // 'auto' | 'top' | 'smooth'
  }
});
```

### Advanced Router Configuration

```typescript
const microCore = new MicroCore({
  router: {
    mode: 'browser',
    base: '/',
    
    // Route guards
    guards: {
      beforeEach: async (to, from, next) => {
        // Global before guard
        console.log(`Navigating from ${from.path} to ${to.path}`);
        
        // Authentication check
        if (to.meta?.requiresAuth && !isAuthenticated()) {
          next('/login');
          return;
        }
        
        // Permission check
        if (to.meta?.requiredRole && !hasRole(to.meta.requiredRole)) {
          next('/unauthorized');
          return;
        }
        
        next();
      },
      
      afterEach: (to, from) => {
        // Global after guard
        console.log(`Navigated to ${to.path}`);
        
        // Analytics tracking
        analytics.track('page_view', {
          path: to.path,
          title: to.meta?.title
        });
      }
    },
    
    // Route caching
    cache: {
      enabled: true,
      maxAge: 5 * 60 * 1000, // 5 minutes
      maxSize: 100 // Maximum cached routes
    },
    
    // Route preloading
    preload: {
      enabled: true,
      strategy: 'hover', // 'hover' | 'visible' | 'idle'
      delay: 100 // Delay in milliseconds
    }
  }
});
```

## Communication Configuration

### Event Bus Configuration

```typescript
const microCore = new MicroCore({
  communication: {
    eventBus: {
      // Maximum listeners per event
      maxListeners: 100,
      
      // Enable debug mode
      enableDebug: process.env.NODE_ENV === 'development',
      
      // Event validation
      validation: {
        enabled: true,
        schemas: {
          'user:login': {
            type: 'object',
            properties: {
              userId: { type: 'string' },
              timestamp: { type: 'number' }
            },
            required: ['userId']
          }
        }
      },
      
      // Rate limiting
      rateLimit: {
        enabled: true,
        maxEvents: 100,
        timeWindow: 1000, // 1 second
        blockDuration: 5000 // 5 seconds
      },
      
      // Event middleware
      middleware: [
        // Logging middleware
        (event, data, next) => {
          console.log(`Event: ${event}`, data);
          next();
        },
        
        // Validation middleware
        (event, data, next) => {
          if (validateEventData(event, data)) {
            next();
          } else {
            throw new Error(`Invalid data for event: ${event}`);
          }
        }
      ]
    }
  }
});
```

### Global State Configuration

```typescript
const microCore = new MicroCore({
  communication: {
    globalState: {
      // State persistence
      persistence: {
        enabled: true,
        storage: 'localStorage', // 'localStorage' | 'sessionStorage' | 'indexedDB'
        prefix: 'micro-core-',
        
        // Keys to persist
        keys: ['user', 'theme', 'settings'],
        
        // Keys to exclude
        excludeKeys: ['temp', 'cache'],
        
        // Serialization
        serialize: JSON.stringify,
        deserialize: JSON.parse,
        
        // Compression
        compress: true,
        
        // Encryption
        encrypt: {
          enabled: true,
          algorithm: 'AES-GCM',
          key: 'your-encryption-key'
        }
      },
      
      // State validation
      validation: {
        enabled: true,
        schemas: {
          user: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              email: { type: 'string', format: 'email' }
            },
            required: ['id', 'name']
          }
        }
      },
      
      // State middleware
      middleware: [
        // Logging middleware
        (action, state, next) => {
          console.log(`State ${action}:`, state);
          next();
        },
        
        // Audit middleware
        (action, state, next) => {
          auditLog.record({
            action,
            state: sanitizeState(state),
            timestamp: Date.now()
          });
          next();
        }
      ],
      
      // Computed state
      computed: {
        'user.displayName': (state) => {
          const user = state.user;
          return user ? `${user.firstName} ${user.lastName}` : '';
        },
        
        'cart.total': (state) => {
          const cart = state.cart;
          return cart ? cart.items.reduce((sum, item) => sum + item.price, 0) : 0;
        }
      }
    }
  }
});
```

## Performance Configuration

### Loading Configuration

```typescript
const microCore = new MicroCore({
  performance: {
    // Loading strategy
    loading: {
      strategy: 'lazy', // 'eager' | 'lazy' | 'manual'
      
      // Concurrent loading
      concurrency: 3,
      
      // Loading timeout
      timeout: 10000, // 10 seconds
      
      // Retry configuration
      retry: {
        attempts: 3,
        delay: 1000, // 1 second
        backoff: 'exponential' // 'linear' | 'exponential'
      }
    },
    
    // Preloading configuration
    preload: {
      // Preload on idle
      idle: ['critical-app', 'navigation-app'],
      
      // Preload on hover
      hover: ['.nav-link[href="/users"]'],
      
      // Preload on viewport
      viewport: {
        enabled: true,
        threshold: 0.1, // 10% visible
        rootMargin: '50px'
      },
      
      // Predictive preloading
      predictive: {
        enabled: true,
        threshold: 0.7, // 70% probability
        maxPredictions: 5
      }
    },
    
    // Resource hints
    resourceHints: {
      prefetch: ['user-app', 'dashboard-app'],
      preconnect: ['https://api.example.com'],
      preload: [
        { href: '/critical.css', as: 'style' },
        { href: '/critical.js', as: 'script' }
      ]
    }
  }
});
```

### Caching Configuration

```typescript
const microCore = new MicroCore({
  performance: {
    cache: {
      // Memory cache
      memory: {
        enabled: true,
        maxSize: 50 * 1024 * 1024, // 50MB
        ttl: 30 * 60 * 1000, // 30 minutes
        
        // Cache strategies
        strategies: {
          'static-assets': {
            ttl: 24 * 60 * 60 * 1000, // 24 hours
            maxSize: 20 * 1024 * 1024 // 20MB
          },
          'api-responses': {
            ttl: 5 * 60 * 1000, // 5 minutes
            maxSize: 10 * 1024 * 1024 // 10MB
          }
        }
      },
      
      // HTTP cache
      http: {
        enabled: true,
        headers: {
          'Cache-Control': 'public, max-age=31536000', // 1 year
          'ETag': true,
          'Last-Modified': true
        }
      },
      
      // Service Worker cache
      serviceWorker: {
        enabled: true,
        strategy: 'cache-first', // 'cache-first' | 'network-first' | 'stale-while-revalidate'
        
        // Cache resources
        resources: [
          '/static/js/*.js',
          '/static/css/*.css',
          '/static/images/*'
        ],
        
        // Cache configuration
        config: {
          cacheName: 'micro-core-cache',
          maxEntries: 100,
          maxAgeSeconds: 24 * 60 * 60 // 24 hours
        }
      }
    }
  }
});
```

## Error Handling Configuration

### Global Error Handling

```typescript
const microCore = new MicroCore({
  errorHandler: {
    // Load error handling
    onLoadError: (error, app) => {
      console.error(`Failed to load ${app.name}:`, error);
      
      // Show user notification
      showNotification({
        type: 'error',
        title: 'Application Load Failed',
        message: `Failed to load ${app.name}. Please try again.`,
        actions: [
          {
            label: 'Retry',
            action: () => microCore.reloadApp(app.name)
          },
          {
            label: 'Refresh Page',
            action: () => window.location.reload()
          }
        ]
      });
      
      // Report to error tracking
      errorTracker.captureException(error, {
        tags: { type: 'app-load-error', appName: app.name },
        extra: { appConfig: app.config }
      });
    },
    
    // Mount error handling
    onMountError: (error, app) => {
      console.error(`Failed to mount ${app.name}:`, error);
      
      // Show fallback UI
      showFallbackUI(app.container, {
        title: 'Service Unavailable',
        message: 'This service is temporarily unavailable.',
        retry: () => microCore.remountApp(app.name)
      });
      
      // Report error
      errorTracker.captureException(error, {
        tags: { type: 'app-mount-error', appName: app.name }
      });
    },
    
    // Runtime error handling
    onRuntimeError: (error, app) => {
      console.error(`Runtime error in ${app.name}:`, error);
      
      // Report error
      errorTracker.captureException(error, {
        tags: { type: 'app-runtime-error', appName: app.name }
      });
    },
    
    // Unhandled promise rejection
    onUnhandledRejection: (reason, promise, app) => {
      console.error(`Unhandled rejection in ${app?.name || 'unknown'}:`, reason);
      
      // Report error
      errorTracker.captureException(new Error(reason), {
        tags: { type: 'unhandled-rejection', appName: app?.name }
      });
    }
  }
});
```

## Development Configuration

### Development Mode Settings

```typescript
const developmentConfig = {
  // Enable development mode
  development: {
    enabled: true,
    
    // Debug settings
    debug: {
      enabled: true,
      level: 'verbose', // 'error' | 'warn' | 'info' | 'debug' | 'verbose'
      
      // Debug categories
      categories: [
        'lifecycle',
        'communication',
        'routing',
        'sandbox',
        'performance'
      ]
    },
    
    // Performance metrics
    performance: {
      enabled: true,
      showMetrics: true,
      logSlowOperations: true,
      slowThreshold: 100 // milliseconds
    },
    
    // Hot reload
    hotReload: {
      enabled: true,
      watchFiles: ['**/*.js', '**/*.ts', '**/*.vue', '**/*.jsx'],
      ignoreFiles: ['node_modules/**', 'dist/**']
    },
    
    // Mock data
    mockData: {
      enabled: true,
      apiMocks: {
        '/api/users': { users: [{ id: 1, name: 'John' }] },
        '/api/profile': { id: 1, name: 'John', email: '<EMAIL>' }
      }
    }
  }
};
```

### Production Configuration

```typescript
const productionConfig = {
  // Disable development features
  development: {
    enabled: false
  },
  
  // Production optimizations
  production: {
    enabled: true,
    
    // Minification
    minify: {
      enabled: true,
      removeComments: true,
      removeWhitespace: true
    },
    
    // Compression
    compression: {
      enabled: true,
      algorithm: 'gzip',
      level: 6
    },
    
    // Bundle optimization
    bundleOptimization: {
      enabled: true,
      splitChunks: true,
      treeShaking: true
    }
  },
  
  // Strict security
  sandbox: {
    type: 'proxy',
    strict: true,
    whitelist: ['console', 'fetch'],
    blacklist: ['eval', 'Function', 'debugger']
  },
  
  // Performance optimizations
  performance: {
    cache: {
      enabled: true,
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    },
    preload: {
      enabled: true,
      critical: ['navigation-app', 'header-app']
    }
  }
};
```

## Application Configuration

### Basic Application Registration

```typescript
microCore.registerApp({
  // Required: Application name (must be unique)
  name: 'user-management',
  
  // Required: Application entry point
  entry: 'http://localhost:3001',
  
  // Required: Container selector or element
  container: '#user-app',
  
  // Required: Activation condition
  activeWhen: '/users'
});
```

### Advanced Application Configuration

```typescript
microCore.registerApp({
  name: 'user-management',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeWhen: ['/users', '/profile'],
  
  // Application metadata
  meta: {
    title: 'User Management',
    description: 'User management micro-application',
    version: '1.0.0',
    author: 'Development Team',
    requiresAuth: true,
    requiredRole: 'admin'
  },
  
  // Initial props
  props: {
    theme: 'dark',
    locale: 'en-US',
    apiBaseUrl: 'https://api.example.com'
  },
  
  // Loading configuration
  loading: {
    strategy: 'lazy',
    timeout: 10000,
    showSpinner: true,
    spinnerTemplate: '<div class="loading">Loading...</div>'
  },
  
  // Sandbox configuration (overrides global)
  sandbox: {
    type: 'proxy',
    strict: true,
    css: {
      isolation: 'scoped',
      prefix: 'user-app-'
    }
  },
  
  // Error boundary
  errorBoundary: {
    enabled: true,
    fallback: (error, app) => `
      <div class="error-boundary">
        <h2>Error in ${app.name}</h2>
        <p>${error.message}</p>
        <button onclick="location.reload()">Reload</button>
      </div>
    `
  },
  
  // Lifecycle hooks
  beforeLoad: async (app) => {
    console.log(`Loading ${app.name}...`);
    // Prepare for loading
  },
  
  afterLoad: async (app) => {
    console.log(`${app.name} loaded`);
    // Post-load setup
  },
  
  beforeMount: async (app) => {
    console.log(`Mounting ${app.name}...`);
    // Set up container, prepare props
  },
  
  afterMount: async (app) => {
    console.log(`${app.name} mounted`);
    // Post-mount setup
  },
  
  beforeUnmount: async (app) => {
    console.log(`Unmounting ${app.name}...`);
    // Cleanup before unmount
  },
  
  afterUnmount: async (app) => {
    console.log(`${app.name} unmounted`);
    // Final cleanup
  },
  
  // Custom configuration
  custom: {
    analytics: {
      trackPageViews: true,
      trackUserActions: true
    },
    features: {
      enableNotifications: true,
      enableDarkMode: true
    }
  }
});
```

## Environment-Specific Configuration

### Configuration Factory

```typescript
// config/index.ts
interface EnvironmentConfig {
  development: MicroCoreConfig;
  staging: MicroCoreConfig;
  production: MicroCoreConfig;
}

const createConfig = (env: string): MicroCoreConfig => {
  const baseConfig: MicroCoreConfig = {
    container: '#app',
    adapters: [new ReactAdapter(), new VueAdapter()]
  };
  
  const envConfigs: EnvironmentConfig = {
    development: {
      ...baseConfig,
      development: {
        enabled: true,
        debug: { enabled: true, level: 'verbose' },
        hotReload: { enabled: true }
      },
      sandbox: {
        type: 'proxy',
        strict: false
      },
      apps: {
        'user-management': 'http://localhost:3001',
        'order-management': 'http://localhost:3002'
      }
    },
    
    staging: {
      ...baseConfig,
      development: {
        enabled: false
      },
      sandbox: {
        type: 'proxy',
        strict: true
      },
      apps: {
        'user-management': 'https://staging-user.example.com',
        'order-management': 'https://staging-order.example.com'
      },
      performance: {
        cache: { enabled: true, maxAge: 10 * 60 * 1000 } // 10 minutes
      }
    },
    
    production: {
      ...baseConfig,
      development: {
        enabled: false
      },
      production: {
        enabled: true,
        minify: { enabled: true },
        compression: { enabled: true }
      },
      sandbox: {
        type: 'proxy',
        strict: true,
        blacklist: ['eval', 'Function', 'debugger']
      },
      apps: {
        'user-management': 'https://user.example.com',
        'order-management': 'https://order.example.com'
      },
      performance: {
        cache: { enabled: true, maxAge: 24 * 60 * 60 * 1000 }, // 24 hours
        preload: { enabled: true, critical: ['navigation', 'header'] }
      },
      errorHandler: {
        onLoadError: (error, app) => {
          errorTracker.captureException(error, {
            tags: { appName: app.name, type: 'load-error' }
          });
        }
      }
    }
  };
  
  return envConfigs[env] || envConfigs.development;
};

export default createConfig;
```

### Usage

```typescript
// main.ts
import createConfig from './config';

const env = process.env.NODE_ENV || 'development';
const config = createConfig(env);

const microCore = new MicroCore(config);

// Register applications with environment-specific URLs
Object.entries(config.apps).forEach(([name, entry]) => {
  microCore.registerApp({
    name,
    entry,
    container: `#${name}`,
    activeWhen: `/${name.replace('-management', '')}`
  });
});

microCore.start();
```

## Plugin Configuration

### Router Plugin

```typescript
import { RouterPlugin } from '@micro-core/plugin-router';

microCore.use(RouterPlugin, {
  mode: 'browser',
  base: '/',
  
  // Route configuration
  routes: [
    {
      path: '/users',
      app: 'user-management',
      meta: { requiresAuth: true }
    },
    {
      path: '/orders',
      app: 'order-management',
      meta: { requiresAuth: true, role: 'admin' }
    }
  ],
  
  // Guards
  guards: {
    beforeEach: async (to, from, next) => {
      if (to.meta?.requiresAuth && !isAuthenticated()) {
        next('/login');
        return;
      }
      next();
    }
  },
  
  // Caching
  cache: {
    enabled: true,
    maxAge: 5 * 60 * 1000
  }
});
```

### Communication Plugin

```typescript
import { CommunicationPlugin } from '@micro-core/plugin-communication';

microCore.use(CommunicationPlugin, {
  eventBus: {
    maxListeners: 100,
    enableDebug: false,
    middleware: [
      (event, data, next) => {
        console.log(`Event: ${event}`, data);
        next();
      }
    ]
  },
  
  globalState: {
    persistence: {
      enabled: true,
      storage: 'localStorage',
      keys: ['user', 'theme', 'settings']
    },
    validation: {
      enabled: true,
      schemas: {
        user: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' }
          }
        }
      }
    }
  }
});
```

### Authentication Plugin

```typescript
import { AuthPlugin } from '@micro-core/plugin-auth';

microCore.use(AuthPlugin, {
  // Authentication provider
  provider: 'oauth2',
  
  // OAuth2 configuration
  oauth2: {
    clientId: 'your-client-id',
    redirectUri: 'http://localhost:3000/callback',
    scope: 'openid profile email',
    responseType: 'code'
  },
  
  // Token storage
  tokenStorage: {
    type: 'localStorage',
    key: 'auth_token'
  },
  
  // Auto refresh
  autoRefresh: {
    enabled: true,
    threshold: 5 * 60 * 1000 // 5 minutes before expiry
  },
  
  // Route protection
  routeProtection: {
    enabled: true,
    loginRoute: '/login',
    defaultRoute: '/dashboard'
  }
});
```

## Configuration Validation

### Schema Validation

```typescript
import Joi from 'joi';

const configSchema = Joi.object({
  container: Joi.alternatives().try(
    Joi.string(),
    Joi.object(),
    Joi.function()
  ).required(),
  
  adapters: Joi.array().items(Joi.object()).required(),
  
  sandbox: Joi.object({
    type: Joi.string().valid('proxy', 'iframe', 'webcomponent').required(),
    strict: Joi.boolean(),
    whitelist: Joi.array().items(Joi.string()),
    blacklist: Joi.array().items(Joi.string())
  }),
  
  router: Joi.object({
    mode: Joi.string().valid('browser', 'hash', 'memory'),
    base: Joi.string(),
    caseSensitive: Joi.boolean()
  }),
  
  performance: Joi.object({
    cache: Joi.object({
      enabled: Joi.boolean(),
      maxAge: Joi.number().positive()
    }),
    preload: Joi.object({
      enabled: Joi.boolean(),
      critical: Joi.array().items(Joi.string())
    })
  })
});

// Validate configuration
const validateConfig = (config: any) => {
  const { error, value } = configSchema.validate(config);
  if (error) {
    throw new Error(`Configuration validation failed: ${error.message}`);
  }
  return value;
};

// Usage
const config = validateConfig({
  container: '#app',
  adapters: [new ReactAdapter()],
  sandbox: { type: 'proxy', strict: true }
});
```

## Configuration Best Practices

### 1. Environment Separation

```typescript
// ✅ Good: Separate configurations by environment
const configs = {
  development: { /* dev config */ },
  staging: { /* staging config */ },
  production: { /* prod config */ }
};

// ❌ Bad: Single configuration with conditionals
const config = {
  debug: process.env.NODE_ENV === 'development',
  // ... lots of conditionals
};
```

### 2. Configuration Validation

```typescript
// ✅ Good: Validate configuration
const validateConfig = (config) => {
  if (!config.container) {
    throw new Error('Container is required');
  }
  // ... more validation
};

// ❌ Bad: No validation
const microCore = new MicroCore(config); // Might fail at runtime
```

### 3. Secure Configuration

```typescript
// ✅ Good: Secure production configuration
const productionConfig = {
  sandbox: {
    type: 'proxy',
    strict: true,
    blacklist: ['eval', 'Function']
  },
  errorHandler: {
    onError: (error) => {
      // Log error without exposing sensitive info
      logger.error('Application error', { message: error.message });
    }
  }
};

// ❌ Bad: Insecure configuration
const badConfig = {
  sandbox: { strict: false }, // Too permissive
  errorHandler: {
    onError: (error) => {
      console.log(error); // Exposes sensitive info
    }
  }
};
```

## Configuration Examples

### E-commerce Platform

```typescript
const ecommerceConfig = {
  container: '#app',
  adapters: [new ReactAdapter(), new VueAdapter()],
  
  sandbox: {
    type: 'proxy',
    strict: true,
    css: { isolation: 'scoped' }
  },
  
  router: {
    mode: 'browser',
    base: '/',
    guards: {
      beforeEach: async (to, from, next) => {
        if (to.path.startsWith('/admin') && !isAdmin()) {
          next('/unauthorized');
          return;
        }
        next();
      }
    }
  },
  
  communication: {
    globalState: {
      persistence: {
        enabled: true,
        keys: ['cart', 'user', 'preferences']
      }
    }
  },
  
  performance: {
    preload: {
      critical: ['header', 'navigation', 'cart']
    },
    cache: {
      enabled: true,
      maxAge: 30 * 60 * 1000 // 30 minutes
    }
  }
};
```

### Enterprise Dashboard

```typescript
const dashboardConfig = {
  container: '#dashboard',
  adapters: [new ReactAdapter(), new AngularAdapter()],
  
  sandbox: {
    type: 'webcomponent',
    shadowDOM: true,
    styleIsolation: true
  },
  
  router: {
    mode: 'browser',
    base: '/dashboard/',
    guards: {
      beforeEach: async (to, from, next) => {
        const hasPermission = await checkPermission(to.meta?.permission);
        if (!hasPermission) {
          next('/access-denied');
          return;
        }
        next();
      }
    }
  },
  
  communication: {
    eventBus: {
      validation: { enabled: true },
      rateLimit: { enabled: true, maxEvents: 50 }
    }
  },
  
  errorHandler: {
    onLoadError: (error, app) => {
      notificationService.error(`Failed to load ${app.name}`);
      errorTracker.captureException(error);
    }
  }
};
```

## Next Steps

- [Getting Started](./getting-started.md) - Build your first application
- [Core Concepts](./core-concepts.md) - Understand the fundamentals
- [Features](./features/) - Explore specific features
- [Best Practices](./best-practices/) - Follow recommended patterns
- [API Reference](../api/) - Detailed API documentation

## Resources

- 📚 [Configuration Examples](https://github.com/micro-core/examples/tree/main/configurations)
- 🔧 [Configuration Generator](https://micro-core.dev/config-generator)
- 💬 [Community Discussions](https://github.com/micro-core/micro-core/discussions)
- 📧 [Support](mailto:<EMAIL>)
