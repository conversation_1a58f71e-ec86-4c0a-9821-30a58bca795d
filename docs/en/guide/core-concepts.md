# Core Concepts

This guide covers the fundamental concepts you need to understand to work effectively with Micro-Core.

## Architecture Overview

Micro-Core follows a layered architecture with clear separation of concerns:

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core Layered Architecture               │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                  Application Layer                          │ │
│  │                                                             │ │
│  │  Your micro-applications built with React, Vue, Angular,    │ │
│  │  or any other framework. Each app is independent and        │ │
│  │  can be developed, tested, and deployed separately.         │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                │                                │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                   Adapter Layer                             │ │
│  │                                                             │ │
│  │  Framework-specific adapters that bridge between your       │ │
│  │  applications and the Micro-Core kernel. Handle lifecycle,  │ │
│  │  props, and framework-specific concerns.                    │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                │                                │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Plugin Layer                             │ │
│  │                                                             │ │
│  │  Extensible plugins that add functionality like routing,    │ │
│  │  communication, authentication, and more. Can be official   │ │
│  │  or custom plugins.                                         │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                │                                │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                   Micro-Core Kernel                         │ │
│  │                                                             │ │
│  │  The core runtime that manages applications, provides       │ │
│  │  sandboxing, handles loading, and coordinates between       │ │
│  │  different parts of the system.                             │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 1. Micro-Applications

### What is a Micro-Application?

A micro-application is an independent frontend application that:

- Has its own codebase and repository
- Can be developed and deployed independently
- Runs in isolation from other applications
- Communicates with other applications through well-defined APIs

### Micro-Application Structure

```typescript
// A typical micro-application exports lifecycle functions
export interface MicroApplication {
  // Optional: Bootstrap the application
  bootstrap?: (props?: any) => Promise<void>;
  
  // Required: Mount the application
  mount: (props?: any) => Promise<void>;
  
  // Required: Unmount the application
  unmount: (props?: any) => Promise<void>;
  
  // Optional: Update the application
  update?: (props?: any) => Promise<void>;
}
```

### Example Micro-Application

```typescript
// user-management/src/index.ts
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

let root: ReactDOM.Root | null = null;

export async function bootstrap() {
  console.log('User management app bootstrapped');
}

export async function mount(props: any) {
  const container = props.container || document.getElementById('user-app');
  root = ReactDOM.createRoot(container);
  root.render(<App {...props} />);
}

export async function unmount() {
  if (root) {
    root.unmount();
    root = null;
  }
}

export async function update(props: any) {
  // Handle prop updates
  if (root) {
    root.render(<App {...props} />);
  }
}
```

## 2. Application Lifecycle

### Lifecycle States

Every micro-application goes through several states:

```typescript
enum ApplicationStatus {
  NOT_LOADED = 'NOT_LOADED',       // Initial state
  LOADING = 'LOADING',             // Loading resources
  LOAD_ERROR = 'LOAD_ERROR',       // Failed to load
  LOADED = 'LOADED',               // Resources loaded
  BOOTSTRAPPING = 'BOOTSTRAPPING', // Running bootstrap
  NOT_MOUNTED = 'NOT_MOUNTED',     // Ready to mount
  MOUNTING = 'MOUNTING',           // Mounting process
  MOUNTED = 'MOUNTED',             // Successfully mounted
  UPDATING = 'UPDATING',           // Updating props
  UNMOUNTING = 'UNMOUNTING',       // Unmounting process
  SKIP_BECAUSE_BROKEN = 'SKIP_BECAUSE_BROKEN' // Broken, skip
}
```

### Lifecycle Flow

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Application Lifecycle Flow                   │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    load()    ┌─────────────┐                   │
│  │ NOT_LOADED  │─────────────▶│   LOADING   │                   │
│  └─────────────┘              └──────┬──────┘                   │
│                                       │                         │
│                                       ▼                         │
│                               ┌─────────────┐                   │
│                          ┌────│   LOADED    │                   │
│                          │    └──────┬──────┘                   │
│                          │           │ bootstrap()              │
│                          │           ▼                         │
│                          │    ┌─────────────┐                   │
│                          │    │BOOTSTRAPPING│                   │
│                          │    └──────┬──────┘                   │
│                          │           │                         │
│                          │           ▼                         │
│  ┌─────────────┐         │    ┌─────────────┐    mount()       │
│  │ LOAD_ERROR  │◀────────┘    │ NOT_MOUNTED │─────────────┐    │
│  └─────────────┘              └─────────────┘             │    │
│                                       ▲                   │    │
│                                       │ unmount()         ▼    │
│                                       │            ┌─────────────┐│
│                                ┌─────────────┐     │  MOUNTING   ││
│                                │ UNMOUNTING  │◀────┤             ││
│                                └─────────────┘     └──────┬──────┘│
│                                                           │       │
│                                                           ▼       │
│                                                    ┌─────────────┐│
│                                                    │   MOUNTED   ││
│                                                    └──────┬──────┘│
│                                                           │       │
│                                                           ▼       │
│                                                    ┌─────────────┐│
│                                                    │  UPDATING   ││
│                                                    └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

### Lifecycle Hooks

You can hook into the lifecycle at various points:

```typescript
microCore.registerApp({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#user-app',
  activeWhen: '/users',
  
  // Lifecycle hooks
  beforeLoad: async (app) => {
    console.log('About to load', app.name);
    // Prepare for loading
  },
  
  afterLoad: async (app) => {
    console.log('Loaded', app.name);
    // Post-load setup
  },
  
  beforeMount: async (app) => {
    console.log('About to mount', app.name);
    // Prepare container, set props
  },
  
  afterMount: async (app) => {
    console.log('Mounted', app.name);
    // Post-mount setup
  },
  
  beforeUnmount: async (app) => {
    console.log('About to unmount', app.name);
    // Cleanup before unmount
  },
  
  afterUnmount: async (app) => {
    console.log('Unmounted', app.name);
    // Final cleanup
  }
});
```

## 3. Sandbox Isolation

### Why Sandboxing?

Sandboxing ensures that micro-applications don't interfere with each other:

- **JavaScript Isolation**: Prevent global variable conflicts
- **CSS Isolation**: Prevent style bleeding between applications
- **DOM Isolation**: Prevent DOM manipulation conflicts
- **Event Isolation**: Prevent event listener conflicts

### Sandbox Types

#### Proxy Sandbox (Recommended)

Uses ES6 Proxy to intercept global variable access:

```typescript
const microCore = new MicroCore({
  sandbox: {
    type: 'proxy',
    strict: true,
    whitelist: ['console', 'fetch'],
    blacklist: ['eval', 'Function']
  }
});
```

#### Iframe Sandbox

Provides maximum isolation using iframe:

```typescript
const microCore = new MicroCore({
  sandbox: {
    type: 'iframe',
    allowSameOrigin: false,
    allowScripts: true
  }
});
```

#### WebComponent Sandbox

Uses Shadow DOM for style isolation:

```typescript
const microCore = new MicroCore({
  sandbox: {
    type: 'webcomponent',
    shadowDOM: true,
    styleIsolation: true
  }
});
```

## 4. Communication

### Communication Methods

Micro-Core provides several ways for applications to communicate:

#### Event Bus

Publish-subscribe pattern for loose coupling:

```typescript
// Publisher
EventBus.emit('user:login', { userId: '123', name: 'John' });

// Subscriber
EventBus.on('user:login', (user) => {
  console.log('User logged in:', user);
});
```

#### Global State

Reactive state management:

```typescript
// Set global state
GlobalState.set('user', { id: '123', name: 'John' });

// Get global state
const user = GlobalState.get('user');

// Watch for changes
GlobalState.watch('user', (newUser, oldUser) => {
  console.log('User changed:', newUser);
});
```

#### Direct Communication

Direct method calls between applications:

```typescript
// Get application instance
const userApp = microCore.getApp('user-management');

// Call method directly
await userApp.updateUserProfile({ name: 'Jane' });
```

### Communication Patterns

#### 1. Event-Driven Architecture

```typescript
// User app emits events
EventBus.emit('user:profile:updated', { userId, profile });

// Other apps listen for events
EventBus.on('user:profile:updated', ({ userId, profile }) => {
  // Update local cache
  updateUserCache(userId, profile);
});
```

#### 2. Shared State Pattern

```typescript
// Shared shopping cart state
GlobalState.set('cart', {
  items: [],
  total: 0
});

// Product app adds items
const addToCart = (product) => {
  const cart = GlobalState.get('cart');
  cart.items.push(product);
  cart.total += product.price;
  GlobalState.set('cart', cart);
};

// Cart app displays items
GlobalState.watch('cart', (cart) => {
  updateCartDisplay(cart);
});
```

## 5. Routing

### Route-Based Activation

Applications are activated based on routes:

```typescript
microCore.registerApp({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#content',
  
  // Simple string match
  activeWhen: '/users',
  
  // Array of routes
  activeWhen: ['/users', '/profile'],
  
  // Function-based matching
  activeWhen: (location) => {
    return location.pathname.startsWith('/users');
  },
  
  // Regular expression
  activeWhen: /^\/users/
});
```

### Route Configuration

```typescript
const microCore = new MicroCore({
  router: {
    mode: 'browser', // or 'hash'
    base: '/',
    
    // Route guards
    guards: {
      beforeEach: async (to, from, next) => {
        if (to.meta?.requiresAuth && !isAuthenticated()) {
          next('/login');
          return;
        }
        next();
      }
    }
  }
});
```

### Dynamic Routing

Register routes at runtime:

```typescript
// Register new route
microCore.addRoute({
  path: '/admin',
  app: 'admin-app',
  meta: { requiresAuth: true, role: 'admin' }
});

// Remove route
microCore.removeRoute('/admin');
```

## 6. Framework Adapters

### Purpose of Adapters

Adapters bridge the gap between Micro-Core and specific frameworks:

- Handle framework-specific lifecycle
- Manage props and state
- Provide framework-specific utilities
- Ensure proper cleanup

### Using Adapters

```typescript
import { MicroCore } from '@micro-core/core';
import { ReactAdapter } from '@micro-core/adapter-react';
import { VueAdapter } from '@micro-core/adapter-vue';

const microCore = new MicroCore({
  container: '#app',
  adapters: [
    new ReactAdapter(),
    new VueAdapter()
  ]
});
```

### Custom Adapters

You can create custom adapters for other frameworks:

```typescript
import { BaseAdapter } from '@micro-core/core';

class SvelteAdapter extends BaseAdapter {
  name = 'svelte';
  
  async mount(app: Application, props: any) {
    // Svelte-specific mounting logic
    const { default: App } = await import(app.entry);
    const svelteApp = new App({
      target: app.container,
      props
    });
    
    app.instance = svelteApp;
  }
  
  async unmount(app: Application) {
    if (app.instance) {
      app.instance.$destroy();
      app.instance = null;
    }
  }
  
  async update(app: Application, props: any) {
    if (app.instance) {
      app.instance.$set(props);
    }
  }
}
```

## 7. Plugin System

### Plugin Architecture

Plugins extend Micro-Core functionality:

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Plugin System Architecture                   │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Plugin Registry                          │ │
│  │                                                             │ │
│  │  Manages plugin lifecycle, dependencies, and execution      │ │
│  │  order. Provides hooks and events for plugin interaction.   │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                │                                │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Core Plugins                             │ │
│  │                                                             │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │Router Plugin│  │Comm Plugin  │  │  Auth Plugin        │ │ │
│  │  │             │  │             │  │                     │ │ │
│  │  │ • Routes    │  │ • EventBus  │  │ • Authentication    │ │ │
│  │  │ • Guards    │  │ • State     │  │ • Authorization     │ │ │
│  │  │ • Cache     │  │ • Messages  │  │ • Permissions       │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                │                                │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Custom Plugins                           │ │
│  │                                                             │ │
│  │  User-defined plugins that extend functionality for         │ │
│  │  specific use cases, integrations, or business logic.       │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### Using Plugins

```typescript
import { MicroCore } from '@micro-core/core';
import { RouterPlugin } from '@micro-core/plugin-router';
import { CommunicationPlugin } from '@micro-core/plugin-communication';

const microCore = new MicroCore();

// Use official plugins
microCore.use(RouterPlugin, {
  mode: 'browser',
  base: '/'
});

microCore.use(CommunicationPlugin, {
  eventBus: {
    maxListeners: 100
  },
  globalState: {
    persistence: true
  }
});
```

### Creating Custom Plugins

```typescript
import { BasePlugin } from '@micro-core/core';

class AnalyticsPlugin extends BasePlugin {
  name = 'analytics';
  
  install(microCore: MicroCore, options: any) {
    // Plugin initialization
    this.setupAnalytics(options);
    
    // Hook into lifecycle events
    microCore.on('app:mounted', (app) => {
      this.trackEvent('app_mounted', { appName: app.name });
    });
    
    microCore.on('route:changed', (route) => {
      this.trackPageView(route.path);
    });
  }
  
  private setupAnalytics(options: any) {
    // Initialize analytics service
  }
  
  private trackEvent(event: string, data: any) {
    // Track custom event
  }
  
  private trackPageView(path: string) {
    // Track page view
  }
}

// Use custom plugin
microCore.use(AnalyticsPlugin, {
  apiKey: 'your-analytics-key'
});
```

## 8. Configuration

### Basic Configuration

```typescript
const microCore = new MicroCore({
  // Container element
  container: '#app',
  
  // Framework adapters
  adapters: [
    new ReactAdapter(),
    new VueAdapter()
  ],
  
  // Sandbox configuration
  sandbox: {
    type: 'proxy',
    strict: true
  },
  
  // Router configuration
  router: {
    mode: 'browser',
    base: '/'
  },
  
  // Performance settings
  performance: {
    prefetch: true,
    preload: ['critical-app'],
    cache: {
      enabled: true,
      maxAge: 30 * 60 * 1000
    }
  }
});
```

### Advanced Configuration

```typescript
const microCore = new MicroCore({
  container: '#app',
  adapters: [new ReactAdapter()],
  
  // Advanced sandbox settings
  sandbox: {
    type: 'proxy',
    strict: true,
    whitelist: ['console', 'fetch'],
    blacklist: ['eval', 'Function'],
    css: {
      isolation: 'scoped',
      prefixing: true
    }
  },
  
  // Advanced router settings
  router: {
    mode: 'browser',
    base: '/',
    caseSensitive: false,
    guards: {
      beforeEach: async (to, from, next) => {
        // Global route guard
        if (to.meta?.requiresAuth && !isAuthenticated()) {
          next('/login');
          return;
        }
        next();
      }
    }
  },
  
  // Communication settings
  communication: {
    eventBus: {
      maxListeners: 100,
      enableDebug: false
    },
    globalState: {
      persistence: {
        enabled: true,
        storage: 'localStorage',
        prefix: 'micro-core-'
      }
    }
  },
  
  // Error handling
  errorHandler: {
    onLoadError: (error, app) => {
      console.error(`Failed to load ${app.name}:`, error);
    },
    onMountError: (error, app) => {
      console.error(`Failed to mount ${app.name}:`, error);
    }
  },
  
  // Development settings
  development: {
    enableDebug: process.env.NODE_ENV === 'development',
    showPerformanceMetrics: true,
    enableHotReload: true
  }
});
```

## 9. Performance Concepts

### Lazy Loading

Applications are loaded only when needed:

```typescript
microCore.registerApp({
  name: 'heavy-app',
  entry: 'http://localhost:3001',
  container: '#content',
  activeWhen: '/heavy',
  
  // Lazy loading configuration
  loading: {
    strategy: 'lazy',
    preload: false,
    timeout: 10000
  }
});
```

### Preloading

Critical applications can be preloaded:

```typescript
const microCore = new MicroCore({
  performance: {
    preload: {
      // Preload on idle
      idle: ['user-app', 'navigation-app'],
      
      // Preload on hover
      hover: ['.nav-link[href="/users"]'],
      
      // Preload based on probability
      predictive: {
        enabled: true,
        threshold: 0.8
      }
    }
  }
});
```

### Caching

Multiple caching layers for optimal performance:

```typescript
const microCore = new MicroCore({
  cache: {
    // Memory cache
    memory: {
      enabled: true,
      maxSize: 50 * 1024 * 1024, // 50MB
      ttl: 30 * 60 * 1000 // 30 minutes
    },
    
    // HTTP cache
    http: {
      enabled: true,
      headers: {
        'Cache-Control': 'public, max-age=31536000'
      }
    },
    
    // Service Worker cache
    serviceWorker: {
      enabled: true,
      strategy: 'cache-first'
    }
  }
});
```

## 10. Error Handling

### Error Boundaries

Micro-Core provides error boundaries for each application:

```typescript
microCore.registerApp({
  name: 'user-app',
  entry: 'http://localhost:3001',
  container: '#content',
  activeWhen: '/users',
  
  // Error handling
  errorBoundary: {
    enabled: true,
    fallback: (error, app) => {
      return `
        <div class="error-boundary">
          <h2>Something went wrong in ${app.name}</h2>
          <p>${error.message}</p>
          <button onclick="location.reload()">Reload</button>
        </div>
      `;
    }
  }
});
```

### Global Error Handling

```typescript
const microCore = new MicroCore({
  errorHandler: {
    onLoadError: (error, app) => {
      // Handle load errors
      console.error(`Load error in ${app.name}:`, error);
      
      // Show user-friendly message
      showNotification({
        type: 'error',
        message: `Failed to load ${app.name}. Please try again.`
      });
    },
    
    onMountError: (error, app) => {
      // Handle mount errors
      console.error(`Mount error in ${app.name}:`, error);
      
      // Show fallback UI
      showFallbackUI(app.container, {
        title: 'Service Unavailable',
        message: 'This service is temporarily unavailable.'
      });
    },
    
    onRuntimeError: (error, app) => {
      // Handle runtime errors
      console.error(`Runtime error in ${app.name}:`, error);
      
      // Report to error tracking service
      errorTracker.captureException(error, {
        tags: { appName: app.name }
      });
    }
  }
});
```

## 11. Development vs Production

### Development Configuration

```typescript
const developmentConfig = {
  container: '#app',
  adapters: [new ReactAdapter()],
  
  // Development-specific settings
  development: {
    enableDebug: true,
    showPerformanceMetrics: true,
    enableHotReload: true,
    mockData: true
  },
  
  // Relaxed sandbox for development
  sandbox: {
    type: 'proxy',
    strict: false,
    whitelist: ['console', 'fetch', 'debugger']
  },
  
  // Development error handling
  errorHandler: {
    onLoadError: (error, app) => {
      console.error('Load error:', error);
      // Show detailed error in development
    }
  }
};
```

### Production Configuration

```typescript
const productionConfig = {
  container: '#app',
  adapters: [new ReactAdapter()],
  
  // Production-specific settings
  production: {
    enableDebug: false,
    minifyOutput: true,
    enableCompression: true
  },
  
  // Strict sandbox for production
  sandbox: {
    type: 'proxy',
    strict: true,
    whitelist: ['console', 'fetch'],
    blacklist: ['eval', 'Function', 'debugger']
  },
  
  // Production error handling
  errorHandler: {
    onLoadError: (error, app) => {
      // Log to monitoring service
      errorTracker.captureException(error);
      
      // Show user-friendly message
      showErrorMessage('Service temporarily unavailable');
    }
  },
  
  // Performance optimizations
  performance: {
    prefetch: true,
    preload: ['critical-app'],
    cache: {
      enabled: true,
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
  }
};
```

## Best Practices Summary

1. **Clear Boundaries**: Define clear responsibilities for each micro-application
2. **Loose Coupling**: Use events and shared state for communication
3. **Proper Isolation**: Choose appropriate sandbox type for your needs
4. **Performance**: Implement lazy loading and caching strategies
5. **Error Handling**: Provide graceful fallbacks and error boundaries
6. **Testing**: Test applications both in isolation and integration
7. **Monitoring**: Implement proper logging and error tracking

## Next Steps

Now that you understand the core concepts:

1. **[Getting Started](./getting-started.md)** - Build your first application
2. **[Configuration](./configuration.md)** - Learn detailed configuration options
3. **[Features](./features/)** - Explore specific features in depth
4. **[Best Practices](./best-practices/)** - Follow recommended patterns
5. **[Examples](../examples/)** - See real-world implementations

## Resources

- 📚 [API Reference](../api/) - Detailed API documentation
- 🎯 [Examples Repository](https://github.com/micro-core/examples)
- 💬 [Community Discussions](https://github.com/micro-core/micro-core/discussions)
- 📧 [Support](mailto:<EMAIL>)
