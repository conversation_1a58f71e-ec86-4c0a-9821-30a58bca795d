# User Guide

Welcome to the Micro-Core User Guide! This guide will help you learn and master the Micro-Core micro-frontend framework from scratch, from basic concepts to advanced applications, step by step building modern micro-frontend applications.

## 📚 Learning Path

### 🚀 Quick Start (1-2 Days)

Suitable for developers new to micro-frontends, quickly understand basic concepts and usage methods.

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Quick Start Learning Path                     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐ │
│  │ 1. Framework│───▶│ 2. Environment│───▶│ 3. First App        │ │
│  │    Intro    │    │    Setup     │    │ • Hello World       │ │
│  │ • What is MF│    │ • Install    │    │ • Basic Config      │ │
│  │ • Core Concepts│  │ • Project Init│   │ • Run & Debug       │ │
│  │ • Use Cases │    │ • Dev Tools  │    │                     │ │
│  └─────────────┘    └─────────────┘    └─────────────────────┘ │
│                                                   │             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐ │
│  │ 6. Deploy   │◀───│ 5. App Comm │◀───│ 4. Multi-App        │ │
│  │ • Build     │    │ • Event Bus │    │ • Register Apps     │ │
│  │ • Deploy    │    │ • State Share│    │ • Route Config      │ │
│  │ • Monitor   │    │ • Data Pass │    │ • Lifecycle Mgmt    │ │
│  └─────────────┘    └─────────────┘    └─────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

- **[Framework Introduction](./introduction.md)** - Understand Micro-Core's design philosophy and core advantages
- **[Getting Started](./getting-started.md)** - Build your first micro-frontend app in 5 minutes
- **[Core Concepts](./core-concepts.md)** - Master basic micro-frontend concepts and terminology

### 🏗️ Deep Understanding (3-5 Days)

Learn Micro-Core's core features and advanced capabilities in depth.

- **[Application Management](./features/app-management.md)** - App registration, lifecycle, state management
- **[Routing System](./features/routing.md)** - Route configuration, navigation control, nested routes
- **[Sandbox Isolation](./features/sandbox.md)** - JavaScript sandbox, CSS isolation, security mechanisms
- **[Application Communication](./features/communication.md)** - Event bus, state sharing, message passing
- **[Plugin System](./plugin-system.md)** - Plugin development, feature extension, ecosystem integration

### 🚀 Advanced Applications (1-2 Weeks)

Master enterprise-level application development and performance optimization techniques.

- **[Advanced Features](./advanced/)** - Sidecar pattern, preloading, middleware system
- **[Performance Optimization](./performance.md)** - Loading optimization, caching strategies, monitoring analysis
- **[Best Practices](./best-practices/)** - Architecture design, development standards, deployment strategies
- **[Troubleshooting](./troubleshooting.md)** - Common issues, debugging techniques, solutions

## 🎯 Core Features Overview

### Micro-Kernel Architecture

Micro-Core adopts a micro-kernel architecture design with streamlined and efficient core functionality:

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core Architecture                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│                    ┌─────────────────────┐                     │
│                    │   Application Layer │                     │
│                    │ React │ Vue │Angular │                     │
│                    └─────────────────────┘                     │
│                              │                                 │
│                    ┌─────────────────────┐                     │
│                    │   Adapter Layer     │                     │
│                    │ Adapters & Plugins  │                     │
│                    └─────────────────────┘                     │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                 Micro-Kernel (Micro-Core)               │   │
│  │                                                         │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │   │
│  │  │ App Manager │ │ Router Sys  │ │ Communication   │   │   │
│  │  │ • Register  │ │ • Navigate  │ │ • Event Bus     │   │   │
│  │  │ • Lifecycle │ │ • Guards    │ │ • State Mgmt    │   │   │
│  │  │ • State     │ │ • Cache     │ │ • Message Pass  │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────────┘   │   │
│  │                                                         │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │   │
│  │  │ Sandbox Sys │ │ Loader      │ │ Error Handler   │   │   │
│  │  │ • JS Isolate│ │ • Resource  │ │ • Exception     │   │   │
│  │  │ • CSS Isolate│ │ • Cache Mgmt│ │ • Degradation   │   │   │
│  │  │ • DOM Isolate│ │ • Preload   │ │ • Error Boundary│   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────────┘   │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                    ┌─────────────────────┐                     │
│                    │   Runtime Env       │                     │
│                    │ Browser │ Node.js   │                     │
│                    └─────────────────────┘                     │
└─────────────────────────────────────────────────────────────────┘
```

### Multi-Sandbox Support

Provides multiple sandbox isolation solutions to meet different scenario requirements:

| Sandbox Type | Isolation Level | Performance | Compatibility | Use Cases |
|--------------|-----------------|-------------|---------------|-----------|
| **Proxy Sandbox** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | Modern browsers, high performance |
| **Iframe Sandbox** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | Strong isolation, high security |
| **WebComponent Sandbox** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | Style isolation, component dev |
| **DefineProperty Sandbox** | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | High compatibility requirements |

### Framework Adapters

Supports mainstream frontend frameworks for seamless integration:

::: code-group

```typescript [React Adapter]
import { ReactAdapter } from '@micro-core/adapter-react'

const reactAdapter = new ReactAdapter({
  // React specific configuration
  strictMode: true,
  suspense: true,
  errorBoundary: true,
  
  // Lifecycle hooks
  beforeMount: (app) => {
    console.log('React app about to mount:', app.name)
  },
  
  afterMount: (app) => {
    console.log('React app mounted:', app.name)
  }
})
```

```typescript [Vue Adapter]
import { VueAdapter } from '@micro-core/adapter-vue'

const vueAdapter = new VueAdapter({
  // Vue specific configuration
  version: 3, // Vue 2 or Vue 3
  devtools: true,
  
  // Global configuration
  globalProperties: {
    $microCore: microCore
  },
  
  // Lifecycle hooks
  beforeMount: (app) => {
    console.log('Vue app about to mount:', app.name)
  }
})
```

```typescript [Angular Adapter]
import { AngularAdapter } from '@micro-core/adapter-angular'

const angularAdapter = new AngularAdapter({
  // Angular specific configuration
  zone: true,
  enableProdMode: false,
  
  // Dependency injection
  providers: [
    { provide: 'MICRO_CORE', useValue: microCore }
  ],
  
  // Lifecycle hooks
  beforeBootstrap: (app) => {
    console.log('Angular app about to bootstrap:', app.name)
  }
})
```

:::

## 🛠️ Development Tools

### CLI Tools

```bash
# Install CLI tools
npm install -g @micro-core/cli

# Create new project
micro-core create my-project --template react-vue

# Add micro-application
micro-core add app user-center --framework react

# Start development server
micro-core dev

# Build production version
micro-core build --env production
```

### Developer Panel

In development environment, Micro-Core provides powerful debugging tools:

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Developer Panel Features                      │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │ App Inspector   │    │ Performance     │    │ Event Tracker   ││
│  │                 │    │ Monitor         │    │                 ││
│  │ • App List      │    │ • Load Time     │    │ • Event Log     ││
│  │ • State View    │    │ • Memory Usage  │    │ • Comm Records  ││
│  │ • Lifecycle     │    │ • Performance   │    │ • State Changes ││
│  │ • Config Info   │    │ • Bottleneck    │    │ • Error Tracking││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │ Route Debugger  │    │ Sandbox Monitor │    │ Network Panel   ││
│  │                 │    │                 │    │                 ││
│  │ • Route History │    │ • Isolation     │    │ • Resource Load ││
│  │ • Navigation    │    │ • Global Vars   │    │ • Request Monitor││
│  │ • Guard Exec    │    │ • Memory Leaks  │    │ • Cache Status  ││
│  │ • Param Parse   │    │ • Security Check│    │ • Error Logs    ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

## 📖 Documentation Navigation

### Basic Tutorials

- **[Installation](./installation.md)** - Environment requirements, installation steps, project configuration
- **[Getting Started](./getting-started.md)** - 5-minute quick start guide
- **[Core Concepts](./core-concepts.md)** - Basic micro-frontend concepts and terminology
- **[Build Integration](./build-integration.md)** - Integration configuration with build tools

### Core Features

- **[Application Management](./features/app-management.md)** - App registration, lifecycle, state management
- **[Routing System](./features/routing.md)** - Route configuration, navigation control, nested routes
- **[Sandbox Isolation](./features/sandbox.md)** - JavaScript sandbox, CSS isolation, security mechanisms
- **[Application Communication](./features/communication.md)** - Event bus, state sharing, message passing
- **[State Management](./features/state-management.md)** - Global state, reactive updates, persistence
- **[Lifecycle](./features/lifecycle.md)** - Application lifecycle hooks and management

### Advanced Features

- **[Plugin System](./plugin-system.md)** - Plugin development, feature extension, ecosystem integration
- **[Middleware](./middleware.md)** - Middleware mechanism, custom middleware development
- **[Sidecar Pattern](./advanced/sidecar-mode.md)** - Sidecar architecture, independent deployment
- **[Prefetch Strategy](./advanced/prefetch.md)** - Smart preloading, performance optimization
- **[Adapter Development](./advanced/adapters.md)** - Custom framework adapters
- **[Loader System](./advanced/loaders.md)** - Resource loading, cache management

### Best Practices

- **[Architecture Design](./best-practices/architecture.md)** - Micro-frontend architecture design principles
- **[Performance Optimization](./best-practices/performance.md)** - Loading optimization, runtime optimization
- **[Error Handling](./best-practices/error-handling.md)** - Exception handling, degradation strategies
- **[Testing Strategy](./best-practices/testing.md)** - Unit testing, integration testing, E2E testing
- **[Deployment Strategy](./best-practices/deployment.md)** - Deployment solutions, CI/CD integration

### Tools and Ecosystem

- **[Build Tools](./build-integration.md)** - Vite, Webpack, Rollup integration
- **[Development Tools](./advanced/build-integration.md)** - CLI tools, debug panel
- **[Performance Monitoring](./performance.md)** - Performance analysis, monitoring alerts
- **[Troubleshooting](./troubleshooting.md)** - Common issues, debugging techniques

## 🎮 Practical Examples

### Basic Example

```typescript
// Simple micro-frontend application
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  container: '#app'
})

// Register React application
microCore.registerApp({
  name: 'header',
  entry: 'http://localhost:3001',
  container: '#header',
  activeWhen: () => true // Always active
})

// Register Vue application
microCore.registerApp({
  name: 'main',
  entry: 'http://localhost:3002', 
  container: '#main',
  activeWhen: '/main'
})

microCore.start()
```

### Enterprise Configuration

```typescript
// Enterprise-level micro-frontend configuration
import { MicroCore } from '@micro-core/core'
import { ReactAdapter } from '@micro-core/adapter-react'
import { VueAdapter } from '@micro-core/adapter-vue'
import { RouterPlugin } from '@micro-core/plugin-router'
import { AuthPlugin } from '@micro-core/plugin-auth'

const microCore = new MicroCore({
  // Container configuration
  container: '#micro-app-container',
  
  // Sandbox configuration
  sandbox: {
    type: 'proxy',
    css: true,
    js: true,
    globalWhitelist: ['console', 'location']
  },
  
  // Performance configuration
  prefetch: {
    idle: ['user-center', 'order-system'],
    hover: ['admin-panel']
  },
  
  // Error handling
  errorHandler: {
    onJSError: (error, app) => {
      console.error('JS Error:', error)
      // Error reporting
      reportError(error, app)
    }
  }
})

// Register adapters
microCore.registerAdapter('react', new ReactAdapter())
microCore.registerAdapter('vue', new VueAdapter())

// Use plugins
microCore.use(RouterPlugin, {
  mode: 'history',
  base: '/app'
})

microCore.use(AuthPlugin, {
  loginUrl: '/auth/login',
  tokenStorage: 'localStorage'
})

// Register applications
microCore.registerApps([
  {
    name: 'user-center',
    entry: 'http://localhost:3001',
    activeWhen: '/user',
    props: { theme: 'dark' }
  },
  {
    name: 'order-system', 
    entry: 'http://localhost:3002',
    activeWhen: '/order',
    beforeMount: async (app) => {
      // Permission check
      await checkPermission('order:read')
    }
  }
])

microCore.start()
```

## 🚀 Next Steps

Choose the learning path that suits you:

### 🔰 Beginner
1. Read [Framework Introduction](./introduction.md) to understand basic concepts
2. Follow [Getting Started](./getting-started.md) to build your first app
3. Learn [Core Concepts](./core-concepts.md) to master fundamentals

### 🏗️ Deep Learning
1. Explore [Core Features](./features/) to understand each module
2. Learn [Plugin System](./plugin-system.md) to extend functionality
3. Master [Advanced Features](./advanced/) to improve skills

### 🚀 Practical Application
1. Reference [Best Practices](./best-practices/) for architecture design
2. Check [Example Projects](../examples/) for practical learning
3. Read [Troubleshooting](./troubleshooting.md) to solve problems

## 💬 Getting Help

If you encounter problems during learning:

- 📖 Check [API Documentation](../api/) for detailed explanations
- 🔍 Search [GitHub Issues](https://github.com/micro-core/micro-core/issues) for solutions
- 💬 Ask questions in [Discussions](https://github.com/micro-core/micro-core/discussions)
- 📧 Send <NAME_EMAIL> for technical support

Let's start the Micro-Core learning journey! 🎉