# Best Practices

This guide summarizes best practices for developing micro-frontend applications with Micro-Core, helping you build high-quality, maintainable micro-frontend systems.

## Architecture Design Principles

### 1. Single Responsibility Principle

Each micro-application should focus on one business domain:

```typescript
// ✅ Good design
const userManagementApp = {
  name: 'user-management',
  routes: ['/users/*'],
  responsibilities: ['User CRUD', 'Permission Management', 'User Authentication']
}

const orderManagementApp = {
  name: 'order-management', 
  routes: ['/orders/*'],
  responsibilities: ['Order Processing', 'Payment Management', 'Logistics Tracking']
}

// ❌ Avoid this design
const monolithApp = {
  name: 'everything-app',
  routes: ['/*'],
  responsibilities: ['User Management', 'Order Processing', 'Product Management', 'Payment', 'Logistics...']
}
```

### 2. Loose Coupling Design

Micro-applications should communicate through standardized interfaces:

```typescript
// ✅ Communicate through event bus
import { EventBus } from '@micro-core/core'

// Send event
EventBus.emit('user:login', { userId: '123', userName: 'John' })

// Listen to event
EventBus.on('user:login', (userData) => {
  // Handle user login event
})

// ❌ Avoid direct method calls to other apps
// window.userApp.login() // Tight coupling, hard to maintain
```

### 3. Progressive Migration

Migration from monolithic applications to micro-frontends should be progressive:

```typescript
// Phase 1: Route-level splitting
const migrationPlan = {
  phase1: {
    routes: ['/admin/*'],
    strategy: 'iframe' // Minimal risk
  },
  phase2: {
    routes: ['/dashboard/*'],
    strategy: 'sandbox' // Better integration
  },
  phase3: {
    routes: ['/users/*', '/orders/*'],
    strategy: 'native' // Full integration
  }
}
```

## Application Splitting Strategy

### Split by Business Domain

```typescript
// E-commerce system splitting example
const microApps = [
  {
    name: 'product-catalog',
    domain: 'Product Management',
    routes: ['/products/*', '/categories/*'],
    team: 'product-team'
  },
  {
    name: 'user-center',
    domain: 'User Center', 
    routes: ['/profile/*', '/settings/*'],
    team: 'user-team'
  },
  {
    name: 'order-system',
    domain: 'Order System',
    routes: ['/orders/*', '/checkout/*'],
    team: 'order-team'
  }
]
```

### Split by Technology Stack

```typescript
// Mixed technology stack example
const techStackApps = [
  {
    name: 'legacy-admin',
    framework: 'jQuery + Bootstrap',
    strategy: 'iframe', // Isolate legacy system
    routes: ['/legacy-admin/*']
  },
  {
    name: 'modern-dashboard',
    framework: 'React + Ant Design',
    strategy: 'sandbox',
    routes: ['/dashboard/*']
  },
  {
    name: 'mobile-app',
    framework: 'Vue 3 + Vant',
    strategy: 'native',
    routes: ['/mobile/*']
  }
]
```

## State Management Best Practices

### 1. State Isolation

```typescript
// ✅ Each app manages its own state
class UserApp {
  private store = new Vuex.Store({
    state: {
      currentUser: null,
      permissions: []
    }
  })
  
  // Share necessary state through events
  onUserLogin(user) {
    this.store.commit('setCurrentUser', user)
    EventBus.emit('user:login', { userId: user.id })
  }
}

// ❌ Avoid global state pollution
// window.globalState = {} // Can cause state confusion
```

### 2. Shared State Management

```typescript
// Use Micro-Core's state management
import { GlobalState } from '@micro-core/core'

// Define shared state
const sharedState = GlobalState.create({
  user: {
    id: null,
    name: '',
    permissions: []
  },
  theme: 'light',
  locale: 'en-US'
})

// Use shared state in application
class OrderApp {
  mounted() {
    // Subscribe to user state changes
    sharedState.subscribe('user', (user) => {
      this.updateUserContext(user)
    })
  }
}
```

## Route Management

### 1. Route Planning

```typescript
// ✅ Clear route planning
const routeConfig = {
  '/': 'main-app',           // Main application
  '/users/*': 'user-app',    // User management
  '/orders/*': 'order-app',  // Order management
  '/admin/*': 'admin-app',   // Admin panel
  '/mobile/*': 'mobile-app'  // Mobile app
}

// ❌ Avoid route conflicts
const badRouteConfig = {
  '/admin/*': 'admin-app',
  '/admin/users/*': 'user-app' // Conflict!
}
```

### 2. Route Guards

```typescript
// Global route guards
import { Router } from '@micro-core/plugin-router'

Router.beforeEach(async (to, from, next) => {
  // Permission check
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login')
    return
  }
  
  // Preload application
  if (to.meta.preload) {
    await preloadApp(to.meta.app)
  }
  
  next()
})
```

## Style Isolation

### 1. CSS Isolation Strategy

```typescript
// Configure style isolation
const sandboxConfig = {
  css: {
    isolation: 'scoped', // 'scoped' | 'shadow' | 'prefix'
    prefix: 'micro-app-', // Prefix isolation
    excludes: [
      'antd', // Exclude third-party libraries
      'element-ui'
    ]
  }
}
```

### 2. Style Best Practices

```css
/* ✅ Use CSS Modules */
.container {
  padding: 16px;
}

.title {
  font-size: 24px;
  color: #333;
}

/* ✅ Use CSS-in-JS */
const styles = {
  container: {
    padding: '16px'
  },
  title: {
    fontSize: '24px',
    color: '#333'
  }
}

/* ❌ Avoid global style pollution */
.container { /* May affect other apps */
  padding: 16px !important;
}
```

## Performance Optimization

### 1. Application Preloading

```typescript
// Smart preloading strategy
const preloadConfig = {
  strategy: 'predictive', // 'eager' | 'lazy' | 'predictive'
  rules: [
    {
      condition: 'user-role:admin',
      apps: ['admin-app', 'analytics-app']
    },
    {
      condition: 'route-frequency:high',
      apps: ['dashboard-app']
    }
  ]
}
```

### 2. Resource Optimization

```typescript
// Shared dependencies configuration
const sharedDependencies = {
  'react': '^18.0.0',
  'react-dom': '^18.0.0',
  'antd': '^5.0.0',
  '@micro-core/core': '^1.0.0'
}

// Webpack configuration
module.exports = {
  externals: sharedDependencies,
  optimization: {
    splitChunks: {
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        }
      }
    }
  }
}
```

## Error Handling

### 1. Global Error Handling

```typescript
// Configure global error handling
import { ErrorHandler } from '@micro-core/core'

ErrorHandler.configure({
  // Application load error
  onAppLoadError: (error, appName) => {
    console.error(`App ${appName} failed to load:`, error)
    // Show fallback UI
    showFallbackUI(appName)
  },
  
  // Runtime error
  onRuntimeError: (error, appName) => {
    console.error(`Runtime error in app ${appName}:`, error)
    // Error reporting
    reportError(error, appName)
  }
})
```

### 2. Application-Level Error Boundaries

```typescript
// React error boundary
class AppErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false }
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true }
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('Application error:', error, errorInfo)
    // Error reporting
    reportError(error, this.props.appName)
  }
  
  render() {
    if (this.state.hasError) {
      return <FallbackComponent />
    }
    
    return this.props.children
  }
}
```

## Testing Strategy

### 1. Unit Testing

```typescript
// Application unit testing
import { render, screen } from '@testing-library/react'
import { MicroApp } from './micro-app'

describe('MicroApp', () => {
  it('should render correctly', () => {
    render(<MicroApp />)
    expect(screen.getByText('Welcome')).toBeInTheDocument()
  })
  
  it('should handle events correctly', () => {
    const mockHandler = jest.fn()
    EventBus.on('test-event', mockHandler)
    
    render(<MicroApp />)
    EventBus.emit('test-event', { data: 'test' })
    
    expect(mockHandler).toHaveBeenCalledWith({ data: 'test' })
  })
})
```

### 2. Integration Testing

```typescript
// Micro-application integration testing
import { MicroCore } from '@micro-core/core'
import { createTestEnvironment } from '@micro-core/test-utils'

describe('Micro Apps Integration', () => {
  let microCore
  
  beforeEach(() => {
    const testEnv = createTestEnvironment()
    microCore = new MicroCore(testEnv.config)
  })
  
  it('should load and communicate between apps', async () => {
    // Load applications
    await microCore.loadApp('user-app')
    await microCore.loadApp('order-app')
    
    // Test inter-app communication
    const userApp = microCore.getApp('user-app')
    const orderApp = microCore.getApp('order-app')
    
    userApp.emit('user:login', { userId: '123' })
    
    // Verify order app received the event
    expect(orderApp.currentUser.id).toBe('123')
  })
})
```

## Deployment Strategy

### 1. Independent Deployment

```yaml
# Independent application deployment configuration
version: '3.8'
services:
  user-app:
    build: ./apps/user-app
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
      
  order-app:
    build: ./apps/order-app  
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=production
      
  main-app:
    build: ./apps/main-app
    ports:
      - "3000:3000"
    environment:
      - USER_APP_URL=http://user-app:3000
      - ORDER_APP_URL=http://order-app:3000
```

### 2. CDN Deployment

```typescript
// CDN resource configuration
const cdnConfig = {
  baseUrl: 'https://cdn.example.com/micro-apps/',
  apps: {
    'user-app': {
      version: '1.2.3',
      entry: 'user-app/1.2.3/index.js'
    },
    'order-app': {
      version: '2.1.0', 
      entry: 'order-app/2.1.0/index.js'
    }
  }
}
```

## Monitoring and Debugging

### 1. Performance Monitoring

```typescript
// Performance monitoring configuration
import { PerformanceMonitor } from '@micro-core/core'

PerformanceMonitor.configure({
  // Application load time monitoring
  trackAppLoad: true,
  
  // Route change monitoring
  trackRouteChange: true,
  
  // Memory usage monitoring
  trackMemoryUsage: true,
  
  // Custom metrics
  customMetrics: [
    'user-interaction-time',
    'api-response-time'
  ]
})
```

### 2. Debugging Tools

```typescript
// Development environment debugging configuration
const debugConfig = {
  devtools: true,
  logging: {
    level: 'debug',
    categories: ['router', 'sandbox', 'communication']
  },
  
  // Visual debugging panel
  debugPanel: {
    enabled: true,
    position: 'bottom-right'
  }
}
```

## Team Collaboration

### 1. Development Standards

```typescript
// Application interface specification
interface MicroAppInterface {
  // Required methods
  mount(container: HTMLElement): Promise<void>
  unmount(): Promise<void>
  
  // Optional methods
  update?(props: any): Promise<void>
  
  // Lifecycle hooks
  beforeMount?(): Promise<void>
  afterMount?(): Promise<void>
  beforeUnmount?(): Promise<void>
  afterUnmount?(): Promise<void>
}
```

### 2. Version Management

```json
{
  "name": "@company/user-app",
  "version": "1.2.3",
  "microApp": {
    "name": "user-app",
    "entry": "./dist/index.js",
    "dependencies": {
      "@micro-core/core": "^1.0.0",
      "react": "^18.0.0"
    },
    "exports": {
      "mount": "./dist/mount.js",
      "unmount": "./dist/unmount.js"
    }
  }
}
```

## Common Pitfalls and Solutions

### 1. Memory Leaks

```typescript
// ✅ Proper event cleanup
class MicroApp {
  private eventListeners = []
  
  mount() {
    const handler = this.handleEvent.bind(this)
    EventBus.on('global-event', handler)
    this.eventListeners.push(['global-event', handler])
  }
  
  unmount() {
    // Clean up event listeners
    this.eventListeners.forEach(([event, handler]) => {
      EventBus.off(event, handler)
    })
    this.eventListeners = []
  }
}
```

### 2. Style Conflicts

```typescript
// ✅ Use style isolation
const sandboxConfig = {
  css: {
    isolation: 'scoped',
    prefix: 'app-prefix-'
  }
}

// ✅ Or use Shadow DOM
const shadowConfig = {
  css: {
    isolation: 'shadow'
  }
}
```

### 3. Global Variable Pollution

```typescript
// ✅ Use sandbox isolation
const proxyConfig = {
  sandbox: {
    type: 'proxy',
    isolation: ['window', 'document', 'location']
  }
}
```

## Security Best Practices

### 1. Content Security Policy

```typescript
// Configure CSP for micro-applications
const cspConfig = {
  directives: {
    'default-src': ["'self'"],
    'script-src': ["'self'", "'unsafe-inline'"],
    'style-src': ["'self'", "'unsafe-inline'"],
    'img-src': ["'self'", 'data:', 'https:'],
    'connect-src': ["'self'", 'https://api.example.com'],
    'frame-src': ["'none'"],
    'object-src': ["'none'"]
  }
}
```

### 2. Input Validation

```typescript
// ✅ Validate all inputs
function validateUserInput(input: any): boolean {
  // Type validation
  if (typeof input !== 'object' || input === null) {
    return false
  }
  
  // Required fields validation
  const requiredFields = ['name', 'email']
  for (const field of requiredFields) {
    if (!input[field] || typeof input[field] !== 'string') {
      return false
    }
  }
  
  // Format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(input.email)) {
    return false
  }
  
  return true
}
```

### 3. Secure Communication

```typescript
// ✅ Encrypt sensitive data
import { encrypt, decrypt } from '@micro-core/crypto'

class SecureCommunication {
  sendMessage(appName: string, data: any) {
    // Encrypt sensitive data
    const encryptedData = encrypt(JSON.stringify(data))
    
    EventBus.emit(`secure:${appName}`, {
      encrypted: true,
      data: encryptedData,
      timestamp: Date.now()
    })
  }
  
  receiveMessage(encryptedMessage: any) {
    if (encryptedMessage.encrypted) {
      const decryptedData = decrypt(encryptedMessage.data)
      return JSON.parse(decryptedData)
    }
    
    return encryptedMessage.data
  }
}
```

## Performance Optimization Techniques

### 1. Bundle Splitting

```typescript
// Webpack configuration for optimal bundle splitting
module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        // Vendor libraries
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          priority: 10,
          reuseExistingChunk: true
        },
        
        // Common utilities
        common: {
          name: 'common',
          minChunks: 2,
          priority: 5,
          reuseExistingChunk: true
        },
        
        // Framework-specific chunks
        react: {
          test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
          name: 'react',
          priority: 20
        }
      }
    }
  }
}
```

### 2. Lazy Loading

```typescript
// ✅ Implement lazy loading for micro-applications
const LazyMicroApp = React.lazy(() => 
  import('./MicroApp').then(module => ({
    default: module.MicroApp
  }))
)

function AppContainer() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <LazyMicroApp />
    </Suspense>
  )
}
```

### 3. Resource Caching

```typescript
// Configure intelligent caching
const cacheConfig = {
  // Application cache
  apps: {
    strategy: 'stale-while-revalidate',
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    maxEntries: 50
  },
  
  // Static assets cache
  assets: {
    strategy: 'cache-first',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    patterns: [
      /\.(?:js|css|png|jpg|jpeg|svg|woff2?)$/
    ]
  }
}
```

## Accessibility Best Practices

### 1. ARIA Support

```typescript
// ✅ Implement proper ARIA attributes
function AccessibleMicroApp() {
  return (
    <div 
      role="main"
      aria-label="User Management Application"
      aria-describedby="app-description"
    >
      <h1 id="app-title">User Management</h1>
      <p id="app-description">
        Manage user accounts and permissions
      </p>
      
      <nav role="navigation" aria-label="User actions">
        <button 
          aria-expanded={isMenuOpen}
          aria-controls="user-menu"
          onClick={toggleMenu}
        >
          User Menu
        </button>
      </nav>
    </div>
  )
}
```

### 2. Keyboard Navigation

```typescript
// ✅ Support keyboard navigation
class KeyboardNavigationHandler {
  constructor(container: HTMLElement) {
    this.container = container
    this.setupKeyboardHandlers()
  }
  
  private setupKeyboardHandlers() {
    this.container.addEventListener('keydown', (event) => {
      switch (event.key) {
        case 'Tab':
          this.handleTabNavigation(event)
          break
        case 'Enter':
        case ' ':
          this.handleActivation(event)
          break
        case 'Escape':
          this.handleEscape(event)
          break
      }
    })
  }
}
```

## Internationalization (i18n)

### 1. Multi-language Support

```typescript
// ✅ Implement i18n support
import { i18n } from '@micro-core/i18n'

// Configure supported languages
i18n.configure({
  defaultLocale: 'en',
  supportedLocales: ['en', 'zh-CN', 'ja', 'ko'],
  fallbackLocale: 'en',
  
  // Load translations dynamically
  loadPath: '/locales/{{lng}}/{{ns}}.json'
})

// Use in components
function UserGreeting({ user }) {
  const { t } = useTranslation()
  
  return (
    <div>
      <h1>{t('welcome.title', { name: user.name })}</h1>
      <p>{t('welcome.description')}</p>
    </div>
  )
}
```

### 2. RTL Support

```css
/* ✅ Support right-to-left languages */
.container {
  direction: ltr;
}

.container[dir="rtl"] {
  direction: rtl;
}

.text-align-start {
  text-align: left;
}

[dir="rtl"] .text-align-start {
  text-align: right;
}
```

## Documentation Standards

### 1. API Documentation

```typescript
/**
 * User management micro-application
 * 
 * @example
 * ```typescript
 * const userApp = new UserApp({
 *   container: '#user-container',
 *   apiEndpoint: 'https://api.example.com/users'
 * })
 * 
 * await userApp.mount()
 * ```
 */
class UserApp implements MicroAppInterface {
  /**
   * Mount the application to the specified container
   * 
   * @param container - The DOM element to mount the app
   * @returns Promise that resolves when mounting is complete
   * 
   * @throws {Error} When container is not found
   */
  async mount(container: HTMLElement): Promise<void> {
    // Implementation
  }
}
```

### 2. README Template

```markdown
# Micro-Application Name

Brief description of what this micro-application does.

## Features

- Feature 1
- Feature 2
- Feature 3

## Installation

\`\`\`bash
npm install @company/micro-app-name
\`\`\`

## Usage

\`\`\`typescript
import { MicroApp } from '@company/micro-app-name'

const app = new MicroApp({
  // configuration
})

await app.mount(document.getElementById('container'))
\`\`\`

## API Reference

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| apiUrl | string | - | API endpoint URL |
| theme | string | 'light' | UI theme |

## Development

\`\`\`bash
npm run dev
npm run test
npm run build
\`\`\`

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details.
```

## Reference Materials

- [Architecture Design Guide](/guide/architecture)
- [Performance Optimization Guide](/guide/performance)
- [Security Best Practices](/guide/security)
- [Troubleshooting Guide](/guide/troubleshooting)
- [API Reference](/api/)
- [Migration Guide](/migration/)

## Conclusion

Following these best practices will help you build robust, scalable, and maintainable micro-frontend applications with Micro-Core. Remember that best practices evolve over time, so stay updated with the latest recommendations and community feedback.

For more specific guidance on your use case, consider:

1. **Start Small**: Begin with a simple micro-application and gradually add complexity
2. **Measure Performance**: Use monitoring tools to track performance metrics
3. **Gather Feedback**: Collect feedback from users and developers
4. **Iterate Continuously**: Continuously improve based on lessons learned
5. **Stay Updated**: Keep up with Micro-Core updates and community best practices
