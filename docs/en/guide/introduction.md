# Introduction

Welcome to Micro-Core, a next-generation micro-frontend architecture solution designed to help you build scalable, maintainable, and high-performance web applications.

## What is Micro-Core?

Micro-Core is a comprehensive micro-frontend framework that enables you to:

- **Break down monolithic frontends** into smaller, manageable pieces
- **Enable independent development** by different teams
- **Support multiple frameworks** (<PERSON><PERSON>, Vue, Angular, etc.) in a single application
- **Provide runtime isolation** between different parts of your application
- **Scale your frontend architecture** as your organization grows

## Why Micro-Frontends?

### The Problem with Monolithic Frontends

As applications grow, monolithic frontends become increasingly difficult to maintain:

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Monolithic Frontend Problems                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   Large Bundle   │    │   Team Conflicts │    │   Tech Debt     ││
│  │                 │    │                 │    │                 ││
│  │ • Slow builds   │    │ • Merge conflicts│   │ • Legacy code   ││
│  │ • Long deploys  │    │ • Coordination   │   │ • Hard to update││
│  │ • Memory issues │    │ • Dependencies   │   │ • Risk averse   ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   Scaling Issues │    │   Innovation    │    │   Maintenance   ││
│  │                 │    │   Bottlenecks   │    │   Nightmare     ││
│  │ • Team size     │    │                 │    │                 ││
│  │ • Feature flags │    │ • Framework lock│   │ • Bug fixes     ││
│  │ • Release cycles│    │ • Experiment    │   │ • Refactoring   ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

### The Micro-Frontend Solution

Micro-frontends solve these problems by:

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Frontend Benefits                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   Independent   │    │   Technology    │    │   Team          ││
│  │   Deployment    │    │   Freedom       │    │   Autonomy      ││
│  │                 │    │                 │    │                 ││
│  │ • Faster builds │    │ • Choose stack  │    │ • Own roadmap   ││
│  │ • Quick releases│    │ • Upgrade path  │    │ • Clear scope   ││
│  │ • Rollback easy │    │ • Best tools    │    │ • Responsibility││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   Scalability   │    │   Resilience    │    │   Innovation    ││
│  │                 │    │                 │    │                 ││
│  │ • Horizontal    │    │ • Fault isolation│   │ • Experiment    ││
│  │ • Team growth   │    │ • Graceful fail │   │ • A/B testing   ││
│  │ • Feature scope │    │ • Recovery      │   │ • New features  ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

## Micro-Core Architecture

### Core Principles

Micro-Core is built on four fundamental principles:

1. **Micro-Kernel Architecture**: A lightweight core with plugin-based extensions
2. **Runtime Isolation**: Complete isolation between micro-applications
3. **Framework Agnostic**: Support for any frontend framework
4. **Developer Experience**: Simple APIs and powerful tooling

### Architecture Overview

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core Architecture                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Application Layer                        │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ React App   │  │  Vue App    │  │   Angular App       │ │ │
│  │  │             │  │             │  │                     │ │ │
│  │  │ • Components│  │ • Components│  │ • Components        │ │ │
│  │  │ • Services  │  │ • Services  │  │ • Services          │ │ │
│  │  │ • State     │  │ • State     │  │ • State             │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                │                                │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Adapter Layer                            │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │React Adapter│  │ Vue Adapter │  │  Angular Adapter    │ │ │
│  │  │             │  │             │  │                     │ │ │
│  │  │ • Lifecycle │  │ • Lifecycle │  │ • Lifecycle         │ │ │
│  │  │ • Props     │  │ • Props     │  │ • Props             │ │ │
│  │  │ • Events    │  │ • Events    │  │ • Events            │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                │                                │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Plugin Layer                             │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │Router Plugin│  │Comm Plugin  │  │  Auth Plugin        │ │ │
│  │  │             │  │             │  │                     │ │ │
│  │  │ • Routes    │  │ • EventBus  │  │ • Authentication    │ │ │
│  │  │ • Guards    │  │ • State     │  │ • Authorization     │ │ │
│  │  │ • Cache     │  │ • Messages  │  │ • Permissions       │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                │                                │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Micro-Core Kernel                        │ │
│  │                                                             │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │App Manager  │  │ Sandbox     │  │  Loader             │ │ │
│  │  │             │  │             │  │                     │ │ │
│  │  │ • Registry  │  │ • Isolation │  │ • Resource Loading  │ │ │
│  │  │ • Lifecycle │  │ • Security  │  │ • Caching           │ │ │
│  │  │ • Events    │  │ • Cleanup   │  │ • Preloading        │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Key Features

### 1. Multiple Sandbox Types

Micro-Core provides various sandbox isolation strategies:

- **Proxy Sandbox**: High-performance JavaScript isolation using ES6 Proxy
- **Iframe Sandbox**: Maximum isolation using iframe containers
- **WebComponent Sandbox**: Style isolation using Shadow DOM
- **DefineProperty Sandbox**: Compatibility sandbox for older browsers
- **Namespace Sandbox**: Lightweight isolation for simple use cases
- **Federated Component Sandbox**: Module federation support

### 2. Framework Adapters

Built-in support for popular frameworks:

- **React**: 16.8+, 17.x, 18.x support
- **Vue**: 2.7+ and 3.x support
- **Angular**: 12+ support
- **Svelte**: Latest version support
- **Solid.js**: Modern reactive framework support
- **Native HTML/JS**: Pure JavaScript applications

### 3. Communication System

Multiple ways for micro-applications to communicate:

- **Event Bus**: Publish-subscribe pattern for loose coupling
- **Global State**: Reactive state management across applications
- **Direct Communication**: Direct method calls between applications
- **Props Passing**: Initial data passing through configuration

### 4. Advanced Routing

Sophisticated routing capabilities:

- **Unified Routing**: Centralized route management
- **Dynamic Routes**: Runtime route registration
- **Route Guards**: Authentication and authorization
- **Route Caching**: Performance optimization
- **Nested Routes**: Complex routing hierarchies

## Getting Started

### Quick Example

Here's a simple example of how to use Micro-Core:

```typescript
import { MicroCore } from '@micro-core/core';
import { ReactAdapter } from '@micro-core/adapter-react';

// Create micro-core instance
const microCore = new MicroCore({
  container: '#app',
  adapters: [new ReactAdapter()]
});

// Register micro-applications
microCore.registerApp({
  name: 'header-app',
  entry: 'http://localhost:3001',
  container: '#header',
  activeWhen: '/'
});

microCore.registerApp({
  name: 'user-app',
  entry: 'http://localhost:3002',
  container: '#content',
  activeWhen: '/users'
});

// Start the system
microCore.start();
```

### Basic HTML Structure

```html
<!DOCTYPE html>
<html>
<head>
  <title>My Micro-Frontend App</title>
</head>
<body>
  <div id="app">
    <div id="header"></div>
    <div id="content"></div>
  </div>
  <script src="./main.js"></script>
</body>
</html>
```

## Use Cases

### 1. Large Enterprise Applications

Perfect for large organizations with multiple teams:

- **Team Independence**: Each team owns their micro-application
- **Technology Diversity**: Different teams can use different frameworks
- **Gradual Migration**: Migrate from monolith piece by piece
- **Scalable Development**: Add new features without affecting existing ones

### 2. Multi-Brand Platforms

Ideal for platforms serving multiple brands:

- **Brand Isolation**: Each brand has its own micro-applications
- **Shared Components**: Common functionality shared across brands
- **A/B Testing**: Easy to test different versions
- **White-Label Solutions**: Customize for different clients

### 3. Legacy System Modernization

Great for modernizing legacy applications:

- **Progressive Enhancement**: Add modern features gradually
- **Risk Mitigation**: Isolate new features from legacy code
- **Technology Upgrade**: Use modern frameworks alongside legacy code
- **Incremental Migration**: Move functionality piece by piece

### 4. Plugin-Based Architectures

Excellent for extensible applications:

- **Third-Party Plugins**: Allow external developers to contribute
- **Marketplace**: Create an ecosystem of extensions
- **Dynamic Loading**: Load plugins on demand
- **Sandboxed Execution**: Secure plugin execution

## Comparison with Other Solutions

### vs. Single-SPA

| Feature | Micro-Core | Single-SPA |
|---------|------------|------------|
| **Sandbox Isolation** | ✅ Multiple types | ❌ Limited |
| **Framework Support** | ✅ Built-in adapters | ⚠️ Manual setup |
| **Communication** | ✅ Multiple methods | ⚠️ Basic events |
| **Developer Experience** | ✅ Rich tooling | ⚠️ Basic |
| **Performance** | ✅ Optimized | ⚠️ Manual optimization |

### vs. qiankun

| Feature | Micro-Core | qiankun |
|---------|------------|---------|
| **Sandbox Types** | ✅ 6 types | ⚠️ 2 types |
| **Framework Agnostic** | ✅ True | ⚠️ React-focused |
| **Plugin System** | ✅ Rich ecosystem | ❌ Limited |
| **Migration Tools** | ✅ Built-in | ⚠️ Manual |
| **International** | ✅ Global community | ⚠️ China-focused |

### vs. Module Federation

| Feature | Micro-Core | Module Federation |
|---------|------------|-------------------|
| **Runtime Isolation** | ✅ Complete | ⚠️ Limited |
| **Framework Support** | ✅ Any framework | ⚠️ Webpack-based |
| **Communication** | ✅ Rich APIs | ❌ Manual |
| **Lifecycle Management** | ✅ Complete | ❌ Basic |
| **Development Tools** | ✅ Rich tooling | ⚠️ Basic |

## Architecture Benefits

### 1. Scalability

- **Horizontal Scaling**: Add new micro-applications easily
- **Team Scaling**: Multiple teams can work independently
- **Feature Scaling**: Add features without affecting existing ones
- **Performance Scaling**: Load only what's needed

### 2. Maintainability

- **Clear Boundaries**: Each micro-application has clear responsibilities
- **Independent Updates**: Update applications independently
- **Isolated Testing**: Test applications in isolation
- **Reduced Complexity**: Smaller, focused codebases

### 3. Flexibility

- **Technology Choice**: Use the best tool for each job
- **Gradual Migration**: Migrate at your own pace
- **Experimentation**: Try new technologies safely
- **Customization**: Adapt to specific requirements

### 4. Resilience

- **Fault Isolation**: Failures don't cascade
- **Graceful Degradation**: Applications can fail independently
- **Recovery**: Easy to recover from failures
- **Monitoring**: Monitor each application separately

## When to Use Micro-Core

### ✅ Good Fit

- Large applications with multiple teams
- Organizations wanting technology diversity
- Legacy system modernization projects
- Applications requiring high scalability
- Platforms with plugin architectures
- Multi-brand or white-label solutions

### ❌ Not Recommended

- Small applications with single team
- Simple websites or blogs
- Applications with tight coupling requirements
- Projects with very limited resources
- Applications requiring real-time performance

## Next Steps

Ready to get started with Micro-Core?

1. **[Installation](./installation.md)** - Set up Micro-Core in your project
2. **[Getting Started](./getting-started.md)** - Build your first micro-frontend
3. **[Core Concepts](./core-concepts.md)** - Understand the fundamentals
4. **[Examples](../examples/)** - Explore example projects
5. **[Best Practices](./best-practices/)** - Learn recommended patterns

## Community and Support

Join our growing community:

- 🐙 **GitHub**: [Source code and issues](https://github.com/echo008/micro-core)
- 💬 **Discord**: [Community chat](https://discord.gg/micro-core)
- 📚 **Documentation**: [Complete guides](https://micro-core.dev/docs)
- 📧 **Email**: [Support team](mailto:<EMAIL>)
- 🎯 **Discussions**: [GitHub discussions](https://github.com/echo008/micro-core/discussions)

## License

Micro-Core is open source software licensed under the [MIT License](https://github.com/echo008/micro-core/blob/main/LICENSE).

---

Welcome to the future of frontend architecture! 🚀