# 使用指南

欢迎来到 Micro-Core 使用指南！本指南将帮助您从零开始学习和掌握 Micro-Core 微前端框架，从基础概念到高级应用，逐步构建现代化的微前端应用。

## 📚 学习路径

### 🚀 快速入门 (1-2 天)

适合初次接触微前端的开发者，快速了解基本概念和使用方法。

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    快速入门学习路径                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐ │
│  │ 1. 框架介绍  │───▶│ 2. 环境搭建  │───▶│ 3. 第一个应用       │ │
│  │ • 什么是微前端│    │ • 安装依赖   │    │ • Hello World      │ │
│  │ • 核心概念   │    │ • 项目初始化 │    │ • 基础配置         │ │
│  │ • 适用场景   │    │ • 开发工具   │    │ • 运行调试         │ │
│  └─────────────┘    └─────────────┘    └─────────────────────┘ │
│                                                   │             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────────────┐ │
│  │ 6. 部署上线  │◀───│ 5. 应用通信  │◀───│ 4. 多应用集成       │ │
│  │ • 构建配置   │    │ • 事件总线   │    │ • 注册多个应用      │ │
│  │ • 部署策略   │    │ • 状态共享   │    │ • 路由配置         │ │
│  │ • 监控告警   │    │ • 数据传递   │    │ • 生命周期管理      │ │
│  └─────────────┘    └─────────────┘    └─────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

- **[框架介绍](./introduction.md)** - 了解 Micro-Core 的设计理念和核心优势
- **[快速开始](./getting-started.md)** - 5 分钟搭建第一个微前端应用
- **[核心概念](./core-concepts.md)** - 掌握微前端的基本概念和术语

### 🏗️ 深入理解 (3-5 天)

深入学习 Micro-Core 的核心功能和高级特性。

- **[应用管理](./features/app-management.md)** - 应用注册、生命周期、状态管理
- **[路由系统](./features/routing.md)** - 路由配置、导航控制、嵌套路由
- **[沙箱隔离](./features/sandbox.md)** - JavaScript 沙箱、CSS 隔离、安全机制
- **[应用通信](./features/communication.md)** - 事件总线、状态共享、消息传递
- **[插件系统](./plugin-system.md)** - 插件开发、扩展功能、生态集成

### 🚀 高级应用 (1-2 周)

掌握企业级应用开发和性能优化技巧。

- **[高级特性](./advanced/)** - 边车模式、预加载、中间件系统
- **[性能优化](./performance.md)** - 加载优化、缓存策略、监控分析
- **[最佳实践](./best-practices/)** - 架构设计、开发规范、部署策略
- **[故障排除](./troubleshooting.md)** - 常见问题、调试技巧、解决方案

## 🎯 核心特性概览

### 微内核架构

Micro-Core 采用微内核架构设计，核心功能精简高效：

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    Micro-Core 架构图                            │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│                    ┌─────────────────────┐                     │
│                    │     应用层 (Apps)    │                     │
│                    │ React │ Vue │Angular │                     │
│                    └─────────────────────┘                     │
│                              │                                 │
│                    ┌─────────────────────┐                     │
│                    │    适配器层          │                     │
│                    │ Adapters & Plugins  │                     │
│                    └─────────────────────┘                     │
│                              │                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                   微内核 (Micro-Core)                   │   │
│  │                                                         │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │   │
│  │  │ 应用管理器   │ │ 路由系统     │ │ 通信系统         │   │   │
│  │  │ • 注册      │ │ • 导航      │ │ • 事件总线       │   │   │
│  │  │ • 生命周期   │ │ • 守卫      │ │ • 状态管理       │   │   │
│  │  │ • 状态跟踪   │ │ • 缓存      │ │ • 消息传递       │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────────┘   │   │
│  │                                                         │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │   │
│  │  │ 沙箱系统     │ │ 加载器       │ │ 错误处理         │   │   │
│  │  │ • JS 隔离   │ │ • 资源加载   │ │ • 异常捕获       │   │   │
│  │  │ • CSS 隔离  │ │ • 缓存管理   │ │ • 降级处理       │   │   │
│  │  │ • DOM 隔离  │ │ • 预加载     │ │ • 错误边界       │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────────┘   │   │
│  └─────────────────────────────────────────────────────────┘   │
│                              │                                 │
│                    ┌─────────────────────┐                     │
│                    │    运行时环境        │                     │
│                    │ Browser │ Node.js   │                     │
│                    └─────────────────────┘                     │
└─────────────────────────────────────────────────────────────────┘
```

### 多沙箱支持

提供多种沙箱隔离方案，满足不同场景需求：

| 沙箱类型 | 隔离级别 | 性能 | 兼容性 | 适用场景 |
|----------|----------|------|--------|----------|
| **Proxy 沙箱** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | 现代浏览器，高性能要求 |
| **Iframe 沙箱** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 强隔离需求，安全要求高 |
| **WebComponent 沙箱** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 样式隔离，组件化开发 |
| **DefineProperty 沙箱** | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 兼容性要求高 |

### 框架适配器

支持主流前端框架，实现无缝集成：

::: code-group

```typescript [React 适配器]
import { ReactAdapter } from '@micro-core/adapter-react'

const reactAdapter = new ReactAdapter({
  // React 特定配置
  strictMode: true,
  suspense: true,
  errorBoundary: true,
  
  // 生命周期钩子
  beforeMount: (app) => {
    console.log('React 应用即将挂载:', app.name)
  },
  
  afterMount: (app) => {
    console.log('React 应用挂载完成:', app.name)
  }
})
```

```typescript [Vue 适配器]
import { VueAdapter } from '@micro-core/adapter-vue'

const vueAdapter = new VueAdapter({
  // Vue 特定配置
  version: 3, // Vue 2 或 Vue 3
  devtools: true,
  
  // 全局配置
  globalProperties: {
    $microCore: microCore
  },
  
  // 生命周期钩子
  beforeMount: (app) => {
    console.log('Vue 应用即将挂载:', app.name)
  }
})
```

```typescript [Angular 适配器]
import { AngularAdapter } from '@micro-core/adapter-angular'

const angularAdapter = new AngularAdapter({
  // Angular 特定配置
  zone: true,
  enableProdMode: false,
  
  // 依赖注入
  providers: [
    { provide: 'MICRO_CORE', useValue: microCore }
  ],
  
  // 生命周期钩子
  beforeBootstrap: (app) => {
    console.log('Angular 应用即将启动:', app.name)
  }
})
```

:::

## 🛠️ 开发工具

### CLI 工具

```bash
# 安装 CLI 工具
npm install -g @micro-core/cli

# 创建新项目
micro-core create my-project --template react-vue

# 添加微应用
micro-core add app user-center --framework react

# 启动开发服务器
micro-core dev

# 构建生产版本
micro-core build --env production
```

### 开发者面板

在开发环境中，Micro-Core 提供强大的调试工具：

```ascii
┌─────────────────────────────────────────────────────────────────┐
│                    开发者面板功能                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   应用检查器     │    │   性能监控       │    │   事件追踪       ││
│  │                 │    │                 │    │                 ││
│  │ • 应用列表      │    │ • 加载时间      │    │ • 事件日志      ││
│  │ • 状态查看      │    │ • 内存使用      │    │ • 通信记录      ││
│  │ • 生命周期      │    │ • 性能指标      │    │ • 状态变更      ││
│  │ • 配置信息      │    │ • 瓶颈分析      │    │ • 错误追踪      ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   路由调试       │    │   沙箱监控       │    │   网络面板       ││
│  │                 │    │                 │    │                 ││
│  │ • 路由历史      │    │ • 隔离状态      │    │ • 资源加载      ││
│  │ • 导航记录      │    │ • 全局变量      │    │ • 请求监控      ││
│  │ • 守卫执行      │    │ • 内存泄漏      │    │ • 缓存状态      ││
│  │ • 参数解析      │    │ • 安全检查      │    │ • 错误日志      ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

## 📖 文档导航

### 基础教程

- **[安装配置](./installation.md)** - 环境要求、安装步骤、项目配置
- **[快速开始](./getting-started.md)** - 5 分钟快速上手指南
- **[核心概念](./core-concepts.md)** - 微前端基础概念和术语
- **[构建集成](./build-integration.md)** - 与构建工具的集成配置

### 核心功能

- **[应用管理](./features/app-management.md)** - 应用注册、生命周期、状态管理
- **[路由系统](./features/routing.md)** - 路由配置、导航控制、嵌套路由
- **[沙箱隔离](./features/sandbox.md)** - JavaScript 沙箱、CSS 隔离、安全机制
- **[应用通信](./features/communication.md)** - 事件总线、状态共享、消息传递
- **[状态管理](./features/state-management.md)** - 全局状态、响应式更新、持久化
- **[生命周期](./features/lifecycle.md)** - 应用生命周期钩子和管理

### 高级特性

- **[插件系统](./plugin-system.md)** - 插件开发、扩展功能、生态集成
- **[中间件](./middleware.md)** - 中间件机制、自定义中间件开发
- **[边车模式](./advanced/sidecar-mode.md)** - Sidecar 架构、独立部署
- **[预加载策略](./advanced/prefetch.md)** - 智能预加载、性能优化
- **[适配器开发](./advanced/adapters.md)** - 自定义框架适配器
- **[加载器系统](./advanced/loaders.md)** - 资源加载、缓存管理

### 最佳实践

- **[架构设计](./best-practices/architecture.md)** - 微前端架构设计原则
- **[性能优化](./best-practices/performance.md)** - 加载优化、运行时优化
- **[错误处理](./best-practices/error-handling.md)** - 异常处理、降级策略
- **[测试策略](./best-practices/testing.md)** - 单元测试、集成测试、E2E 测试
- **[部署策略](./best-practices/deployment.md)** - 部署方案、CI/CD 集成

### 工具和生态

- **[构建工具](./build-integration.md)** - Vite、Webpack、Rollup 集成
- **[开发工具](./advanced/build-integration.md)** - CLI 工具、调试面板
- **[性能监控](./performance.md)** - 性能分析、监控告警
- **[故障排除](./troubleshooting.md)** - 常见问题、调试技巧

## 🎮 实践案例

### 基础示例

```typescript
// 简单的微前端应用
import { MicroCore } from '@micro-core/core'

const microCore = new MicroCore({
  container: '#app'
})

// 注册 React 应用
microCore.registerApp({
  name: 'header',
  entry: 'http://localhost:3001',
  container: '#header',
  activeWhen: () => true // 始终激活
})

// 注册 Vue 应用
microCore.registerApp({
  name: 'main',
  entry: 'http://localhost:3002', 
  container: '#main',
  activeWhen: '/main'
})

microCore.start()
```

### 企业级配置

```typescript
// 企业级微前端配置
import { MicroCore } from '@micro-core/core'
import { ReactAdapter } from '@micro-core/adapter-react'
import { VueAdapter } from '@micro-core/adapter-vue'
import { RouterPlugin } from '@micro-core/plugin-router'
import { AuthPlugin } from '@micro-core/plugin-auth'

const microCore = new MicroCore({
  // 容器配置
  container: '#micro-app-container',
  
  // 沙箱配置
  sandbox: {
    type: 'proxy',
    css: true,
    js: true,
    globalWhitelist: ['console', 'location']
  },
  
  // 性能配置
  prefetch: {
    idle: ['user-center', 'order-system'],
    hover: ['admin-panel']
  },
  
  // 错误处理
  errorHandler: {
    onJSError: (error, app) => {
      console.error('JS Error:', error)
      // 错误上报
      reportError(error, app)
    }
  }
})

// 注册适配器
microCore.registerAdapter('react', new ReactAdapter())
microCore.registerAdapter('vue', new VueAdapter())

// 使用插件
microCore.use(RouterPlugin, {
  mode: 'history',
  base: '/app'
})

microCore.use(AuthPlugin, {
  loginUrl: '/auth/login',
  tokenStorage: 'localStorage'
})

// 注册应用
microCore.registerApps([
  {
    name: 'user-center',
    entry: 'http://localhost:3001',
    activeWhen: '/user',
    props: { theme: 'dark' }
  },
  {
    name: 'order-system', 
    entry: 'http://localhost:3002',
    activeWhen: '/order',
    beforeMount: async (app) => {
      // 权限检查
      await checkPermission('order:read')
    }
  }
])

microCore.start()
```

## 🚀 下一步

选择适合您的学习路径：

### 🔰 新手入门
1. 阅读 [框架介绍](./introduction.md) 了解基本概念
2. 跟随 [快速开始](./getting-started.md) 搭建第一个应用
3. 学习 [核心概念](./core-concepts.md) 掌握基础知识

### 🏗️ 深入学习
1. 探索 [核心功能](./features/) 了解各个模块
2. 学习 [插件系统](./plugin-system.md) 扩展功能
3. 掌握 [高级特性](./advanced/) 提升技能

### 🚀 实战应用
1. 参考 [最佳实践](./best-practices/) 设计架构
2. 查看 [示例项目](../examples/) 学习实战
3. 阅读 [故障排除](./troubleshooting.md) 解决问题

## 💬 获取帮助

如果您在学习过程中遇到问题：

- 📖 查看 [API 文档](../api/) 获取详细说明
- 🔍 搜索 [GitHub Issues](https://github.com/micro-core/micro-core/issues) 查找解决方案
- 💬 在 [讨论区](https://github.com/micro-core/micro-core/discussions) 提问交流
- 📧 发送邮件到 <EMAIL> 获取技术支持

让我们开始 Micro-Core 的学习之旅吧！🎉