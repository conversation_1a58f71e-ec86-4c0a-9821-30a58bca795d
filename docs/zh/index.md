---
layout: home

hero:
  name: "Micro-Core"
  text: "下一代微前端架构解决方案"
  tagline: "微内核 + 边车模式 + 多工程设计，构建现代化微前端应用"
  image:
    src: /logo.svg
    alt: Micro-Core
  actions:
    - theme: brand
      text: 快速开始
      link: /zh/guide/getting-started
    - theme: alt
      text: 查看示例
      link: /zh/examples/
    - theme: alt
      text: GitHub
      link: https://github.com/echo008/micro-core

features:
  - icon: 🚀
    title: 高性能架构
    details: 基于微内核架构设计，支持应用级别的沙箱隔离，确保应用间互不干扰，性能卓越。
  - icon: 🔧
    title: 灵活适配器系统
    details: 内置 React、Vue、Angular 等主流框架适配器，支持多框架混合开发，无缝集成。
  - icon: 🛡️
    title: 完善的沙箱机制
    details: 提供 JS 沙箱、CSS 沙箱、DOM 沙箱等多层隔离机制，确保应用安全稳定运行。
  - icon: 📡
    title: 强大的通信系统
    details: 支持事件总线、状态共享、消息传递等多种通信方式，应用间协作更加便捷。
  - icon: 🔌
    title: 丰富的插件生态
    details: 提供路由、认证、状态管理等核心插件，支持自定义插件开发，扩展性极强。
  - icon: ⚡
    title: 边车模式支持
    details: 独创边车模式设计，支持独立部署和运行，降低系统复杂度，提升开发效率。
  - icon: 🛠️
    title: 完善的开发工具
    details: 提供调试面板、性能分析、配置生成器等开发工具，提升开发体验和效率。
  - icon: 📚
    title: 详尽的文档和示例
    details: 提供完整的 API 文档、最佳实践指南和丰富的示例代码，快速上手无障碍。
---

## 快速体验

```bash
# 安装核心包
npm install @micro-core/core

# 安装适配器（根据需要选择）
npm install @micro-core/adapter-react
npm install @micro-core/adapter-vue
npm install @micro-core/adapter-angular
```

```typescript
import { MicroCore } from '@micro-core/core'
import { ReactAdapter } from '@micro-core/adapter-react'

// 创建微前端实例
const microCore = new MicroCore({
  container: '#app',
  adapters: [new ReactAdapter()]
})

// 注册应用
microCore.registerApp({
  name: 'react-app',
  entry: 'http://localhost:3001',
  container: '#react-container',
  activeWhen: '/react'
})

// 启动应用
microCore.start()
```

## 核心特性

### 🏗️ 微内核架构

采用微内核架构设计，核心功能精简高效，通过插件和适配器扩展功能，保证系统的稳定性和可扩展性。

```
┌─────────────────────────────────────────┐
│                微内核                    │
├─────────────────────────────────────────┤
│  应用管理 │ 生命周期 │ 路由系统 │ 沙箱   │
├─────────────────────────────────────────┤
│           插件系统 │ 适配器系统          │
├─────────────────────────────────────────┤
│    React   │   Vue   │  Angular  │ ...  │
└─────────────────────────────────────────┘
```

### 🔄 边车模式

独创边车模式设计，支持应用独立部署和运行，降低系统耦合度：

- **独立部署**：每个应用可以独立部署和更新
- **资源隔离**：应用间资源完全隔离，互不影响
- **按需加载**：支持应用按需加载，提升性能
- **版本管理**：支持应用版本管理和回滚

### 🛡️ 多层沙箱隔离

提供完善的沙箱隔离机制，确保应用安全：

- **JavaScript 沙箱**：隔离全局变量和函数
- **CSS 沙箱**：防止样式污染和冲突
- **DOM 沙箱**：隔离 DOM 操作和事件
- **网络沙箱**：控制网络请求和资源访问

## 生态系统

### 官方适配器

- **@micro-core/adapter-react** - React 应用适配器
- **@micro-core/adapter-vue** - Vue 应用适配器  
- **@micro-core/adapter-angular** - Angular 应用适配器
- **@micro-core/adapter-svelte** - Svelte 应用适配器
- **@micro-core/adapter-solid** - Solid.js 应用适配器

### 官方插件

- **@micro-core/plugin-router** - 路由管理插件
- **@micro-core/plugin-communication** - 应用通信插件
- **@micro-core/plugin-auth** - 身份认证插件
- **@micro-core/plugin-state** - 状态管理插件

### 构建工具集成

- **@micro-core/builder-vite** - Vite 构建集成
- **@micro-core/builder-webpack** - Webpack 构建集成
- **@micro-core/builder-rollup** - Rollup 构建集成

## 社区支持

- [GitHub 仓库](https://github.com/echo008/micro-core) - 源码和问题反馈
- [讨论区](https://github.com/echo008/micro-core/discussions) - 技术讨论和交流
- [示例项目](https://github.com/echo008/micro-core-examples) - 完整示例项目
- [更新日志](/zh/changelog) - 版本更新记录

## 开源协议

Micro-Core 基于 [MIT 协议](https://github.com/echo008/micro-core/blob/main/LICENSE) 开源，欢迎贡献代码和反馈问题。