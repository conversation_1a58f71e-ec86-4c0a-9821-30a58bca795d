import { defineConfig } from 'vitepress'
import { en } from './config/en'
import { zh } from './config/zh'

export default defineConfig({
  title: 'Micro-Core',
  description: '下一代微前端架构解决方案',

  // 清理 URL，移除 .html 后缀
  cleanUrls: true,

  // 启用最后更新时间
  lastUpdated: true,

  // 启用 sitemap
  sitemap: {
    hostname: 'https://micro-core.dev'
  },

  // HTML head 配置
  head: [
    ['link', { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/favicon-32x32.png' }],
    ['link', { rel: 'icon', type: 'image/png', sizes: '16x16', href: '/favicon-16x16.png' }],
    ['link', { rel: 'apple-touch-icon', sizes: '180x180', href: '/apple-touch-icon.png' }],
    ['link', { rel: 'manifest', href: '/site.webmanifest' }],
    ['meta', { name: 'theme-color', content: '#646cff' }],
    ['meta', { name: 'og:type', content: 'website' }],
    ['meta', { name: 'og:locale', content: 'zh' }],
    ['meta', { name: 'og:site_name', content: 'Micro-Core' }],
    ['meta', { name: 'og:image', content: '/og-image.png' }],
    ['meta', { name: 'viewport', content: 'width=device-width, initial-scale=1.0' }]
  ],

  // 多语言配置
  locales: {
    root: {
      label: '简体中文',
      lang: 'zh-CN',
      ...zh
    },
    en: {
      label: 'English',
      lang: 'en-US',
      ...en
    }
  },

  // 主题配置
  themeConfig: {
    logo: '/logo.svg',

    // 社交链接
    socialLinks: [
      { icon: 'github', link: 'https://github.com/echo008/micro-core' }
    ],

    // 搜索配置
    search: {
      provider: 'local',
      options: {
        locales: {
          zh: {
            translations: {
              button: {
                buttonText: '搜索文档',
                buttonAriaLabel: '搜索文档'
              },
              modal: {
                noResultsText: '无法找到相关结果',
                resetButtonTitle: '清除查询条件',
                footer: {
                  selectText: '选择',
                  navigateText: '切换'
                }
              }
            }
          }
        }
      }
    },

    // 页脚
    footer: {
      message: 'Released under the MIT License.',
      copyright: 'Copyright © 2024-present Echo Zhang & Micro-Core Contributors'
    }
  },

  // Markdown 配置
  markdown: {
    lineNumbers: true,
    config: (md) => {
      // 可以在这里添加 markdown-it 插件
    }
  },

  // Vite 配置
  vite: {
    optimizeDeps: {
      exclude: ['vitepress']
    },
    server: {
      hmr: {
        overlay: false
      }
    }
  }
})