# micro-core微前端架构项目

## Core Features

- 微前端核心运行时

- 插件化架构系统

- 多框架适配器

- 构建工具集成

- 共享资源管理

- 边车模式支持

## Tech Stack

{
  "Web": {
    "arch": "react",
    "component": null
  }
}

## Design

现代化技术文档风格，深蓝色主色调配合亮蓝色强调色，采用卡片式布局和清晰信息层级，注重开发者体验

## Plan

Note: 

- [ ] is holding
- [/] is doing
- [X] is done

---

[X] 项目初始化和基础配置

[X] packages/shared共享工具包开发

[X] packages/core核心运行时开发

[X] packages/plugins插件系统开发

[X] packages/adapters适配器系统开发

[X] packages/builders构建工具适配开发

[X] packages/sidecar边车模式开发

[X] apps/playground开发调试环境搭建

[X] apps/examples示例应用开发

[X] 单元测试开发和配置

[X] 集成测试开发

[X] E2E测试开发

[X] 性能基准测试开发

[X] docs文档系统初始化

[X] docs核心文档编写

[X] docs API文档生成

[X] docs示例文档开发

[X] CI/CD流水线配置

[/] 项目发布和部署配置
