# 微前端核心组件库开发

## Core Features

- 核心运行时引擎

- 边车模式支持

- 共享工具包

- 适配器系统

- 插件系统

- 构建工具适配

- 示例应用

- 文档系统

## Tech Stack

{
  "Web": {
    "arch": "react",
    "component": "shadcn"
  },
  "Backend": "Node.js + TypeScript",
  "Build": "Vite + Turborepo",
  "Test": "Vitest + Playwright",
  "Docs": "VitePress"
}

## Design

采用Material Design设计语言，深蓝色主色调配合浅灰背景，模块化分层架构设计，包含文档主页、API文档页面、示例演示页面等核心界面

## Plan

Note: 

- [ ] is holding
- [/] is doing
- [X] is done

---

[X] 项目初始化和基础配置

[X] 开发packages/core核心运行时模块

[X] 开发packages/sidecar边车模式模块

[X] 开发packages/shared共享工具包

[X] 开发packages/adapters适配器系统

[X] 开发packages/plugins插件系统

[X] 开发packages/builders构建工具适配

[X] 开发apps示例应用

[X] 构建docs文档系统

[/] 完善测试套件和质量管控

[ ] 项目打包和发布准备
