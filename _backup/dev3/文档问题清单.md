/guide/getting-started
/api/plugins/communication
/api/plugins/auth
/api/adapters/angular
/examples/frameworks/solid
/examples/advanced/multi-app
/examples/advanced/communication
/examples/advanced/shared-state
/examples/advanced/dynamic-routing
/examples/advanced/performance
/examples/compatibility/qiankun
/examples/compatibility/wujie
/migration/qiankun/config-migration
/migration/qiankun/lifecycle-migration
/migration/qiankun/communication-migration
/migration/wujie/config-migration
/migration/wujie/sandbox-migration
/migration/wujie/communication-migration
/migration/wujie/complete-example
/migration/general/progressive
/migration/general/compatibility
/migration/general/testing
/ecosystem/plugins/router
/ecosystem/plugins/communication
/ecosystem/plugins/auth
/ecosystem/adapters/react
/ecosystem/adapters/vue
/ecosystem/adapters/angular
/ecosystem/builders/webpack
/ecosystem/builders/vite
/ecosystem/builders/rollup
/playground/framework-example
/playground/advanced-features
/playground/qiankun-migration
/playground/wujie-migration
/playground/config-generator
/playground/performance-test
/playground/benchmark
/playground/tutorial
/playground/debug-panel
/playground/performance-analysis
/playground/dev-tools





中间件、跨应用通讯






- 整个文档系统的问题：
  * 大量文档内容缺失
  * 文档内容不完整
  * 缺失大量完整清晰的ASCII Art架构图、流程图、时序图等
  * 分类和章节不完整
  * 缺失大量完整清晰的代码示例
  * 缺失大量完整清晰的配置示例
  * 缺失大量完整清晰的API文档
  * 缺失大量完整清晰的使用示例
  * 缺失大量完整清晰的插件文档
  * 缺失大量完整清晰的适配器文档
  * 缺失大量完整清晰的兼容性文档
  * 缺失大量完整清晰的性能优化文档
  * 缺失大量完整清晰的故障排除文档
  * 缺失大量完整清晰的更新日志
  * 缺失大量完整清晰的贡献指南
  * 英文文档完全空缺
  * 所有的信息要完整、清晰、准确、专业
- 浏览器问题：
  * 浏览器站点图标icon缺失
  * 深浅主题切换默认跟随系统
  * 中英文切换默认跟随系统
- 导航栏问题：
  * 左侧标题左侧没有logo图标
  * 导航栏点击文档内容，导航栏没有高亮
  * “指南”中“高级特性”子模块内容缺失
  * “示例”中“框架示例”、“高级示例”、“兼容性示例”子模块内容缺失
  * 导航栏中“生态系统”没有分类
  * 导航栏中“演练场”没有分类
- 首页模块问题：
  * 最顶部被导航栏遮挡
  * 没有重点展示微内核、边车模式、多工程概念
  * 缺失完整的ASCII Art架构图、流程图、时序等
- 指南模块问题：
  * 【开始】子模块中的“快速开始”文档错误
- API模块问题：
  * 【插件 API】子模块中的“路由插件”、“通信插件”、“认证插件”内容缺失
  * 【适配器 API】子模块中的“React 适配器”、“Vue 适配器”、“Angular 适配器”内容缺失
- 示例模块问题：
  * 【框架示例】子模块中的“Solid 示例”内容缺失
  * 【高级示例】子模块中的“多应用协作”、“应用间通信”、“共享状态”、“动态路由”、“高性能加载”内容缺失
  * 【兼容性示例】子模块中的“qiankun 兼容示例”、“Wujie 兼容示例”内容缺失
- 迁移模块问题：
  * 【qiankun 迁移】子模块中的“配置迁移”、“生命周期迁移”、“通信迁移”内容缺失
  * 【Wujie 迁移】子模块中的“配置迁移”、“沙箱迁移”、“通信迁移”、”完整示例“内容缺失
  * 【通用策略】子模块中的“渐进式迁移”、“兼容性处理”、“测试策略”内容缺失
- 演练场模块问题：
  * 演练场模块内容渲染异常
  * 【快速体验】中”基础示例“、”框架示例“、”高级特性“点击‘立即体验’后，页面内容404
  * 【迁移演练】中“qiankun 迁移示例”、“Wujie 迁移示例”点击‘立即体验’后，页面内容404
  * 【自定义演练】中”配置生成器“、”性能测试“、”基准测试“页面显示异常和功能异常
  * 【交互式教程】中”步骤式学习“页面显示异常和功能异常
  * 【开发工具】中”调试面板“、”性能分析“、”开发者工具“页面显示异常和功能异常



@docs 从这些方面，认认真真、全面、清晰的修复上述所有问题，不要出现幻觉，认真执行