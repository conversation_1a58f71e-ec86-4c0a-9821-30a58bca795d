# micro-core 测试指南

## 🎯 测试概述

本指南介绍如何运行、理解和扩展 micro-core 核心包的测试套件。

## 📁 测试文件结构

```
packages/core/src/__tests__/
├── core-basic.test.ts      # 基础功能测试
├── integration.test.ts     # 集成测试
├── performance.test.ts     # 性能基准测试
└── boundary.test.ts        # 边界和错误处理测试
```

## 🚀 运行测试

### 基础测试命令

```bash
# 运行所有测试
npm test

# 运行特定测试文件
npx vitest run src/__tests__/core-basic.test.ts

# 运行测试并生成覆盖率报告
npx vitest run --coverage

# 监听模式运行测试
npx vitest

# 运行性能测试
npx vitest run src/__tests__/performance.test.ts
```

### 测试选项

```bash
# 详细输出
npx vitest run --reporter=verbose

# 静默模式
npx vitest run --reporter=basic --silent

# 跳过覆盖率检查
npx vitest run --no-coverage

# 并行运行测试
npx vitest run --threads
```

## 📊 测试类型说明

### 1. 基础功能测试 (`core-basic.test.ts`)

**目的**: 验证每个核心组件的基本功能

**覆盖组件**:
- EventBus（事件总线）
- AppRegistry（应用注册中心）
- LifecycleManager（生命周期管理器）
- PluginSystem（插件系统）
- MicroCoreKernel（微前端内核）

**示例测试**:
```typescript
describe('EventBus 基础功能', () => {
    it('应该能够发布和订阅事件', () => {
        const mockListener = vi.fn();
        eventBus.on('test-event', mockListener);
        eventBus.emit('test-event', { data: 'test' });
        expect(mockListener).toHaveBeenCalledWith({ data: 'test' });
    });
});
```

### 2. 集成测试 (`integration.test.ts`)

**目的**: 验证组件间的协作和数据流

**测试场景**:
- 事件驱动的组件通信
- 插件系统集成
- 完整的应用管理流程
- 错误处理和恢复

**示例测试**:
```typescript
it('应该能够通过事件总线进行组件间通信', async () => {
    const mockListener = vi.fn();
    eventBus.on('app:registered', mockListener);
    appRegistry.register(mockAppConfig);
    expect(mockListener).toHaveBeenCalled();
});
```

### 3. 性能测试 (`performance.test.ts`)

**目的**: 验证系统性能是否满足基准要求

**性能基准**:
- 事件处理: 10,000 个事件 < 100ms
- 应用注册: 1,000 个应用 < 100ms
- 插件管理: 500 个插件 < 100ms
- 内存使用: 大量操作后 < 10MB 增长

**示例测试**:
```typescript
it('应该能够快速处理大量事件发布', () => {
    const { duration } = measurePerformance(() => {
        for (let i = 0; i < 10000; i++) {
            eventBus.emit('performance-test', { data: i });
        }
    }, '发布 10000 个事件');
    
    expect(duration).toBeLessThan(100);
});
```

### 4. 边界测试 (`boundary.test.ts`)

**目的**: 验证极端情况和错误输入的处理

**测试场景**:
- 空值和 null 输入
- 无效配置处理
- 大量数据处理
- 异常情况恢复

**示例测试**:
```typescript
it('应该处理无效的应用配置', () => {
    const invalidConfigs = [null, undefined, {}, { name: '' }];
    invalidConfigs.forEach(config => {
        expect(() => {
            appRegistry.register(config as any);
        }).toThrow();
    });
});
```

## 🛠️ 测试工具和辅助函数

### 性能测量工具

```typescript
function measurePerformance<T>(fn: () => T, name: string): { result: T; duration: number } {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    const duration = end - start;
    console.log(`${name}: ${duration.toFixed(2)}ms`);
    return { result, duration };
}
```

### 异步性能测量

```typescript
async function measureAsyncPerformance<T>(
    fn: () => Promise<T>, 
    name: string
): Promise<{ result: T; duration: number }> {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    const duration = end - start;
    console.log(`${name}: ${duration.toFixed(2)}ms`);
    return { result, duration };
}
```

### Mock 数据生成

```typescript
// 生成测试应用配置
function createMockAppConfig(name: string): AppConfig {
    return {
        name,
        entry: `http://localhost:3000/${name}.js`,
        activeWhen: `/${name}`,
        container: `#${name}-container`
    };
}

// 生成测试插件
function createMockPlugin(name: string): Plugin {
    return {
        name,
        version: '1.0.0',
        install: vi.fn(),
        uninstall: vi.fn()
    };
}
```

## 📈 测试最佳实践

### 1. 测试隔离

```typescript
beforeEach(() => {
    // 为每个测试创建独立的实例
    eventBus = new EventBus();
    appRegistry = new AppRegistry(eventBus);
    lifecycleManager = new LifecycleManager(eventBus);
});

afterEach(() => {
    // 清理资源
    try {
        if (eventBus) {
            eventBus.destroy();
        }
    } catch (error) {
        // 忽略清理错误
    }
});
```

### 2. 异步测试处理

```typescript
it('应该能够处理异步操作', async () => {
    const result = await lifecycleManager.bootstrap(app);
    expect(result.success).toBe(true);
});
```

### 3. 错误测试

```typescript
it('应该正确处理错误情况', () => {
    expect(() => {
        appRegistry.register(invalidConfig);
    }).toThrow('应用配置无效');
});
```

### 4. 性能断言

```typescript
it('应该满足性能要求', () => {
    const { duration } = measurePerformance(() => {
        // 执行操作
    }, '操作名称');
    
    expect(duration).toBeLessThan(expectedTime);
});
```

## 🔧 扩展测试

### 添加新的测试用例

1. **选择合适的测试文件**:
   - 基础功能 → `core-basic.test.ts`
   - 组件协作 → `integration.test.ts`
   - 性能相关 → `performance.test.ts`
   - 边界情况 → `boundary.test.ts`

2. **遵循命名约定**:
   ```typescript
   describe('组件名称 功能描述', () => {
       it('应该能够执行特定操作', () => {
           // 测试代码
       });
   });
   ```

3. **使用适当的断言**:
   ```typescript
   // 基本断言
   expect(result).toBe(expected);
   expect(result).toEqual(expected);
   expect(result).toBeDefined();
   
   // 异常断言
   expect(() => operation()).toThrow();
   expect(() => operation()).toThrow('错误消息');
   
   // 异步断言
   await expect(asyncOperation()).resolves.toBe(expected);
   await expect(asyncOperation()).rejects.toThrow();
   ```

### 添加新的性能基准

```typescript
it('应该满足新的性能要求', () => {
    const operationCount = 1000;
    const maxDuration = 50; // ms
    
    const { duration } = measurePerformance(() => {
        for (let i = 0; i < operationCount; i++) {
            // 执行操作
        }
    }, `执行 ${operationCount} 次操作`);
    
    expect(duration).toBeLessThan(maxDuration);
});
```

## 📊 测试报告解读

### 测试输出格式

```
✓ EventBus 基础功能 > 应该能够发布和订阅事件
✓ AppRegistry 基础功能 > 应该能够注册应用
✗ MicroCoreKernel 基础功能 > 应该能够销毁内核

Test Files  1 passed (1)
     Tests  18 passed | 1 failed (19)
  Duration  3.76s
```

### 性能报告解读

```
发布 10000 个事件: 45.23ms ✓
注册 1000 个应用: 78.91ms ✓
安装 500 个插件: 34.56ms ✓
```

### 覆盖率报告

```
File                    | % Stmts | % Branch | % Funcs | % Lines
------------------------|---------|----------|---------|--------
src/communication/     |   95.2  |   88.9   |   100   |   94.8
src/runtime/           |   92.1  |   85.4   |   96.7  |   91.3
src/types/             |   100   |   100    |   100   |   100
```

## 🚨 常见问题

### 1. 测试超时

**问题**: 测试运行时间过长
**解决**: 增加超时时间或优化测试逻辑

```typescript
it('长时间运行的测试', async () => {
    // 设置超时时间
}, 10000); // 10秒超时
```

### 2. 内存泄漏

**问题**: 测试后内存不释放
**解决**: 确保在 `afterEach` 中清理资源

```typescript
afterEach(() => {
    eventBus?.destroy();
    // 清理其他资源
});
```

### 3. 异步测试失败

**问题**: 异步操作未正确等待
**解决**: 使用 `async/await` 或 `Promise`

```typescript
it('异步测试', async () => {
    const result = await asyncOperation();
    expect(result).toBeDefined();
});
```

## 📚 参考资源

- [Vitest 官方文档](https://vitest.dev/)
- [测试最佳实践](https://github.com/goldbergyoni/javascript-testing-best-practices)
- [性能测试指南](https://web.dev/performance-testing/)

---

**记住**: 好的测试不仅验证功能正确性，还要确保代码的健壮性和性能表现！
