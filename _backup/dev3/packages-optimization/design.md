# Packages 优化设计文档

## 概述

本设计文档基于需求分析，提供了 micro-core packages 目录优化的完整技术方案。通过系统性的架构重构、代码迁移和性能优化，实现代码重复率降低、包体积减少和构建性能提升的目标。

## 架构设计

### 整体架构图

```mermaid
graph TD
    A[packages/core] --> B[packages/shared]
    C[packages/adapters/*] --> B
    C --> A
    D[packages/builders/*] --> B
    D --> A
    E[packages/plugins/*] --> B
    E --> A
    F[packages/sidecar] --> B
    F --> A
    
    subgraph "Shared 包结构"
        B1[utils/] --> B
        B2[types/] --> B
        B3[constants/] --> B
        B4[helpers/] --> B
    end
    
    subgraph "优化后依赖关系"
        G[统一工具函数] --> H[减少重复]
        I[统一类型定义] --> J[提升一致性]
        K[统一常量定义] --> L[简化维护]
    end
```

### 核心设计原则

1. **单一职责原则**: 每个包专注于特定功能领域
2. **依赖倒置原则**: 高层模块不依赖低层模块，都依赖抽象
3. **开放封闭原则**: 对扩展开放，对修改封闭
4. **向后兼容原则**: 保持 API 兼容性，渐进式迁移

## 组件和接口设计

### 1. Shared 包重构设计

#### 1.1 工具函数模块 (packages/shared/utils)

```typescript
// packages/shared/utils/src/index.ts
export * from './type-check/core';
export * from './url';
export * from './logger';
export * from './format';
export * from './config';
export * from './dom';
export * from './adapter';

// packages/shared/utils/src/type-check/core.ts
export interface TypeChecker {
  isObject(value: unknown): value is Record<string, unknown>;
  isFunction(value: unknown): value is Function;
  isString(value: unknown): value is string;
  isNumber(value: unknown): value is number;
  isBoolean(value: unknown): value is boolean;
  isArray(value: unknown): value is unknown[];
  isPromise(value: unknown): value is Promise<unknown>;
  isEmpty(value: unknown): boolean;
}

// packages/shared/utils/src/logger/index.ts
export interface Logger {
  debug(message: string, ...args: unknown[]): void;
  info(message: string, ...args: unknown[]): void;
  warn(message: string, ...args: unknown[]): void;
  error(message: string, ...args: unknown[]): void;
}

export interface LoggerConfig {
  namespace: string;
  level?: 'debug' | 'info' | 'warn' | 'error';
  enableTimestamp?: boolean;
  enableColors?: boolean;
}

export function createLogger(config: LoggerConfig): Logger;
```

#### 1.2 类型定义模块 (packages/shared/types)

```typescript
// packages/shared/types/src/base.ts
export interface BaseAdapterConfig<T = any> {
  name: string;
  framework: string;
  entry?: string;
  container?: string | HTMLElement;
  props?: Record<string, any>;
  sandbox?: SandboxConfig;
  lifecycle?: LifecycleHooks;
  specific?: T;
}

// packages/shared/types/src/app.ts
export interface MicroAppConfig {
  name: string;
  entry: string;
  container?: string | HTMLElement;
  activeRule?: string | ((location: Location) => boolean);
  props?: Record<string, any>;
  loader?: {
    loading?: HTMLElement | string;
    error?: HTMLElement | string;
  };
}

// packages/shared/types/src/sandbox.ts
export interface SandboxConfig {
  strictStyleIsolation?: boolean;
  experimentalStyleIsolation?: boolean;
  excludeAssetFilter?: (assetUrl: string) => boolean;
  globalContext?: Record<string, any>;
}

// packages/shared/types/src/lifecycle.ts
export interface LifecycleHooks {
  beforeLoad?: (app: MicroAppConfig) => Promise<void> | void;
  beforeMount?: (app: MicroAppConfig) => Promise<void> | void;
  afterMount?: (app: MicroAppConfig) => Promise<void> | void;
  beforeUnmount?: (app: MicroAppConfig) => Promise<void> | void;
  afterUnmount?: (app: MicroAppConfig) => Promise<void> | void;
}
```

#### 1.3 格式化工具模块 (packages/shared/utils/format)

```typescript
// packages/shared/utils/src/format/index.ts
export interface FormatUtils {
  formatBytes(bytes: number): string;
  formatTime(ms: number): string;
  formatError(error: unknown): string;
}

export interface FormatConfig {
  bytesDecimalPlaces?: number;
  timeUnit?: 'ms' | 's' | 'auto';
  errorStackTrace?: boolean;
}

export function createFormatter(config?: FormatConfig): FormatUtils;
```

### 2. Core 包重构设计

#### 2.1 兼容层设计

```typescript
// packages/core/src/utils.ts - 兼容层实现
import { 
  createLogger as sharedCreateLogger,
  isValidUrl as sharedIsValidUrl,
  formatError as sharedFormatError,
  isObject as sharedIsObject,
  isFunction as sharedIsFunction,
  isString as sharedIsString,
  isNumber as sharedIsNumber,
  isBoolean as sharedIsBoolean,
  isArray as sharedIsArray,
  isPromise as sharedIsPromise,
  isEmpty as sharedIsEmpty
} from '@micro-core/shared/utils';

// 保持原有导出，确保向后兼容
export const createLogger = sharedCreateLogger;
export const isValidUrl = sharedIsValidUrl;
export const formatError = sharedFormatError;
export const isObject = sharedIsObject;
export const isFunction = sharedIsFunction;
export const isString = sharedIsString;
export const isNumber = sharedIsNumber;
export const isBoolean = sharedIsBoolean;
export const isArray = sharedIsArray;
export const isPromise = sharedIsPromise;
export const isEmpty = sharedIsEmpty;

// 添加废弃警告（仅开发环境）
if (process.env.NODE_ENV === 'development') {
  console.warn(
    '[DEPRECATED] 从 @micro-core/core 导入工具函数已废弃。' +
    '请改为从 @micro-core/shared/utils 导入。'
  );
}
```

### 3. Adapters 包重构设计

#### 3.1 通用适配器基类

```typescript
// packages/shared/utils/src/adapter/base.ts
export abstract class BaseAdapter<TConfig extends BaseAdapterConfig> {
  protected config: TConfig;
  protected lifecycleManager: LifecycleManager;
  protected sandboxManager: SandboxManager;
  protected communicationManager: CommunicationManager;

  constructor(
    config: TConfig,
    dependencies: AdapterDependencies
  ) {
    this.config = config;
    this.lifecycleManager = dependencies.lifecycleManager;
    this.sandboxManager = dependencies.sandboxManager;
    this.communicationManager = dependencies.communicationManager;
  }

  abstract canHandle(appConfig: any): boolean;
  abstract load(appConfig: TConfig): Promise<void>;
  abstract mount(): Promise<void>;
  abstract unmount(): Promise<void>;
  abstract update(props: any): Promise<void>;
}

// packages/shared/utils/src/adapter/factory.ts
export interface AdapterFactory {
  createAdapter<T extends BaseAdapterConfig>(
    type: string,
    config: T,
    dependencies: AdapterDependencies
  ): BaseAdapter<T>;
}

export function createAdapterFactory(): AdapterFactory;
```

#### 3.2 通用工具函数

```typescript
// packages/shared/utils/src/adapter/common.ts
export interface AdapterUtils {
  formatError(error: Error, context?: any): string;
  mergeConfigs<T>(base: T, override: Partial<T>): T;
  createContainer(appName: string, parentElement?: HTMLElement): HTMLElement;
  cleanupContainer(container: HTMLElement): void;
  validateConfig<T>(config: T, schema: any): boolean;
}

export function createAdapterUtils(): AdapterUtils;
```

### 4. Builders 包重构设计

#### 4.1 通用构建器接口

```typescript
// packages/shared/types/src/builder.ts
export interface BuilderConfig {
  name: string;
  entry: string;
  output: {
    path: string;
    filename: string;
    library?: string;
    libraryTarget?: string;
  };
  externals?: Record<string, string>;
  optimization?: {
    minimize?: boolean;
    splitChunks?: boolean;
  };
}

export interface Builder {
  build(config: BuilderConfig): Promise<BuildResult>;
  watch(config: BuilderConfig): Promise<void>;
  analyze(config: BuilderConfig): Promise<AnalysisResult>;
}

export interface BuildResult {
  success: boolean;
  assets: Asset[];
  stats: BuildStats;
  errors?: Error[];
  warnings?: string[];
}
```

#### 4.2 通用构建工具

```typescript
// packages/shared/utils/src/builder/common.ts
export interface BuilderUtils {
  validateConfig(config: BuilderConfig): boolean;
  formatBuildStats(stats: BuildStats): string;
  optimizeAssets(assets: Asset[]): Asset[];
  generateReport(result: BuildResult): string;
}

export function createBuilderUtils(): BuilderUtils;
```

## 数据模型设计

### 1. 配置数据模型

```typescript
// packages/shared/types/src/config.ts
export interface GlobalConfig {
  // 全局配置
  debug?: boolean;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
  
  // 沙箱配置
  sandbox?: {
    strictStyleIsolation?: boolean;
    experimentalStyleIsolation?: boolean;
  };
  
  // 预加载配置
  prefetch?: {
    enabled?: boolean;
    strategy?: 'idle' | 'visible' | 'manual';
  };
  
  // 错误处理配置
  errorHandler?: {
    enabled?: boolean;
    reportUrl?: string;
  };
}

export interface AppConfigRegistry {
  apps: Map<string, MicroAppConfig>;
  adapters: Map<string, BaseAdapterConfig>;
  builders: Map<string, BuilderConfig>;
}
```

### 2. 状态管理模型

```typescript
// packages/shared/types/src/state.ts
export interface AppState {
  status: 'NOT_LOADED' | 'LOADING' | 'NOT_BOOTSTRAPPED' | 'NOT_MOUNTED' | 'MOUNTED' | 'UNMOUNTING' | 'UNLOADING' | 'SKIP_BECAUSE_BROKEN';
  error?: Error;
  loadTime?: number;
  mountTime?: number;
}

export interface GlobalState {
  apps: Map<string, AppState>;
  activeApps: Set<string>;
  loadingApps: Set<string>;
  errorApps: Set<string>;
}
```

## 错误处理设计

### 1. 统一错误处理机制

```typescript
// packages/shared/utils/src/error/handler.ts
export interface ErrorContext {
  appName?: string;
  operation?: string;
  timestamp: number;
  userAgent?: string;
  url?: string;
}

export interface ErrorHandler {
  handleError(error: Error, context?: ErrorContext): void;
  formatError(error: Error, context?: ErrorContext): string;
  reportError(error: Error, context?: ErrorContext): Promise<void>;
}

export class MicroCoreErrorHandler implements ErrorHandler {
  private config: ErrorHandlerConfig;
  
  constructor(config: ErrorHandlerConfig) {
    this.config = config;
  }
  
  handleError(error: Error, context?: ErrorContext): void {
    const formattedError = this.formatError(error, context);
    
    if (this.config.logToConsole) {
      console.error(formattedError);
    }
    
    if (this.config.reportToServer) {
      this.reportError(error, context);
    }
    
    if (this.config.onError) {
      this.config.onError(error, context);
    }
  }
  
  formatError(error: Error, context?: ErrorContext): string {
    const timestamp = new Date(context?.timestamp || Date.now()).toISOString();
    const appName = context?.appName || 'unknown';
    const operation = context?.operation || 'unknown';
    
    return `[${timestamp}] [${appName}] [${operation}] ${error.name}: ${error.message}${
      error.stack ? '\n' + error.stack : ''
    }`;
  }
  
  async reportError(error: Error, context?: ErrorContext): Promise<void> {
    if (!this.config.reportUrl) return;
    
    try {
      await fetch(this.config.reportUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          error: {
            name: error.name,
            message: error.message,
            stack: error.stack,
          },
          context,
        }),
      });
    } catch (reportError) {
      console.error('Failed to report error:', reportError);
    }
  }
}
```

### 2. 错误边界设计

```typescript
// packages/shared/utils/src/error/boundary.ts
export interface ErrorBoundaryConfig {
  fallback?: (error: Error) => HTMLElement | string;
  onError?: (error: Error, errorInfo: any) => void;
  enableRecovery?: boolean;
  maxRetries?: number;
}

export class ErrorBoundary {
  private config: ErrorBoundaryConfig;
  private retryCount = 0;
  
  constructor(config: ErrorBoundaryConfig) {
    this.config = config;
  }
  
  catch(error: Error, errorInfo?: any): HTMLElement | null {
    this.retryCount++;
    
    if (this.config.onError) {
      this.config.onError(error, errorInfo);
    }
    
    if (this.config.enableRecovery && this.retryCount <= (this.config.maxRetries || 3)) {
      // 尝试恢复
      return null;
    }
    
    if (this.config.fallback) {
      const fallbackElement = this.config.fallback(error);
      return typeof fallbackElement === 'string' 
        ? this.createErrorElement(fallbackElement)
        : fallbackElement;
    }
    
    return this.createDefaultErrorElement(error);
  }
  
  private createErrorElement(content: string): HTMLElement {
    const element = document.createElement('div');
    element.innerHTML = content;
    element.style.cssText = `
      padding: 20px;
      border: 1px solid #ff6b6b;
      border-radius: 4px;
      background-color: #ffe0e0;
      color: #d63031;
      font-family: monospace;
    `;
    return element;
  }
  
  private createDefaultErrorElement(error: Error): HTMLElement {
    return this.createErrorElement(`
      <h3>应用加载失败</h3>
      <p><strong>错误:</strong> ${error.message}</p>
      <details>
        <summary>详细信息</summary>
        <pre>${error.stack}</pre>
      </details>
    `);
  }
}
```

## 测试策略设计

### 1. 单元测试架构

```typescript
// packages/shared/test-utils/src/index.ts
export interface TestUtils {
  createMockApp(config?: Partial<MicroAppConfig>): MicroAppConfig;
  createMockAdapter<T>(type: string, config?: Partial<T>): BaseAdapter<T>;
  createMockContainer(): HTMLElement;
  createMockLifecycleManager(): LifecycleManager;
  createMockSandboxManager(): SandboxManager;
}

export function createTestUtils(): TestUtils;

// packages/shared/test-utils/src/matchers.ts
export interface CustomMatchers {
  toBeValidConfig(): boolean;
  toHaveBeenCalledWithApp(appName: string): boolean;
  toBeInState(state: AppState['status']): boolean;
}

declare global {
  namespace jest {
    interface Matchers<R> extends CustomMatchers {}
  }
}
```

### 2. 集成测试设计

```typescript
// tests/integration/adapter-system.test.ts
describe('Adapter System Integration', () => {
  let adapterFactory: AdapterFactory;
  let testUtils: TestUtils;
  
  beforeEach(() => {
    adapterFactory = createAdapterFactory();
    testUtils = createTestUtils();
  });
  
  test('should create and mount React adapter', async () => {
    const config = testUtils.createMockApp({
      name: 'test-react-app',
      framework: 'react',
    });
    
    const adapter = adapterFactory.createAdapter('react', config, {
      lifecycleManager: testUtils.createMockLifecycleManager(),
      sandboxManager: testUtils.createMockSandboxManager(),
      communicationManager: testUtils.createMockCommunicationManager(),
    });
    
    expect(adapter.canHandle(config)).toBe(true);
    
    await adapter.load(config);
    await adapter.mount();
    
    expect(adapter.getStatus()).toBe('MOUNTED');
  });
});
```

## 性能优化设计

### 1. 懒加载机制

```typescript
// packages/shared/utils/src/loader/lazy.ts
export interface LazyLoadConfig {
  strategy: 'idle' | 'visible' | 'manual';
  threshold?: number;
  rootMargin?: string;
}

export class LazyLoader {
  private config: LazyLoadConfig;
  private observer?: IntersectionObserver;
  
  constructor(config: LazyLoadConfig) {
    this.config = config;
    this.setupObserver();
  }
  
  load<T>(loader: () => Promise<T>, element?: HTMLElement): Promise<T> {
    switch (this.config.strategy) {
      case 'idle':
        return this.loadOnIdle(loader);
      case 'visible':
        return this.loadOnVisible(loader, element);
      case 'manual':
        return loader();
      default:
        return loader();
    }
  }
  
  private loadOnIdle<T>(loader: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      if ('requestIdleCallback' in window) {
        requestIdleCallback(async () => {
          try {
            const result = await loader();
            resolve(result);
          } catch (error) {
            reject(error);
          }
        });
      } else {
        setTimeout(async () => {
          try {
            const result = await loader();
            resolve(result);
          } catch (error) {
            reject(error);
          }
        }, 0);
      }
    });
  }
  
  private loadOnVisible<T>(loader: () => Promise<T>, element?: HTMLElement): Promise<T> {
    if (!element || !this.observer) {
      return loader();
    }
    
    return new Promise((resolve, reject) => {
      const callback = async () => {
        try {
          const result = await loader();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      };
      
      element.dataset.lazyCallback = callback.toString();
      this.observer!.observe(element);
    });
  }
  
  private setupObserver(): void {
    if (this.config.strategy !== 'visible' || !('IntersectionObserver' in window)) {
      return;
    }
    
    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const callback = entry.target.dataset.lazyCallback;
            if (callback) {
              eval(callback)();
              this.observer!.unobserve(entry.target);
            }
          }
        });
      },
      {
        threshold: this.config.threshold || 0.1,
        rootMargin: this.config.rootMargin || '0px',
      }
    );
  }
}
```

### 2. 缓存机制设计

```typescript
// packages/shared/utils/src/cache/manager.ts
export interface CacheConfig {
  maxSize?: number;
  ttl?: number; // Time to live in milliseconds
  strategy?: 'lru' | 'fifo' | 'lfu';
}

export interface CacheEntry<T> {
  value: T;
  timestamp: number;
  accessCount: number;
  size: number;
}

export class CacheManager<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private config: CacheConfig;
  private currentSize = 0;
  
  constructor(config: CacheConfig = {}) {
    this.config = {
      maxSize: 100,
      ttl: 5 * 60 * 1000, // 5 minutes
      strategy: 'lru',
      ...config,
    };
  }
  
  set(key: string, value: T, size = 1): void {
    this.evictExpired();
    
    if (this.cache.has(key)) {
      this.currentSize -= this.cache.get(key)!.size;
    }
    
    while (this.currentSize + size > this.config.maxSize!) {
      this.evictOne();
    }
    
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      accessCount: 0,
      size,
    });
    
    this.currentSize += size;
  }
  
  get(key: string): T | undefined {
    this.evictExpired();
    
    const entry = this.cache.get(key);
    if (!entry) return undefined;
    
    entry.accessCount++;
    return entry.value;
  }
  
  has(key: string): boolean {
    this.evictExpired();
    return this.cache.has(key);
  }
  
  delete(key: string): boolean {
    const entry = this.cache.get(key);
    if (entry) {
      this.currentSize -= entry.size;
      return this.cache.delete(key);
    }
    return false;
  }
  
  clear(): void {
    this.cache.clear();
    this.currentSize = 0;
  }
  
  private evictExpired(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.config.ttl!) {
        this.delete(key);
      }
    }
  }
  
  private evictOne(): void {
    if (this.cache.size === 0) return;
    
    let keyToEvict: string;
    
    switch (this.config.strategy) {
      case 'lru':
        keyToEvict = this.findLRUKey();
        break;
      case 'fifo':
        keyToEvict = this.cache.keys().next().value;
        break;
      case 'lfu':
        keyToEvict = this.findLFUKey();
        break;
      default:
        keyToEvict = this.cache.keys().next().value;
    }
    
    this.delete(keyToEvict);
  }
  
  private findLRUKey(): string {
    let oldestKey = '';
    let oldestTime = Infinity;
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }
    
    return oldestKey;
  }
  
  private findLFUKey(): string {
    let leastUsedKey = '';
    let leastCount = Infinity;
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.accessCount < leastCount) {
        leastCount = entry.accessCount;
        leastUsedKey = key;
      }
    }
    
    return leastUsedKey;
  }
}
```

## 监控和指标设计

### 1. 性能监控

```typescript
// packages/shared/utils/src/monitor/performance.ts
export interface PerformanceMetrics {
  loadTime: number;
  mountTime: number;
  unmountTime: number;
  memoryUsage: number;
  bundleSize: number;
  errorCount: number;
}

export interface PerformanceMonitor {
  startTiming(operation: string): string;
  endTiming(timingId: string): number;
  recordMetric(name: string, value: number): void;
  getMetrics(): PerformanceMetrics;
  generateReport(): string;
}

export class MicroCorePerformanceMonitor implements PerformanceMonitor {
  private timings = new Map<string, number>();
  private metrics = new Map<string, number[]>();
  
  startTiming(operation: string): string {
    const timingId = `${operation}_${Date.now()}_${Math.random()}`;
    this.timings.set(timingId, performance.now());
    return timingId;
  }
  
  endTiming(timingId: string): number {
    const startTime = this.timings.get(timingId);
    if (!startTime) return 0;
    
    const duration = performance.now() - startTime;
    this.timings.delete(timingId);
    
    const operation = timingId.split('_')[0];
    this.recordMetric(`${operation}_time`, duration);
    
    return duration;
  }
  
  recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(value);
  }
  
  getMetrics(): PerformanceMetrics {
    const getAverage = (name: string) => {
      const values = this.metrics.get(name) || [];
      return values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0;
    };
    
    return {
      loadTime: getAverage('load_time'),
      mountTime: getAverage('mount_time'),
      unmountTime: getAverage('unmount_time'),
      memoryUsage: this.getMemoryUsage(),
      bundleSize: getAverage('bundle_size'),
      errorCount: this.metrics.get('error_count')?.length || 0,
    };
  }
  
  generateReport(): string {
    const metrics = this.getMetrics();
    return `
Performance Report:
- Average Load Time: ${metrics.loadTime.toFixed(2)}ms
- Average Mount Time: ${metrics.mountTime.toFixed(2)}ms
- Average Unmount Time: ${metrics.unmountTime.toFixed(2)}ms
- Memory Usage: ${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB
- Bundle Size: ${(metrics.bundleSize / 1024).toFixed(2)}KB
- Error Count: ${metrics.errorCount}
    `.trim();
  }
  
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }
}
```

### 2. 健康检查设计

```typescript
// packages/shared/utils/src/monitor/health.ts
export interface HealthCheckConfig {
  interval?: number;
  timeout?: number;
  retries?: number;
}

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  checks: Record<string, CheckResult>;
  timestamp: number;
}

export interface CheckResult {
  status: 'pass' | 'fail' | 'warn';
  message?: string;
  duration: number;
}

export class HealthChecker {
  private config: HealthCheckConfig;
  private checks = new Map<string, () => Promise<CheckResult>>();
  private intervalId?: number;
  
  constructor(config: HealthCheckConfig = {}) {
    this.config = {
      interval: 30000, // 30 seconds
      timeout: 5000,   // 5 seconds
      retries: 3,
      ...config,
    };
  }
  
  addCheck(name: string, checkFn: () => Promise<CheckResult>): void {
    this.checks.set(name, checkFn);
  }
  
  async runChecks(): Promise<HealthStatus> {
    const results: Record<string, CheckResult> = {};
    
    for (const [name, checkFn] of this.checks.entries()) {
      try {
        const startTime = performance.now();
        const result = await Promise.race([
          checkFn(),
          new Promise<CheckResult>((_, reject) =>
            setTimeout(() => reject(new Error('Timeout')), this.config.timeout)
          ),
        ]);
        result.duration = performance.now() - startTime;
        results[name] = result;
      } catch (error) {
        results[name] = {
          status: 'fail',
          message: error instanceof Error ? error.message : 'Unknown error',
          duration: this.config.timeout!,
        };
      }
    }
    
    const status = this.calculateOverallStatus(results);
    
    return {
      status,
      checks: results,
      timestamp: Date.now(),
    };
  }
  
  startMonitoring(callback: (status: HealthStatus) => void): void {
    this.intervalId = window.setInterval(async () => {
      const status = await this.runChecks();
      callback(status);
    }, this.config.interval);
  }
  
  stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
  }
  
  private calculateOverallStatus(results: Record<string, CheckResult>): HealthStatus['status'] {
    const statuses = Object.values(results).map(r => r.status);
    
    if (statuses.every(s => s === 'pass')) {
      return 'healthy';
    }
    
    if (statuses.some(s => s === 'fail')) {
      return 'unhealthy';
    }
    
    return 'degraded';
  }
}
```

这个设计文档提供了完整的技术架构和实现方案，涵盖了代码重构、性能优化、错误处理、测试策略和监控机制等各个方面。接下来我将创建详细的任务列表。