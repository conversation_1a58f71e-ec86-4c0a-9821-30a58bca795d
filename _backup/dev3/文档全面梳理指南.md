# 全面梳理和优化 VitePress 技术文档的 AI 开发提示词

## 任务背景

作为 Micro-Core 微前端架构项目的一部分，您需要对 `/Users/<USER>/Desktop/micro-core/docs` 目录下的技术文档进行全面梳理和优化。该文档系统基于 VitePress 2.0.0-alpha.8 构建，采用中文为主要语言，旨在为开发者提供完整的技术文档和使用指南。

## 核心目标

✅ 已完成 1. **完整性检查**：确保所有文档内容完整，无缺失模块
✅ 已完成 2. **规范性检查**：确保文档格式、结构符合规范
✅ 已完成 3. **一致性检查**：确保中英文切换、主题切换等功能正常工作
✅ 已完成 4. **可用性检查**：确保文档系统在各种设备和浏览器上正常显示
✅ 已完成 5. **可执行性**：提供具体可行的优化建议和实施方案

## 检查框架

### 1. 文档完整性检查

针对每个文档模块进行以下检查：

✅ 已完成 - [x] 是否存在占位符内容（如 TODO、TBD 等）
✅ 已完成 - [x] 代码示例是否完整且可运行
✅ 已完成 - [x] 是否所有功能模块都有对应的文档说明
✅ 已完成 - [x] 是否所有 API 都有详细说明
✅ 已完成 - [x] 是否所有配置项都有解释
✅ 已完成 - [x] 是否包含常见问题解答
✅ 已完成 - [x] 是否包含迁移指南（从其他微前端框架）
✅ 已完成 - [x] 是否包含最佳实践指南

### 2. 结构规范性检查

检查文档结构是否符合以下规范：

✅ 已完成 - [x] 目录结构是否清晰，层级合理
✅ 已完成 - [x] 文件命名是否规范统一
✅ 已完成 - [x] 导航结构是否合理，不超过3层
✅ 已完成 - [x] 侧边栏配置是否完整
✅ 已完成 - [x] 页面内目录（TOC）是否自动生成且正确
✅ 已完成 - [x] 链接是否有效，无死链
✅ 已完成 - [x] 图片资源是否正确引用

### 3. 内容质量检查

评估文档内容质量：

✅ 已完成 - [x] 技术描述是否准确无误
✅ 已完成 - [x] 语言表达是否清晰易懂
✅ 已完成 - [x] 术语使用是否统一
✅ 已完成 - [x] 是否包含适当的图表和示例
✅ 已完成 - [x] 是否有明确的安装和配置说明
✅ 已完成 - [x] 是否包含故障排除指南
✅ 已完成 - [x] 是否提供实际应用案例

### 4. 用户体验检查

评估文档系统的用户体验：

✅ 已完成 - [x] 响应式设计是否良好，适配各种设备
✅ 已完成 - [x] 搜索功能是否有效且准确
✅ 已完成 - [x] 主题切换（深色/浅色）是否正常工作
✅ 已完成 - [x] 语言切换（中/英文）是否正常工作
✅ 已完成 - [x] 页面加载速度是否合理
✅ 已完成 - [x] 导航是否直观易用
✅ 已完成 - [x] 代码高亮显示是否正常

### 5. 技术实现检查

检查文档系统的技术实现：

✅ 已完成 - [x] VitePress 配置是否正确
✅ 已完成 - [x] 自定义主题是否正常工作
✅ 已完成 - [x] 插件是否正确集成
✅ 已完成 - [x] 构建和部署流程是否顺畅
✅ 已完成 - [x] SEO 优化是否到位
✅ 已完成 - [x] 多语言支持是否完善

## 具体检查项

### 首页 ([index.md](file:///Users/<USER>/Desktop/micro-core/docs/index.md))

✅ 已完成 - [x] Hero 部分是否清晰传达项目价值
✅ 已完成 - [x] 特性介绍是否准确且有吸引力
✅ 已完成 - [x] ASCII 图表是否正确显示
✅ 已完成 - [x] 行动按钮是否指向正确页面
✅ 已完成 - [x] 为什么选择 micro-core 部分是否解决用户痛点

### 指南部分 (`/guide/`)

✅ 已完成 - [x] 入门指南是否适合初学者
✅ 已完成 - [x] 安装说明是否全面且平台无关
✅ 已完成 - [x] 核心概念是否解释清楚并配有示例
✅ 已完成 - [x] 功能文档是否包含实际用例
✅ 已完成 - [x] 高级主题是否假设适当的先验知识
✅ 已完成 - [x] 最佳实践是否可操作且有证据支持

### API 参考 (`/api/`)

✅ 已完成 - [x] 所有公共 API 是否都有文档
✅ 已完成 - [x] 参数、返回值和异常是否明确指定
✅ 已完成 - [x] 代码示例是否演示典型用法
✅ 已完成 - [x] 是否有相关 API 和指南部分的交叉引用

### 示例 (`/examples/`)

✅ 已完成 - [x] 示例是否涵盖常见用例
✅ 已完成 - [x] 代码是否完整且可运行
✅ 已完成 - [x] 解释是否关注关键概念
✅ 已完成 - [x] 示例复杂度是否渐进

### 迁移指南 (`/migration/`)

✅ 已完成 - [x] 从其他框架到 micro-core 的路径是否清晰
✅ 已完成 - [x] API 映射表是否全面
✅ 已完成 - [x] 分步说明是否配有示例
✅ 已完成 - [x] 是否识别常见陷阱和解决方案

### 生态系统 (`/ecosystem/`)

✅ 已完成 - [x] 插件、适配器和构建器是否文档齐全
✅ 已完成 - [x] 安装和配置说明是否清晰
✅ 已完成 - [x] 与核心功能的集成示例
✅ 已完成 - [x] 突出性能考虑因素
✅ 已完成 - [x] 提供兼容性信息

### 演练场 (`/playground/`)

✅ 已完成 - [x] 交互式示例是否功能齐全且具有教育意义
✅ 已完成 - [x] 教程是否引导用户了解关键概念
✅ 已完成 - [x] 配置工具是否直观
✅ 已完成 - [x] 性能测试工具是否提供有意义的见解
✅ 已完成 - [x] 开发者工具是否增强调试体验

## 优化建议格式

对于每个识别出的问题或改进机会，请提供：

1. **问题描述**：清楚解释问题
2. **影响**：这对用户和文档质量有何影响
3. **建议**：解决该问题的具体步骤
4. **优先级**：根据用户影响分为高/中/低
5. **实施工作量**：估计实施所需的工作量（小/中/大）

## 交付成果

✅ 已完成 1. **完整性报告**：记录所有缺失内容并提供优先级建议
✅ 已完成 2. **结构分析**：评估当前组织结构并建议改进
✅ 已完成 3. **质量评估**：识别内容质量问题和不一致性
✅ 已完成 4. **用户体验审查**：评估导航、搜索和整体可用性
✅ 已完成 5. **生产准备检查清单**：全面的部署检查清单
✅ 已完成 6. **行动计划**：优先级改进列表及时间估算

## 成功标准

一次成功的审计将产生：
- 覆盖框架 100% 功能的文档
- 整个文档的一致结构和格式
- 技术上准确且最新的内容
- 各种设备类型的出色用户体验
- 可公开发布的生产就绪文档

## 约束条件

- 保持现有的多语言结构
- 保留当前导航层次结构，除非建议改进
- 确保所有代码示例都能正常运行和测试
- 遵循现有的文档风格和语调

这个全面的审计框架将确保 Micro-Core 文档达到技术文档的最高标准，并为采用该框架的开发人员提供卓越的体验。