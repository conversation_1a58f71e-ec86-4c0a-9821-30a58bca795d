/**
 * 文档解析器 - 解析四个规范文档的内容
 * 基于：开发设计指导方案.md、完整目录结构设计.md、优化建议清单.md、结构优化清单.md
 */

export interface DocumentSpec {
    name: string;
    version: string;
    lastUpdated: string;
    requirements: RequirementSpec[];
}

export interface RequirementSpec {
    id: string;
    category: string;
    type: 'structure' | 'implementation' | 'configuration' | 'documentation';
    priority: 'high' | 'medium' | 'low';
    description: string;
    expectedPath: string;
    expectedContent?: string[];
    validationRules?: ValidationRule[];
}

export interface ValidationRule {
    name: string;
    pattern?: RegExp;
    required: boolean;
    errorMessage: string;
}

/**
 * 基于开发设计指导方案.md的规范要求
 */
const DEVELOPMENT_GUIDE_SPECS: RequirementSpec[] = [
    // 核心架构要求
    {
        id: 'core-kernel',
        category: 'core',
        type: 'implementation',
        priority: 'high',
        description: '微内核架构实现 - 核心库小于15KB',
        expectedPath: 'packages/core/src/kernel.ts',
        expectedContent: ['MicroCoreKernel', 'registerApplication', 'start', 'loadApp', 'unloadApp'],
        validationRules: [
            {
                name: 'class-definition',
                pattern: /export\s+class\s+MicroCoreKernel/,
                required: true,
                errorMessage: '缺少MicroCoreKernel类定义'
            },
            {
                name: 'register-method',
                pattern: /registerApplication\s*\(/,
                required: true,
                errorMessage: '缺少registerApplication方法'
            }
        ]
    },
    {
        id: 'plugin-system',
        category: 'core',
        type: 'implementation',
        priority: 'high',
        description: '100%插件化设计的插件系统',
        expectedPath: 'packages/core/src/plugin-system.ts',
        expectedContent: ['PluginSystem', 'register', 'applyHooks'],
        validationRules: [
            {
                name: 'plugin-system-class',
                pattern: /export\s+class\s+PluginSystem/,
                required: true,
                errorMessage: '缺少PluginSystem类定义'
            }
        ]
    },
    // Sidecar模式要求
    {
        id: 'sidecar-container',
        category: 'sidecar',
        type: 'implementation',
        priority: 'high',
        description: 'Sidecar模式零配置启动',
        expectedPath: 'packages/sidecar/src/index.ts',
        expectedContent: ['SidecarContainer', 'createSidecar'],
        validationRules: [
            {
                name: 'sidecar-export',
                pattern: /export.*SidecarContainer/,
                required: true,
                errorMessage: '缺少SidecarContainer导出'
            }
        ]
    },
    // 沙箱策略要求
    {
        id: 'proxy-sandbox',
        category: 'plugins',
        type: 'implementation',
        priority: 'high',
        description: 'Proxy沙箱策略实现',
        expectedPath: 'packages/plugins/plugin-sandbox-proxy/src/index.ts',
        expectedContent: ['ProxySandboxPlugin', 'createProxySandbox'],
        validationRules: [
            {
                name: 'proxy-sandbox-plugin',
                pattern: /export.*ProxySandboxPlugin/,
                required: true,
                errorMessage: '缺少ProxySandboxPlugin实现'
            }
        ]
    },
    {
        id: 'iframe-sandbox',
        category: 'plugins',
        type: 'implementation',
        priority: 'high',
        description: 'Iframe沙箱策略实现',
        expectedPath: 'packages/plugins/plugin-sandbox-iframe/src/index.ts',
        expectedContent: ['IframeSandboxPlugin', 'createIframeSandbox'],
        validationRules: [
            {
                name: 'iframe-sandbox-plugin',
                pattern: /export.*IframeSandboxPlugin/,
                required: true,
                errorMessage: '缺少IframeSandboxPlugin实现'
            }
        ]
    },
    {
        id: 'webcomponent-sandbox',
        category: 'plugins',
        type: 'implementation',
        priority: 'medium',
        description: 'WebComponent沙箱策略实现',
        expectedPath: 'packages/plugins/plugin-sandbox-webcomponent/src/index.ts',
        expectedContent: ['WebComponentSandboxPlugin', 'createWebComponentSandbox'],
        validationRules: [
            {
                name: 'webcomponent-sandbox-plugin',
                pattern: /export.*WebComponentSandboxPlugin/,
                required: true,
                errorMessage: '缺少WebComponentSandboxPlugin实现'
            }
        ]
    },
    // 兼容性插件要求
    {
        id: 'qiankun-compat',
        category: 'plugins',
        type: 'implementation',
        priority: 'medium',
        description: 'qiankun兼容插件实现',
        expectedPath: 'packages/plugins/plugin-qiankun-compat/src/index.ts',
        expectedContent: ['registerMicroApps', 'start', 'loadMicroApp', 'initGlobalState'],
        validationRules: [
            {
                name: 'qiankun-api',
                pattern: /export.*registerMicroApps/,
                required: true,
                errorMessage: '缺少qiankun兼容API'
            }
        ]
    },
    {
        id: 'wujie-compat',
        category: 'plugins',
        type: 'implementation',
        priority: 'medium',
        description: 'Wujie兼容插件实现',
        expectedPath: 'packages/plugins/plugin-wujie-compat/src/index.ts',
        expectedContent: ['startApp', 'setupApp', 'destroyApp', 'preloadApp'],
        validationRules: [
            {
                name: 'wujie-api',
                pattern: /export.*startApp/,
                required: true,
                errorMessage: '缺少Wujie兼容API'
            }
        ]
    },
    // 高性能加载器要求
    {
        id: 'worker-loader',
        category: 'plugins',
        type: 'implementation',
        priority: 'medium',
        description: 'Worker加载器插件实现',
        expectedPath: 'packages/plugins/plugin-loader-worker/src/index.ts',
        expectedContent: ['WorkerLoaderPlugin', 'preloadResources'],
        validationRules: [
            {
                name: 'worker-loader-plugin',
                pattern: /export.*WorkerLoaderPlugin/,
                required: true,
                errorMessage: '缺少WorkerLoaderPlugin实现'
            }
        ]
    },
    {
        id: 'wasm-loader',
        category: 'plugins',
        type: 'implementation',
        priority: 'medium',
        description: 'WebAssembly加载器插件实现',
        expectedPath: 'packages/plugins/plugin-loader-wasm/src/index.ts',
        expectedContent: ['WasmLoaderPlugin', 'loadModule'],
        validationRules: [
            {
                name: 'wasm-loader-plugin',
                pattern: /export.*WasmLoaderPlugin/,
                required: true,
                errorMessage: '缺少WasmLoaderPlugin实现'
            }
        ]
    },
    // 框架适配器要求
    {
        id: 'react-adapter',
        category: 'adapters',
        type: 'implementation',
        priority: 'high',
        description: 'React适配器实现',
        expectedPath: 'packages/adapters/adapter-react/src/index.ts',
        expectedContent: ['ReactAdapter', 'mount', 'unmount'],
        validationRules: [
            {
                name: 'react-adapter-class',
                pattern: /export.*ReactAdapter/,
                required: true,
                errorMessage: '缺少ReactAdapter类'
            }
        ]
    },
    {
        id: 'vue3-adapter',
        category: 'adapters',
        type: 'implementation',
        priority: 'high',
        description: 'Vue3适配器实现',
        expectedPath: 'packages/adapters/adapter-vue3/src/index.ts',
        expectedContent: ['Vue3Adapter', 'mount', 'unmount'],
        validationRules: [
            {
                name: 'vue3-adapter-class',
                pattern: /export.*Vue3Adapter/,
                required: true,
                errorMessage: '缺少Vue3Adapter类'
            }
        ]
    },
    {
        id: 'vue2-adapter',
        category: 'adapters',
        type: 'implementation',
        priority: 'medium',
        description: 'Vue2适配器实现',
        expectedPath: 'packages/adapters/adapter-vue2/src/index.ts',
        expectedContent: ['Vue2Adapter', 'mount', 'unmount'],
        validationRules: [
            {
                name: 'vue2-adapter-class',
                pattern: /export.*Vue2Adapter/,
                required: true,
                errorMessage: '缺少Vue2Adapter类'
            }
        ]
    },
    {
        id: 'angular-adapter',
        category: 'adapters',
        type: 'implementation',
        priority: 'medium',
        description: 'Angular适配器实现',
        expectedPath: 'packages/adapters/adapter-angular/src/index.ts',
        expectedContent: ['AngularAdapter', 'mount', 'unmount'],
        validationRules: [
            {
                name: 'angular-adapter-class',
                pattern: /export.*AngularAdapter/,
                required: true,
                errorMessage: '缺少AngularAdapter类'
            }
        ]
    },
    // 构建工具适配器要求
    {
        id: 'vite-builder',
        category: 'builders',
        type: 'implementation',
        priority: 'high',
        description: 'Vite构建适配器实现',
        expectedPath: 'packages/builders/builder-vite/src/index.ts',
        expectedContent: ['ViteBuilder', 'createVitePlugin'],
        validationRules: [
            {
                name: 'vite-builder-class',
                pattern: /export.*ViteBuilder/,
                required: true,
                errorMessage: '缺少ViteBuilder类'
            }
        ]
    },
    {
        id: 'webpack-builder',
        category: 'builders',
        type: 'implementation',
        priority: 'medium',
        description: 'Webpack构建适配器实现',
        expectedPath: 'packages/builders/builder-webpack/src/index.ts',
        expectedContent: ['WebpackBuilder', 'createWebpackPlugin'],
        validationRules: [
            {
                name: 'webpack-builder-class',
                pattern: /export.*WebpackBuilder/,
                required: true,
                errorMessage: '缺少WebpackBuilder类'
            }
        ]
    },
    // 配置文件要求
    {
        id: 'typescript-config',
        category: 'config',
        type: 'configuration',
        priority: 'high',
        description: 'TypeScript 5.3+严格模式配置',
        expectedPath: 'tsconfig.json',
        expectedContent: ['strict', 'noImplicitAny', 'noImplicitReturns'],
        validationRules: [
            {
                name: 'strict-mode',
                pattern: /"strict":\s*true/,
                required: true,
                errorMessage: '必须启用TypeScript严格模式'
            }
        ]
    },
    {
        id: 'test-config',
        category: 'config',
        type: 'configuration',
        priority: 'high',
        description: '测试覆盖率100%配置',
        expectedPath: 'vitest.config.ts',
        expectedContent: ['coverage', 'threshold'],
        validationRules: [
            {
                name: 'coverage-threshold',
                pattern: /coverage.*threshold/,
                required: true,
                errorMessage: '必须配置100%测试覆盖率要求'
            }
        ]
    }
];

/**
 * 基于完整目录结构设计.md的目录结构要求
 */
const DIRECTORY_STRUCTURE_SPECS: RequirementSpec[] = [
    // 根目录结构
    {
        id: 'root-package-json',
        category: 'root',
        type: 'configuration',
        priority: 'high',
        description: '根目录package.json配置',
        expectedPath: 'package.json',
        expectedContent: ['workspaces', '@micro-core', 'pnpm'],
        validationRules: [
            {
                name: 'workspace-config',
                pattern: /"workspaces"/,
                required: true,
                errorMessage: '缺少workspaces配置'
            }
        ]
    },
    {
        id: 'pnpm-workspace',
        category: 'root',
        type: 'configuration',
        priority: 'high',
        description: 'pnpm工作空间配置',
        expectedPath: 'pnpm-workspace.yaml',
        expectedContent: ['packages/*', 'apps/*'],
        validationRules: [
            {
                name: 'packages-workspace',
                pattern: /packages\/\*/,
                required: true,
                errorMessage: '缺少packages/*工作空间配置'
            }
        ]
    },
    {
        id: 'turbo-config',
        category: 'root',
        type: 'configuration',
        priority: 'high',
        description: 'Turborepo配置',
        expectedPath: 'turbo.json',
        expectedContent: ['pipeline', 'build', 'test'],
        validationRules: [
            {
                name: 'pipeline-config',
                pattern: /"pipeline"/,
                required: true,
                errorMessage: '缺少pipeline配置'
            }
        ]
    },
    // packages目录结构
    {
        id: 'packages-core-structure',
        category: 'packages',
        type: 'structure',
        priority: 'high',
        description: 'packages/core目录结构',
        expectedPath: 'packages/core',
        expectedContent: ['src/', 'package.json', 'README.md', 'tsconfig.json'],
        validationRules: [
            {
                name: 'core-src-directory',
                required: true,
                errorMessage: '缺少packages/core/src目录'
            }
        ]
    },
    {
        id: 'packages-sidecar-structure',
        category: 'packages',
        type: 'structure',
        priority: 'high',
        description: 'packages/sidecar目录结构',
        expectedPath: 'packages/sidecar',
        expectedContent: ['src/', 'package.json', 'README.md'],
        validationRules: [
            {
                name: 'sidecar-src-directory',
                required: true,
                errorMessage: '缺少packages/sidecar/src目录'
            }
        ]
    },
    // apps目录结构
    {
        id: 'apps-main-structure',
        category: 'apps',
        type: 'structure',
        priority: 'high',
        description: 'apps/main-app-vite目录结构',
        expectedPath: 'apps/main-app-vite',
        expectedContent: ['src/', 'package.json', 'vite.config.ts', 'index.html'],
        validationRules: [
            {
                name: 'main-app-src',
                required: true,
                errorMessage: '缺少主应用src目录'
            }
        ]
    },
    {
        id: 'apps-sub-react-structure',
        category: 'apps',
        type: 'structure',
        priority: 'high',
        description: 'apps/sub-app-react目录结构',
        expectedPath: 'apps/sub-app-react',
        expectedContent: ['src/', 'package.json', 'vite.config.ts'],
        validationRules: [
            {
                name: 'react-app-src',
                required: true,
                errorMessage: '缺少React子应用src目录'
            }
        ]
    },
    // docs目录结构
    {
        id: 'docs-structure',
        category: 'docs',
        type: 'structure',
        priority: 'medium',
        description: 'docs目录结构',
        expectedPath: 'docs',
        expectedContent: ['.vitepress/', 'guide/', 'api/', 'examples/'],
        validationRules: [
            {
                name: 'vitepress-config',
                required: true,
                errorMessage: '缺少VitePress配置目录'
            }
        ]
    }
];

/**
 * 基于优化建议清单.md的优化要求
 */
const OPTIMIZATION_SPECS: RequirementSpec[] = [
    {
        id: 'performance-optimization',
        category: 'performance',
        type: 'implementation',
        priority: 'high',
        description: '性能优化 - 核心库小于15KB',
        expectedPath: 'packages/core/dist',
        expectedContent: ['index.js'],
        validationRules: [
            {
                name: 'bundle-size',
                required: true,
                errorMessage: '核心库大小必须小于15KB'
            }
        ]
    },
    {
        id: 'test-coverage',
        category: 'quality',
        type: 'configuration',
        priority: 'high',
        description: '测试覆盖率100%',
        expectedPath: 'coverage',
        expectedContent: ['lcov.info'],
        validationRules: [
            {
                name: 'coverage-100',
                required: true,
                errorMessage: '测试覆盖率必须达到100%'
            }
        ]
    }
];

/**
 * 基于结构优化清单.md的结构优化要求
 */
const STRUCTURE_OPTIMIZATION_SPECS: RequirementSpec[] = [
    {
        id: 'directory-naming',
        category: 'structure',
        type: 'structure',
        priority: 'medium',
        description: '目录命名规范化',
        expectedPath: 'apps',
        expectedContent: ['main-app-vite', 'sub-app-react', 'sub-app-vue3'],
        validationRules: [
            {
                name: 'naming-convention',
                pattern: /^[a-z][a-z0-9-]*$/,
                required: true,
                errorMessage: '目录命名必须使用kebab-case格式'
            }
        ]
    },
    {
        id: 'config-file-cleanup',
        category: 'config',
        type: 'configuration',
        priority: 'low',
        description: '配置文件去重',
        expectedPath: '.',
        expectedContent: ['.eslintrc.js', '.prettierrc.js'],
        validationRules: [
            {
                name: 'no-duplicate-config',
                required: true,
                errorMessage: '不应存在重复的配置文件'
            }
        ]
    }
];

/**
 * 文档解析器类
 */
export class DocumentParser {
    private developmentSpecs: RequirementSpec[] = DEVELOPMENT_GUIDE_SPECS;
    private structureSpecs: RequirementSpec[] = DIRECTORY_STRUCTURE_SPECS;
    private optimizationSpecs: RequirementSpec[] = OPTIMIZATION_SPECS;
    private structureOptimizationSpecs: RequirementSpec[] = STRUCTURE_OPTIMIZATION_SPECS;

    /**
     * 获取所有规范要求
     */
    getAllRequirements(): RequirementSpec[] {
        return [
            ...this.developmentSpecs,
            ...this.structureSpecs,
            ...this.optimizationSpecs,
            ...this.structureOptimizationSpecs
        ];
    }

    /**
     * 按类别获取规范要求
     */
    getRequirementsByCategory(category: string): RequirementSpec[] {
        return this.getAllRequirements().filter(spec => spec.category === category);
    }

    /**
     * 按优先级获取规范要求
     */
    getRequirementsByPriority(priority: 'high' | 'medium' | 'low'): RequirementSpec[] {
        return this.getAllRequirements().filter(spec => spec.priority === priority);
    }

    /**
     * 按类型获取规范要求
     */
    getRequirementsByType(type: 'structure' | 'implementation' | 'configuration' | 'documentation'): RequirementSpec[] {
        return this.getAllRequirements().filter(spec => spec.type === type);
    }

    /**
     * 获取文档规范信息
     */
    getDocumentSpecs(): DocumentSpec[] {
        return [
            {
                name: '开发设计指导方案.md',
                version: 'v1.0.0',
                lastUpdated: '2025年7月',
                requirements: this.developmentSpecs
            },
            {
                name: '完整目录结构设计.md',
                version: 'v1.0.0',
                lastUpdated: '2025年7月',
                requirements: this.structureSpecs
            },
            {
                name: '优化建议清单.md',
                version: 'v1.0.0',
                lastUpdated: '2025年1月',
                requirements: this.optimizationSpecs
            },
            {
                name: '结构优化清单.md',
                version: 'v1.0.0',
                lastUpdated: '2025年7月',
                requirements: this.structureOptimizationSpecs
            }
        ];
    }

    /**
     * 验证内容是否符合规范
     */
    validateContent(content: string, spec: RequirementSpec): boolean {
        if (!spec.validationRules) return true;

        return spec.validationRules.every(rule => {
            if (rule.pattern) {
                return rule.pattern.test(content);
            }
            return true;
        });
    }

    /**
     * 获取验证错误信息
     */
    getValidationErrors(content: string, spec: RequirementSpec): string[] {
        if (!spec.validationRules) return [];

        const errors: string[] = [];
        spec.validationRules.forEach(rule => {
            if (rule.required && rule.pattern && !rule.pattern.test(content)) {
                errors.push(rule.errorMessage);
            }
        });

        return errors;
    }
}

export default DocumentParser;