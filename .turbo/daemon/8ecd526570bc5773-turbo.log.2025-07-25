2025-07-25T00:20:19.423719Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/package.json")}
2025-07-25T00:20:19.423763Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:20:23.700307Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/package.json")}
2025-07-25T00:20:23.700364Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:20:28.299363Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/vite.config.ts")}
2025-07-25T00:20:28.299385Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:20:28.801098Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/tsconfig.json")}
2025-07-25T00:20:28.801113Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:20:33.004868Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/tsconfig.json")}
2025-07-25T00:20:33.004878Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:20:55.242140Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/types.ts")}
2025-07-25T00:20:55.242165Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:20:59.699203Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/types.ts")}
2025-07-25T00:20:59.699250Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:21:19.098684Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/worker-script.ts")}
2025-07-25T00:21:19.098707Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:22:09.099694Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/worker-manager.ts")}
2025-07-25T00:22:09.099715Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:22:50.700096Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/cache-manager.ts")}
2025-07-25T00:22:50.700141Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:23:15.199847Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/cache-manager.ts")}
2025-07-25T00:23:15.199878Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:23:35.630878Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/index.ts")}
2025-07-25T00:23:35.630908Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:23:40.545021Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/worker-manager.ts")}
2025-07-25T00:23:40.545032Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:23:45.302504Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/worker-manager.ts")}
2025-07-25T00:23:45.302535Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:24:00.598865Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/worker-script.ts")}
2025-07-25T00:24:00.598873Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:24:15.512724Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/README.md")}
2025-07-25T00:24:15.512734Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:24:19.820950Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/README.md")}
2025-07-25T00:24:19.820974Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:24:28.098476Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/package.json")}
2025-07-25T00:24:28.098486Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-loader-wasm"), path: AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm") }}))
2025-07-25T00:24:32.798393Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/vite.config.ts")}
2025-07-25T00:24:32.798413Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-loader-wasm"), path: AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm") }}))
2025-07-25T00:24:46.998128Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/tsconfig.json")}
2025-07-25T00:24:46.998257Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-loader-wasm"), path: AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm") }}))
2025-07-25T00:25:00.999500Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/types.ts")}
2025-07-25T00:25:00.999526Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-loader-wasm"), path: AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm") }}))
2025-07-25T00:25:47.011905Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/wasm-manager.ts")}
2025-07-25T00:25:47.012045Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-loader-wasm"), path: AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm") }}))
2025-07-25T00:25:51.398542Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/wasm-manager.ts")}
2025-07-25T00:25:51.398559Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-loader-wasm"), path: AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm") }}))
2025-07-25T00:25:57.001679Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/cache-manager.ts")}
2025-07-25T00:25:57.001774Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:26:10.253314Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/wasm-manager.ts")}
2025-07-25T00:26:10.253352Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-loader-wasm"), path: AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm") }}))
2025-07-25T00:26:25.198103Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/index.ts")}
2025-07-25T00:26:25.198122Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-loader-wasm"), path: AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm") }}))
2025-07-25T00:26:46.300842Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/README.md")}
2025-07-25T00:26:46.301083Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-loader-wasm"), path: AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm") }}))
2025-07-25T00:26:50.504062Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/README.md")}
2025-07-25T00:26:50.504094Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-loader-wasm"), path: AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm") }}))
2025-07-25T00:26:55.400458Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/index.ts")}
2025-07-25T00:26:55.400472Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:27:17.868662Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm/src/index.ts")}
2025-07-25T00:27:17.868686Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/plugin-loader-wasm"), path: AnchoredSystemPathBuf("packages/plugins/plugin-loader-wasm") }}))
2025-07-25T00:27:17.868868Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-25T00:27:22.699939Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-loader-worker/src/index.ts")}
2025-07-25T00:27:22.699958Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:27:27.100783Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/package.json")}
2025-07-25T00:27:27.100803Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:27:31.599792Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/package.json")}
2025-07-25T00:27:31.599811Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:27:32.200031Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/vite.config.ts")}
2025-07-25T00:27:32.200043Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:27:36.516912Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/vite.config.ts")}
2025-07-25T00:27:36.516934Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:27:47.602720Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/tsconfig.json")}
2025-07-25T00:27:47.602737Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:27:51.802151Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/tsconfig.json")}
2025-07-25T00:27:51.802219Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:28:07.441881Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/src/types.ts")}
2025-07-25T00:28:07.447649Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:28:11.802261Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/src/types.ts")}
2025-07-25T00:28:11.802281Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:28:37.413062Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/src/html-entry.ts")}
2025-07-25T00:28:37.413097Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:28:41.899347Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/src/html-entry.ts")}
2025-07-25T00:28:41.899374Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:29:11.901445Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/src/api.ts")}
2025-07-25T00:29:11.901486Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:29:16.199640Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/src/api.ts")}
2025-07-25T00:29:16.199654Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:29:51.100122Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/src/index.ts")}
2025-07-25T00:29:51.100158Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:30:30.999793Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/README.md")}
2025-07-25T00:30:31.000078Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:30:35.399334Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/plugin-qiankun-compat/README.md")}
2025-07-25T00:30:35.399350Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:30:47.399745Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-25T00:30:47.399768Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:30:52.500050Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-25T00:30:52.500065Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:31:11.899106Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-angular/package.json")}
2025-07-25T00:31:11.899125Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-angular"), path: AnchoredSystemPathBuf("packages/adapters/adapter-angular") }}))
2025-07-25T00:31:41.898612Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-angular/src/index.ts")}
2025-07-25T00:31:41.898633Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-angular"), path: AnchoredSystemPathBuf("packages/adapters/adapter-angular") }}))
2025-07-25T00:32:18.236384Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-angular/README.md")}
2025-07-25T00:32:18.236583Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-angular"), path: AnchoredSystemPathBuf("packages/adapters/adapter-angular") }}))
2025-07-25T00:32:22.700520Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-angular/README.md")}
2025-07-25T00:32:22.700548Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/adapter-angular"), path: AnchoredSystemPathBuf("packages/adapters/adapter-angular") }}))
2025-07-25T00:32:27.658223Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-svelte/package.json")}
2025-07-25T00:32:27.658336Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:32:31.813878Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-svelte/package.json")}
2025-07-25T00:32:31.813896Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:33:16.112299Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-svelte/src/index.ts")}
2025-07-25T00:33:16.112329Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:33:20.601826Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/adapter-svelte/src/index.ts")}
2025-07-25T00:33:20.601869Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:33:29.916818Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/builder-vite/package.json")}
2025-07-25T00:33:29.916871Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/builder-vite"), path: AnchoredSystemPathBuf("packages/builders/builder-vite") }}))
2025-07-25T00:34:00.648315Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/builder-vite/src/index.ts")}
2025-07-25T00:34:00.648717Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/builder-vite"), path: AnchoredSystemPathBuf("packages/builders/builder-vite") }}))
2025-07-25T00:34:07.400460Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/builder-vite/src/manifest.ts")}
2025-07-25T00:34:07.400474Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/builder-vite"), path: AnchoredSystemPathBuf("packages/builders/builder-vite") }}))
2025-07-25T00:34:11.901332Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/builder-vite/src/manifest.ts")}
2025-07-25T00:34:11.901348Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/builder-vite"), path: AnchoredSystemPathBuf("packages/builders/builder-vite") }}))
2025-07-25T00:34:43.719720Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/builder-vite/src/transform.ts")}
2025-07-25T00:34:43.721230Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/builder-vite"), path: AnchoredSystemPathBuf("packages/builders/builder-vite") }}))
2025-07-25T00:34:48.400667Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/builder-vite/src/transform.ts")}
2025-07-25T00:34:48.400689Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/builder-vite"), path: AnchoredSystemPathBuf("packages/builders/builder-vite") }}))
2025-07-25T00:35:21.482639Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/builder-vite/README.md")}
2025-07-25T00:35:21.498701Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/builder-vite"), path: AnchoredSystemPathBuf("packages/builders/builder-vite") }}))
2025-07-25T00:35:25.901372Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/builder-vite/README.md")}
2025-07-25T00:35:25.901393Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/builder-vite"), path: AnchoredSystemPathBuf("packages/builders/builder-vite") }}))
2025-07-25T00:35:37.698314Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-25T00:35:37.698344Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:35:58.298265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-25T00:35:58.298294Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:36:08.898191Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("vitest.config.ts")}
2025-07-25T00:36:08.898218Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:36:18.106827Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("tests/setup/test-setup.ts"), AnchoredSystemPathBuf("tests/setup")}
2025-07-25T00:36:18.106838Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:36:22.399621Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("tests/setup/test-setup.ts")}
2025-07-25T00:36:22.399670Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:36:39.109694Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("tests/unit/core"), AnchoredSystemPathBuf("tests/unit/core/kernel.test.ts")}
2025-07-25T00:36:39.109725Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:36:43.600284Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("tests/unit/core/kernel.test.ts")}
2025-07-25T00:36:43.600294Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:38:36.106105Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-25T00:38:36.106175Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:41:36.698951Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup/项目完成总结.md"), AnchoredSystemPathBuf("项目完成总结.md")}
2025-07-25T00:41:36.699016Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:41:36.842540Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("最终项目完成报告.md"), AnchoredSystemPathBuf("_backup/Micro-Core微前端架构项目初始化与开发.md"), AnchoredSystemPathBuf("_backup/最终项目完成报告.md"), AnchoredSystemPathBuf("_backup/已完成任务清单.md"), AnchoredSystemPathBuf("Micro-Core微前端架构项目初始化与开发.md"), AnchoredSystemPathBuf("已完成任务清单.md")}
2025-07-25T00:41:36.842581Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:41:36.921717Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages子包开发状态深度分析报告.md"), AnchoredSystemPathBuf("_backup/packages子包开发状态深度分析报告.md")}
2025-07-25T00:41:36.921746Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:46:27.908591Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup/操作记录.md")}
2025-07-25T00:46:27.909985Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:46:45.097723Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json")}
2025-07-25T00:46:45.097732Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:47:20.598118Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json")}
2025-07-25T00:47:20.598247Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:47:20.697609Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json"), AnchoredSystemPathBuf("Micro-Core微前端架构项目开发.md")}
2025-07-25T00:47:20.697622Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:48:05.798421Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("package.json")}
2025-07-25T00:48:05.798459Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(All)
2025-07-25T00:48:06.118682Z  WARN build:build_inner: turborepo_repository::package_graph::builder: Unable to calculate transitive closures: Workspace 'packages/adapters/adapter-react' not found in lockfile
2025-07-25T00:48:14.197442Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("turbo.json")}
2025-07-25T00:48:14.197473Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(All)
2025-07-25T00:48:14.317203Z  WARN build:build_inner: turborepo_repository::package_graph::builder: Unable to calculate transitive closures: Workspace 'packages/plugins/plugin-loader-worker' not found in lockfile
2025-07-25T00:48:28.798790Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("tsconfig.json")}
2025-07-25T00:48:28.798800Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:48:32.498263Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".eslintrc.json")}
2025-07-25T00:48:32.498299Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:48:36.642181Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".eslintrc.json")}
2025-07-25T00:48:36.642191Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:48:40.801568Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".prettierrc")}
2025-07-25T00:48:40.801618Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:49:13.898643Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/package.json")}
2025-07-25T00:49:13.898752Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:49:13.998398Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/tsup.config.ts")}
2025-07-25T00:49:13.998418Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:49:18.198179Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/tsup.config.ts")}
2025-07-25T00:49:18.198200Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:49:36.497320Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types/index.ts")}
2025-07-25T00:49:36.497442Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:49:49.898166Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types/events.ts")}
2025-07-25T00:49:49.898266Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:49:53.998734Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types/events.ts")}
2025-07-25T00:49:53.998788Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:49:57.597578Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types/sandbox.ts")}
2025-07-25T00:49:57.597588Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:50:01.798303Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types/sandbox.ts")}
2025-07-25T00:50:01.798345Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:50:08.498842Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types/router.ts")}
2025-07-25T00:50:08.498865Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:50:12.697882Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types/router.ts")}
2025-07-25T00:50:12.697915Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:50:59.097620Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/kernel.ts")}
2025-07-25T00:50:59.097692Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:51:23.498332Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/event-bus.ts")}
2025-07-25T00:51:23.498352Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:51:32.029644Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/app-registry.ts")}
2025-07-25T00:51:32.035231Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:52:02.598720Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/lifecycle-manager.ts")}
2025-07-25T00:52:02.598745Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:52:09.898083Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json"), AnchoredSystemPathBuf("Micro-Core微前端架构项目开发.md")}
2025-07-25T00:52:09.898099Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T00:52:45.551744Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/sandbox-manager.ts")}
2025-07-25T00:52:45.551766Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:52:58.724042Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/plugin-system.ts")}
2025-07-25T00:52:58.724084Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:53:22.498486Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/resource-manager.ts")}
2025-07-25T00:53:22.498515Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:53:47.817664Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/error-handler.ts")}
2025-07-25T00:53:47.817690Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:53:54.402106Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/global-state.ts")}
2025-07-25T00:53:54.402119Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:53:58.635895Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/global-state.ts")}
2025-07-25T00:53:58.635908Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:54:02.997779Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/index.ts")}
2025-07-25T00:54:02.997788Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:54:16.599850Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/constants.ts")}
2025-07-25T00:54:16.599882Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:54:50.994137Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/utils.ts")}
2025-07-25T00:54:50.994157Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:54:56.793341Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/kernel.ts")}
2025-07-25T00:54:56.793351Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:55:01.193232Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/kernel.ts")}
2025-07-25T00:55:01.193248Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:55:05.593022Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/kernel.ts")}
2025-07-25T00:55:05.593101Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:55:22.093619Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/kernel.ts")}
2025-07-25T00:55:22.100908Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:55:26.491479Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/kernel.ts")}
2025-07-25T00:55:26.491492Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:55:30.997916Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/kernel.ts")}
2025-07-25T00:55:30.997989Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:55:35.692285Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/kernel.ts")}
2025-07-25T00:55:35.692326Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:55:54.790629Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/kernel.ts")}
2025-07-25T00:55:54.790648Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:56:49.090597Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/kernel.ts")}
2025-07-25T00:56:49.090615Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T00:58:11.895153Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/README.md")}
2025-07-25T00:58:11.895174Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:00:31.390968Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/kernel.test.ts")}
2025-07-25T01:00:31.390986Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:00:31.589539Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vitest.config.ts")}
2025-07-25T01:00:31.589551Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:00:35.791540Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/vitest.config.ts")}
2025-07-25T01:00:35.791561Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:00:36.192597Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/setup.ts")}
2025-07-25T01:00:36.192609Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:00:40.491832Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/setup.ts")}
2025-07-25T01:00:40.491862Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:00:45.090963Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/package.json")}
2025-07-25T01:00:45.090980Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:01:42.790254Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json"), AnchoredSystemPathBuf("Micro-Core微前端架构项目开发.md")}
2025-07-25T01:01:42.790386Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:01:45.889275Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/communication"), AnchoredSystemPathBuf("packages/plugins/router"), AnchoredSystemPathBuf("packages/plugins/sandbox-proxy"), AnchoredSystemPathBuf("packages/plugins/sandbox-iframe"), AnchoredSystemPathBuf("packages/plugins/style-isolation"), AnchoredSystemPathBuf("packages/plugins/wujie-compat"), AnchoredSystemPathBuf("packages/plugins/prefetch"), AnchoredSystemPathBuf("packages/plugins/qiankun-compat")}
2025-07-25T01:01:45.889285Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:01:54.097298Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/router/package.json")}
2025-07-25T01:01:54.097331Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:01:58.390054Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/router/package.json")}
2025-07-25T01:01:58.390071Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:02:36.092055Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/router/src/index.ts"), AnchoredSystemPathBuf("packages/plugins/router/src")}
2025-07-25T01:02:36.092074Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:02:40.506043Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/router/src/index.ts")}
2025-07-25T01:02:40.512345Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:02:53.191158Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/communication/package.json")}
2025-07-25T01:02:53.191424Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:02:57.391174Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/communication/package.json")}
2025-07-25T01:02:57.391186Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:03:40.190369Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/communication/src"), AnchoredSystemPathBuf("packages/plugins/communication/src/index.ts")}
2025-07-25T01:03:40.190429Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:03:44.591014Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/communication/src/index.ts")}
2025-07-25T01:03:44.591031Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:03:47.690585Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/sandbox-proxy/package.json")}
2025-07-25T01:03:47.690594Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:03:51.906897Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/sandbox-proxy/package.json")}
2025-07-25T01:03:51.906934Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:04:39.813072Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/sandbox-proxy/src/index.ts"), AnchoredSystemPathBuf("packages/plugins/sandbox-proxy/src")}
2025-07-25T01:04:39.813115Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:04:44.089100Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/sandbox-proxy/src/index.ts")}
2025-07-25T01:04:44.089117Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:04:59.290469Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/style-isolation/package.json")}
2025-07-25T01:04:59.290500Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:05:03.489395Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/style-isolation/package.json")}
2025-07-25T01:05:03.489409Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:06:06.512930Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/style-isolation/src"), AnchoredSystemPathBuf("packages/plugins/style-isolation/src/index.ts")}
2025-07-25T01:06:06.513439Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:06:10.790459Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/style-isolation/src/index.ts")}
2025-07-25T01:06:10.790472Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:06:14.891588Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/prefetch/package.json")}
2025-07-25T01:06:14.891598Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:06:19.088723Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/prefetch/package.json")}
2025-07-25T01:06:19.088873Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:07:32.723229Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/prefetch/src/index.ts"), AnchoredSystemPathBuf("packages/plugins/prefetch/src")}
2025-07-25T01:07:32.724445Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:07:37.290994Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/plugins/prefetch/src/index.ts")}
2025-07-25T01:07:37.291028Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:07:44.591136Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json"), AnchoredSystemPathBuf("Micro-Core微前端架构项目开发.md")}
2025-07-25T01:07:44.591168Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:07:53.590754Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/solid"), AnchoredSystemPathBuf("packages/adapters/svelte"), AnchoredSystemPathBuf("packages/adapters/vue2"), AnchoredSystemPathBuf("packages/adapters/angular"), AnchoredSystemPathBuf("packages/adapters/vue3"), AnchoredSystemPathBuf("packages/adapters/react")}
2025-07-25T01:07:53.590763Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:08:02.902681Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/react/package.json")}
2025-07-25T01:08:02.902715Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:08:07.199018Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/react/package.json")}
2025-07-25T01:08:07.199125Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:08:55.390775Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/react/src"), AnchoredSystemPathBuf("packages/adapters/react/src/index.ts")}
2025-07-25T01:08:55.390995Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:08:59.690544Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/react/src/index.ts")}
2025-07-25T01:08:59.690561Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:09:02.690850Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/vue3/package.json")}
2025-07-25T01:09:02.690863Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:09:06.890728Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/vue3/package.json")}
2025-07-25T01:09:06.890750Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:10:19.091941Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/vue3/src"), AnchoredSystemPathBuf("packages/adapters/vue3/src/index.ts")}
2025-07-25T01:10:19.091999Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:10:23.488609Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/vue3/src/index.ts")}
2025-07-25T01:10:23.488734Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:10:28.691096Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/angular/package.json")}
2025-07-25T01:10:28.691242Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:10:32.902848Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/angular/package.json")}
2025-07-25T01:10:32.910080Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:12:00.161507Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/angular/src/index.ts"), AnchoredSystemPathBuf("packages/adapters/angular/src")}
2025-07-25T01:12:00.161546Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:12:04.760330Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/adapters/angular/src/index.ts")}
2025-07-25T01:12:04.760434Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:12:11.659828Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json"), AnchoredSystemPathBuf("Micro-Core微前端架构项目开发.md")}
2025-07-25T01:12:11.659842Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:12:22.759906Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/vite"), AnchoredSystemPathBuf("packages/builders/webpack"), AnchoredSystemPathBuf("packages/builders/rollup")}
2025-07-25T01:12:22.759930Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:12:31.460340Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/vite/package.json")}
2025-07-25T01:12:31.460358Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:12:35.831737Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/vite/package.json")}
2025-07-25T01:12:35.831760Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:13:37.133457Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/vite/src/index.ts"), AnchoredSystemPathBuf("packages/builders/vite/src")}
2025-07-25T01:13:37.133570Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:13:41.458585Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/vite/src/index.ts")}
2025-07-25T01:13:41.458599Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:14:03.558253Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/webpack/package.json")}
2025-07-25T01:14:03.558331Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:14:07.758759Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/webpack/package.json")}
2025-07-25T01:14:07.758769Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:15:08.767161Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/webpack/src/index.ts"), AnchoredSystemPathBuf("packages/builders/webpack/src")}
2025-07-25T01:15:08.767453Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:15:13.258153Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/builders/webpack/src/index.ts")}
2025-07-25T01:15:13.258461Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:15:14.057932Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/src/types"), AnchoredSystemPathBuf("packages/shared/src/utils"), AnchoredSystemPathBuf("packages/shared/src"), AnchoredSystemPathBuf("packages/shared/src/constants"), AnchoredSystemPathBuf("packages/shared/src/helpers")}
2025-07-25T01:15:14.057946Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:15:18.276815Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/package.json")}
2025-07-25T01:15:18.279260Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:15:22.481199Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/package.json")}
2025-07-25T01:15:22.481226Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:15:35.689751Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/src/index.ts")}
2025-07-25T01:15:35.689883Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:15:39.856103Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/src/index.ts")}
2025-07-25T01:15:39.856113Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:16:03.162246Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/src/types/index.ts")}
2025-07-25T01:16:03.162580Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:16:07.356531Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/src/types/index.ts")}
2025-07-25T01:16:07.357014Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:16:45.383505Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/src/constants/index.ts")}
2025-07-25T01:16:45.384249Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:16:49.555445Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/src/constants/index.ts")}
2025-07-25T01:16:49.555465Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:17:34.278166Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/src/utils/index.ts")}
2025-07-25T01:17:34.279381Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:17:38.755904Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/src/utils/index.ts")}
2025-07-25T01:17:38.755915Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:18:49.855439Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/src/helpers/index.ts")}
2025-07-25T01:18:49.855500Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:18:54.454599Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/src/helpers/index.ts")}
2025-07-25T01:18:54.454639Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:19:02.075514Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("Micro-Core微前端架构项目开发.md"), AnchoredSystemPathBuf(".codebuddy/analysis-summary.json")}
2025-07-25T01:19:02.075540Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:19:18.554381Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/src/plugins"), AnchoredSystemPathBuf("packages/sidecar/src/core"), AnchoredSystemPathBuf("packages/sidecar/src/utils")}
2025-07-25T01:19:18.554426Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-25T01:19:18.554627Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-25T01:19:31.054628Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/package.json")}
2025-07-25T01:19:31.054680Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-25T01:20:42.853603Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/src/index.ts")}
2025-07-25T01:20:42.853658Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-25T01:21:02.526669Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/src/core/sidecar-manager.ts")}
2025-07-25T01:21:02.526701Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-25T01:21:06.654198Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/src/core/sidecar-manager.ts")}
2025-07-25T01:21:06.654208Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-25T01:21:16.252876Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/src/core/auto-discovery.ts")}
2025-07-25T01:21:16.252897Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-25T01:21:20.562707Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/src/core/auto-discovery.ts")}
2025-07-25T01:21:20.562781Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-25T01:21:32.953213Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/src/core/config-manager.ts")}
2025-07-25T01:21:32.953224Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-25T01:21:37.253926Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/src/core/config-manager.ts")}
2025-07-25T01:21:37.253944Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-25T01:21:56.357095Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/src/utils/framework-detector.ts")}
2025-07-25T01:21:56.357111Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-25T01:22:00.553730Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/src/utils/framework-detector.ts")}
2025-07-25T01:22:00.553741Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-25T01:22:19.752993Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json"), AnchoredSystemPathBuf("Micro-Core微前端架构项目开发.md")}
2025-07-25T01:22:19.753036Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:22:19.853756Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json"), AnchoredSystemPathBuf("Micro-Core微前端架构项目开发.md")}
2025-07-25T01:22:19.853772Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:22:33.053468Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/package.json")}
2025-07-25T01:22:33.053487Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:22:38.109327Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/vite.config.ts")}
2025-07-25T01:22:38.109512Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:22:48.909197Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/main.tsx")}
2025-07-25T01:22:48.909221Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:22:53.068678Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/main.tsx")}
2025-07-25T01:22:53.068697Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:23:28.654636Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/App.tsx")}
2025-07-25T01:23:28.654667Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:23:33.154457Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/App.tsx")}
2025-07-25T01:23:33.154483Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:24:18.059476Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/App.css")}
2025-07-25T01:24:18.059522Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:24:22.452536Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/App.css")}
2025-07-25T01:24:22.452622Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:24:35.553266Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/index.css")}
2025-07-25T01:24:35.553530Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:24:39.859892Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/src/index.css")}
2025-07-25T01:24:39.859905Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:24:57.953768Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/tsconfig.json")}
2025-07-25T01:24:57.953793Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:24:58.253759Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/tsconfig.node.json")}
2025-07-25T01:24:58.253769Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:25:02.452595Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/tsconfig.node.json")}
2025-07-25T01:25:02.452671Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:25:02.754932Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/index.html")}
2025-07-25T01:25:02.754949Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:25:06.951365Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/main-app-vite/index.html")}
2025-07-25T01:25:06.951397Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("main-app-vite"), path: AnchoredSystemPathBuf("apps/main-app-vite") }}))
2025-07-25T01:25:11.451877Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/package.json")}
2025-07-25T01:25:11.451895Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("sub-app-react"), path: AnchoredSystemPathBuf("apps/sub-app-react") }}))
2025-07-25T01:25:31.452673Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/vite.config.ts")}
2025-07-25T01:25:31.452866Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("sub-app-react"), path: AnchoredSystemPathBuf("apps/sub-app-react") }}))
2025-07-25T01:25:33.852597Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/main.tsx")}
2025-07-25T01:25:33.852630Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("sub-app-react"), path: AnchoredSystemPathBuf("apps/sub-app-react") }}))
2025-07-25T01:25:38.050615Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/main.tsx")}
2025-07-25T01:25:38.050624Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("sub-app-react"), path: AnchoredSystemPathBuf("apps/sub-app-react") }}))
2025-07-25T01:26:15.955275Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/sub-app-react/src/App.tsx")}
2025-07-25T01:26:15.955884Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("sub-app-react"), path: AnchoredSystemPathBuf("apps/sub-app-react") }}))
2025-07-25T01:26:23.151799Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json"), AnchoredSystemPathBuf("Micro-Core微前端架构项目开发.md")}
2025-07-25T01:26:23.151819Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:26:54.751591Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/utils.test.ts")}
2025-07-25T01:26:54.751618Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:26:59.050538Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/utils.test.ts")}
2025-07-25T01:26:59.050560Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:27:17.151248Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/event-bus.test.ts")}
2025-07-25T01:27:17.151326Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:27:21.450931Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/__tests__/event-bus.test.ts")}
2025-07-25T01:27:21.450971Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:27:47.652025Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/src/__tests__/utils.test.ts"), AnchoredSystemPathBuf("packages/shared/src/__tests__")}
2025-07-25T01:27:47.652052Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:27:52.053868Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/src/__tests__/utils.test.ts")}
2025-07-25T01:27:52.054335Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:28:25.650934Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/src/__tests__/framework-detector.test.ts"), AnchoredSystemPathBuf("packages/sidecar/src/__tests__")}
2025-07-25T01:28:25.651072Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-25T01:28:30.151293Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/sidecar/src/__tests__/framework-detector.test.ts")}
2025-07-25T01:28:30.151320Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/sidecar"), path: AnchoredSystemPathBuf("packages/sidecar") }}))
2025-07-25T01:29:01.765414Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("scripts/test.sh")}
2025-07-25T01:29:01.766289Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:29:05.851300Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("scripts/test.sh")}
2025-07-25T01:29:05.851360Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:31:36.747756Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json")}
2025-07-25T01:31:36.747797Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:31:36.848080Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json")}
2025-07-25T01:31:36.848093Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:31:57.353995Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup/Micro-Core微前端架构项目开发.md"), AnchoredSystemPathBuf("Micro-Core微前端架构项目开发.md")}
2025-07-25T01:31:57.354021Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:38:23.147862Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup/操作记录.md")}
2025-07-25T01:38:23.147944Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:43:54.542754Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup/操作记录.md")}
2025-07-25T01:43:54.542822Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:44:06.953547Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json")}
2025-07-25T01:44:06.953577Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:44:54.241141Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json")}
2025-07-25T01:44:54.241162Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:44:54.341242Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json"), AnchoredSystemPathBuf("Micro-Core微前端架构项目完整开发.md")}
2025-07-25T01:44:54.341261Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:46:00.941197Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/resource-manager.ts")}
2025-07-25T01:46:00.941223Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:46:05.942384Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types/index.ts")}
2025-07-25T01:46:05.942424Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:46:36.113729Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types/app.ts")}
2025-07-25T01:46:36.113759Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:46:39.540327Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types/app.ts")}
2025-07-25T01:46:39.540341Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-07-25T01:46:40.241026Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json")}
2025-07-25T01:46:40.241058Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:46:51.440004Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json")}
2025-07-25T01:46:51.440018Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:46:51.541314Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/resource-manager.ts"), AnchoredSystemPathBuf("Micro-Core微前端架构项目完整开发.md"), AnchoredSystemPathBuf("packages/core/src/types/index.ts"), AnchoredSystemPathBuf(".codebuddy/analysis-summary.json")}
2025-07-25T01:46:51.541326Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@micro-core/core"), path: AnchoredSystemPathBuf("packages/core") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:47:53.540281Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup/操作记录.md")}
2025-07-25T01:47:53.540320Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:51:38.139145Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup/操作记录.md")}
2025-07-25T01:51:38.139205Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:53:28.338431Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup/操作记录.md")}
2025-07-25T01:53:28.338508Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:53:54.337197Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup/操作记录.md")}
2025-07-25T01:53:54.337231Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:55:18.136447Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json")}
2025-07-25T01:55:18.136573Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:55:22.036025Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json")}
2025-07-25T01:55:22.036033Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:57:08.634725Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("_backup/操作记录.md")}
2025-07-25T01:57:08.634753Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:57:13.634970Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json")}
2025-07-25T01:57:13.634979Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:58:01.644954Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json")}
2025-07-25T01:58:01.645013Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:58:01.744701Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json"), AnchoredSystemPathBuf("micro-core微前端架构项目.md")}
2025-07-25T01:58:01.744712Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:58:01.850392Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("micro-core微前端架构项目.md")}
2025-07-25T01:58:01.850426Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:58:18.647281Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".codebuddy/analysis-summary.json"), AnchoredSystemPathBuf("micro-core微前端架构项目.md")}
2025-07-25T01:58:18.647315Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-25T01:58:38.872944Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("package.json")}
2025-07-25T01:58:38.872991Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(All)
