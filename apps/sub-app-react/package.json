{"name": "sub-app-react", "version": "0.1.0", "description": "Micro-Core React子应用示例", "type": "module", "scripts": {"dev": "vite --port 3001", "build": "tsc && vite build", "preview": "vite preview", "serve": "vite preview --port 3001", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/adapter-react": "workspace:*", "@micro-core/builder-vite": "workspace:*", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/node": "^20.0.0", "@vitejs/plugin-react": "^4.7.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "typescript": "^5.3.0", "vite": "^5.4.0", "vitest": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/user-event": "^14.5.0", "jsdom": "^23.0.0"}, "keywords": ["micro-frontend", "sub-app", "react", "vite", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}