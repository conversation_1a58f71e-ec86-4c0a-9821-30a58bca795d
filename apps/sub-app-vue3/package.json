{"name": "sub-app-vue3", "version": "0.1.0", "description": "Micro-Core Vue 3 子应用示例", "type": "module", "scripts": {"dev": "vite --port 3002", "build": "vue-tsc && vite build", "preview": "vite preview", "serve": "vite preview --port 3002", "type-check": "vue-tsc --noEmit", "lint": "eslint src --ext .ts,.vue", "lint:fix": "eslint src --ext .ts,.vue --fix", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/adapter-vue3": "workspace:*", "@micro-core/builder-vite": "workspace:*", "vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0"}, "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-vue": "^5.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vue/tsconfig": "^0.5.0", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "typescript": "^5.3.0", "vite": "^5.4.0", "vitest": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "@vue/test-utils": "^2.4.0", "jsdom": "^23.0.0", "vue-tsc": "^1.8.0"}, "keywords": ["micro-frontend", "sub-app", "vue3", "vite", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}