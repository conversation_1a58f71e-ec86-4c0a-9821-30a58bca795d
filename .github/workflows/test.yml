name: 测试工作流

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
      
    - name: 设置 Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 运行 ESLint
      run: npm run lint
      
    - name: 运行类型检查
      run: npm run type-check
      
    - name: 运行单元测试
      run: npm run test:unit
      
    - name: 运行集成测试
      run: npm run test:integration
      
    - name: 运行端到端测试
      run: npm run test:e2e
      
    - name: 生成测试覆盖率报告
      run: npm run test:coverage
      
    - name: 上传覆盖率报告到 Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        
    - name: 运行性能基准测试
      run: npm run test:benchmark
      
    - name: 构建项目
      run: npm run build
      
    - name: 运行构建产物测试
      run: npm run test:build

  performance-regression:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
      
    - name: 设置 Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 运行性能回归测试
      run: npm run test:performance-regression
      
    - name: 上传性能报告
      uses: actions/upload-artifact@v3
      with:
        name: performance-report
        path: ./reports/performance/

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v3
      
    - name: 运行安全扫描
      run: npm audit --audit-level moderate
      
    - name: 运行 Snyk 安全检查
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

  notify:
    runs-on: ubuntu-latest
    needs: [test, performance-regression, security-scan]
    if: always()
    
    steps:
    - name: 通知测试结果
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#ci-cd'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      if: always()