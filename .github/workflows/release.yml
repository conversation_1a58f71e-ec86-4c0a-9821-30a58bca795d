name: Release

on:
  workflow_dispatch:
    inputs:
      version:
        description: '发布版本类型'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
          - prerelease
      prerelease:
        description: '是否为预发布版本'
        required: false
        default: false
        type: boolean

jobs:
  release:
    name: 创建发布
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write
      pull-requests: write
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          registry-url: 'https://registry.npmjs.org'

      - name: 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: '8'

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 运行完整测试
        run: pnpm run test:all

      - name: 构建项目
        run: pnpm run build

      - name: 配置 Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: 版本升级
        run: |
          if [ "${{ github.event.inputs.version }}" = "prerelease" ]; then
            pnpm run version:prerelease
          else
            pnpm run version:${{ github.event.inputs.version }}
          fi

      - name: 生成变更日志
        run: pnpm run changelog

      - name: 提交版本变更
        run: |
          git add .
          git commit -m "chore: release version $(node -p "require('./package.json').version")"
          git push

      - name: 创建 Git 标签
        run: |
          VERSION=$(node -p "require('./package.json').version")
          git tag -a "v$VERSION" -m "Release v$VERSION"
          git push origin "v$VERSION"

      - name: 生成发布说明
        id: release_notes
        run: |
          VERSION=$(node -p "require('./package.json').version")
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          
          # 生成发布说明
          cat > release_notes.md << 'EOF'
          ## 🚀 新功能
          
          - 完整的微前端核心运行时
          - 插件化架构系统
          - 多框架适配器支持
          - 构建工具集成
          - 共享资源管理
          - 边车模式支持
          
          ## 🔧 改进
          
          - 优化应用加载性能
          - 增强错误处理机制
          - 改进开发者体验
          - 完善文档和示例
          
          ## 📚 文档
          
          - [快速开始](https://micro-core.dev/guide/getting-started)
          - [API 文档](https://micro-core.dev/api/)
          - [示例代码](https://micro-core.dev/examples/)
          
          ## 🐛 修复
          
          - 修复路由匹配问题
          - 解决样式隔离冲突
          - 优化内存使用
          
          ## 💔 破坏性变更
          
          无破坏性变更
          
          ## 📦 安装
          
          ```bash
          npm install @micro-core/core@$VERSION
          ```
          
          ## 🔗 相关链接
          
          - [GitHub](https://github.com/your-org/micro-core)
          - [文档站点](https://micro-core.dev)
          - [NPM](https://www.npmjs.com/package/@micro-core/core)
          EOF

      - name: 创建 GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ steps.release_notes.outputs.version }}
          release_name: Release v${{ steps.release_notes.outputs.version }}
          body_path: release_notes.md
          draft: false
          prerelease: ${{ github.event.inputs.prerelease }}

      - name: 发布到 NPM
        run: pnpm run publish:all
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: 发送发布通知
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: |
            🎉 micro-core v${{ steps.release_notes.outputs.version }} 发布成功！
            
            📦 NPM: https://www.npmjs.com/package/@micro-core/core
            📚 文档: https://micro-core.dev
            🔗 GitHub: https://github.com/your-org/micro-core/releases/tag/v${{ steps.release_notes.outputs.version }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}