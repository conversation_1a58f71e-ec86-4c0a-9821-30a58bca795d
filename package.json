{"name": "@micro-core/monorepo", "version": "0.1.0", "description": "现代化微前端框架，支持多框架集成和插件化架构", "type": "module", "private": true, "workspaces": ["packages/*", "apps/*"], "scripts": {"dev": "pnpm run --parallel dev", "build": "pnpm run --recursive build", "clean": "pnpm run --recursive clean && rimraf dist coverage .nyc_output", "lint": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "lint:check": "eslint . --ext .ts,.tsx,.js,.jsx", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md,yml,yaml}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md,yml,yaml}\"", "type-check": "pnpm run --recursive type-check", "test": "pnpm run test:unit", "test:unit": "vitest run", "test:unit:watch": "vitest", "test:integration": "pnpm run --recursive test:integration", "test:e2e": "playwright test", "test:performance": "pnpm run --recursive test:performance", "test:all": "pnpm run test:unit && pnpm run test:integration && pnpm run test:e2e && pnpm run test:performance", "test:coverage": "vitest run --coverage", "test:smoke:production": "pnpm run --recursive test:smoke:production", "docs:dev": "cd docs && pnpm run dev", "docs:build": "cd docs && pnpm run build", "docs:preview": "cd docs && pnpm run preview", "docs:generate-api": "cd docs && pnpm run generate-api-docs", "playground:dev": "cd apps/playground && pnpm run dev", "playground:build": "cd apps/playground && pnpm run build", "examples:dev": "cd apps/examples && pnpm run dev", "examples:build": "cd apps/examples && pnpm run build", "version:patch": "npm version patch --no-git-tag-version", "version:minor": "npm version minor --no-git-tag-version", "version:major": "npm version major --no-git-tag-version", "version:prerelease": "npm version prerelease --no-git-tag-version", "release": "node scripts/release.js", "release:patch": "node scripts/release.js patch", "release:minor": "node scripts/release.js minor", "release:major": "node scripts/release.js major", "release:prerelease": "node scripts/release.js prerelease", "release:auto": "semantic-release", "publish:all": "node scripts/publish.js", "publish:dry-run": "node scripts/publish.js --dry-run", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "docker:build": "docker build -t micro-core .", "docker:build:docs": "docker build --target docs -t micro-core:docs .", "docker:build:cdn": "docker build --target cdn -t micro-core:cdn .", "docker:dev": "docker-compose up dev", "docker:test": "docker-compose up test", "docker:e2e": "docker-compose up e2e", "docker:docs": "docker-compose up docs", "docker:cdn": "docker-compose up cdn", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "prepare": "husky install", "postinstall": "pnpm run build"}, "keywords": ["微前端", "micro-frontend", "微服务", "模块联邦", "single-spa", "qiankun", "react", "vue", "angular", "typescript"], "author": {"name": "Micro Core Team", "email": "<EMAIL>", "url": "https://micro-core.dev"}, "license": "MIT", "homepage": "https://micro-core.dev", "repository": {"type": "git", "url": "https://github.com/your-org/micro-core.git"}, "bugs": {"url": "https://github.com/your-org/micro-core/issues"}, "engines": {"node": ">=16.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0", "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@playwright/test": "^1.40.1", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitest/coverage-v8": "^3.2.4", "conventional-changelog-cli": "^4.1.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "rimraf": "^5.0.5", "semantic-release": "^22.0.8", "typescript": "^5.3.3", "vitest": "^3.2.4"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release": {"branches": ["main", {"name": "develop", "prerelease": "beta"}], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github"]}}