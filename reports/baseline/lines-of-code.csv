language,filename,blank,comment,code,"github.com/AlDanial/cloc v 2.06  T=1.96 s (274.9 files/s 58999.0 lines/s)"
JavaScript,packages/shared/dist/index.cjs,20,339,4158
JavaScript,packages/shared/dist/index.js,19,339,3857
Markdown,packages/shared/README.md,164,0,876
Markdown,packages/builders/README.md,122,0,547
TypeScript,packages/shared/helpers/src/index.ts,107,68,540
TypeScript,packages/plugins/plugin-router/tests/unit/router-plugin.test.ts,135,26,498
Markdown,packages/sidecar/docs/examples/basic-usage.md,92,4,496
TypeScript,packages/plugins/plugin-router/src/enhanced-router-manager.ts,107,181,494
Markdown,packages/adapters/adapter-solid/README.md,145,0,473
TypeScript,packages/builders/builder-parcel/tests/unit/parcel-builder.test.ts,91,9,473
TypeScript,packages/plugins/plugin-loader-wasm/src/wasm-module-manager.ts,96,108,473
Markdown,packages/adapters/adapter-svelte/README.md,157,2,464
TypeScript,packages/sidecar/tests/integration/sidecar-integration.test.ts,121,60,457
Markdown,packages/plugins/plugin-sandbox-webcomponent/README.md,132,5,455
Markdown,packages/sidecar/README.md,102,0,455
TypeScript,packages/plugins/plugin-sandbox-proxy/tests/unit/proxy-sandbox-plugin.test.ts,127,43,454
Markdown,packages/plugins/plugin-router/README.md,119,0,448
TypeScript,packages/adapters/adapter-vue3/src/utils/vue-utils.ts,66,127,436
TypeScript,packages/plugins/plugin-devtools/src/devtools-plugin.ts,87,121,436
TypeScript,packages/builders/builder-rspack/tests/unit/rspack-builder.test.ts,89,10,433
TypeScript,packages/adapters/adapter-solid/tests/unit/solid-adapter.test.ts,113,25,432
TypeScript,packages/builders/builder-vite/tests/vite-builder.test.ts,83,9,426
Markdown,packages/shared/docs/performance-guide.md,106,0,426
Markdown,packages/adapters/README.md,126,0,420
TypeScript,packages/builders/builder-turbopack/tests/unit/turbopack-builder.test.ts,91,12,419
TypeScript,packages/plugins/plugin-communication/tests/unit/communication-plugin.test.ts,132,79,417
TypeScript,packages/shared/utils/src/performance.ts,82,111,416
Markdown,packages/builders/builder-rollup/README.md,93,1,410
TypeScript,packages/adapters/adapter-solid/tests/integration/signal-bridge.test.ts,107,44,398
Markdown,packages/shared/docs/api.md,114,0,395
TypeScript,packages/adapters/adapter-vue2/tests/vue2-adapter.test.ts,81,9,386
TypeScript,packages/sidecar/tests/browser-compat.test.ts,93,47,382
Markdown,packages/shared/docs/coding-standards.md,73,0,381
TypeScript,packages/adapters/tests/setup.ts,61,89,373
Markdown,packages/builders/builder-turbopack/README.md,68,0,371
TypeScript,packages/core/src/runtime/lifecycle-manager.ts,96,115,370
JavaScript,packages/adapters/scripts/test-runner.js,86,106,365
TypeScript,packages/sidecar/tests/unit/message-bridge.test.ts,85,14,365
TypeScript,packages/adapters/adapter-vue2/src/utils/vue2-utils.ts,57,123,364
TypeScript,packages/adapters/tests/integration/adapter-integration.test.ts,100,21,363
TypeScript,packages/shared/types/__tests__/index.test.ts,54,7,361
Markdown,packages/plugins/plugin-communication/README.md,116,0,360
TypeScript,packages/builders/builder-webpack/src/index.ts,47,71,347
TypeScript,packages/core/__tests__/boundary.test.ts,74,26,344
TypeScript,packages/builders/builder-esbuild/tests/unit/esbuild-builder.test.ts,71,8,341
TypeScript,packages/builders/builder-vite/src/utils.ts,32,56,341
TypeScript,packages/plugins/plugin-auth/tests/unit/auth-plugin-corrected.test.ts,84,15,337
TypeScript,packages/builders/builder-vite/src/vite-builder.ts,52,65,336
TypeScript,packages/sidecar/src/compat-mode.ts,80,136,336
TypeScript,packages/plugins/test/setup.ts,32,31,335
TypeScript,packages/adapters/shared/adapter-utils.ts,64,118,334
TypeScript,packages/plugins/plugin-loader-wasm/src/wasm-instance-pool.ts,65,70,330
TypeScript,packages/plugins/plugin-auth/tests/integration/auth-integration.test.ts,81,44,328
TypeScript,packages/plugins/plugin-communication/src/enhanced-communication-manager.ts,68,119,328
TypeScript,packages/adapters/adapter-vue2/src/vue2-adapter.ts,58,84,327
Markdown,packages/builders/builder-rspack/README.md,63,0,326
TypeScript,packages/builders/builder-rspack/src/rspack-builder.ts,35,41,325
TypeScript,packages/shared/utils/src/security-validator.ts,67,95,323
Markdown,packages/sidecar/docs/MIGRATION_GUIDE.md,78,0,318
TypeScript,packages/adapters/adapter-react/tests/unit/react-adapter.test.ts,62,12,315
TypeScript,packages/sidecar/src/isolation/event-isolator.ts,20,95,315
TypeScript,packages/core/__tests__/micro-core.test.ts,69,4,314
TypeScript,packages/sidecar/src/utils/error-handler.ts,53,110,312
Markdown,packages/builders/builder-webpack/README.md,71,0,311
TypeScript,packages/plugins/plugin-communication/src/index.ts,68,153,311
TypeScript,packages/plugins/plugin-sandbox-proxy/src/enhanced-proxy-sandbox.ts,83,155,311
Markdown,packages/shared/CONTRIBUTING.md,81,0,308
TypeScript,packages/shared/utils/src/error-handler.ts,46,68,306
TypeScript,packages/sidecar/src/core/sidecar-manager.ts,65,105,306
TypeScript,packages/adapters/adapter-svelte/tests/integration/component-wrapper.test.ts,71,34,303
TypeScript,packages/adapters/adapter-vue2/src/utils.ts,51,73,301
TypeScript,packages/adapters/adapter-react/src/utils/react-utils.ts,43,81,300
TypeScript,packages/shared/utils/src/framework-detector.ts,64,83,298
TypeScript,packages/shared/utils/__tests__/object.test.ts,62,5,296
TypeScript,packages/sidecar/src/utils/security-validator.ts,68,92,296
TypeScript,packages/plugins/plugin-devtools/src/enhanced-devtools-manager.ts,61,74,293
TypeScript,packages/adapters/adapter-vue3/tests/unit/vue3-adapter.test.ts,70,9,292
TypeScript,packages/builders/builder-rollup/tests/unit/rollup-builder.test.ts,59,8,292
TypeScript,packages/sidecar/src/index.ts,54,91,292
TypeScript,packages/builders/builder-webpack/tests/unit/webpack-builder.test.ts,58,10,289
TypeScript,packages/core/src/runtime/app-loader.ts,47,56,289
TypeScript,packages/adapters/adapter-vue3/src/vue3-adapter.ts,71,102,288
TypeScript,packages/plugins/plugin-loader-wasm/src/wasm-loader-plugin.ts,63,92,287
TypeScript,packages/adapters/adapter-react/src/utils/fiber-utils.ts,69,107,285
TypeScript,packages/shared/types/src/index.ts,68,357,284
TypeScript,packages/plugins/plugin-auth/tests/unit/auth-plugin.test.ts,73,11,282
Markdown,packages/plugins/plugin-sandbox-proxy/README.md,91,0,281
TypeScript,packages/shared/utils/src/env.ts,41,69,281
TypeScript,packages/adapters/shared/src/base-adapter.ts,80,103,280
TypeScript,packages/core/src/runtime/kernel.ts,73,107,279
TypeScript,packages/plugins/plugin-wujie-compat/src/wujie-api.ts,74,125,275
TypeScript,packages/adapters/adapter-vue3/src/utils.ts,45,64,273
TypeScript,packages/plugins/plugin-prefetch/src/prefetch-plugin.ts,54,92,273
TypeScript,packages/plugins/plugin-auth/src/auth-plugin.ts,73,104,272
TypeScript,packages/plugins/plugin-loader-worker/src/cache-manager.ts,51,56,272
TypeScript,packages/shared/constants/__tests__/index.test.ts,39,6,272
Markdown,packages/core/README.md,71,0,271
TypeScript,packages/plugins/plugin-sandbox-webcomponent/src/utils/slot-manager.ts,56,100,271
TypeScript,packages/shared/utils/src/dom.ts,53,101,271
TypeScript,packages/shared/utils/__tests__/string.test.ts,39,6,270
TypeScript,packages/adapters/adapter-html/tests/html-adapter.test.ts,70,10,267
TypeScript,packages/plugins/plugin-loader-worker/src/worker-manager.ts,60,65,266
Markdown,packages/adapters/adapter-angular/README.md,61,0,265
Markdown,packages/plugins/plugin-auth/README.md,71,0,265
TypeScript,packages/adapters/shared/src/utils.ts,47,64,263
TypeScript,packages/plugins/plugin-sandbox-webcomponent/src/micro-app-element.ts,49,78,263
Markdown,packages/builders/builder-parcel/README.md,67,0,262
TypeScript,packages/adapters/adapter-react/src/react-adapter.ts,45,69,261
TypeScript,packages/adapters/adapter-svelte/tests/unit/lifecycles.test.ts,67,17,261
TypeScript,packages/plugins/plugin-sandbox-proxy/src/proxy-sandbox-plugin.ts,60,76,260
TypeScript,packages/builders/shared/utils.ts,51,110,257
TypeScript,packages/plugins/plugin-sandbox-webcomponent/src/utils/css-processor.ts,75,122,257
TypeScript,packages/shared/utils/src/logger.ts,55,126,256
TypeScript,packages/sidecar/tests/unit/compat-mode.test.ts,51,13,255
TypeScript,packages/shared/helpers/__tests__/index.test.ts,60,19,253
TypeScript,packages/plugins/plugin-router/src/index.ts,58,119,252
Markdown,packages/builders/builder-esbuild/README.md,66,0,251
TypeScript,packages/plugins/plugin-qiankun-compat/src/index.ts,64,114,249
TypeScript,packages/plugins/plugin-loader-wasm/src/wasm-manager.ts,59,65,247
TypeScript,packages/shared/utils/src/url.ts,36,80,247
TypeScript,packages/sidecar/src/core/config-manager.ts,48,86,247
TypeScript,packages/builders/builder-vite/src/vite-plugin.ts,63,21,246
TypeScript,packages/builders/builder-webpack/src/utils.ts,40,52,246
TypeScript,packages/shared/utils/src/storage.ts,48,92,246
TypeScript,packages/builders/shared/base-builder.ts,65,99,245
TypeScript,packages/shared/utils/src/date.ts,43,107,244
TypeScript,packages/builders/builder-esbuild/src/esbuild-builder.ts,30,54,240
TypeScript,packages/plugins/plugin-sandbox-composer/src/sandbox-composer.ts,31,51,240
TypeScript,packages/sidecar/src/sidecar.ts,51,80,240
TypeScript,packages/core/src/runtime/resource-manager.ts,46,77,238
TypeScript,packages/plugins/plugin-loader-worker/src/worker-loader-plugin.ts,43,71,238
TypeScript,packages/sidecar/src/legacy-apps/vanilla-js-app.ts,47,76,238
TypeScript,packages/shared/constants/src/index.ts,35,199,237
TypeScript,packages/adapters/adapter-react/src/utils.ts,44,67,236
TypeScript,packages/core/__tests__/performance.test.ts,55,36,234
TypeScript,packages/shared/utils/src/array.ts,43,107,234
TypeScript,packages/adapters/adapter-html/src/html-adapter.ts,42,87,233
TypeScript,packages/plugins/plugin-sandbox-webcomponent/src/sandbox-container.ts,44,78,233
TypeScript,packages/shared/utils/__tests__/type-check.test.ts,29,6,233
TypeScript,packages/shared/utils/src/async.ts,40,53,233
Markdown,packages/sidecar/docs/README.md,58,1,233
TypeScript,packages/plugins/__tests__/router-plugin.test.ts,64,18,232
TypeScript,packages/plugins/plugin-sandbox-namespace/src/namespace-sandbox.ts,49,81,231
TypeScript,packages/shared/utils/src/object.ts,44,70,231
TypeScript,packages/sidecar/src/utils/performance-monitor.ts,48,93,231
JavaScript,packages/builders/scripts/test-runner.js,47,25,230
TypeScript,packages/plugins/plugin-sandbox-webcomponent/src/index.ts,64,99,229
TypeScript,packages/core/__tests__/lifecycle-manager.test.ts,67,5,227
Markdown,packages/plugins/README.md,80,0,227
TypeScript,packages/sidecar/src/types.ts,32,233,227
TypeScript,packages/shared/utils/src/event-bus.ts,60,95,226
Markdown,packages/builders/builder-vite/README.md,46,0,224
TypeScript,packages/plugins/plugin-sandbox-defineproperty/src/defineproperty-sandbox.ts,55,75,223
TypeScript,packages/plugins/plugin-sandbox-federation/src/federation-sandbox.ts,51,80,222
TypeScript,packages/shared/utils/src/function.ts,37,62,222
Markdown,packages/plugins/plugin-qiankun-compat/README.md,63,0,221
TypeScript,packages/adapters/adapter-react/src/lifecycles.ts,47,58,217
TypeScript,packages/sidecar/src/utils/framework-detector.ts,49,68,217
TypeScript,packages/adapters/adapter-angular/src/index.ts,50,93,216
TypeScript,packages/core/__tests__/core-basic.test.ts,54,19,215
TypeScript,packages/builders/builder-parcel/src/parcel-builder.ts,29,47,213
TypeScript,packages/builders/tests/test-utils.ts,29,62,213
TypeScript,packages/shared/types/src/sidecar.ts,30,227,213
Markdown,packages/adapters/shared/README.md,66,0,208
TypeScript,packages/adapters/adapter-react/src/hooks.ts,60,43,206
TypeScript,packages/sidecar/src/bridge/message-bridge.ts,51,53,205
TypeScript,packages/core/src/types/strict.ts,34,145,204
Markdown,packages/shared/PROJECT_SUMMARY.md,44,0,204
TypeScript,packages/sidecar/src/isolation/script-isolator.ts,23,89,204
TypeScript,packages/core/__tests__/app-registry.test.ts,42,4,203
TypeScript,packages/core/src/runtime/app-registry.ts,44,62,203
TypeScript,packages/plugins/plugin-sandbox-proxy/src/proxy-handler.ts,54,76,201
TypeScript,packages/shared/utils/src/string.ts,45,102,201
TypeScript,packages/adapters/shared/base-adapter.ts,46,162,198
TypeScript,packages/plugins/plugin-router/src/history-adapter.ts,41,73,196
TypeScript,packages/adapters/adapter-react/src/error-boundary.tsx,15,10,195
TypeScript,packages/shared/test/setup.ts,32,25,193
TypeScript,packages/sidecar/src/isolation/global-isolator.ts,31,120,192
TypeScript,packages/builders/builder-webpack/src/webpack-builder.ts,25,32,187
TypeScript,packages/sidecar/src/isolation/isolation-container.ts,35,57,187
TypeScript,packages/sidecar/tests/unit/framework-detector.test.ts,60,16,187
TypeScript,packages/core/__tests__/integration.test.ts,47,37,186
TypeScript,packages/core/src/runtime/error-handler.ts,46,79,186
TypeScript,packages/plugins/plugin-qiankun-compat/src/qiankun-adapter.ts,27,59,186
TypeScript,packages/plugins/plugin-sandbox-iframe/src/iframe-sandbox.ts,47,72,186
TypeScript,packages/sidecar/src/core/auto-discovery.ts,42,68,185
Markdown,packages/shared/helpers/README.md,36,0,182
TypeScript,packages/plugins/plugin-sandbox-proxy/src/proxy-sandbox.ts,34,46,181
TypeScript,packages/builders/builder-turbopack/src/turbopack-builder.ts,23,37,180
TypeScript,packages/plugins/plugin-sandbox-webcomponent/src/utils/shadow-dom-utils.ts,40,65,179
TypeScript,packages/shared/types/src/error.ts,21,58,179
TypeScript,packages/sidecar/src/legacy-apps/jquery-app.ts,34,61,179
TypeScript,packages/shared/constants/index.ts,31,74,178
Markdown,packages/shared/types/README.md,35,0,177
TypeScript,packages/plugins/plugin-sandbox-webcomponent/src/types.ts,20,177,175
TypeScript,packages/sidecar/src/bridge/message-filter.ts,41,89,173
TypeScript,packages/plugins/plugin-wujie-compat/src/wujie-adapter.ts,46,64,172
TypeScript,packages/sidecar/src/bridge/message-serializer.ts,37,36,171
TypeScript,packages/plugins/plugin-metrics/src/metrics-collector.ts,32,59,169
TypeScript,packages/plugins/plugin-sandbox-proxy/src/index.ts,50,76,168
Markdown,packages/adapters/FINAL_OPTIMIZATION_REPORT.md,42,0,167
TypeScript,packages/plugins/plugin-communication/src/global-state.ts,47,75,166
TypeScript,packages/core/src/errors.ts,30,120,162
TypeScript,packages/adapters/adapter-angular/tests/angular-adapter.test.ts,39,11,160
TypeScript,packages/plugins/plugin-router/src/router-sync.ts,35,53,160
TypeScript,packages/sidecar/src/auto-config.ts,37,51,160
TypeScript,packages/adapters/adapter-vue3/src/types.ts,36,144,159
TypeScript,packages/core/__tests__/plugin-system.test.ts,32,3,158
TypeScript,packages/builders/builder-esbuild/src/options.ts,16,28,157
TypeScript,packages/adapters/adapter-angular/src/angular-adapter.ts,32,64,155
TypeScript,packages/sidecar/tests/unit/auto-config.test.ts,36,12,153
JSON,packages/shared/package.json,0,0,151
TypeScript,packages/builders/tests/setup.ts,26,24,150
TypeScript,packages/core/__tests__/kernel.test.ts,30,7,150
TypeScript,packages/plugins/plugin-qiankun-compat/src/api.ts,34,58,149
TypeScript,packages/plugins/plugin-sandbox-webcomponent/src/style-injector.ts,33,53,149
TypeScript,packages/adapters/adapter-svelte/src/index.ts,27,34,143
TypeScript,packages/plugins/plugin-communication/src/communication-plugin.ts,24,45,142
TypeScript,packages/sidecar/src/isolation/style-isolator.ts,32,67,141
TypeScript,packages/adapters/adapter-react/src/lifecycle-adapter.ts,48,90,140
TypeScript,packages/plugins/plugin-auth/src/token-manager.ts,31,54,140
TypeScript,packages/builders/builder-rollup/src/rollup-builder.ts,33,80,139
TypeScript,packages/builders/builder-vite/src/types.ts,22,95,138
Markdown,packages/shared/CHANGELOG.md,32,0,138
TypeScript,packages/plugins/plugin-communication/src/event-bus.ts,28,51,137
TypeScript,packages/core/src/sandbox/base-sandbox.ts,31,93,133
TypeScript,packages/core/src/runtime/plugin-system.ts,30,82,129
TypeScript,packages/plugins/plugin-qiankun-compat/src/html-entry.ts,32,52,129
TypeScript,packages/plugins/plugin-router/src/router-manager.ts,30,60,129
TypeScript,packages/adapters/shared/src/types.ts,27,149,126
TypeScript,packages/sidecar/tests/setup.ts,25,23,124
Markdown,packages/adapters/OPTIMIZATION_SUMMARY.md,33,0,123
Markdown,packages/plugins/plugin-loader-wasm/README.md,42,0,123
TypeScript,packages/shared/utils/src/type-check.ts,32,88,123
TypeScript,packages/sidecar/tests/unit/sidecar-manager.test.ts,26,10,122
TypeScript,packages/plugins/plugin-loader-worker/src/worker-script.ts,27,26,120
JSON,packages/plugins/package.json,0,0,118
TypeScript,packages/adapters/adapter-vue3/src/lifecycles.ts,30,46,117
TypeScript,packages/builders/builder-webpack/src/types.ts,13,115,114
JSON,packages/builders/package.json,0,0,114
TypeScript,packages/plugins/plugin-qiankun-compat/src/qiankun-compat-plugin.ts,25,43,113
TypeScript,packages/core/__tests__/event-bus.test.ts,36,1,112
Markdown,packages/shared/constants/README.md,27,0,111
TypeScript,packages/plugins/plugin-prefetch/src/resource-prefetcher.ts,25,43,110
TypeScript,packages/plugins/plugin-wujie-compat/src/props-bridge.ts,25,58,110
TypeScript,packages/adapters/adapter-vue2/src/lifecycles.ts,30,47,109
TypeScript,packages/adapters/shared/src/constants.ts,11,36,109
TypeScript,packages/plugins/plugin-wujie-compat/src/wujie-compat-plugin.ts,20,43,108
TypeScript,packages/builders/shared/types.ts,15,92,107
TypeScript,packages/plugins/plugin-auth/src/permission-checker.ts,44,67,107
TypeScript,packages/sidecar/src/bridge/shared-worker-bridge.ts,27,41,104
TypeScript,packages/adapters/adapter-solid/src/solid-adapter.ts,23,12,102
TypeScript,packages/plugins/plugin-auth/src/auth-guard.ts,26,52,101
TypeScript,packages/core/src/types/app.ts,17,124,99
Markdown,packages/shared/utils/README.md,25,0,99
TypeScript,packages/plugins/plugin-sandbox-namespace/src/namespace-sandbox-plugin.ts,19,30,98
TypeScript,packages/adapters/adapter-vue2/src/index.ts,18,54,97
Markdown,packages/plugins/plugin-loader-worker/README.md,36,0,97
TypeScript,packages/core/src/communication/event-bus.ts,27,61,96
TypeScript,packages/plugins/plugin-sandbox-federation/src/federation-sandbox-plugin.ts,18,30,93
TypeScript,packages/sidecar/src/bridge/post-message-bridge.ts,26,46,92
TypeScript,packages/adapters/adapter-react/src/component-wrapper.tsx,16,12,90
TypeScript,packages/adapters/adapter-vue3/src/index.ts,16,48,89
TypeScript,packages/core/src/constants.ts,14,102,89
TypeScript,packages/plugins/plugin-auth/src/types.ts,12,39,89
TypeScript,packages/builders/builder-webpack/src/plugins.ts,17,24,88
TypeScript,packages/core/__tests__/setup.ts,13,12,87
TypeScript,packages/sidecar/src/bridge/custom-event-bridge.ts,27,44,87
TypeScript,packages/plugins/plugin-loader-worker/src/index.ts,23,51,86
TypeScript,packages/plugins/plugin-wujie-compat/src/index.ts,20,21,86
JavaScript,packages/shared/vitest-config/__tests__/index.test.js,18,6,85
TypeScript,packages/core/src/types/common.ts,17,84,84
TypeScript,packages/plugins/plugin-wujie-compat/src/iframe-bridge.ts,23,46,84
JSON,packages/sidecar/package.json,0,0,84
TypeScript,packages/plugins/plugin-sandbox-defineproperty/src/defineproperty-sandbox-plugin.ts,16,27,83
TypeScript,packages/plugins/plugin-router/src/router-plugin.ts,15,34,79
TypeScript,packages/plugins/plugin-sandbox-iframe/src/post-message-bridge.ts,18,38,79
TypeScript,packages/adapters/adapter-vue2/src/types.ts,9,54,77
TypeScript,packages/plugins/plugin-prefetch/src/types.ts,46,81,75
JSON,packages/adapters/shared/package.json,0,0,74
TypeScript,packages/core/src/utils.ts,17,102,74
JavaScript,packages/shared/jest-config/__tests__/index.test.js,17,5,74
TypeScript,packages/adapters/adapter-vue3/src/constants.ts,8,26,71
JSON,packages/core/package.json,0,0,71
JavaScript,packages/shared/vitest-config/index.js,6,19,71
TypeScript,packages/adapters/vitest.config.ts,18,44,69
TypeScript,packages/builders/builder-vite/src/transform.ts,19,28,68
TypeScript,packages/adapters/adapter-react/src/index.ts,13,39,67
TypeScript,packages/builders/builder-vite/src/manifest.ts,8,19,65
TypeScript,packages/adapters/adapter-react/src/types.ts,11,45,64
TypeScript,packages/plugins/plugin-qiankun-compat/src/qiankun-api.ts,9,26,64
TypeScript,packages/plugins/plugin-sandbox-composer/src/sandbox-composer-plugin.ts,10,20,64
JSON,packages/plugins/plugin-wujie-compat/package.json,0,0,64
TypeScript,packages/plugins/plugin-logger/src/logger.ts,14,27,63
JSON,packages/builders/builder-webpack/package.json,0,0,62
TypeScript,packages/builders/builder-esbuild/src/esbuild-plugin.ts,12,11,61
TypeScript,packages/plugins/plugin-metrics/src/metrics-plugin.ts,16,21,61
JavaScript,packages/shared/jest-config/index.js,15,25,60
JSON,packages/plugins/plugin-devtools/package.json,0,0,59
Markdown,packages/shared/vitest-config/README.md,30,0,59
TypeScript,packages/builders/vitest.config.ts,17,43,56
JSON,packages/plugins/plugin-prefetch/package.json,0,0,56
TypeScript,packages/core/src/index.ts,20,76,55
TypeScript,packages/plugins/plugin-loader-worker/src/types.ts,8,42,55
TypeScript,packages/plugins/plugin-sandbox-iframe/src/index.ts,14,17,55
TypeScript,packages/shared/types/src/router.ts,9,30,55
JSON,packages/adapters/adapter-react/package.json,0,0,54
TypeScript,packages/builders/builder-parcel/vite.config.ts,2,5,54
TypeScript,packages/builders/builder-rspack/vite.config.ts,2,5,54
Markdown,packages/shared/jest-config/README.md,25,0,54
JSON,packages/adapters/adapter-angular/package.json,0,0,53
JSON,packages/builders/builder-vite/package.json,0,0,53
JSON,packages/plugins/plugin-loader-wasm/package.json,0,0,53
TypeScript,packages/plugins/plugin-qiankun-compat/src/types.ts,8,40,53
TypeScript,packages/shared/types/src/sandbox.ts,8,27,53
JSON,packages/adapters/adapter-svelte/package.json,0,0,52
JSON,packages/adapters/adapter-vue2/package.json,0,0,52
TypeScript,packages/builders/builder-esbuild/vite.config.ts,2,5,52
TypeScript,packages/builders/builder-rollup/vite.config.ts,2,5,52
TypeScript,packages/builders/builder-turbopack/vite.config.ts,2,5,52
TypeScript,packages/builders/builder-webpack/vite.config.ts,2,5,52
TypeScript,packages/plugins/plugin-loader-wasm/src/types.ts,7,41,52
JSON,packages/plugins/plugin-loader-worker/package.json,0,0,52
JSON,packages/plugins/plugin-qiankun-compat/package.json,0,0,52
JSON,packages/adapters/adapter-vue3/package.json,0,0,51
TypeScript,packages/shared/types/src/app.ts,5,18,51
TypeScript,packages/plugins/plugin-logger/src/logger-plugin.ts,14,17,50
TypeScript,packages/plugins/plugin-metrics/src/types.ts,7,28,50
TypeScript,packages/plugins/plugin-wujie-compat/src/types.ts,7,35,50
JSON,packages/plugins/plugin-sandbox-proxy/package.json,0,0,49
TypeScript,packages/shared/utils/src/index.ts,22,30,49
JSON,packages/adapters/adapter-solid/package.json,0,0,48
JSON,packages/plugins/plugin-communication/package.json,0,0,48
TypeScript,packages/plugins/plugin-loader-wasm/src/index.ts,15,37,48
JSON,packages/plugins/plugin-router/package.json,0,0,48
JSON,packages/builders/builder-esbuild/package.json,0,0,47
JSON,packages/builders/builder-parcel/package.json,0,0,47
TypeScript,packages/plugins/plugin-auth/vite.config.ts,1,1,47
TypeScript,packages/plugins/plugin-communication/vite.config.ts,1,1,47
TypeScript,packages/plugins/plugin-logger/vite.config.ts,1,1,47
TypeScript,packages/plugins/plugin-metrics/vite.config.ts,1,1,47
TypeScript,packages/plugins/plugin-prefetch/vite.config.ts,1,1,47
TypeScript,packages/plugins/plugin-sandbox-composer/src/types.ts,10,13,47
TypeScript,packages/plugins/plugin-sandbox-composer/vite.config.ts,1,1,47
TypeScript,packages/plugins/plugin-sandbox-defineproperty/vite.config.ts,1,1,47
TypeScript,packages/plugins/plugin-sandbox-federation/vite.config.ts,1,1,47
TypeScript,packages/plugins/plugin-sandbox-iframe/vite.config.ts,1,1,47
TypeScript,packages/plugins/plugin-sandbox-namespace/vite.config.ts,1,1,47
TypeScript,packages/plugins/plugin-wujie-compat/vite.config.ts,1,1,47
TypeScript,packages/core/__tests__/utils.test.ts,9,0,45
JSON,packages/plugins/plugin-auth/package.json,0,0,45
JSON,packages/plugins/plugin-sandbox-federation/package.json,0,0,45
JSON,packages/plugins/plugin-sandbox-webcomponent/package.json,0,0,45
JSON,packages/plugins/plugin-metrics/package.json,0,0,44
JSON,packages/plugins/plugin-sandbox-composer/package.json,0,0,44
JSON,packages/plugins/plugin-sandbox-defineproperty/package.json,0,0,44
TypeScript,packages/plugins/plugin-sandbox-federation/src/types.ts,8,29,44
JSON,packages/plugins/plugin-sandbox-namespace/package.json,0,0,44
TypeScript,packages/sidecar/src/legacy-apps/index.ts,9,20,44
TypeScript,packages/core/src/types/events.ts,9,21,43
TypeScript,packages/plugins/plugin-devtools/src/types.ts,5,48,43
JSON,packages/plugins/plugin-logger/package.json,0,0,43
TypeScript,packages/plugins/vitest.config.ts,1,16,43
TypeScript,packages/adapters/adapter-solid/vite.config.ts,1,0,42
TypeScript,packages/adapters/adapter-svelte/vite.config.ts,1,0,42
TypeScript,packages/builders/builder-vite/vite.config.ts,1,0,42
JavaScript,packages/plugins/test-runner.js,10,5,42
JSON,packages/builders/tsconfig.json,0,0,41
TypeScript,packages/plugins/plugin-sandbox-namespace/src/types.ts,6,32,41
JSON,packages/adapters/adapter-html/package.json,0,0,40
TypeScript,packages/adapters/adapter-vue2/vite.config.ts,1,0,40
JSON,packages/shared/jest-config/package.json,0,0,40
TypeScript,packages/shared/types/src/event.ts,7,24,40
JSON,packages/shared/utils/package.json,0,0,40
TypeScript,packages/adapters/adapter-html/vite.config.ts,1,0,38
JSON,packages/sidecar/tsconfig.json,0,0,38
TypeScript,packages/adapters/adapter-react/vite.config.ts,1,0,37
TypeScript,packages/adapters/adapter-vue3/vite.config.ts,1,0,37
TypeScript,packages/core/src/types/plugin.ts,10,29,37
TypeScript,packages/core/src/types/resource.ts,5,45,37
JSON,packages/shared/vitest-config/package.json,0,0,37
JSON,packages/adapters/adapter-react/tsconfig.json,0,0,36
TypeScript,packages/adapters/shared/vite.config.ts,1,0,36
JSON,packages/shared/ts-config/base.json,0,0,36
TypeScript,packages/plugins/plugin-sandbox-defineproperty/src/types.ts,6,26,35
TypeScript,packages/shared/types/src/core.ts,4,15,35
TypeScript,packages/adapters/adapter-angular/vite.config.ts,1,0,34
TypeScript,packages/plugins/plugin-devtools/vite.config.ts,1,0,33
JSON,packages/plugins/tsconfig.json,0,0,33
JSON,packages/shared/constants/package.json,0,0,33
JSON,packages/shared/eslint-config/package.json,0,0,33
TypeScript,packages/sidecar/vitest.config.ts,2,27,32
TypeScript,packages/adapters/adapter-react/tests/unit/setup.ts,5,7,30
TypeScript,packages/adapters/adapter-solid/src/lifecycles.ts,9,5,30
TypeScript,packages/core/src/types/sandbox.ts,6,39,30
TypeScript,packages/core/vite.config.ts,1,0,30
TypeScript,packages/builders/builder-parcel/src/parcel-transformer.ts,9,9,29
TypeScript,packages/builders/tsup.config.ts,1,0,29
JSON,packages/core/tsconfig.json,0,0,29
TypeScript,packages/plugins/tsup.config.ts,1,0,29
TypeScript,packages/shared/types/src/communication.ts,4,15,29
JSON,packages/shared/helpers/package.json,0,0,28
JSON,packages/shared/tsconfig.json,0,0,28
JSON,packages/shared/types/package.json,0,0,28
TypeScript,packages/shared/types/src/resource.ts,4,15,28
TypeScript,packages/shared/types/src/utils.ts,17,54,28
JSON,packages/adapters/adapter-solid/tsconfig.json,0,0,27
TypeScript,packages/core/vitest.config.ts,1,1,27
TypeScript,packages/plugins/plugin-router/vite.config.ts,1,0,27
TypeScript,packages/plugins/plugin-sandbox-proxy/vite.config.ts,1,0,27
TypeScript,packages/plugins/plugin-sandbox-webcomponent/vite.config.ts,1,0,27
TypeScript,packages/builders/builder-esbuild/src/types.ts,3,20,26
TypeScript,packages/core/src/types/communication.ts,7,40,26
TypeScript,packages/plugins/plugin-loader-wasm/vite.config.ts,1,0,26
TypeScript,packages/plugins/plugin-loader-worker/vite.config.ts,1,0,26
TypeScript,packages/plugins/plugin-qiankun-compat/vite.config.ts,1,0,26
JSON,packages/plugins/plugin-router/tsconfig.json,0,0,26
TypeScript,packages/sidecar/vite.config.ts,1,0,26
JSON,packages/adapters/adapter-vue2/tsconfig.json,0,0,25
TypeScript,packages/core/src/types/router.ts,5,34,25
TypeScript,packages/shared/vitest.config.ts,1,1,25
JSON,packages/adapters/adapter-vue3/tsconfig.json,0,0,24
JSON,packages/adapters/shared/tsconfig.json,0,0,24
JSON,packages/shared/prettier-config/package.json,0,0,24
TypeScript,packages/shared/types/src/lifecycle.ts,4,15,24
TypeScript,packages/sidecar/tsup.config.ts,1,0,24
TypeScript,packages/builders/builder-parcel/src/types.ts,3,17,23
TypeScript,packages/plugins/plugin-sandbox-iframe/src/types.ts,3,20,23
JSON,packages/shared/ts-config/tsconfig.json,0,0,23
TypeScript,packages/core/src/types/error.ts,5,31,22
JSON,packages/plugins/plugin-loader-worker/tsconfig.json,0,0,22
TypeScript,packages/builders/shared/index.ts,3,8,21
TypeScript,packages/core/src/types/lifecycle.ts,4,28,21
TypeScript,packages/plugins/plugin-logger/src/types.ts,4,13,21
JSON,packages/plugins/plugin-qiankun-compat/tsconfig.json,0,0,21
JavaScript,packages/shared/eslint-config/base.js,0,0,21
JSON,packages/shared/ts-config/package.json,0,0,21
TypeScript,packages/shared/types/src/plugin.ts,2,9,21
JSON,packages/adapters/adapter-angular/tsconfig.json,0,0,20
TypeScript,packages/adapters/adapter-solid/src/types.ts,4,16,20
TypeScript,packages/core/src/types.ts,2,7,19
TypeScript,packages/shared/types/src/adapter.ts,4,16,18
TypeScript,packages/core/tsup.config.ts,1,0,17
TypeScript,packages/shared/tsup.config.ts,1,0,17
JSON,packages/shared/types/tsconfig.json,0,0,16
JSON,packages/shared/utils/tsconfig.json,0,0,16
Bourne Shell,packages/adapters/adapter-html/node_modules/.bin/vite,2,0,15
Bourne Shell,packages/adapters/adapter-html/node_modules/.bin/vitest,2,0,15
Bourne Shell,packages/adapters/shared/node_modules/.bin/rimraf,2,0,15
Bourne Shell,packages/builders/builder-webpack/node_modules/.bin/browserslist,2,0,15
Bourne Shell,packages/builders/builder-webpack/node_modules/.bin/eslint,2,0,15
Bourne Shell,packages/builders/builder-webpack/node_modules/.bin/webpack,2,0,15
Bourne Shell,packages/builders/builder-webpack/node_modules/.bin/webpack-dev-server,2,0,15
Bourne Shell,packages/builders/node_modules/.bin/acorn,2,0,15
Bourne Shell,packages/builders/node_modules/.bin/browserslist,2,0,15
Bourne Shell,packages/builders/node_modules/.bin/rollup,2,0,15
Bourne Shell,packages/builders/node_modules/.bin/vite,2,0,15
Bourne Shell,packages/builders/node_modules/.bin/webpack,2,0,15
Bourne Shell,packages/plugins/plugin-auth/node_modules/.bin/vitest,2,0,15
JSON,packages/plugins/plugin-devtools/tsconfig.json,0,0,15
Bourne Shell,packages/plugins/plugin-sandbox-proxy/node_modules/.bin/vite,2,0,15
Bourne Shell,packages/plugins/plugin-sandbox-proxy/node_modules/.bin/vitest,2,0,15
Bourne Shell,packages/plugins/plugin-sandbox-webcomponent/node_modules/.bin/tsup,2,0,15
Bourne Shell,packages/plugins/plugin-sandbox-webcomponent/node_modules/.bin/tsup-node,2,0,15
Bourne Shell,packages/plugins/plugin-wujie-compat/node_modules/.bin/tsc,2,0,15
Bourne Shell,packages/plugins/plugin-wujie-compat/node_modules/.bin/tsserver,2,0,15
Bourne Shell,packages/plugins/plugin-wujie-compat/node_modules/.bin/vite,2,0,15
Bourne Shell,packages/plugins/plugin-wujie-compat/node_modules/.bin/vitest,2,0,15
Bourne Shell,packages/shared/eslint-config/node_modules/.bin/acorn,2,0,15
Bourne Shell,packages/shared/eslint-config/node_modules/.bin/eslint,2,0,15
Bourne Shell,packages/shared/eslint-config/node_modules/.bin/eslint-config-prettier,2,0,15
Bourne Shell,packages/shared/eslint-config/node_modules/.bin/tsc,2,0,15
Bourne Shell,packages/shared/eslint-config/node_modules/.bin/tsserver,2,0,15
JavaScript,packages/shared/eslint-config/typescript.js,0,0,15
Bourne Shell,packages/shared/prettier-config/node_modules/.bin/prettier,2,0,15
TypeScript,packages/shared/src/index.ts,6,14,15
Bourne Shell,packages/shared/vitest-config/node_modules/.bin/vitest,2,0,15
Bourne Shell,packages/sidecar/node_modules/.bin/eslint,2,0,15
Bourne Shell,packages/sidecar/node_modules/.bin/rimraf,2,0,15
Bourne Shell,packages/sidecar/node_modules/.bin/tsc,2,0,15
Bourne Shell,packages/sidecar/node_modules/.bin/tsserver,2,0,15
Bourne Shell,packages/sidecar/node_modules/.bin/tsup,2,0,15
Bourne Shell,packages/sidecar/node_modules/.bin/tsup-node,2,0,15
Bourne Shell,packages/sidecar/node_modules/.bin/vitest,2,0,15
TypeScript,packages/adapters/adapter-react/vitest.config.ts,1,0,14
TypeScript,packages/shared/types/src/builder.ts,3,13,14
Bourne Shell,packages/builders/builder-esbuild/node_modules/.bin/esbuild,2,0,12
Bourne Shell,packages/builders/node_modules/.bin/esbuild,2,0,12
Bourne Shell,packages/plugins/plugin-sandbox-webcomponent/node_modules/.bin/esbuild,2,0,12
Bourne Shell,packages/sidecar/node_modules/.bin/esbuild,2,0,12
TypeScript,packages/core/src/router/index.ts,3,21,11
TypeScript,packages/core/src/types/index.ts,1,5,11
TypeScript,packages/plugins/plugin-auth/src/index.ts,3,6,11
TypeScript,packages/plugins/plugin-devtools/src/index.ts,3,7,11
JavaScript,packages/shared/prettier-config/index.js,0,0,11
TypeScript,packages/shared/utils/tsup.config.ts,1,0,11
TypeScript,packages/core/src/communication/index.ts,3,13,9
TypeScript,packages/core/src/runtime/index.ts,2,8,8
TypeScript,packages/plugins/plugin-sandbox-defineproperty/src/index.ts,2,5,7
TypeScript,packages/plugins/plugin-sandbox-namespace/src/index.ts,2,5,7
TypeScript,packages/sidecar/src/bridge.ts,2,5,7
TypeScript,packages/plugins/plugin-sandbox-federation/src/index.ts,2,5,6
TypeScript,packages/shared/vitest-config/index.d.ts,6,20,6
TypeScript,packages/sidecar/src/isolation.ts,2,5,6
TypeScript,packages/sidecar/src/isolation/index.ts,2,6,6
TypeScript,packages/shared/jest-config/index.d.ts,5,17,5
TypeScript,packages/sidecar/src/proxy.ts,2,5,5
TypeScript,packages/adapters/adapter-solid/src/index.ts,2,7,4
TypeScript,packages/adapters/shared/src/index.ts,4,8,4
TypeScript,packages/builders/builder-esbuild/src/index.ts,2,7,4
TypeScript,packages/builders/builder-parcel/src/index.ts,2,7,4
TypeScript,packages/core/src/types/enums.ts,1,6,4
TypeScript,packages/plugins/plugin-logger/src/index.ts,2,7,4
TypeScript,packages/plugins/plugin-metrics/src/index.ts,2,7,4
TypeScript,packages/plugins/plugin-sandbox-composer/src/index.ts,2,7,4
TypeScript,packages/adapters/adapter-html/src/index.ts,3,7,3
TypeScript,packages/builders/builder-rollup/src/index.ts,1,5,3
TypeScript,packages/builders/builder-rspack/src/index.ts,1,5,3
TypeScript,packages/builders/builder-turbopack/src/index.ts,1,5,3
TypeScript,packages/builders/builder-vite/src/index.ts,1,5,3
TypeScript,packages/core/src/sandbox/index.ts,2,6,3
TypeScript,packages/shared/utils/vitest.config.ts,1,0,3
JSON,packages/adapters/adapter-react/node_modules/.vite/vitest/da39a3ee5e6b4b0d3255bfef95601890afd80709/results.json,0,0,1
JSON,packages/core/node_modules/.vite/vitest/results.json,0,0,1
JSON,packages/plugins/coverage/.tmp/coverage-0.json,0,0,1
JSON,packages/plugins/coverage/.tmp/coverage-1.json,0,0,1
JSON,packages/plugins/coverage/.tmp/coverage-2.json,0,0,1
JSON,packages/plugins/coverage/.tmp/coverage-3.json,0,0,1
JSON,packages/plugins/coverage/.tmp/coverage-4.json,0,0,1
JSON,packages/plugins/coverage/.tmp/coverage-5.json,0,0,1
JSON,packages/plugins/coverage/.tmp/coverage-6.json,0,0,1
JSON,packages/plugins/node_modules/.vite/vitest/results.json,0,0,1
JSON,packages/shared/node_modules/.vite/vitest/results.json,0,0,1
TypeScript,packages/shared/src/constants/index.ts,1,5,1
TypeScript,packages/shared/src/helpers/index.ts,1,5,1
TypeScript,packages/shared/src/types/index.ts,1,5,1
TypeScript,packages/shared/src/utils/index.ts,1,5,1
JSON,packages/sidecar/node_modules/.vite/vitest/results.json,0,0,1
SUM,,15150,18120,82185
