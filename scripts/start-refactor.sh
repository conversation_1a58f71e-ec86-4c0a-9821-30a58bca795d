#!/bin/bash

# Micro Core 重构启动脚本
# 用于一键启动重构环境和工具

set -e

echo "🚀 启动 Micro Core 重构环境..."

# 检查 Node.js 版本
NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="16.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ 需要 Node.js >= $REQUIRED_VERSION，当前版本: $NODE_VERSION"
    exit 1
fi

echo "✅ Node.js 版本检查通过: $NODE_VERSION"

# 安装依赖
echo "📦 安装项目依赖..."
npm ci

# 安装代码分析工具
echo "🔧 安装代码分析工具..."
npm install -g madge dependency-cruiser jscpd

# 创建重构分支
echo "🌿 创建重构分支..."
BRANCH_NAME="feature/packages-optimization-$(date +%Y%m%d)"
git checkout -b "$BRANCH_NAME" 2>/dev/null || git checkout "$BRANCH_NAME"

# 生成基准报告
echo "📊 生成当前状态基准报告..."
mkdir -p reports/baseline

# 代码重复率分析
echo "  - 分析代码重复率..."
jscpd --reporters json --output reports/baseline/duplication.json packages/ || true

# 依赖关系分析
echo "  - 分析依赖关系..."
madge --json packages/ > reports/baseline/dependencies.json || true

# 包体积分析
echo "  - 分析包体积..."
npm run build 2>/dev/null || true
du -sh packages/*/dist 2>/dev/null > reports/baseline/bundle-sizes.txt || echo "暂无构建产物" > reports/baseline/bundle-sizes.txt

# 测试覆盖率基线
echo "  - 生成测试覆盖率基线..."
npm run test:coverage 2>/dev/null || echo "暂无测试覆盖率数据" > reports/baseline/coverage.txt

# 性能基线
echo "  - 建立性能监控基线..."
node scripts/performance-baseline.js 2>/dev/null || echo "暂无性能基线数据" > reports/baseline/performance.txt

# 启动开发环境
echo "🛠️  启动开发环境..."

# 启动类型检查
echo "  - 启动 TypeScript 类型检查..."
npm run type-check:watch &
TYPE_CHECK_PID=$!

# 启动测试监听
echo "  - 启动测试监听..."
npm run test:watch &
TEST_PID=$!

# 启动文档服务器
echo "  - 启动文档服务器..."
npm run docs:dev &
DOCS_PID=$!

# 创建 PID 文件用于后续清理
echo "$TYPE_CHECK_PID $TEST_PID $DOCS_PID" > .refactor-pids

echo ""
echo "🎉 重构环境启动完成！"
echo ""
echo "📋 环境信息:"
echo "  - 重构分支: $BRANCH_NAME"
echo "  - 基准报告: reports/baseline/"
echo "  - 类型检查: 运行中 (PID: $TYPE_CHECK_PID)"
echo "  - 测试监听: 运行中 (PID: $TEST_PID)"
echo "  - 文档服务: 运行中 (PID: $DOCS_PID)"
echo ""
echo "🔧 可用命令:"
echo "  npm run refactor:verify  - 验证重构完成度"
echo "  npm run refactor:report  - 生成重构报告"
echo "  npm run refactor:stop    - 停止重构环境"
echo ""
echo "📚 参考文档:"
echo "  - 任务清单: .kiro/specs/packages-optimization/tasks.md"
echo "  - 迁移指南: docs/migration-guide.md"
echo "  - API 文档: http://localhost:3001"
echo ""
echo "⚠️  注意: 使用 npm run refactor:stop 或 scripts/stop-refactor.sh 来正确停止环境"