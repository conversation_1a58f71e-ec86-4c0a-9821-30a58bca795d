#!/usr/bin/env node

/**
 * 测试质量评估和增强系统
 * 
 * 此脚本用于分析测试质量，识别弱测试和缺失的边缘情况，
 * 并生成测试增强建议。
 */

const fs = require('fs');
const path = require('path');
const { analyzeTestQuality } = require('./test-quality/analyzer');
const { generateTestEnhancements } = require('./test-quality/enhancer');

// 配置
const PACKAGES_DIR = path.resolve(__dirname, '../packages');
const OUTPUT_DIR = path.resolve(__dirname, '../reports/test-quality');

// 确保输出目录存在
if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * 主函数
 */
async function main() {
    console.log('开始测试质量评估和增强...');

    try {
        // 分析测试质量
        console.log('步骤 1: 分析测试质量...');
        const testQualityReport = analyzeTestQuality(PACKAGES_DIR, OUTPUT_DIR);

        // 生成测试增强建议
        console.log('步骤 2: 生成测试增强建议...');
        const enhancementReport = generateTestEnhancements(testQualityReport, PACKAGES_DIR, OUTPUT_DIR);

        console.log('测试质量评估和增强完成！');
        console.log(`报告已生成到 ${OUTPUT_DIR}`);

        // 输出摘要
        printSummary(testQualityReport, enhancementReport);
    } catch (error) {
        console.error('执行过程中出错:', error);
        process.exit(1);
    }
}

/**
 * 打印摘要信息
 * @param {Object} testQualityReport - 测试质量报告
 * @param {Object} enhancementReport - 测试增强报告
 */
function printSummary(testQualityReport, enhancementReport) {
    console.log('\n===== 测试质量评估摘要 =====');

    let totalTests = 0;
    let totalWeakTests = 0;
    let totalMissingEdgeCases = 0;
    let totalQualityScore = 0;
    let packageCount = 0;

    Object.entries(testQualityReport).forEach(([packageName, data]) => {
        console.log(`\n包: ${packageName}`);
        console.log(`  测试文件数: ${data.testFiles.length}`);
        console.log(`  测试用例数: ${data.summary.totalTests}`);
        console.log(`  弱测试数: ${data.summary.weakTests}`);
        console.log(`  缺少边缘情况数: ${data.summary.missingEdgeCases}`);
        console.log(`  质量评分: ${data.summary.qualityScore}%`);

        totalTests += data.summary.totalTests;
        totalWeakTests += data.summary.weakTests;
        totalMissingEdgeCases += data.summary.missingEdgeCases;
        totalQualityScore += data.summary.qualityScore;
        packageCount++;
    });

    console.log('\n===== 总体测试质量 =====');
    console.log(`总测试用例数: ${totalTests}`);
    console.log(`总弱测试数: ${totalWeakTests}`);
    console.log(`总缺少边缘情况数: ${totalMissingEdgeCases}`);
    console.log(`平均质量评分: ${packageCount > 0 ? Math.round(totalQualityScore / packageCount) : 0}%`);

    console.log('\n===== 测试增强建议摘要 =====');

    let totalWeakTestEnhancements = 0;
    let totalEdgeCaseEnhancements = 0;
    let totalNewTestSuggestions = 0;

    Object.entries(enhancementReport).forEach(([packageName, data]) => {
        console.log(`\n包: ${packageName}`);
        console.log(`  弱测试增强: ${data.weakTestEnhancements.length}`);
        console.log(`  边缘情况增强: ${data.edgeCaseEnhancements.length}`);
        console.log(`  新测试建议: ${data.newTestSuggestions.length}`);

        totalWeakTestEnhancements += data.weakTestEnhancements.length;
        totalEdgeCaseEnhancements += data.edgeCaseEnhancements.length;
        totalNewTestSuggestions += data.newTestSuggestions.length;
    });

    console.log('\n===== 总体增强建议 =====');
    console.log(`总弱测试增强: ${totalWeakTestEnhancements}`);
    console.log(`总边缘情况增强: ${totalEdgeCaseEnhancements}`);
    console.log(`总新测试建议: ${totalNewTestSuggestions}`);
    console.log(`总增强建议数: ${totalWeakTestEnhancements + totalEdgeCaseEnhancements + totalNewTestSuggestions}`);
}

// 执行主函数
main().catch(error => {
    console.error('未捕获的错误:', error);
    process.exit(1);
});