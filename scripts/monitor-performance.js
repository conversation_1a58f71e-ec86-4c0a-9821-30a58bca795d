#!/usr/bin/env node

/**
 * Micro Core 性能监控脚本
 * 用于监控和分析项目性能指标
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class PerformanceMonitor {
    constructor() {
        this.metrics = {
            build: {},
            bundle: {},
            runtime: {},
            memory: {},
            network: {}
        };
        this.reportDir = path.join(process.cwd(), 'reports/performance');
        this.ensureReportDir();
    }

    /**
     * 确保报告目录存在
     */
    ensureReportDir() {
        if (!fs.existsSync(this.reportDir)) {
            fs.mkdirSync(this.reportDir, { recursive: true });
        }
    }

    /**
     * 运行所有性能监控
     */
    async runAllMonitoring() {
        console.log('🚀 开始性能监控...\n');

        await this.monitorBuildPerformance();
        await this.monitorBundleSize();
        await this.monitorRuntimePerformance();
        await this.monitorMemoryUsage();
        await this.generateReport();

        console.log('\n✅ 性能监控完成！');
        console.log(`📊 报告已保存到: ${this.reportDir}`);
    }

    /**
     * 监控构建性能
     */
    async monitorBuildPerformance() {
        console.log('⏱️  监控构建性能...');

        try {
            // 清理之前的构建产物
            execSync('npm run clean', { stdio: 'pipe' });

            // 监控完整构建时间
            const fullBuildStart = Date.now();
            execSync('npm run build', { stdio: 'pipe' });
            const fullBuildTime = Date.now() - fullBuildStart;

            // 监控增量构建时间
            const incrementalBuildStart = Date.now();
            execSync('npm run build', { stdio: 'pipe' });
            const incrementalBuildTime = Date.now() - incrementalBuildStart;

            // 监控类型检查时间
            const typeCheckStart = Date.now();
            execSync('npm run type-check', { stdio: 'pipe' });
            const typeCheckTime = Date.now() - typeCheckStart;

            this.metrics.build = {
                fullBuildTime: fullBuildTime / 1000,
                incrementalBuildTime: incrementalBuildTime / 1000,
                typeCheckTime: typeCheckTime / 1000,
                timestamp: new Date().toISOString()
            };

            console.log(`  ✅ 完整构建时间: ${(fullBuildTime / 1000).toFixed(2)}s`);
            console.log(`  ✅ 增量构建时间: ${(incrementalBuildTime / 1000).toFixed(2)}s`);
            console.log(`  ✅ 类型检查时间: ${(typeCheckTime / 1000).toFixed(2)}s`);

        } catch (error) {
            console.log(`  ❌ 构建性能监控失败: ${error.message}`);
        }
    }

    /**
     * 监控包体积
     */
    async monitorBundleSize() {
        console.log('📦 监控包体积...');

        try {
            const packages = [
                'packages/shared/types',
                'packages/shared/utils', 
                'packages/shared/test-utils',
                'packages/core',
                'packages/adapters/adapter-react',
                'packages/adapters/adapter-vue2',
                'packages/adapters/adapter-vue3',
                'packages/builders/builder-vite',
                'packages/builders/builder-webpack'
            ];

            const bundleSizes = {};
            let totalSize = 0;

            for (const pkg of packages) {
                const distPath = path.join(process.cwd(), pkg, 'dist');
                if (fs.existsSync(distPath)) {
                    try {
                        const sizeOutput = execSync(`du -sb ${distPath}`, { encoding: 'utf8' });
                        const size = parseInt(sizeOutput.split('\t')[0]);
                        const sizeKB = Math.round(size / 1024);
                        
                        bundleSizes[pkg] = {
                            bytes: size,
                            kb: sizeKB,
                            mb: (size / 1024 / 1024).toFixed(2)
                        };
                        
                        totalSize += size;
                        console.log(`  📊 ${pkg}: ${sizeKB}KB`);
                    } catch (error) {
                        console.log(`  ⚠️  无法获取 ${pkg} 的大小`);
                    }
                }
            }

            this.metrics.bundle = {
                packages: bundleSizes,
                total: {
                    bytes: totalSize,
                    kb: Math.round(totalSize / 1024),
                    mb: (totalSize / 1024 / 1024).toFixed(2)
                },
                timestamp: new Date().toISOString()
            };

            console.log(`  ✅ 总包体积: ${Math.round(totalSize / 1024)}KB`);

        } catch (error) {
            console.log(`  ❌ 包体积监控失败: ${error.message}`);
        }
    }

    /**
     * 监控运行时性能
     */
    async monitorRuntimePerformance() {
        console.log('🏃 监控运行时性能...');

        try {
            // 创建性能测试脚本
            const testScript = `
                const { performance } = require('perf_hooks');
                
                // 测试工具函数性能
                const { isObject, formatBytes } = require('./packages/shared/utils/dist');
                
                const iterations = 10000;
                const testData = { name: 'test', value: 123 };
                
                // 测试 isObject 性能
                const isObjectStart = performance.now();
                for (let i = 0; i < iterations; i++) {
                    isObject(testData);
                }
                const isObjectTime = performance.now() - isObjectStart;
                
                // 测试 formatBytes 性能
                const formatBytesStart = performance.now();
                for (let i = 0; i < iterations; i++) {
                    formatBytes(1024 * i);
                }
                const formatBytesTime = performance.now() - formatBytesStart;
                
                console.log(JSON.stringify({
                    isObject: { time: isObjectTime, iterations },
                    formatBytes: { time: formatBytesTime, iterations }
                }));
            `;

            fs.writeFileSync('/tmp/perf-test.js', testScript);
            const result = execSync('node /tmp/perf-test.js', { encoding: 'utf8' });
            const perfData = JSON.parse(result.trim());

            this.metrics.runtime = {
                functions: {
                    isObject: {
                        timeMs: perfData.isObject.time,
                        avgTimeMs: perfData.isObject.time / perfData.isObject.iterations,
                        opsPerSecond: Math.round(perfData.isObject.iterations / (perfData.isObject.time / 1000))
                    },
                    formatBytes: {
                        timeMs: perfData.formatBytes.time,
                        avgTimeMs: perfData.formatBytes.time / perfData.formatBytes.iterations,
                        opsPerSecond: Math.round(perfData.formatBytes.iterations / (perfData.formatBytes.time / 1000))
                    }
                },
                timestamp: new Date().toISOString()
            };

            console.log(`  ✅ isObject: ${this.metrics.runtime.functions.isObject.opsPerSecond} ops/s`);
            console.log(`  ✅ formatBytes: ${this.metrics.runtime.functions.formatBytes.opsPerSecond} ops/s`);

            // 清理临时文件
            fs.unlinkSync('/tmp/perf-test.js');

        } catch (error) {
            console.log(`  ❌ 运行时性能监控失败: ${error.message}`);
        }
    }

    /**
     * 监控内存使用
     */
    async monitorMemoryUsage() {
        console.log('💾 监控内存使用...');

        try {
            // 获取 Node.js 进程内存使用
            const memUsage = process.memoryUsage();
            
            // 创建内存压力测试
            const memoryTestScript = `
                const { CacheFactory } = require('./packages/shared/utils/dist');
                
                // 创建缓存并填充数据
                const cache = CacheFactory.createLRU({ maxSize: 1000 });
                
                const initialMemory = process.memoryUsage();
                
                // 填充缓存
                for (let i = 0; i < 1000; i++) {
                    cache.set('key' + i, { data: 'x'.repeat(1000), index: i });
                }
                
                const afterCacheMemory = process.memoryUsage();
                
                console.log(JSON.stringify({
                    initial: initialMemory,
                    afterCache: afterCacheMemory,
                    difference: {
                        rss: afterCacheMemory.rss - initialMemory.rss,
                        heapUsed: afterCacheMemory.heapUsed - initialMemory.heapUsed,
                        heapTotal: afterCacheMemory.heapTotal - initialMemory.heapTotal
                    }
                }));
            `;

            fs.writeFileSync('/tmp/memory-test.js', memoryTestScript);
            const result = execSync('node /tmp/memory-test.js', { encoding: 'utf8' });
            const memoryData = JSON.parse(result.trim());

            this.metrics.memory = {
                baseline: {
                    rss: Math.round(memUsage.rss / 1024 / 1024),
                    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
                    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
                    external: Math.round(memUsage.external / 1024 / 1024)
                },
                cacheTest: {
                    initialRss: Math.round(memoryData.initial.rss / 1024 / 1024),
                    afterCacheRss: Math.round(memoryData.afterCache.rss / 1024 / 1024),
                    memoryIncrease: Math.round(memoryData.difference.rss / 1024 / 1024),
                    heapIncrease: Math.round(memoryData.difference.heapUsed / 1024 / 1024)
                },
                timestamp: new Date().toISOString()
            };

            console.log(`  ✅ 基线内存使用: ${this.metrics.memory.baseline.rss}MB`);
            console.log(`  ✅ 缓存测试内存增长: ${this.metrics.memory.cacheTest.memoryIncrease}MB`);

            // 清理临时文件
            fs.unlinkSync('/tmp/memory-test.js');

        } catch (error) {
            console.log(`  ❌ 内存使用监控失败: ${error.message}`);
        }
    }

    /**
     * 生成性能报告
     */
    async generateReport() {
        console.log('📊 生成性能报告...');

        const report = {
            timestamp: new Date().toISOString(),
            summary: this.generateSummary(),
            metrics: this.metrics,
            recommendations: this.generateRecommendations()
        };

        // 保存详细报告
        const reportFile = path.join(this.reportDir, `performance-${Date.now()}.json`);
        fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

        // 保存最新报告
        const latestReportFile = path.join(this.reportDir, 'latest.json');
        fs.writeFileSync(latestReportFile, JSON.stringify(report, null, 2));

        // 生成 HTML 报告
        this.generateHTMLReport(report);

        console.log(`  ✅ 详细报告: ${reportFile}`);
        console.log(`  ✅ 最新报告: ${latestReportFile}`);
        console.log(`  ✅ HTML 报告: ${path.join(this.reportDir, 'report.html')}`);
    }

    /**
     * 生成性能摘要
     */
    generateSummary() {
        const summary = {
            build: {
                status: 'unknown',
                score: 0
            },
            bundle: {
                status: 'unknown', 
                score: 0
            },
            runtime: {
                status: 'unknown',
                score: 0
            },
            memory: {
                status: 'unknown',
                score: 0
            }
        };

        // 构建性能评分
        if (this.metrics.build.fullBuildTime) {
            const buildTime = this.metrics.build.fullBuildTime;
            if (buildTime < 30) {
                summary.build = { status: 'excellent', score: 100 };
            } else if (buildTime < 60) {
                summary.build = { status: 'good', score: 80 };
            } else if (buildTime < 120) {
                summary.build = { status: 'fair', score: 60 };
            } else {
                summary.build = { status: 'poor', score: 40 };
            }
        }

        // 包体积评分
        if (this.metrics.bundle.total) {
            const totalKB = this.metrics.bundle.total.kb;
            if (totalKB < 500) {
                summary.bundle = { status: 'excellent', score: 100 };
            } else if (totalKB < 800) {
                summary.bundle = { status: 'good', score: 80 };
            } else if (totalKB < 1200) {
                summary.bundle = { status: 'fair', score: 60 };
            } else {
                summary.bundle = { status: 'poor', score: 40 };
            }
        }

        // 运行时性能评分
        if (this.metrics.runtime.functions) {
            const avgOps = Object.values(this.metrics.runtime.functions)
                .reduce((sum, func) => sum + func.opsPerSecond, 0) / 
                Object.keys(this.metrics.runtime.functions).length;
            
            if (avgOps > 1000000) {
                summary.runtime = { status: 'excellent', score: 100 };
            } else if (avgOps > 500000) {
                summary.runtime = { status: 'good', score: 80 };
            } else if (avgOps > 100000) {
                summary.runtime = { status: 'fair', score: 60 };
            } else {
                summary.runtime = { status: 'poor', score: 40 };
            }
        }

        // 内存使用评分
        if (this.metrics.memory.baseline) {
            const baselineMemory = this.metrics.memory.baseline.rss;
            if (baselineMemory < 50) {
                summary.memory = { status: 'excellent', score: 100 };
            } else if (baselineMemory < 100) {
                summary.memory = { status: 'good', score: 80 };
            } else if (baselineMemory < 200) {
                summary.memory = { status: 'fair', score: 60 };
            } else {
                summary.memory = { status: 'poor', score: 40 };
            }
        }

        return summary;
    }

    /**
     * 生成优化建议
     */
    generateRecommendations() {
        const recommendations = [];

        // 构建性能建议
        if (this.metrics.build.fullBuildTime > 60) {
            recommendations.push({
                category: 'build',
                priority: 'high',
                title: '优化构建时间',
                description: '构建时间超过60秒，建议优化 TypeScript 配置或使用增量编译'
            });
        }

        // 包体积建议
        if (this.metrics.bundle.total && this.metrics.bundle.total.kb > 800) {
            recommendations.push({
                category: 'bundle',
                priority: 'medium',
                title: '减少包体积',
                description: '包体积超过800KB，建议启用 tree-shaking 或代码分割'
            });
        }

        // 运行时性能建议
        if (this.metrics.runtime.functions) {
            const slowFunctions = Object.entries(this.metrics.runtime.functions)
                .filter(([, func]) => func.opsPerSecond < 100000);
            
            if (slowFunctions.length > 0) {
                recommendations.push({
                    category: 'runtime',
                    priority: 'medium',
                    title: '优化函数性能',
                    description: `以下函数性能较低: ${slowFunctions.map(([name]) => name).join(', ')}`
                });
            }
        }

        // 内存使用建议
        if (this.metrics.memory.cacheTest && this.metrics.memory.cacheTest.memoryIncrease > 50) {
            recommendations.push({
                category: 'memory',
                priority: 'low',
                title: '优化内存使用',
                description: '缓存测试显示内存增长较大，建议检查内存泄漏'
            });
        }

        return recommendations;
    }

    /**
     * 生成 HTML 报告
     */
    generateHTMLReport(report) {
        const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Micro Core 性能报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .content { padding: 30px; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric-card { background: #f8f9fa; border-radius: 8px; padding: 20px; border-left: 4px solid #007bff; }
        .metric-value { font-size: 2em; font-weight: bold; color: #333; }
        .metric-label { color: #666; margin-top: 5px; }
        .status-excellent { border-left-color: #28a745; }
        .status-good { border-left-color: #17a2b8; }
        .status-fair { border-left-color: #ffc107; }
        .status-poor { border-left-color: #dc3545; }
        .recommendations { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .recommendation { margin: 10px 0; padding: 10px; background: white; border-radius: 4px; }
        .priority-high { border-left: 4px solid #dc3545; }
        .priority-medium { border-left: 4px solid #ffc107; }
        .priority-low { border-left: 4px solid #28a745; }
        .timestamp { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Micro Core 性能报告</h1>
            <p class="timestamp">生成时间: ${new Date(report.timestamp).toLocaleString('zh-CN')}</p>
        </div>
        
        <div class="content">
            <h2>📊 性能概览</h2>
            <div class="metric-grid">
                <div class="metric-card status-${report.summary.build.status}">
                    <div class="metric-value">${report.metrics.build.fullBuildTime ? report.metrics.build.fullBuildTime.toFixed(1) + 's' : 'N/A'}</div>
                    <div class="metric-label">构建时间</div>
                </div>
                <div class="metric-card status-${report.summary.bundle.status}">
                    <div class="metric-value">${report.metrics.bundle.total ? report.metrics.bundle.total.kb + 'KB' : 'N/A'}</div>
                    <div class="metric-label">包体积</div>
                </div>
                <div class="metric-card status-${report.summary.runtime.status}">
                    <div class="metric-value">${report.metrics.runtime.functions ? Object.values(report.metrics.runtime.functions)[0].opsPerSecond.toLocaleString() : 'N/A'}</div>
                    <div class="metric-label">运行时性能 (ops/s)</div>
                </div>
                <div class="metric-card status-${report.summary.memory.status}">
                    <div class="metric-value">${report.metrics.memory.baseline ? report.metrics.memory.baseline.rss + 'MB' : 'N/A'}</div>
                    <div class="metric-label">内存使用</div>
                </div>
            </div>
            
            ${report.recommendations.length > 0 ? `
            <h2>💡 优化建议</h2>
            <div class="recommendations">
                ${report.recommendations.map(rec => `
                    <div class="recommendation priority-${rec.priority}">
                        <h4>${rec.title}</h4>
                        <p>${rec.description}</p>
                    </div>
                `).join('')}
            </div>
            ` : ''}
            
            <h2>📈 详细指标</h2>
            <pre style="background: #f8f9fa; padding: 20px; border-radius: 8px; overflow-x: auto;">
${JSON.stringify(report.metrics, null, 2)}
            </pre>
        </div>
    </div>
</body>
</html>
        `;

        fs.writeFileSync(path.join(this.reportDir, 'report.html'), html);
    }
}

// 运行性能监控
if (require.main === module) {
    const monitor = new PerformanceMonitor();
    monitor.runAllMonitoring().catch(error => {
        console.error('❌ 性能监控出错:', error);
        process.exit(1);
    });
}

module.exports = PerformanceMonitor;