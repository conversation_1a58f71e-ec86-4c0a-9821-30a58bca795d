/**
 * 测试增强生成器
 * 
 * 此模块用于生成改进测试的建议和代码片段，
 * 包括添加缺失的边缘情况测试、增强弱测试等。
 */

const fs = require('fs');
const path = require('path');
const { parse } = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

/**
 * 生成测试增强建议
 * @param {Object} testQualityReport - 测试质量报告
 * @param {string} packagesDir - 包目录路径
 * @param {string} outputDir - 输出目录路径
 * @returns {Object} - 测试增强建议
 */
function generateTestEnhancements(testQualityReport, packagesDir, outputDir) {
    console.log('生成测试增强建议...');

    const enhancementReport = {};

    // 处理每个包的测试质量报告
    Object.entries(testQualityReport).forEach(([packageName, data]) => {
        enhancementReport[packageName] = {
            name: packageName,
            weakTestEnhancements: [],
            edgeCaseEnhancements: [],
            newTestSuggestions: []
        };

        // 处理弱测试
        data.weakTests.forEach(test => {
            const enhancement = generateWeakTestEnhancement(test, packagesDir);
            if (enhancement) {
                enhancementReport[packageName].weakTestEnhancements.push(enhancement);
            }
        });

        // 处理缺少边缘情况的测试
        data.missingEdgeCases.forEach(test => {
            const enhancement = generateEdgeCaseEnhancement(test, packagesDir);
            if (enhancement) {
                enhancementReport[packageName].edgeCaseEnhancements.push(enhancement);
            }
        });

        // 处理缺失的测试文件
        if (data.missingTests && data.missingTests.length > 0) {
            data.missingTests.forEach(missingTest => {
                const suggestion = generateNewTestSuggestion(missingTest, packagesDir);
                if (suggestion) {
                    enhancementReport[packageName].newTestSuggestions.push(suggestion);
                }
            });
        }
    });

    // 生成增强报告
    generateEnhancementReport(enhancementReport, outputDir);

    return enhancementReport;
}

/**
 * 生成弱测试的增强建议
 * @param {Object} test - 弱测试信息
 * @param {string} packagesDir - 包目录路径
 * @returns {Object} - 增强建议
 */
function generateWeakTestEnhancement(test, packagesDir) {
    console.log(`生成弱测试增强建议: ${test.file} - ${test.name}`);

    try {
        // 读取测试文件内容
        const content = fs.readFileSync(test.file, 'utf8');

        // 解析测试文件
        const ast = parse(content, {
            sourceType: 'module',
            plugins: ['jsx', 'typescript', 'decorators-legacy']
        });

        // 查找测试函数
        let testNode = null;
        let testParentPath = null;

        traverse(ast, {
            CallExpression(path) {
                const callee = path.node.callee;
                const args = path.node.arguments;

                // 检查是否是目标测试函数
                if (
                    ((callee.type === 'Identifier' &&
                        (callee.name === 'it' || callee.name === 'test')) ||
                        (callee.type === 'MemberExpression' &&
                            callee.object.type === 'Identifier' &&
                            (callee.object.name === 'it' || callee.object.name === 'test'))) &&
                    args[0] &&
                    args[0].type === 'StringLiteral' &&
                    args[0].value === test.name &&
                    args[1] &&
                    args[1].loc.start.line === test.line
                ) {
                    testNode = path.node;
                    testParentPath = path;
                }
            }
        });

        if (!testNode) {
            return null;
        }

        // 分析测试函数体
        const testFn = testNode.arguments[1];
        const testFnBody = testFn.body;

        // 生成增强建议
        const enhancement = {
            file: test.file,
            name: test.name,
            line: test.line,
            reason: test.reason,
            originalCode: generate(testNode).code,
            enhancedCode: '',
            explanation: ''
        };

        // 根据问题类型生成不同的增强建议
        if (test.reason === '没有断言') {
            // 为没有断言的测试添加断言
            enhancement.enhancedCode = generateAssertionEnhancement(testNode, testFn);
            enhancement.explanation = '添加了基本的断言来验证函数行为';
        } else if (test.reason.includes('复杂测试但断言太少')) {
            // 为断言太少的复杂测试添加更多断言
            enhancement.enhancedCode = generateMoreAssertionsEnhancement(testNode, testFn);
            enhancement.explanation = '添加了更多断言来全面验证函数行为';
        }

        return enhancement;
    } catch (error) {
        console.error(`生成弱测试增强建议时出错 (${test.file}):`, error.message);
        return null;
    }
}

/**
 * 生成缺少边缘情况的测试的增强建议
 * @param {Object} test - 测试信息
 * @param {string} packagesDir - 包目录路径
 * @returns {Object} - 增强建议
 */
function generateEdgeCaseEnhancement(test, packagesDir) {
    console.log(`生成边缘情况测试增强建议: ${test.file} - ${test.name}`);

    try {
        // 读取测试文件内容
        const content = fs.readFileSync(test.file, 'utf8');

        // 解析测试文件
        const ast = parse(content, {
            sourceType: 'module',
            plugins: ['jsx', 'typescript', 'decorators-legacy']
        });

        // 查找测试函数
        let testNode = null;

        traverse(ast, {
            CallExpression(path) {
                const callee = path.node.callee;
                const args = path.node.arguments;

                // 检查是否是目标测试函数
                if (
                    ((callee.type === 'Identifier' &&
                        (callee.name === 'it' || callee.name === 'test')) ||
                        (callee.type === 'MemberExpression' &&
                            callee.object.type === 'Identifier' &&
                            (callee.object.name === 'it' || callee.object.name === 'test'))) &&
                    args[0] &&
                    args[0].type === 'StringLiteral' &&
                    args[0].value === test.name &&
                    args[1] &&
                    args[1].loc.start.line === test.line
                ) {
                    testNode = path.node;
                }
            }
        });

        if (!testNode) {
            return null;
        }

        // 生成增强建议
        const enhancement = {
            file: test.file,
            name: test.name,
            line: test.line,
            reason: test.reason,
            originalCode: generate(testNode).code,
            enhancedCode: '',
            additionalTest: '',
            explanation: ''
        };

        // 根据问题类型生成不同的增强建议
        if (test.reason === '缺少错误处理测试') {
            // 生成错误处理测试
            const result = generateErrorHandlingTest(testNode, test.name);
            enhancement.additionalTest = result.code;
            enhancement.explanation = '添加了错误处理测试来验证函数在异常情况下的行为';
        } else if (test.reason === '缺少空值检查') {
            // 生成空值检查测试
            const result = generateNullCheckTest(testNode, test.name);
            enhancement.additionalTest = result.code;
            enhancement.explanation = '添加了空值检查测试来验证函数对空值的处理';
        }

        return enhancement;
    } catch (error) {
        console.error(`生成边缘情况测试增强建议时出错 (${test.file}):`, error.message);
        return null;
    }
}

/**
 * 生成新测试文件的建议
 * @param {Object} missingTest - 缺失的测试信息
 * @param {string} packagesDir - 包目录路径
 * @returns {Object} - 测试文件建议
 */
function generateNewTestSuggestion(missingTest, packagesDir) {
    console.log(`生成新测试文件建议: ${missingTest.sourceFile}`);

    try {
        // 读取源文件内容
        const sourceFilePath = path.join(packagesDir, missingTest.sourceFile);
        if (!fs.existsSync(sourceFilePath)) {
            return null;
        }

        const content = fs.readFileSync(sourceFilePath, 'utf8');

        // 解析源文件
        const ast = parse(content, {
            sourceType: 'module',
            plugins: ['jsx', 'typescript', 'decorators-legacy']
        });

        // 收集导出的函数和类
        const exports = [];

        traverse(ast, {
            ExportNamedDeclaration(path) {
                const declaration = path.node.declaration;
                if (declaration) {
                    if (declaration.type === 'FunctionDeclaration') {
                        exports.push({
                            type: 'function',
                            name: declaration.id.name,
                            node: declaration
                        });
                    } else if (declaration.type === 'ClassDeclaration') {
                        exports.push({
                            type: 'class',
                            name: declaration.id.name,
                            node: declaration
                        });
                    } else if (declaration.type === 'VariableDeclaration') {
                        declaration.declarations.forEach(declarator => {
                            if (declarator.init &&
                                (declarator.init.type === 'ArrowFunctionExpression' ||
                                    declarator.init.type === 'FunctionExpression')) {
                                exports.push({
                                    type: 'function',
                                    name: declarator.id.name,
                                    node: declarator
                                });
                            }
                        });
                    }
                }
            },
            ExportDefaultDeclaration(path) {
                const declaration = path.node.declaration;
                if (declaration) {
                    if (declaration.type === 'FunctionDeclaration') {
                        exports.push({
                            type: 'function',
                            name: declaration.id ? declaration.id.name : 'default',
                            node: declaration,
                            isDefault: true
                        });
                    } else if (declaration.type === 'ClassDeclaration') {
                        exports.push({
                            type: 'class',
                            name: declaration.id ? declaration.id.name : 'default',
                            node: declaration,
                            isDefault: true
                        });
                    } else if (declaration.type === 'Identifier') {
                        // 引用了其他地方定义的变量
                        exports.push({
                            type: 'reference',
                            name: declaration.name,
                            isDefault: true
                        });
                    }
                }
            }
        });

        // 生成测试文件建议
        const suggestion = {
            sourceFile: missingTest.sourceFile,
            expectedTestFile: missingTest.expectedTestFile,
            exports: exports.map(exp => exp.name),
            testFileContent: generateTestFileContent(missingTest.sourceFile, exports)
        };

        return suggestion;
    } catch (error) {
        console.error(`生成新测试文件建议时出错 (${missingTest.sourceFile}):`, error.message);
        return null;
    }
}

/**
 * 为没有断言的测试生成断言增强
 * @param {Object} testNode - 测试节点
 * @param {Object} testFn - 测试函数节点
 * @returns {string} - 增强后的代码
 */
function generateAssertionEnhancement(testNode, testFn) {
    // 分析测试函数体
    const testFnBody = testFn.body;
    const testFnBodyType = testFnBody.type;

    // 创建新的测试函数体
    let newTestFnBody;

    if (testFnBodyType === 'BlockStatement') {
        // 函数体是代码块
        const statements = testFnBody.body;

        // 查找最后一个语句
        const lastStatement = statements[statements.length - 1];

        if (lastStatement && lastStatement.type === 'ExpressionStatement') {
            // 最后一个语句是表达式，可能是函数调用
            const expression = lastStatement.expression;

            if (expression.type === 'CallExpression') {
                // 最后一个语句是函数调用，为其添加断言
                const newStatements = [...statements];

                // 添加断言
                newStatements.push(
                    t.expressionStatement(
                        t.callExpression(
                            t.memberExpression(
                                t.callExpression(
                                    t.identifier('expect'),
                                    [expression]
                                ),
                                t.identifier('toBeDefined')
                            ),
                            []
                        )
                    )
                );

                // 创建新的函数体
                newTestFnBody = t.blockStatement(newStatements);
            } else {
                // 其他类型的表达式，添加一个基本断言
                const newStatements = [...statements,
                t.expressionStatement(
                    t.callExpression(
                        t.memberExpression(
                            t.callExpression(
                                t.identifier('expect'),
                                [t.booleanLiteral(true)]
                            ),
                            t.identifier('toBe')
                        ),
                        [t.booleanLiteral(true)]
                    )
                )
                ];

                // 创建新的函数体
                newTestFnBody = t.blockStatement(newStatements);
            }
        } else {
            // 没有最后一个语句或不是表达式，添加一个基本断言
            const newStatements = [...statements,
            t.expressionStatement(
                t.callExpression(
                    t.memberExpression(
                        t.callExpression(
                            t.identifier('expect'),
                            [t.booleanLiteral(true)]
                        ),
                        t.identifier('toBe')
                    ),
                    [t.booleanLiteral(true)]
                )
            )
            ];

            // 创建新的函数体
            newTestFnBody = t.blockStatement(newStatements);
        }
    } else {
        // 函数体是表达式
        const expression = testFnBody;

        // 创建新的函数体，包含原表达式和断言
        newTestFnBody = t.blockStatement([
            t.expressionStatement(expression),
            t.expressionStatement(
                t.callExpression(
                    t.memberExpression(
                        t.callExpression(
                            t.identifier('expect'),
                            [expression]
                        ),
                        t.identifier('toBeDefined')
                    ),
                    []
                )
            )
        ]);
    }

    // 创建新的测试函数
    const newTestFn = {
        ...testFn,
        body: newTestFnBody
    };

    // 创建新的测试节点
    const newTestNode = {
        ...testNode,
        arguments: [
            testNode.arguments[0],
            newTestFn
        ]
    };

    // 生成代码
    return generate(newTestNode).code;
}

/**
 * 为断言太少的复杂测试生成更多断言
 * @param {Object} testNode - 测试节点
 * @param {Object} testFn - 测试函数节点
 * @returns {string} - 增强后的代码
 */
function generateMoreAssertionsEnhancement(testNode, testFn) {
    // 分析测试函数体
    const testFnBody = testFn.body;
    const testFnBodyType = testFnBody.type;

    // 创建新的测试函数体
    let newTestFnBody;

    if (testFnBodyType === 'BlockStatement') {
        // 函数体是代码块
        const statements = testFnBody.body;

        // 查找变量声明和函数调用
        const variables = [];
        const functionCalls = [];

        statements.forEach(statement => {
            if (statement.type === 'VariableDeclaration') {
                statement.declarations.forEach(declarator => {
                    if (declarator.id.type === 'Identifier') {
                        variables.push(declarator.id.name);
                    }
                });
            } else if (statement.type === 'ExpressionStatement' &&
                statement.expression.type === 'CallExpression') {
                functionCalls.push(statement.expression);
            }
        });

        // 创建新的语句列表，包含原有语句
        const newStatements = [...statements];

        // 为每个变量添加断言
        variables.forEach(variable => {
            newStatements.push(
                t.expressionStatement(
                    t.callExpression(
                        t.memberExpression(
                            t.callExpression(
                                t.identifier('expect'),
                                [t.identifier(variable)]
                            ),
                            t.identifier('toBeDefined')
                        ),
                        []
                    )
                )
            );
        });

        // 为每个函数调用添加断言（如果没有已有的断言）
        if (functionCalls.length > 0 && !hasAssertion(statements)) {
            const lastFunctionCall = functionCalls[functionCalls.length - 1];

            newStatements.push(
                t.expressionStatement(
                    t.callExpression(
                        t.memberExpression(
                            t.callExpression(
                                t.identifier('expect'),
                                [t.cloneNode(lastFunctionCall)]
                            ),
                            t.identifier('toBeDefined')
                        ),
                        []
                    )
                )
            );
        }

        // 创建新的函数体
        newTestFnBody = t.blockStatement(newStatements);
    } else {
        // 函数体是表达式
        const expression = testFnBody;

        // 创建新的函数体，包含原表达式和断言
        newTestFnBody = t.blockStatement([
            t.variableDeclaration('const', [
                t.variableDeclarator(
                    t.identifier('result'),
                    expression
                )
            ]),
            t.expressionStatement(
                t.callExpression(
                    t.memberExpression(
                        t.callExpression(
                            t.identifier('expect'),
                            [t.identifier('result')]
                        ),
                        t.identifier('toBeDefined')
                    ),
                    []
                )
            ),
            t.expressionStatement(
                t.callExpression(
                    t.memberExpression(
                        t.callExpression(
                            t.identifier('expect'),
                            [
                                t.callExpression(
                                    t.memberExpression(
                                        t.identifier('Object'),
                                        t.identifier('keys')
                                    ),
                                    [t.identifier('result')]
                                )
                            ]
                        ),
                        t.identifier('toHaveLength')
                    ),
                    [t.numericLiteral(1)]
                )
            )
        ]);
    }

    // 创建新的测试函数
    const newTestFn = {
        ...testFn,
        body: newTestFnBody
    };

    // 创建新的测试节点
    const newTestNode = {
        ...testNode,
        arguments: [
            testNode.arguments[0],
            newTestFn
        ]
    };

    // 生成代码
    return generate(newTestNode).code;
}

/**
 * 检查语句列表中是否已有断言
 * @param {Array} statements - 语句列表
 * @returns {boolean} - 是否有断言
 */
function hasAssertion(statements) {
    for (const statement of statements) {
        if (statement.type === 'ExpressionStatement' &&
            statement.expression.type === 'CallExpression') {
            const callExpression = statement.expression;

            // 检查是否是 expect(...).toBe(...) 形式的断言
            if (callExpression.callee.type === 'MemberExpression' &&
                callExpression.callee.object.type === 'CallExpression' &&
                callExpression.callee.object.callee.type === 'Identifier' &&
                callExpression.callee.object.callee.name === 'expect') {
                return true;
            }

            // 检查是否是 assert.equal(...) 形式的断言
            if (callExpression.callee.type === 'MemberExpression' &&
                callExpression.callee.object.type === 'Identifier' &&
                callExpression.callee.object.name === 'assert') {
                return true;
            }
        }
    }

    return false;
}

/**
 * 生成错误处理测试
 * @param {Object} testNode - 测试节点
 * @param {string} testName - 测试名称
 * @returns {Object} - 生成的测试代码
 */
function generateErrorHandlingTest(testNode, testName) {
    // 分析测试函数体，提取被测函数
    const testFn = testNode.arguments[1];
    const testFnBody = testFn.body;

    // 尝试提取被测函数
    let testedFunction = null;

    if (testFnBody.type === 'BlockStatement') {
        const statements = testFnBody.body;

        for (const statement of statements) {
            if (statement.type === 'ExpressionStatement' &&
                statement.expression.type === 'CallExpression') {
                testedFunction = statement.expression.callee;
                break;
            }
        }
    }

    // 创建错误处理测试
    const errorTestName = `${testName} 应该在无效输入时抛出错误`;

    let errorTestCode;

    if (testedFunction) {
        // 使用提取的被测函数
        errorTestCode = `
it('${errorTestName}', () => {
  // 准备无效输入
  const invalidInput = null;
  
  // 验证函数在无效输入时抛出错误
  expect(() => {
    ${generate(testedFunction).code}(invalidInput);
  }).toThrow();
});`;
    } else {
        // 使用通用模板
        errorTestCode = `
it('${errorTestName}', () => {
  // 准备无效输入
  const invalidInput = null;
  
  // 验证函数在无效输入时抛出错误
  expect(() => {
    // 替换为实际的函数调用
    functionUnderTest(invalidInput);
  }).toThrow();
});`;
    }

    return {
        code: errorTestCode
    };
}

/**
 * 生成空值检查测试
 * @param {Object} testNode - 测试节点
 * @param {string} testName - 测试名称
 * @returns {Object} - 生成的测试代码
 */
function generateNullCheckTest(testNode, testName) {
    // 分析测试函数体，提取被测函数
    const testFn = testNode.arguments[1];
    const testFnBody = testFn.body;

    // 尝试提取被测函数
    let testedFunction = null;

    if (testFnBody.type === 'BlockStatement') {
        const statements = testFnBody.body;

        for (const statement of statements) {
            if (statement.type === 'ExpressionStatement' &&
                statement.expression.type === 'CallExpression') {
                testedFunction = statement.expression.callee;
                break;
            }
        }
    }

    // 创建空值检查测试
    const nullTestName = `${testName} 应该正确处理空值`;

    let nullTestCode;

    if (testedFunction) {
        // 使用提取的被测函数
        nullTestCode = `
it('${nullTestName}', () => {
  // 测试 null 输入
  const result = ${generate(testedFunction).code}(null);
  expect(result).toBeDefined();
  
  // 测试 undefined 输入
  const result2 = ${generate(testedFunction).code}(undefined);
  expect(result2).toBeDefined();
});`;
    } else {
        // 使用通用模板
        nullTestCode = `
it('${nullTestName}', () => {
  // 测试 null 输入
  const result = functionUnderTest(null);
  expect(result).toBeDefined();
  
  // 测试 undefined 输入
  const result2 = functionUnderTest(undefined);
  expect(result2).toBeDefined();
});`;
    }

    return {
        code: nullTestCode
    };
}

/**
 * 生成测试文件内容
 * @param {string} sourceFile - 源文件路径
 * @param {Array} exports - 导出项列表
 * @returns {string} - 测试文件内容
 */
function generateTestFileContent(sourceFile, exports) {
    const sourceFileName = path.basename(sourceFile);
    const moduleName = sourceFileName.replace(/\.(js|ts|jsx|tsx)$/, '');
    const relativePath = path.relative(path.join('__tests__'), `../${sourceFile}`);

    // 导入语句
    let importStatement = `import { `;

    // 默认导出
    const defaultExport = exports.find(exp => exp.isDefault);
    let hasNamedExports = false;

    // 命名导出
    const namedExports = exports.filter(exp => !exp.isDefault);
    if (namedExports.length > 0) {
        importStatement += namedExports.map(exp => exp.name).join(', ');
        hasNamedExports = true;
    }

    importStatement += ` } from '${relativePath.replace(/\\/g, '/')}';`;

    // 如果有默认导出，添加默认导入
    let defaultImportStatement = '';
    if (defaultExport) {
        defaultImportStatement = `import ${defaultExport.name} from '${relativePath.replace(/\\/g, '/')}';`;
    }

    // 生成测试套件
    let testSuite = `
describe('${moduleName}', () => {
`;

    // 为每个导出生成测试用例
    exports.forEach(exp => {
        if (exp.type === 'function') {
            testSuite += `
  describe('${exp.name}', () => {
    it('应该正确执行基本功能', () => {
      // 准备测试数据
      const input = {}; // 替换为适当的输入
      
      // 执行被测函数
      const result = ${exp.name}(input);
      
      // 验证结果
      expect(result).toBeDefined();
    });
    
    it('应该处理边缘情况', () => {
      // 测试空输入
      const result = ${exp.name}(null);
      expect(result).toBeDefined();
    });
    
    it('应该在无效输入时抛出错误', () => {
      // 准备无效输入
      const invalidInput = {}; // 替换为会导致错误的输入
      
      // 验证函数抛出错误
      expect(() => {
        ${exp.name}(invalidInput);
      }).toThrow();
    });
  });
`;
        } else if (exp.type === 'class') {
            testSuite += `
  describe('${exp.name}', () => {
    it('应该能够正确实例化', () => {
      // 创建实例
      const instance = new ${exp.name}();
      
      // 验证实例
      expect(instance).toBeInstanceOf(${exp.name});
    });
    
    it('应该有预期的方法', () => {
      // 创建实例
      const instance = new ${exp.name}();
      
      // 验证方法存在
      expect(typeof instance.someMethod).toBe('function'); // 替换为实际方法
    });
  });
`;
        }
    });

    testSuite += `});
`;

    // 组合完整的测试文件内容
    const testFileContent = `${defaultImportStatement}
${importStatement}

${testSuite}`;

    return testFileContent;
}

/**
 * 生成增强报告
 * @param {Object} enhancementReport - 增强报告
 * @param {string} outputDir - 输出目录路径
 */
function generateEnhancementReport(enhancementReport, outputDir) {
    console.log('生成测试增强报告...');
    
    // 创建报告目录
    const reportDir = path.join(outputDir, 'enhancement-reports');
    if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
    }
    
    // 保存JSON报告
    fs.writeFileSync(
        path.join(reportDir, 'test-enhancements.json'),
        JSON.stringify(enhancementReport, null, 2)
    );
    
    // 生成HTML报告
    generateHtmlEnhancementReport(enhancementReport, reportDir);
    
    console.log(`测试增强报告已生成到 ${reportDir}`);
}

/**
 * 生成HTML测试增强报告
 * @param {Object} enhancementReport - 测试增强报告
 * @param {string} reportDir - 报告目录路径
 */
function generateHtmlEnhancementReport(enhancementReport, reportDir) {
    // 生成概览HTML
    let overviewHtml = `
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>测试增强建议报告</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                }
                h1, h2, h3 {
                    color: #2c3e50;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    padding: 10px;
                    border: 1px solid #ddd;
                    text-align: left;
                }
                th {
                    background-color: #f2f2f2;
                }
                tr:nth-child(even) {
                    background-color: #f9f9f9;
                }
                .summary-box {
                    padding: 15px;
                    margin-bottom: 20px;
                    border-radius: 4px;
                    background-color: #f5f5f5;
                    border-left: 5px solid #2c3e50;
                }
                .nav {
                    margin-bottom: 20px;
                }
                .nav a {
                    display: inline-block;
                    padding: 8px 16px;
                    margin-right: 10px;
                    background-color: #2c3e50;
                    color: white;
                    text-decoration: none;
                    border-radius: 4px;
                }
                .nav a:hover {
                    background-color: #1a252f;
                }
                pre {
                    background-color: #f5f5f5;
                    padding: 10px;
                    border-radius: 4px;
                    overflow-x: auto;
                }
                code {
                    font-family: Consolas, Monaco, 'Andale Mono', monospace;
                }
            </style>
        </head>
        <body>
            <h1>测试增强建议报告</h1>
            
            <div class="summary-box">
                <h2>概览</h2>
                <table>
                    <tr>
                        <th>包名</th>
                        <th>弱测试增强</th>
                        <th>边缘情况增强</th>
                        <th>新测试建议</th>
                        <th>总计</th>
                    </tr>
    `;
    
    // 添加每个包的概览行
    Object.entries(enhancementReport).forEach(([packageName, data]) => {
        const totalEnhancements = 
            data.weakTestEnhancements.length + 
            data.edgeCaseEnhancements.length + 
            data.newTestSuggestions.length;
        
        overviewHtml += `
            <tr>
                <td><a href="${packageName}-enhancements.html">${packageName}</a></td>
                <td>${data.weakTestEnhancements.length}</td>
                <td>${data.edgeCaseEnhancements.length}</td>
                <td>${data.newTestSuggestions.length}</td>
                <td>${totalEnhancements}</td>
            </tr>
        `;
        
        // 为每个包生成详细报告
        generatePackageEnhancementReport(packageName, data, reportDir);
    });
    
    overviewHtml += `
                </table>
            </div>
            
            <p>报告生成时间: ${new Date().toLocaleString()}</p>
        </body>
        </html>
    `;
    
    // 保存概览HTML
    fs.writeFileSync(path.join(reportDir, 'index.html'), overviewHtml);
}

/**
 * 为单个包生成测试增强报告
 * @param {string} packageName - 包名
 * @param {Object} data - 包的增强数据
 * @param {string} reportDir - 报告目录路径
 */
function generatePackageEnhancementReport(packageName, data, reportDir) {
    let html = `
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${packageName} - 测试增强建议</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                }
                h1, h2, h3 {
                    color: #2c3e50;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    padding: 10px;
                    border: 1px solid #ddd;
                    text-align: left;
                }
                th {
                    background-color: #f2f2f2;
                }
                tr:nth-child(even) {
                    background-color: #f9f9f9;
                }
                .summary-box {
                    padding: 15px;
                    margin-bottom: 20px;
                    border-radius: 4px;
                    background-color: #f5f5f5;
                    border-left: 5px solid #2c3e50;
                }
                .nav {
                    margin-bottom: 20px;
                }
                .nav a {
                    display: inline-block;
                    padding: 8px 16px;
                    margin-right: 10px;
                    background-color: #2c3e50;
                    color: white;
                    text-decoration: none;
                    border-radius: 4px;
                }
                .nav a:hover {
                    background-color: #1a252f;
                }
                pre {
                    background-color: #f5f5f5;
                    padding: 10px;
                    border-radius: 4px;
                    overflow-x: auto;
                }
                code {
                    font-family: Consolas, Monaco, 'Andale Mono', monospace;
                }
                .enhancement {
                    margin-bottom: 30px;
                    padding: 15px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                }
                .enhancement h3 {
                    margin-top: 0;
                }
                .code-diff {
                    display: flex;
                    flex-direction: row;
                    gap: 20px;
                }
                .code-block {
                    flex: 1;
                }
            </style>
        </head>
        <body>
            <h1>${packageName} - 测试增强建议</h1>
            <div class="nav">
                <a href="index.html">概览</a>
                <a href="${packageName}-enhancements.html">包详情</a>
            </div>
            
            <div class="summary-box">
                <h2>增强摘要</h2>
                <ul>
                    <li>弱测试增强: ${data.weakTestEnhancements.length}</li>
                    <li>边缘情况增强: ${data.edgeCaseEnhancements.length}</li>
                    <li>新测试建议: ${data.newTestSuggestions.length}</li>
                </ul>
            </div>
    `;
    
    // 添加弱测试增强
    if (data.weakTestEnhancements.length > 0) {
        html += `
            <h2>弱测试增强</h2>
        `;
        
        data.weakTestEnhancements.forEach((enhancement, index) => {
            html += `
                <div class="enhancement">
                    <h3>${index + 1}. ${enhancement.name} (${path.basename(enhancement.file)}:${enhancement.line})</h3>
                    <p><strong>问题:</strong> ${enhancement.reason}</p>
                    <p><strong>解释:</strong> ${enhancement.explanation}</p>
                    
                    <div class="code-diff">
                        <div class="code-block">
                            <h4>原始代码:</h4>
                            <pre><code>${escapeHtml(enhancement.originalCode)}</code></pre>
                        </div>
                        <div class="code-block">
                            <h4>增强代码:</h4>
                            <pre><code>${escapeHtml(enhancement.enhancedCode)}</code></pre>
                        </div>
                    </div>
                </div>
            `;
        });
    }
    
    // 添加边缘情况增强
    if (data.edgeCaseEnhancements.length > 0) {
        html += `
            <h2>边缘情况增强</h2>
        `;
        
        data.edgeCaseEnhancements.forEach((enhancement, index) => {
            html += `
                <div class="enhancement">
                    <h3>${index + 1}. ${enhancement.name} (${path.basename(enhancement.file)}:${enhancement.line})</h3>
                    <p><strong>问题:</strong> ${enhancement.reason}</p>
                    <p><strong>解释:</strong> ${enhancement.explanation}</p>
                    
                    <h4>建议添加的测试:</h4>
                    <pre><code>${escapeHtml(enhancement.additionalTest)}</code></pre>
                </div>
            `;
        });
    }
    
    // 添加新测试建议
    if (data.newTestSuggestions.length > 0) {
        html += `
            <h2>新测试建议</h2>
        `;
        
        data.newTestSuggestions.forEach((suggestion, index) => {
            html += `
                <div class="enhancement">
                    <h3>${index + 1}. ${path.basename(suggestion.sourceFile)}</h3>
                    <p><strong>源文件:</strong> ${suggestion.sourceFile}</p>
                    <p><strong>预期测试文件:</strong> ${suggestion.expectedTestFile}</p>
                    <p><strong>导出项:</strong> ${suggestion.exports.join(', ')}</p>
                    
                    <h4>建议的测试文件内容:</h4>
                    <pre><code>${escapeHtml(suggestion.testFileContent)}</code></pre>
                </div>
            `;
        });
    }
    
    html += `
            <p>报告生成时间: ${new Date().toLocaleString()}</p>
        </body>
        </html>
    `;
    
    // 保存包的HTML报告
    fs.writeFileSync(path.join(reportDir, `${packageName}-enhancements.html`), html);
}

/**
 * 转义HTML特殊字符
 * @param {string} text - 要转义的文本
 * @returns {string} - 转义后的文本
 */
function escapeHtml(text) {
    return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
}

module.exports = {
    generateTestEnhancements
};
