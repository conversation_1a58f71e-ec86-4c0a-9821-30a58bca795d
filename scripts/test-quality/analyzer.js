/**
 * 测试质量分析器
 * 
 * 此模块用于分析现有测试文件的功能逻辑验证，
 * 识别需要增强的表面测试，
 * 标记具有弱断言或缺少边缘情况的测试。
 */

const fs = require('fs');
const path = require('path');
const { parse } = require('@babel/parser');
const traverse = require('@babel/traverse').default;

/**
 * 分析测试质量
 * @param {string} packagesDir - 包目录路径
 * @param {string} outputDir - 输出目录路径
 * @returns {Object} - 测试质量报告
 */
function analyzeTestQuality(packagesDir, outputDir) {
    console.log('分析测试质量...');

    // 获取所有测试文件
    const testFiles = findAllTestFiles(packagesDir);
    console.log(`找到 ${testFiles.length} 个测试文件`);

    // 分析每个测试文件
    const testQualityReport = {};
    testFiles.forEach(testFile => {
        const packageName = extractPackageName(testFile, packagesDir);

        if (!testQualityReport[packageName]) {
            testQualityReport[packageName] = {
                name: packageName,
                testFiles: [],
                weakTests: [],
                missingEdgeCases: [],
                summary: {
                    totalTests: 0,
                    weakTests: 0,
                    missingEdgeCases: 0,
                    qualityScore: 0
                }
            };
        }

        const fileReport = analyzeTestFile(testFile, packagesDir);
        testQualityReport[packageName].testFiles.push(fileReport);

        // 更新包的摘要信息
        testQualityReport[packageName].summary.totalTests += fileReport.testCount;
        testQualityReport[packageName].summary.weakTests += fileReport.weakTests.length;
        testQualityReport[packageName].summary.missingEdgeCases += fileReport.missingEdgeCases.length;

        // 收集弱测试和缺少边缘情况的测试
        fileReport.weakTests.forEach(test => {
            testQualityReport[packageName].weakTests.push({
                file: testFile,
                ...test
            });
        });

        fileReport.missingEdgeCases.forEach(test => {
            testQualityReport[packageName].missingEdgeCases.push({
                file: testFile,
                ...test
            });
        });
    });

    // 计算每个包的质量评分
    Object.values(testQualityReport).forEach(packageReport => {
        calculateQualityScore(packageReport);
    });

    // 生成测试质量报告
    generateQualityReport(testQualityReport, outputDir);

    return testQualityReport;
}

/**
 * 查找所有测试文件
 * @param {string} packagesDir - 包目录路径
 * @returns {Array} - 测试文件路径列表
 */
function findAllTestFiles(packagesDir) {
    const testFiles = [];

    // 获取所有包目录
    const packageDirs = fs.readdirSync(packagesDir)
        .filter(dir => fs.statSync(path.join(packagesDir, dir)).isDirectory())
        .map(dir => path.join(packagesDir, dir));

    // 查找每个包中的测试文件
    packageDirs.forEach(packageDir => {
        const testDir = path.join(packageDir, '__tests__');
        if (fs.existsSync(testDir)) {
            const files = getAllFiles(testDir)
                .filter(file => /\.(test|spec)\.(js|ts|jsx|tsx)$/.test(file));
            testFiles.push(...files);
        }
    });

    return testFiles;
}

/**
 * 获取目录中的所有文件
 * @param {string} dir - 目录路径
 * @returns {Array} - 文件路径列表
 */
function getAllFiles(dir) {
    let results = [];
    const list = fs.readdirSync(dir);

    list.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat && stat.isDirectory()) {
            // 递归处理子目录
            results = results.concat(getAllFiles(filePath));
        } else {
            results.push(filePath);
        }
    });

    return results;
}

/**
 * 提取包名
 * @param {string} filePath - 文件路径
 * @param {string} packagesDir - 包目录路径
 * @returns {string} - 包名
 */
function extractPackageName(filePath, packagesDir) {
    const relativePath = path.relative(packagesDir, filePath);
    const packageName = relativePath.split(path.sep)[0];
    return packageName;
}

/**
 * 分析测试文件
 * @param {string} testFile - 测试文件路径
 * @param {string} packagesDir - 包目录路径
 * @returns {Object} - 测试文件报告
 */
function analyzeTestFile(testFile, packagesDir) {
    console.log(`分析测试文件: ${testFile}`);

    const fileReport = {
        path: testFile,
        relativePath: path.relative(packagesDir, testFile),
        testCount: 0,
        assertionCount: 0,
        weakTests: [],
        missingEdgeCases: [],
        qualityScore: 0
    };

    try {
        // 读取测试文件内容
        const content = fs.readFileSync(testFile, 'utf8');

        // 解析测试文件
        const ast = parse(content, {
            sourceType: 'module',
            plugins: ['jsx', 'typescript', 'decorators-legacy']
        });

        // 遍历AST
        traverse(ast, {
            // 查找测试函数调用
            CallExpression(path) {
                const callee = path.node.callee;
                const args = path.node.arguments;

                // 检查是否是测试函数 (it, test, describe)
                if (
                    (callee.type === 'Identifier' &&
                        (callee.name === 'it' || callee.name === 'test')) ||
                    (callee.type === 'MemberExpression' &&
                        callee.object.type === 'Identifier' &&
                        (callee.object.name === 'it' || callee.object.name === 'test') &&
                        callee.property.type === 'Identifier' &&
                        (callee.property.name === 'only' || callee.property.name === 'skip'))
                ) {
                    // 这是一个测试用例
                    fileReport.testCount++;

                    // 获取测试名称
                    const testName = args[0] && args[0].type === 'StringLiteral' ?
                        args[0].value : '未命名测试';

                    // 获取测试函数体
                    const testFn = args[1];
                    if (testFn && (testFn.type === 'ArrowFunctionExpression' || testFn.type === 'FunctionExpression')) {
                        // 分析测试函数体
                        const testInfo = analyzeTestFunction(testFn, path);

                        // 更新断言计数
                        fileReport.assertionCount += testInfo.assertionCount;

                        // 检查是否是弱测试
                        if (testInfo.assertionCount === 0) {
                            fileReport.weakTests.push({
                                name: testName,
                                line: testFn.loc.start.line,
                                reason: '没有断言'
                            });
                        } else if (testInfo.assertionCount < 2 && testInfo.complexity > 3) {
                            fileReport.weakTests.push({
                                name: testName,
                                line: testFn.loc.start.line,
                                reason: '复杂测试但断言太少',
                                assertionCount: testInfo.assertionCount,
                                complexity: testInfo.complexity
                            });
                        }

                        // 检查是否缺少边缘情况
                        if (!testInfo.hasErrorHandling && testInfo.complexity > 2) {
                            fileReport.missingEdgeCases.push({
                                name: testName,
                                line: testFn.loc.start.line,
                                reason: '缺少错误处理测试'
                            });
                        }

                        if (!testInfo.hasNullCheck && testInfo.complexity > 2) {
                            fileReport.missingEdgeCases.push({
                                name: testName,
                                line: testFn.loc.start.line,
                                reason: '缺少空值检查'
                            });
                        }
                    }
                }
            }
        });

        // 计算测试质量评分
        calculateFileQualityScore(fileReport);

    } catch (error) {
        console.error(`分析测试文件时出错 (${testFile}):`, error.message);
    }

    return fileReport;
}

/**
 * 分析测试函数
 * @param {Object} testFn - 测试函数节点
 * @param {Object} path - AST路径
 * @returns {Object} - 测试函数信息
 */
function analyzeTestFunction(testFn, path) {
    const testInfo = {
        assertionCount: 0,
        hasErrorHandling: false,
        hasNullCheck: false,
        complexity: 1
    };

    // 遍历测试函数体
    traverse(testFn, {
        // 查找断言
        CallExpression(p) {
            const callee = p.node.callee;

            // 检查是否是断言函数
            if (
                // expect(...).toBe(...)
                (callee.type === 'MemberExpression' &&
                    callee.object.type === 'CallExpression' &&
                    callee.object.callee.type === 'Identifier' &&
                    callee.object.callee.name === 'expect') ||
                // assert.equal(...)
                (callee.type === 'MemberExpression' &&
                    callee.object.type === 'Identifier' &&
                    callee.object.name === 'assert') ||
                // chai.expect(...).to.equal(...)
                (callee.type === 'MemberExpression' &&
                    callee.object.type === 'MemberExpression' &&
                    callee.object.object.type === 'CallExpression' &&
                    callee.object.object.callee.type === 'MemberExpression' &&
                    callee.object.object.callee.object.type === 'Identifier' &&
                    callee.object.object.callee.object.name === 'chai')
            ) {
                testInfo.assertionCount++;
            }

            // 检查是否有错误处理测试
            if (
                // expect(...).toThrow()
                (callee.type === 'MemberExpression' &&
                    callee.property.type === 'Identifier' &&
                    (callee.property.name === 'toThrow' ||
                        callee.property.name === 'toThrowError')) ||
                // assert.throws(...)
                (callee.type === 'MemberExpression' &&
                    callee.object.type === 'Identifier' &&
                    callee.object.name === 'assert' &&
                    callee.property.type === 'Identifier' &&
                    callee.property.name === 'throws')
            ) {
                testInfo.hasErrorHandling = true;
            }
        },

        // 检查是否有空值检查
        BinaryExpression(p) {
            const { left, right, operator } = p.node;

            if (
                // x === null, x !== null
                ((operator === '===' || operator === '!==') &&
                    (right.type === 'NullLiteral' ||
                        (right.type === 'Identifier' && right.name === 'undefined') ||
                        left.type === 'NullLiteral' ||
                        (left.type === 'Identifier' && left.name === 'undefined')))
            ) {
                testInfo.hasNullCheck = true;
            }
        },

        // 计算复杂度
        IfStatement() {
            testInfo.complexity++;
        },

        SwitchStatement() {
            testInfo.complexity++;
        },

        ForStatement() {
            testInfo.complexity++;
        },

        WhileStatement() {
            testInfo.complexity++;
        },

        DoWhileStatement() {
            testInfo.complexity++;
        },

        TryStatement() {
            testInfo.complexity++;
            testInfo.hasErrorHandling = true;
        }
    }, path.scope, path);

    return testInfo;
}

/**
 * 计算文件质量评分
 * @param {Object} fileReport - 文件报告
 */
function calculateFileQualityScore(fileReport) {
    if (fileReport.testCount === 0) {
        fileReport.qualityScore = 0;
        return;
    }

    // 基础分数
    let score = 100;

    // 每个弱测试扣除分数
    score -= (fileReport.weakTests.length / fileReport.testCount) * 30;

    // 每个缺少边缘情况的测试扣除分数
    score -= (fileReport.missingEdgeCases.length / fileReport.testCount) * 20;

    // 如果断言数量少于测试数量的2倍，扣除分数
    if (fileReport.assertionCount < fileReport.testCount * 2) {
        score -= 10;
    }

    // 限制分数范围在0-100之间
    fileReport.qualityScore = Math.max(0, Math.min(100, Math.round(score)));
}

/**
 * 计算包的质量评分
 * @param {Object} packageReport - 包报告
 */
function calculateQualityScore(packageReport) {
    if (packageReport.testFiles.length === 0) {
        packageReport.summary.qualityScore = 0;
        return;
    }

    // 计算平均质量评分
    const totalScore = packageReport.testFiles.reduce((sum, file) => sum + file.qualityScore, 0);
    packageReport.summary.qualityScore = Math.round(totalScore / packageReport.testFiles.length);
}

/**
 * 生成测试质量报告
 * @param {Object} testQualityReport - 测试质量报告
 * @param {string} outputDir - 输出目录路径
 */
function generateQualityReport(testQualityReport, outputDir) {
    console.log('生成测试质量报告...');

    // 创建报告目录
    const reportDir = path.join(outputDir, 'quality-reports');
    if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
    }

    // 保存JSON报告
    fs.writeFileSync(
        path.join(reportDir, 'test-quality.json'),
        JSON.stringify(testQualityReport, null, 2)
    );

    // 生成HTML报告
    generateHtmlQualityReport(testQualityReport, reportDir);

    console.log(`测试质量报告已生成到 ${reportDir}`);
}

/**
 * 生成HTML测试质量报告
 * @param {Object} testQualityReport - 测试质量报告
 * @param {string} reportDir - 报告目录路径
 */
function generateHtmlQualityReport(testQualityReport, reportDir) {
    // 生成概览HTML
    let overviewHtml = `
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>测试质量报告</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                }
                h1, h2, h3 {
                    color: #2c3e50;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    padding: 10px;
                    border: 1px solid #ddd;
                    text-align: left;
                }
                th {
                    background-color: #f2f2f2;
                }
                tr:nth-child(even) {
                    background-color: #f9f9f9;
                }
                .quality-high {
                    background-color: #dff0d8;
                    color: #3c763d;
                }
                .quality-medium {
                    background-color: #fcf8e3;
                    color: #8a6d3b;
                }
                .quality-low {
                    background-color: #f2dede;
                    color: #a94442;
                }
                .summary-box {
                    padding: 15px;
                    margin-bottom: 20px;
                    border-radius: 4px;
                    background-color: #f5f5f5;
                    border-left: 5px solid #2c3e50;
                }
                .nav {
                    margin-bottom: 20px;
                }
                .nav a {
                    display: inline-block;
                    padding: 8px 16px;
                    margin-right: 10px;
                    background-color: #2c3e50;
                    color: white;
                    text-decoration: none;
                    border-radius: 4px;
                }
                .nav a:hover {
                    background-color: #1a252f;
                }
                .progress-bar {
                    height: 20px;
                    background-color: #f5f5f5;
                    border-radius: 4px;
                    overflow: hidden;
                }
                .progress-bar-value {
                    height: 100%;
                    background-color: #5cb85c;
                    text-align: center;
                    line-height: 20px;
                    color: white;
                }
            </style>
        </head>
        <body>
            <h1>测试质量报告</h1>
            
            <div class="summary-box">
                <h2>概览</h2>
                <table>
                    <tr>
                        <th>包名</th>
                        <th>测试文件数</th>
                        <th>测试用例数</th>
                        <th>弱测试数</th>
                        <th>缺少边缘情况数</th>
                        <th>质量评分</th>
                    </tr>
    `;

    // 添加每个包的概览行
    Object.entries(testQualityReport).forEach(([packageName, data]) => {
        const qualityClass = data.summary.qualityScore >= 80 ? 'quality-high' :
            (data.summary.qualityScore >= 60 ? 'quality-medium' : 'quality-low');

        overviewHtml += `
            <tr>
                <td><a href="${packageName}-quality.html">${packageName}</a></td>
                <td>${data.testFiles.length}</td>
                <td>${data.summary.totalTests}</td>
                <td>${data.summary.weakTests}</td>
                <td>${data.summary.missingEdgeCases}</td>
                <td class="${qualityClass}">
                    <div class="progress-bar">
                        <div class="progress-bar-value" style="width: ${data.summary.qualityScore}%">
                            ${data.summary.qualityScore}%
                        </div>
                    </div>
                </td>
            </tr>
        `;

        // 为每个包生成详细报告
        generatePackageQualityReport(packageName, data, reportDir);
    });

    overviewHtml += `
                </table>
            </div>
            
            <h2>需要改进的测试</h2>
            <table>
                <tr>
                    <th>包名</th>
                    <th>文件</th>
                    <th>测试名称</th>
                    <th>问题</th>
                    <th>行号</th>
                </tr>
    `;

    // 获取所有包中需要改进的测试
    const allWeakTests = [];
    Object.entries(testQualityReport).forEach(([packageName, data]) => {
        data.weakTests.forEach(test => {
            allWeakTests.push({
                packageName,
                ...test
            });
        });

        data.missingEdgeCases.forEach(test => {
            allWeakTests.push({
                packageName,
                ...test,
                reason: `缺少边缘情况: ${test.reason}`
            });
        });
    });

    // 按文件名排序
    allWeakTests.sort((a, b) => {
        if (a.file === b.file) {
            return a.line - b.line;
        }
        return a.file.localeCompare(b.file);
    });

    // 添加每个需要改进的测试
    allWeakTests.forEach(test => {
        overviewHtml += `
            <tr>
                <td>${test.packageName}</td>
                <td>${path.basename(test.file)}</td>
                <td>${test.name}</td>
                <td>${test.reason}</td>
                <td>${test.line}</td>
            </tr>
        `;
    });

    overviewHtml += `
                </table>
            </div>
            
            <p>报告生成时间: ${new Date().toLocaleString()}</p>
        </body>
        </html>
    `;

    // 保存概览HTML
    fs.writeFileSync(path.join(reportDir, 'index.html'), overviewHtml);
}

/**
 * 为单个包生成测试质量报告
 * @param {string} packageName - 包名
 * @param {Object} data - 包的质量数据
 * @param {string} reportDir - 报告目录路径
 */
function generatePackageQualityReport(packageName, data, reportDir) {
    let html = `
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${packageName} - 测试质量报告</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                }
                h1, h2, h3 {
                    color: #2c3e50;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    padding: 10px;
                    border: 1px solid #ddd;
                    text-align: left;
                }
                th {
                    background-color: #f2f2f2;
                }
                tr:nth-child(even) {
                    background-color: #f9f9f9;
                }
                .quality-high {
                    background-color: #dff0d8;
                    color: #3c763d;
                }
                .quality-medium {
                    background-color: #fcf8e3;
                    color: #8a6d3b;
                }
                .quality-low {
                    background-color: #f2dede;
                    color: #a94442;
                }
                .summary-box {
                    padding: 15px;
                    margin-bottom: 20px;
                    border-radius: 4px;
                    background-color: #f5f5f5;
                    border-left: 5px solid #2c3e50;
                }
                .nav {
                    margin-bottom: 20px;
                }
                .nav a {
                    display: inline-block;
                    padding: 8px 16px;
                    margin-right: 10px;
                    background-color: #2c3e50;
                    color: white;
                    text-decoration: none;
                    border-radius: 4px;
                }
                .nav a:hover {
                    background-color: #1a252f;
                }
                .progress-bar {
                    height: 20px;
                    background-color: #f5f5f5;
                    border-radius: 4px;
                    overflow: hidden;
                }
                .progress-bar-value {
                    height: 100%;
                    background-color: #5cb85c;
                    text-align: center;
                    line-height: 20px;
                    color: white;
                }
            </style>
        </head>
        <body>
            <h1>${packageName} - 测试质量报告</h1>
            <div class="nav">
                <a href="index.html">概览</a>
                <a href="${packageName}-quality.html">包详情</a>
            </div>
            
            <div class="summary-box">
                <h2>质量摘要</h2>
                <div class="progress-bar">
                    <div class="progress-bar-value" style="width: ${data.summary.qualityScore}%">
                        ${data.summary.qualityScore}%
                    </div>
                </div>
                <ul>
                    <li>测试文件数: ${data.testFiles.length}</li>
                    <li>测试用例数: ${data.summary.totalTests}</li>
                    <li>弱测试数: ${data.summary.weakTests}</li>
                    <li>缺少边缘情况数: ${data.summary.missingEdgeCases}</li>
                </ul>
            </div>
            
            <h2>测试文件质量</h2>
            <table>
                <tr>
                    <th>文件</th>
                    <th>测试用例数</th>
                    <th>断言数</th>
                    <th>弱测试数</th>
                    <th>缺少边缘情况数</th>
                    <th>质量评分</th>
                </tr>
    `;

    // 添加每个测试文件的行
    data.testFiles.forEach(file => {
        const qualityClass = file.qualityScore >= 80 ? 'quality-high' :
            (file.qualityScore >= 60 ? 'quality-medium' : 'quality-low');

        html += `
            <tr>
                <td>${path.basename(file.path)}</td>
                <td>${file.testCount}</td>
                <td>${file.assertionCount}</td>
                <td>${file.weakTests.length}</td>
                <td>${file.missingEdgeCases.length}</td>
                <td class="${qualityClass}">
                    <div class="progress-bar">
                        <div class="progress-bar-value" style="width: ${file.qualityScore}%">
                            ${file.qualityScore}%
                        </div>
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
            </table>
            
            <h2>弱测试</h2>
    `;

    if (data.weakTests.length > 0) {
        html += `
            <table>
                <tr>
                    <th>文件</th>
                    <th>测试名称</th>
                    <th>问题</th>
                    <th>行号</th>
                </tr>
        `;

        data.weakTests.forEach(test => {
            html += `
                <tr>
                    <td>${path.basename(test.file)}</td>
                    <td>${test.name}</td>
                    <td>${test.reason}</td>
                    <td>${test.line}</td>
                </tr>
            `;
        });

        html += `
            </table>
        `;
    } else {
        html += `
            <p>没有弱测试</p>
        `;
    }

    html += `
            <h2>缺少边缘情况的测试</h2>
    `;

    if (data.missingEdgeCases.length > 0) {
        html += `
            <table>
                <tr>
                    <th>文件</th>
                    <th>测试名称</th>
                    <th>缺少的边缘情况</th>
                    <th>行号</th>
                </tr>
        `;

        data.missingEdgeCases.forEach(test => {
            html += `
                <tr>
                    <td>${path.basename(test.file)}</td>
                    <td>${test.name}</td>
                    <td>${test.reason}</td>
                    <td>${test.line}</td>
                </tr>
            `;
        });

        html += `
            </table>
        `;
    } else {
        html += `
            <p>没有缺少边缘情况的测试</p>
        `;
    }

    html += `
            <p>报告生成时间: ${new Date().toLocaleString()}</p>
        </body>
        </html>
    `;

    // 保存包的HTML报告
    fs.writeFileSync(path.join(reportDir, `${packageName}-quality.html`), html);
}

module.exports = {
    analyzeTestQuality
};