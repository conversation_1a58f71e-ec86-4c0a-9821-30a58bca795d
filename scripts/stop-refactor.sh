#!/bin/bash

# Micro Core 重构环境停止脚本

set -e

echo "🛑 停止 Micro Core 重构环境..."

# 读取 PID 文件
if [ -f .refactor-pids ]; then
    PIDS=$(cat .refactor-pids)
    echo "📋 停止后台进程: $PIDS"
    
    for PID in $PIDS; do
        if kill -0 "$PID" 2>/dev/null; then
            echo "  - 停止进程 $PID"
            kill "$PID" 2>/dev/null || true
        fi
    done
    
    rm -f .refactor-pids
    echo "✅ 后台进程已停止"
else
    echo "⚠️  未找到 PID 文件，手动清理进程..."
    
    # 手动清理可能的进程
    pkill -f "tsc --watch" 2>/dev/null || true
    pkill -f "jest --watch" 2>/dev/null || true
    pkill -f "typedoc --watch" 2>/dev/null || true
fi

echo "🧹 清理临时文件..."
rm -rf .refactor-pids
rm -rf node_modules/.cache 2>/dev/null || true

echo "✅ 重构环境已停止"