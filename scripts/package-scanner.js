#!/usr/bin/env node

/**
 * 包扫描工具
 * 
 * 此脚本用于扫描monorepo中的所有包，识别源文件和可导出的函数、类和模块，
 * 并生成可测试代码单元的全面清单。
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const ts = require('typescript');

// 配置
const PACKAGES_DIR = path.resolve(__dirname, '../packages');
const OUTPUT_DIR = path.resolve(__dirname, '../reports/package-scan');
const EXCLUDED_DIRS = ['node_modules', 'dist', 'build', '.turbo', 'coverage'];
const EXCLUDED_FILES = ['.d.ts', '.test.ts', '.spec.ts', '.mock.ts'];

// 确保输出目录存在
if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * 扫描包目录
 */
function scanPackages() {
    console.log('开始扫描包...');

    const packages = fs.readdirSync(PACKAGES_DIR)
        .filter(item => {
            const stats = fs.statSync(path.join(PACKAGES_DIR, item));
            return stats.isDirectory() && !EXCLUDED_DIRS.includes(item);
        });

    console.log(`发现 ${packages.length} 个包`);

    const results = {};

    for (const pkg of packages) {
        const packagePath = path.join(PACKAGES_DIR, pkg);
        const packageInfo = scanPackage(packagePath, pkg);
        results[pkg] = packageInfo;
    }

    // 写入结果
    fs.writeFileSync(
        path.join(OUTPUT_DIR, 'package-inventory.json'),
        JSON.stringify(results, null, 2)
    );

    console.log(`扫描完成。结果已保存到 ${path.join(OUTPUT_DIR, 'package-inventory.json')}`);

    return results;
}

/**
 * 扫描单个包
 */
function scanPackage(packagePath, packageName) {
    console.log(`扫描包: ${packageName}`);

    const packageJsonPath = path.join(packagePath, 'package.json');
    let packageJson = {};

    if (fs.existsSync(packageJsonPath)) {
        packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    }

    const srcDir = path.join(packagePath, 'src');

    if (!fs.existsSync(srcDir)) {
        console.warn(`警告: ${packageName} 没有 src 目录`);
        return {
            name: packageName,
            version: packageJson.version || 'unknown',
            sourceFiles: [],
            exportedItems: [],
            dependencies: packageJson.dependencies || {},
            devDependencies: packageJson.devDependencies || {}
        };
    }

    const sourceFiles = findSourceFiles(srcDir);
    console.log(`发现 ${sourceFiles.length} 个源文件`);

    const exportedItems = [];

    for (const file of sourceFiles) {
        const fileExports = analyzeSourceFile(file);
        exportedItems.push(...fileExports.map(item => ({
            ...item,
            file: path.relative(packagePath, file)
        })));
    }

    console.log(`发现 ${exportedItems.length} 个导出项`);

    // 查找测试文件
    const testFiles = findTestFiles(packagePath);
    console.log(`发现 ${testFiles.length} 个测试文件`);

    // 计算测试覆盖率
    const testCoverage = calculateTestCoverage(sourceFiles, testFiles, exportedItems);

    return {
        name: packageName,
        version: packageJson.version || 'unknown',
        sourceFiles: sourceFiles.map(file => path.relative(packagePath, file)),
        testFiles: testFiles.map(file => path.relative(packagePath, file)),
        exportedItems,
        testCoverage,
        dependencies: packageJson.dependencies || {},
        devDependencies: packageJson.devDependencies || {}
    };
}

/**
 * 查找源文件
 */
function findSourceFiles(dir) {
    const results = [];

    function traverse(currentDir) {
        const items = fs.readdirSync(currentDir);

        for (const item of items) {
            const itemPath = path.join(currentDir, item);
            const stats = fs.statSync(itemPath);

            if (stats.isDirectory()) {
                if (!EXCLUDED_DIRS.includes(item)) {
                    traverse(itemPath);
                }
            } else if (stats.isFile()) {
                if (
                    (itemPath.endsWith('.ts') || itemPath.endsWith('.tsx')) &&
                    !EXCLUDED_FILES.some(ext => itemPath.endsWith(ext))
                ) {
                    results.push(itemPath);
                }
            }
        }
    }

    traverse(dir);
    return results;
}

/**
 * 查找测试文件
 */
function findTestFiles(packagePath) {
    const results = [];

    function traverse(currentDir) {
        if (!fs.existsSync(currentDir)) return;

        const items = fs.readdirSync(currentDir);

        for (const item of items) {
            const itemPath = path.join(currentDir, item);
            const stats = fs.statSync(itemPath);

            if (stats.isDirectory()) {
                if (!EXCLUDED_DIRS.includes(item)) {
                    traverse(itemPath);
                }
            } else if (stats.isFile()) {
                if (
                    itemPath.endsWith('.test.ts') ||
                    itemPath.endsWith('.test.tsx') ||
                    itemPath.endsWith('.spec.ts') ||
                    itemPath.endsWith('.spec.tsx')
                ) {
                    results.push(itemPath);
                }
            }
        }
    }

    traverse(packagePath);
    return results;
}

/**
 * 分析源文件，提取导出的函数、类和变量
 */
function analyzeSourceFile(filePath) {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const sourceFile = ts.createSourceFile(
        filePath,
        fileContent,
        ts.ScriptTarget.Latest,
        true
    );

    const exportedItems = [];

    function visit(node) {
        // 检查导出声明
        if (
            ts.isExportDeclaration(node) ||
            (ts.isVariableStatement(node) && hasExportModifier(node)) ||
            (ts.isFunctionDeclaration(node) && hasExportModifier(node)) ||
            (ts.isClassDeclaration(node) && hasExportModifier(node)) ||
            (ts.isInterfaceDeclaration(node) && hasExportModifier(node)) ||
            (ts.isTypeAliasDeclaration(node) && hasExportModifier(node))
        ) {
            const exportInfo = extractExportInfo(node);
            if (exportInfo) {
                exportedItems.push(exportInfo);
            }
        }

        ts.forEachChild(node, visit);
    }

    visit(sourceFile);
    return exportedItems;
}

/**
 * 检查节点是否有导出修饰符
 */
function hasExportModifier(node) {
    return node.modifiers && node.modifiers.some(mod => mod.kind === ts.SyntaxKind.ExportKeyword);
}

/**
 * 提取导出信息
 */
function extractExportInfo(node) {
    if (ts.isExportDeclaration(node)) {
        // 处理 export { x, y } 或 export * from 'module'
        if (node.exportClause && ts.isNamedExports(node.exportClause)) {
            const elements = node.exportClause.elements;
            return {
                type: 'NamedExport',
                names: elements.map(e => e.name.text),
                location: {
                    line: ts.getLineAndCharacterOfPosition(node.getSourceFile(), node.pos).line + 1,
                    column: ts.getLineAndCharacterOfPosition(node.getSourceFile(), node.pos).character + 1
                }
            };
        } else if (node.moduleSpecifier) {
            // export * from 'module'
            return {
                type: 'StarExport',
                module: node.moduleSpecifier.text,
                location: {
                    line: ts.getLineAndCharacterOfPosition(node.getSourceFile(), node.pos).line + 1,
                    column: ts.getLineAndCharacterOfPosition(node.getSourceFile(), node.pos).character + 1
                }
            };
        }
    } else if (ts.isFunctionDeclaration(node) && node.name) {
        // 处理 export function x() {}
        return {
            type: 'Function',
            name: node.name.text,
            location: {
                line: ts.getLineAndCharacterOfPosition(node.getSourceFile(), node.pos).line + 1,
                column: ts.getLineAndCharacterOfPosition(node.getSourceFile(), node.pos).character + 1
            }
        };
    } else if (ts.isClassDeclaration(node) && node.name) {
        // 处理 export class X {}
        return {
            type: 'Class',
            name: node.name.text,
            location: {
                line: ts.getLineAndCharacterOfPosition(node.getSourceFile(), node.pos).line + 1,
                column: ts.getLineAndCharacterOfPosition(node.getSourceFile(), node.pos).character + 1
            }
        };
    } else if (ts.isVariableStatement(node)) {
        // 处理 export const x = 1
        const declarations = node.declarationList.declarations;
        return {
            type: 'Variable',
            names: declarations.map(d => d.name.getText()),
            location: {
                line: ts.getLineAndCharacterOfPosition(node.getSourceFile(), node.pos).line + 1,
                column: ts.getLineAndCharacterOfPosition(node.getSourceFile(), node.pos).character + 1
            }
        };
    } else if (ts.isInterfaceDeclaration(node) && node.name) {
        // 处理 export interface X {}
        return {
            type: 'Interface',
            name: node.name.text,
            location: {
                line: ts.getLineAndCharacterOfPosition(node.getSourceFile(), node.pos).line + 1,
                column: ts.getLineAndCharacterOfPosition(node.getSourceFile(), node.pos).character + 1
            }
        };
    } else if (ts.isTypeAliasDeclaration(node) && node.name) {
        // 处理 export type X = {}
        return {
            type: 'Type',
            name: node.name.text,
            location: {
                line: ts.getLineAndCharacterOfPosition(node.getSourceFile(), node.pos).line + 1,
                column: ts.getLineAndCharacterOfPosition(node.getSourceFile(), node.pos).character + 1
            }
        };
    }

    return null;
}

/**
 * 计算测试覆盖率
 */
function calculateTestCoverage(sourceFiles, testFiles, exportedItems) {
    // 简单的覆盖率计算，基于文件数量
    const sourceFileCount = sourceFiles.length;
    const testFileCount = testFiles.length;

    // 导出项数量
    const exportedItemCount = exportedItems.length;

    // 估算覆盖率
    let estimatedCoverage = 0;
    if (sourceFileCount > 0) {
        estimatedCoverage = Math.min(testFileCount / sourceFileCount, 1) * 100;
    }

    return {
        sourceFileCount,
        testFileCount,
        exportedItemCount,
        estimatedCoverage: Math.round(estimatedCoverage)
    };
}

// 执行扫描
const results = scanPackages();

// 生成摘要报告
const summary = Object.entries(results).map(([pkg, info]) => ({
    package: pkg,
    sourceFiles: info.sourceFiles.length,
    testFiles: info.testFiles.length,
    exportedItems: info.exportedItems.length,
    estimatedCoverage: info.testCoverage.estimatedCoverage
}));

console.log('\n包扫描摘要:');
console.table(summary);

// 写入摘要
fs.writeFileSync(
    path.join(OUTPUT_DIR, 'package-summary.json'),
    JSON.stringify(summary, null, 2)
);

// 找出覆盖率低于100%的包
const lowCoveragePackages = summary.filter(pkg => pkg.estimatedCoverage < 100);
if (lowCoveragePackages.length > 0) {
    console.log('\n覆盖率低于100%的包:');
    console.table(lowCoveragePackages);

    fs.writeFileSync(
        path.join(OUTPUT_DIR, 'low-coverage-packages.json'),
        JSON.stringify(lowCoveragePackages, null, 2)
    );
}