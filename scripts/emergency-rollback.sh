#!/bin/bash
set -e

echo "🚨 执行紧急回滚程序..."
echo "================================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取当前分支名
CURRENT_BRANCH=$(git branch --show-current)
echo -e "${BLUE}当前分支: ${CURRENT_BRANCH}${NC}"

# 确认回滚操作
echo -e "${YELLOW}⚠️  这将回滚所有重构更改，是否继续？${NC}"
read -p "请输入 'YES' 确认回滚: " -r
if [[ $REPLY != "YES" ]]; then
    echo "回滚已取消"
    exit 0
fi

# 创建回滚时间戳
ROLLBACK_TIMESTAMP=$(date +%Y%m%d-%H%M%S)
echo -e "${BLUE}回滚时间戳: ${ROLLBACK_TIMESTAMP}${NC}"

# 1. 保存当前状态（以防需要恢复）
echo -e "${BLUE}💾 保存当前状态...${NC}"
git stash push -m "emergency-rollback-${ROLLBACK_TIMESTAMP}" || echo "没有需要暂存的更改"

# 2. 切换到主分支
echo -e "${BLUE}🔄 切换到主分支...${NC}"
git checkout main
git pull origin main

# 3. 创建回滚分支
ROLLBACK_BRANCH="hotfix/emergency-rollback-${ROLLBACK_TIMESTAMP}"
echo -e "${BLUE}🌿 创建回滚分支: ${ROLLBACK_BRANCH}${NC}"
git checkout -b "$ROLLBACK_BRANCH"

# 4. 恢复关键文件（如果备份存在）
echo -e "${BLUE}📁 检查备份文件...${NC}"
BACKUP_DIR="backups/$(date +%Y%m%d)"
if [ -d "$BACKUP_DIR" ]; then
    echo -e "${GREEN}找到今日备份，正在恢复...${NC}"
    
    # 恢复 core 包工具文件
    if [ -f "$BACKUP_DIR/core-utils.ts.bak" ]; then
        cp "$BACKUP_DIR/core-utils.ts.bak" packages/core/src/utils.ts
        echo "✅ 恢复 core/utils.ts"
    fi
    
    # 恢复 shared 包索引文件
    if [ -f "$BACKUP_DIR/shared-index.ts.bak" ]; then
        cp "$BACKUP_DIR/shared-index.ts.bak" packages/shared/src/index.ts
        echo "✅ 恢复 shared/index.ts"
    fi
else
    echo -e "${YELLOW}⚠️  未找到今日备份文件${NC}"
fi

# 5. 清理可能的重构产物
echo -e "${BLUE}🧹 清理重构产物...${NC}"

# 删除可能新增的工具目录
if [ -d "packages/shared/utils/src/type-check" ]; then
    rm -rf packages/shared/utils/src/type-check
    echo "✅ 清理 type-check 目录"
fi

if [ -d "packages/shared/utils/src/url" ]; then
    rm -rf packages/shared/utils/src/url
    echo "✅ 清理 url 目录"
fi

if [ -d "packages/shared/utils/src/logger" ]; then
    rm -rf packages/shared/utils/src/logger
    echo "✅ 清理 logger 目录"
fi

# 清理构建缓存
echo -e "${BLUE}🗑️  清理构建缓存...${NC}"
rm -rf packages/*/dist
rm -rf node_modules/.cache
rm -rf .tsbuildinfo

# 6. 重新安装依赖
echo -e "${BLUE}📦 重新安装依赖...${NC}"
pnpm install

# 7. 验证基本功能
echo -e "${BLUE}🧪 验证基本功能...${NC}"

# 构建测试
echo "  - 测试构建..."
if pnpm run build; then
    echo -e "${GREEN}✅ 构建成功${NC}"
else
    echo -e "${RED}❌ 构建失败${NC}"
    echo "需要手动检查构建问题"
fi

# 运行测试
echo "  - 运行测试..."
if pnpm run test; then
    echo -e "${GREEN}✅ 测试通过${NC}"
else
    echo -e "${RED}❌ 测试失败${NC}"
    echo "需要手动检查测试问题"
fi

# 类型检查
echo "  - 类型检查..."
if pnpm run type-check; then
    echo -e "${GREEN}✅ 类型检查通过${NC}"
else
    echo -e "${RED}❌ 类型检查失败${NC}"
    echo "需要手动检查类型问题"
fi

# 8. 生成回滚报告
echo -e "${BLUE}📄 生成回滚报告...${NC}"
mkdir -p reports

cat > reports/rollback-report-${ROLLBACK_TIMESTAMP}.md << EOF
# 紧急回滚报告

## 回滚信息
- **回滚时间**: $(date)
- **原分支**: ${CURRENT_BRANCH}
- **回滚分支**: ${ROLLBACK_BRANCH}
- **回滚原因**: 紧急回滚

## 回滚操作
- [x] 保存当前状态到 stash
- [x] 切换到主分支
- [x] 创建回滚分支
- [x] 恢复备份文件
- [x] 清理重构产物
- [x] 重新安装依赖
- [x] 验证基本功能

## 验证结果
- 构建状态: $(pnpm run build >/dev/null 2>&1 && echo "✅ 成功" || echo "❌ 失败")
- 测试状态: $(pnpm run test >/dev/null 2>&1 && echo "✅ 成功" || echo "❌ 失败")
- 类型检查: $(pnpm run type-check >/dev/null 2>&1 && echo "✅ 成功" || echo "❌ 失败")

## 后续建议
1. 检查系统功能是否正常
2. 分析回滚原因
3. 制定新的重构计划
4. 考虑分阶段重构策略

---
*报告生成时间: $(date)*
EOF

# 9. 提交回滚更改
echo -e "${BLUE}💾 提交回滚更改...${NC}"
git add .
git commit -m "emergency rollback: revert packages refactor at ${ROLLBACK_TIMESTAMP}"

echo "================================================"
echo -e "${GREEN}🎯 紧急回滚完成！${NC}"
echo ""
echo -e "${BLUE}📊 回滚摘要：${NC}"
echo "- 当前分支: ${ROLLBACK_BRANCH}"
echo "- 回滚报告: reports/rollback-report-${ROLLBACK_TIMESTAMP}.md"
echo "- 原状态已保存到 stash: emergency-rollback-${ROLLBACK_TIMESTAMP}"
echo ""
echo -e "${YELLOW}📝 后续操作建议：${NC}"
echo "1. 检查系统功能是否完全正常"
echo "2. 查看回滚报告了解详细信息"
echo "3. 分析导致回滚的原因"
echo "4. 如需恢复重构状态："
echo "   git stash list"
echo "   git stash apply stash@{0}  # 选择对应的 stash"
echo ""
echo -e "${GREEN}✅ 系统已回滚到稳定状态${NC}"