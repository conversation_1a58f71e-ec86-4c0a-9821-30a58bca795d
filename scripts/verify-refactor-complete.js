#!/usr/bin/env node

/**
 * Micro Core 重构完成度验证脚本
 * 检查重构任务的完成情况和质量指标
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class RefactorVerifier {
    constructor() {
        this.results = {
            tasks: { completed: 0, total: 0, details: [] },
            quality: { passed: 0, total: 0, details: [] },
            performance: { passed: 0, total: 0, details: [] },
            compatibility: { passed: 0, total: 0, details: [] }
        };
    }

    /**
     * 运行所有验证
     */
    async runAllVerifications() {
        console.log('🔍 开始验证重构完成度...\n');

        await this.verifyTaskCompletion();
        await this.verifyQualityMetrics();
        await this.verifyPerformanceMetrics();
        await this.verifyCompatibility();

        this.generateReport();
    }

    /**
     * 验证任务完成情况
     */
    async verifyTaskCompletion() {
        console.log('📋 验证任务完成情况...');

        try {
            const tasksFile = path.join(process.cwd(), '.kiro/specs/packages-optimization/tasks.md');
            const content = fs.readFileSync(tasksFile, 'utf8');

            // 统计任务完成情况
            const completedTasks = (content.match(/- \[x\]/g) || []).length;
            const totalTasks = (content.match(/- \[[x ]\]/g) || []).length;

            this.results.tasks.completed = completedTasks;
            this.results.tasks.total = totalTasks;

            // 分析未完成的任务
            const lines = content.split('\n');
            let currentSection = '';

            lines.forEach(line => {
                if (line.startsWith('### ')) {
                    currentSection = line.replace('### ', '').trim();
                } else if (line.match(/- \[ \]/)) {
                    const taskName = line.replace(/- \[ \]/, '').trim();
                    this.results.tasks.details.push({
                        section: currentSection,
                        task: taskName,
                        status: 'pending'
                    });
                }
            });

            console.log(`  ✅ 已完成: ${completedTasks}/${totalTasks} 个任务`);

            if (this.results.tasks.details.length > 0) {
                console.log('  ⚠️  未完成的任务:');
                this.results.tasks.details.forEach(task => {
                    console.log(`    - ${task.section}: ${task.task}`);
                });
            }
        } catch (error) {
            console.log('  ❌ 无法读取任务文件:', error.message);
        }

        console.log();
    }

    /**
     * 验证质量指标
     */
    async verifyQualityMetrics() {
        console.log('📊 验证质量指标...');

        // 检查代码重复率
        await this.checkCodeDuplication();

        // 检查类型覆盖率
        await this.checkTypeScript();

        // 检查测试覆盖率
        await this.checkTestCoverage();

        // 检查代码风格
        await this.checkCodeStyle();

        console.log();
    }

    /**
     * 检查代码重复率
     */
    async checkCodeDuplication() {
        try {
            console.log('  🔍 检查代码重复率...');

            // 运行 jscpd
            const result = execSync('jscpd --reporters json --silent packages/', {
                encoding: 'utf8',
                stdio: 'pipe'
            });

            const report = JSON.parse(result);
            const duplicationRate = report.statistics.total.percentage;

            const passed = duplicationRate < 1; // 目标: < 1%
            this.results.quality.total++;
            if (passed) this.results.quality.passed++;

            this.results.quality.details.push({
                metric: '代码重复率',
                value: `${duplicationRate}%`,
                target: '< 1%',
                passed
            });

            console.log(`    ${passed ? '✅' : '❌'} 代码重复率: ${duplicationRate}% (目标: < 1%)`);
        } catch (error) {
            console.log('    ⚠️  无法检查代码重复率:', error.message);
        }
    }

    /**
     * 检查 TypeScript 类型
     */
    async checkTypeScript() {
        try {
            console.log('  🔍 检查 TypeScript 类型...');

            execSync('npm run type-check', { stdio: 'pipe' });

            this.results.quality.total++;
            this.results.quality.passed++;

            this.results.quality.details.push({
                metric: 'TypeScript 类型检查',
                value: '通过',
                target: '无错误',
                passed: true
            });

            console.log('    ✅ TypeScript 类型检查通过');
        } catch (error) {
            this.results.quality.total++;

            this.results.quality.details.push({
                metric: 'TypeScript 类型检查',
                value: '失败',
                target: '无错误',
                passed: false
            });

            console.log('    ❌ TypeScript 类型检查失败');
        }
    }

    /**
     * 检查测试覆盖率
     */
    async checkTestCoverage() {
        try {
            console.log('  🔍 检查测试覆盖率...');

            execSync('npm run test:coverage', { stdio: 'pipe' });

            // 读取覆盖率报告
            const coverageFile = path.join(process.cwd(), 'coverage/coverage-summary.json');
            if (fs.existsSync(coverageFile)) {
                const coverage = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
                const totalCoverage = coverage.total.lines.pct;

                const passed = totalCoverage >= 85; // 目标: >= 85%
                this.results.quality.total++;
                if (passed) this.results.quality.passed++;

                this.results.quality.details.push({
                    metric: '测试覆盖率',
                    value: `${totalCoverage}%`,
                    target: '>= 85%',
                    passed
                });

                console.log(`    ${passed ? '✅' : '❌'} 测试覆盖率: ${totalCoverage}% (目标: >= 85%)`);
            } else {
                console.log('    ⚠️  未找到覆盖率报告');
            }
        } catch (error) {
            console.log('    ⚠️  无法检查测试覆盖率:', error.message);
        }
    }

    /**
     * 检查代码风格
     */
    async checkCodeStyle() {
        try {
            console.log('  🔍 检查代码风格...');

            execSync('npm run lint', { stdio: 'pipe' });

            this.results.quality.total++;
            this.results.quality.passed++;

            this.results.quality.details.push({
                metric: '代码风格检查',
                value: '通过',
                target: '无错误',
                passed: true
            });

            console.log('    ✅ 代码风格检查通过');
        } catch (error) {
            this.results.quality.total++;

            this.results.quality.details.push({
                metric: '代码风格检查',
                value: '失败',
                target: '无错误',
                passed: false
            });

            console.log('    ❌ 代码风格检查失败');
        }
    }

    /**
     * 验证性能指标
     */
    async verifyPerformanceMetrics() {
        console.log('⚡ 验证性能指标...');

        await this.checkBundleSize();
        await this.checkBuildTime();

        console.log();
    }

    /**
     * 检查包体积
     */
    async checkBundleSize() {
        try {
            console.log('  📦 检查包体积...');

            // 构建项目
            execSync('npm run build', { stdio: 'pipe' });

            // 计算总体积
            const result = execSync('du -sb packages/*/dist 2>/dev/null || echo "0"', {
                encoding: 'utf8'
            });

            const totalSize = result.split('\n')
                .filter(line => line.trim())
                .reduce((sum, line) => {
                    const size = parseInt(line.split('\t')[0]) || 0;
                    return sum + size;
                }, 0);

            const sizeInKB = Math.round(totalSize / 1024);
            const passed = sizeInKB < 945; // 目标: < 945KB

            this.results.performance.total++;
            if (passed) this.results.performance.passed++;

            this.results.performance.details.push({
                metric: '包体积',
                value: `${sizeInKB}KB`,
                target: '< 945KB',
                passed
            });

            console.log(`    ${passed ? '✅' : '❌'} 包体积: ${sizeInKB}KB (目标: < 945KB)`);
        } catch (error) {
            console.log('    ⚠️  无法检查包体积:', error.message);
        }
    }

    /**
     * 检查构建时间
     */
    async checkBuildTime() {
        try {
            console.log('  ⏱️  检查构建时间...');

            const startTime = Date.now();
            execSync('npm run build', { stdio: 'pipe' });
            const buildTime = (Date.now() - startTime) / 1000;

            const passed = buildTime < 70.1; // 目标: < 70.1s

            this.results.performance.total++;
            if (passed) this.results.performance.passed++;

            this.results.performance.details.push({
                metric: '构建时间',
                value: `${buildTime.toFixed(1)}s`,
                target: '< 70.1s',
                passed
            });

            console.log(`    ${passed ? '✅' : '❌'} 构建时间: ${buildTime.toFixed(1)}s (目标: < 70.1s)`);
        } catch (error) {
            console.log('    ⚠️  无法检查构建时间:', error.message);
        }
    }

    /**
     * 验证兼容性
     */
    async verifyCompatibility() {
        console.log('🔄 验证向后兼容性...');

        await this.checkAPICompatibility();
        await this.checkTestCompatibility();

        console.log();
    }

    /**
     * 检查 API 兼容性
     */
    async checkAPICompatibility() {
        try {
            console.log('  🔍 检查 API 兼容性...');

            // 运行兼容性测试
            execSync('npm run test:compatibility', { stdio: 'pipe' });

            this.results.compatibility.total++;
            this.results.compatibility.passed++;

            this.results.compatibility.details.push({
                metric: 'API 兼容性',
                value: '通过',
                target: '100% 兼容',
                passed: true
            });

            console.log('    ✅ API 兼容性测试通过');
        } catch (error) {
            this.results.compatibility.total++;

            this.results.compatibility.details.push({
                metric: 'API 兼容性',
                value: '失败',
                target: '100% 兼容',
                passed: false
            });

            console.log('    ❌ API 兼容性测试失败');
        }
    }

    /**
     * 检查测试兼容性
     */
    async checkTestCompatibility() {
        try {
            console.log('  🔍 检查测试兼容性...');

            // 运行所有测试
            execSync('npm test', { stdio: 'pipe' });

            this.results.compatibility.total++;
            this.results.compatibility.passed++;

            this.results.compatibility.details.push({
                metric: '测试兼容性',
                value: '通过',
                target: '所有测试通过',
                passed: true
            });

            console.log('    ✅ 所有测试通过');
        } catch (error) {
            this.results.compatibility.total++;

            this.results.compatibility.details.push({
                metric: '测试兼容性',
                value: '失败',
                target: '所有测试通过',
                passed: false
            });

            console.log('    ❌ 部分测试失败');
        }
    }

    /**
     * 生成验证报告
     */
    generateReport() {
        console.log('📋 生成验证报告...\n');

        const totalPassed = this.results.quality.passed +
            this.results.performance.passed +
            this.results.compatibility.passed;
        const totalChecks = this.results.quality.total +
            this.results.performance.total +
            this.results.compatibility.total;

        console.log('='.repeat(60));
        console.log('📊 重构完成度验证报告');
        console.log('='.repeat(60));
        console.log();

        // 任务完成情况
        const taskCompletion = (this.results.tasks.completed / this.results.tasks.total * 100).toFixed(1);
        console.log(`📋 任务完成度: ${this.results.tasks.completed}/${this.results.tasks.total} (${taskCompletion}%)`);

        // 质量指标
        const qualityRate = this.results.quality.total > 0 ?
            (this.results.quality.passed / this.results.quality.total * 100).toFixed(1) : '0';
        console.log(`📊 质量指标: ${this.results.quality.passed}/${this.results.quality.total} (${qualityRate}%)`);

        // 性能指标
        const performanceRate = this.results.performance.total > 0 ?
            (this.results.performance.passed / this.results.performance.total * 100).toFixed(1) : '0';
        console.log(`⚡ 性能指标: ${this.results.performance.passed}/${this.results.performance.total} (${performanceRate}%)`);

        // 兼容性
        const compatibilityRate = this.results.compatibility.total > 0 ?
            (this.results.compatibility.passed / this.results.compatibility.total * 100).toFixed(1) : '0';
        console.log(`🔄 兼容性: ${this.results.compatibility.passed}/${this.results.compatibility.total} (${compatibilityRate}%)`);

        console.log();
        console.log('-'.repeat(60));

        const overallRate = totalChecks > 0 ? (totalPassed / totalChecks * 100).toFixed(1) : '0';
        console.log(`🎯 总体完成度: ${totalPassed}/${totalChecks} (${overallRate}%)`);

        // 判断是否可以发布
        const canRelease = taskCompletion >= 95 && overallRate >= 90;
        console.log();

        if (canRelease) {
            console.log('🎉 恭喜！重构已基本完成，可以准备发布！');
        } else {
            console.log('⚠️  重构尚未完成，建议继续完善后再发布。');

            if (taskCompletion < 95) {
                console.log('   - 请完成剩余的重构任务');
            }
            if (overallRate < 90) {
                console.log('   - 请修复质量、性能或兼容性问题');
            }
        }

        console.log();
        console.log('📄 详细报告已保存到: reports/verification-report.json');

        // 保存详细报告
        this.saveDetailedReport();
    }

    /**
     * 保存详细报告
     */
    saveDetailedReport() {
        const reportDir = path.join(process.cwd(), 'reports');
        if (!fs.existsSync(reportDir)) {
            fs.mkdirSync(reportDir, { recursive: true });
        }

        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                tasks: this.results.tasks,
                quality: this.results.quality,
                performance: this.results.performance,
                compatibility: this.results.compatibility
            },
            details: {
                quality: this.results.quality.details,
                performance: this.results.performance.details,
                compatibility: this.results.compatibility.details
            }
        };

        fs.writeFileSync(
            path.join(reportDir, 'verification-report.json'),
            JSON.stringify(report, null, 2)
        );
    }
}

// 运行验证
if (require.main === module) {
    const verifier = new RefactorVerifier();
    verifier.runAllVerifications().catch(error => {
        console.error('❌ 验证过程出错:', error);
        process.exit(1);
    });
}

module.exports = RefactorVerifier;